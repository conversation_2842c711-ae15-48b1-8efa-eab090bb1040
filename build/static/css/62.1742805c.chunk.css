@font-face{fallback:fallback;font-family:revicons;src:url(/static/media/revicons.e8746a624ed098489406.woff) format("woff"),url(/static/media/revicons.57fd05d4ae650374c8de.ttf) format("ttf"),url(/static/media/revicons.a77de540a38981833f9e.eot) format("ttf")}.react-multi-carousel-list{align-items:center;display:flex;overflow:hidden;position:relative}.react-multi-carousel-track{-webkit-backface-visibility:hidden;backface-visibility:hidden;display:flex;flex-direction:row;list-style:none;margin:0;padding:0;position:relative;transform-style:preserve-3d;will-change:transform,transition}.react-multiple-carousel__arrow{background:#00000080;border:0;border-radius:35px;cursor:pointer;min-height:43px;min-width:43px;opacity:1;outline:0;position:absolute;transition:all .5s;z-index:1000}.react-multiple-carousel__arrow:hover{background:#000c}.react-multiple-carousel__arrow:before{color:#fff;display:block;font-family:revicons;font-size:20px;position:relative;text-align:center;z-index:2}.react-multiple-carousel__arrow:disabled{background:#00000080;cursor:default}.react-multiple-carousel__arrow--left{left:calc(4% + 1px)}.react-multiple-carousel__arrow--left:before{content:"\e824"}.react-multiple-carousel__arrow--right{right:calc(4% + 1px)}.react-multiple-carousel__arrow--right:before{content:"\e825"}.react-multi-carousel-dot-list{bottom:0;display:flex;justify-content:center;left:0;list-style:none;margin:0;padding:0;position:absolute;right:0;text-align:center}.react-multi-carousel-dot button{border:2px solid grey;border-radius:50%;box-shadow:none;cursor:pointer;display:inline-block;height:12px;margin:0 6px 0 0;opacity:1;outline:0;padding:0;transition:background .5s;width:12px}.react-multi-carousel-dot button:hover:active,.react-multi-carousel-dot--active button{background:#080808}.react-multi-carousel-item{-webkit-backface-visibility:hidden;backface-visibility:hidden;transform-style:preserve-3d}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.react-multi-carousel-item{flex-shrink:0!important}.react-multi-carousel-track{overflow:visible!important}}[dir=rtl].react-multi-carousel-list{direction:rtl}.rtl.react-multiple-carousel__arrow--right{left:calc(4% + 1px);right:auto}.rtl.react-multiple-carousel__arrow--right:before{content:"\e824"}.rtl.react-multiple-carousel__arrow--left{left:auto;right:calc(4% + 1px)}.rtl.react-multiple-carousel__arrow--left:before{content:"\e825"}
/*# sourceMappingURL=62.1742805c.chunk.css.map*/