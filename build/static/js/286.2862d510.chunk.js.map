{"version": 3, "file": "static/js/286.2862d510.chunk.js", "mappings": "+MAEO,MAAMA,EAAYC,EAAAA,GAAOC,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,iQAWtBC,EAAaJ,EAAAA,GAAOC,IAAGI,IAAAA,GAAAF,EAAAA,EAAAA,GAAA,6QAgBvBG,EAAgBN,EAAAA,GAAOC,IAAGM,IAAAA,GAAAJ,EAAAA,EAAAA,GAAA,+P,QC3BhC,MAAMJ,EAAYC,EAAAA,GAAOC,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,ibA0BtBK,EAAcR,EAAAA,GAAOS,IAAGJ,IAAAA,GAAAF,EAAAA,EAAAA,GAAA,sE,aCxBrC,MAQA,EARmBO,IAAe,IAAd,KAAEC,GAAMD,EAC1B,OACEE,EAAAA,EAAAA,KAACC,EAAgB,CAAAC,UACfF,EAAAA,EAAAA,KAACC,EAAkB,CAACE,IAAKJ,EAAKF,S,uBCPpC,SAASO,IAA2Q,OAA9PA,EAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUH,EAASY,MAAMC,KAAMR,UAAY,CAE5T,SAASS,EAAyBP,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAAkEC,EAAKJ,EAAnED,EAEzF,SAAuCI,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOa,EAAaf,OAAOgB,KAAKV,GAAqB,IAAKH,EAAI,EAAGA,EAAIY,EAAWV,OAAQF,IAAOI,EAAMQ,EAAWZ,GAAQW,EAASG,QAAQV,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAFhNgB,CAA8BZ,EAAQQ,GAAuB,GAAId,OAAOmB,sBAAuB,CAAE,IAAIC,EAAmBpB,OAAOmB,sBAAsBb,GAAS,IAAKH,EAAI,EAAGA,EAAIiB,EAAiBf,OAAQF,IAAOI,EAAMa,EAAiBjB,GAAQW,EAASG,QAAQV,IAAQ,GAAkBP,OAAOQ,UAAUa,qBAAqBX,KAAKJ,EAAQC,KAAgBL,EAAOK,GAAOD,EAAOC,GAAQ,CAAE,OAAOL,CAAQ,CAM3e,IAAIoB,GAAmBC,EAAAA,EAAAA,aAAW,SAAU9B,EAAM+B,GAChD,IAAIC,EAAahC,EAAKiC,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAYlC,EAAKmC,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,EAAyBpB,EAAM,CAAC,QAAS,SAEpD,OAAoBqC,EAAAA,cAAoB,MAAO/B,EAAS,CACtDyB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,SAAU,CACnDU,GAAI,KACJC,GAAI,KACJC,EAAG,OACYZ,EAAAA,cAAoB,WAAY,CAC/Ca,OAAQ,qBACOb,EAAAA,cAAoB,OAAQ,CAC3Cc,GAAI,IACJC,GAAI,KACJC,GAAI,KACJC,GAAI,OAER,IACAzB,EAAiB0B,UAAY,CAC3BtB,MAAOuB,IAAAA,OACPrB,KAAMqB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/C3B,EAAiB4B,YAAc,mBAC/B,U,gBC1CO,MAAMpE,EAAYC,EAAAA,GAAOC,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,qiBAgBpBO,IAAA,IAAG0D,OAAO,KAAEC,IAAQ3D,EAAA,OAAK2D,EAAKC,SAKrBC,IAAA,IAAGH,OAAO,OAAEI,IAAUD,EAAA,SAAAE,OAAQD,EAAOE,KAAI,SAKvCC,IAAA,IAAGP,OAAO,OAAEI,IAAUG,EAAA,OAAKH,EAAOI,SAQ/CC,EAAe7E,EAAAA,GAAOC,IAAGI,IAAAA,GAAAF,EAAAA,EAAAA,GAAA,mHAUzB2E,EAAa9E,EAAAA,GAAOC,IAAGM,IAAAA,GAAAJ,EAAAA,EAAAA,GAAA,8JAavB4E,EAAa/E,EAAAA,GAAOgF,KAAIC,IAAAA,GAAA9E,EAAAA,EAAAA,GAAA,QAExBC,EAAaJ,EAAAA,GAAOkF,GAAEC,IAAAA,GAAAhF,EAAAA,EAAAA,GAAA,yFAQtBiF,EAAWpF,EAAAA,GAAOqF,GAAEC,IAAAA,GAAAnF,EAAAA,EAAAA,GAAA,sLAWToF,IAAA,IAAGnB,OAAO,OAAEI,IAAUe,EAAA,OAAKf,EAAOgB,KAAKC,QCpD/D,EAtBiB/E,IAAiB,IAADgF,EAAA,IAAf,OAAEC,GAAQjF,EAE1B,OADAkF,QAAQC,IAAI,CAAEF,YAEZG,EAAAA,EAAAA,MAACjF,EAAgB,CAAAC,SAAA,EACfgF,EAAAA,EAAAA,MAACjF,EAAmB,CAACkF,UAAU,QAAOjF,SAAA,EACpCF,EAAAA,EAAAA,KAACC,EAAiB,CAAAC,SAAQ,OAAN6E,QAAM,IAANA,OAAM,EAANA,EAAQK,QAC5BpF,EAAAA,EAAAA,KAACC,EAAiB,CAAAC,SAAQ,OAAN6E,QAAM,IAANA,OAAM,EAANA,EAAQM,cAE9BrF,EAAAA,EAAAA,KAACC,EAAiB,CAAAC,SACT,OAAN6E,QAAM,IAANA,GAAY,QAAND,EAANC,EAAQhF,YAAI,IAAA+E,OAAN,EAANA,EAAcQ,KAAI,CAACC,EAAMC,KAEtBN,EAAAA,EAAAA,MAACjF,EAAe,CAACkF,UAAU,YAAWjF,SAAA,EACpCF,EAAAA,EAAAA,KAAC2B,EAAgB,KACjB3B,EAAAA,EAAAA,KAACC,EAAiB,CAAAC,SAAM,OAAJqF,QAAI,IAAJA,OAAI,EAAJA,EAAM7B,UAFgB8B,W,wiBCjBxD,SAASpF,IAA2Q,OAA9PA,EAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUH,EAASY,MAAMC,KAAMR,UAAY,CAE5T,SAASS,EAAyBP,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAAkEC,EAAKJ,EAAnED,EAEzF,SAAuCI,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOa,EAAaf,OAAOgB,KAAKV,GAAqB,IAAKH,EAAI,EAAGA,EAAIY,EAAWV,OAAQF,IAAOI,EAAMQ,EAAWZ,GAAQW,EAASG,QAAQV,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAFhNgB,CAA8BZ,EAAQQ,GAAuB,GAAId,OAAOmB,sBAAuB,CAAE,IAAIC,EAAmBpB,OAAOmB,sBAAsBb,GAAS,IAAKH,EAAI,EAAGA,EAAIiB,EAAiBf,OAAQF,IAAOI,EAAMa,EAAiBjB,GAAQW,EAASG,QAAQV,IAAQ,GAAkBP,OAAOQ,UAAUa,qBAAqBX,KAAKJ,EAAQC,KAAgBL,EAAOK,GAAOD,EAAOC,GAAQ,CAAE,OAAOL,CAAQ,CAM3e,IAAIkF,GAAW7D,EAAAA,EAAAA,aAAW,SAAU9B,EAAM+B,GACxC,IAAIC,EAAahC,EAAKiC,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAYlC,EAAKmC,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,EAAyBpB,EAAM,CAAC,QAAS,SAEpD,OAAoBqC,EAAAA,cAAoB,MAAO/B,EAAS,CACtDyB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,SAAU,CACnDU,GAAI,KACJC,GAAI,KACJC,EAAG,MACYZ,EAAAA,cAAoB,OAAQ,CAC3CuD,EAAG,muBAEP,IACAD,EAASpC,UAAY,CACnBtB,MAAOuB,IAAAA,OACPrB,KAAMqB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/CmC,EAASlC,YAAc,WACvB,UCvCA,SAASnD,IAA2Q,OAA9PA,EAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUH,EAASY,MAAMC,KAAMR,UAAY,CAE5T,SAASS,GAAyBP,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAAkEC,EAAKJ,EAAnED,EAEzF,SAAuCI,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOa,EAAaf,OAAOgB,KAAKV,GAAqB,IAAKH,EAAI,EAAGA,EAAIY,EAAWV,OAAQF,IAAOI,EAAMQ,EAAWZ,GAAQW,EAASG,QAAQV,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAFhNgB,CAA8BZ,EAAQQ,GAAuB,GAAId,OAAOmB,sBAAuB,CAAE,IAAIC,EAAmBpB,OAAOmB,sBAAsBb,GAAS,IAAKH,EAAI,EAAGA,EAAIiB,EAAiBf,OAAQF,IAAOI,EAAMa,EAAiBjB,GAAQW,EAASG,QAAQV,IAAQ,GAAkBP,OAAOQ,UAAUa,qBAAqBX,KAAKJ,EAAQC,KAAgBL,EAAOK,GAAOD,EAAOC,GAAQ,CAAE,OAAOL,CAAQ,CAM3e,IAAIoF,IAAO/D,EAAAA,EAAAA,aAAW,SAAU9B,EAAM+B,GACpC,IAAIC,EAAahC,EAAKiC,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAYlC,EAAKmC,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,GAAyBpB,EAAM,CAAC,QAAS,SAEpD,OAAoBqC,EAAAA,cAAoB,MAAO/B,EAAS,CACtDyB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,OAAQ,CACjDuD,EAAG,mDACYvD,EAAAA,cAAoB,WAAY,CAC/Ca,OAAQ,0BAEZ,IACA2C,GAAKtC,UAAY,CACftB,MAAOuB,IAAAA,OACPrB,KAAMqB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/CqC,GAAKpC,YAAc,OACnB,YCrCA,SAASnD,KAA2Q,OAA9PA,GAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUH,GAASY,MAAMC,KAAMR,UAAY,CAE5T,SAASS,GAAyBP,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAAkEC,EAAKJ,EAAnED,EAEzF,SAAuCI,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOa,EAAaf,OAAOgB,KAAKV,GAAqB,IAAKH,EAAI,EAAGA,EAAIY,EAAWV,OAAQF,IAAOI,EAAMQ,EAAWZ,GAAQW,EAASG,QAAQV,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAFhNgB,CAA8BZ,EAAQQ,GAAuB,GAAId,OAAOmB,sBAAuB,CAAE,IAAIC,EAAmBpB,OAAOmB,sBAAsBb,GAAS,IAAKH,EAAI,EAAGA,EAAIiB,EAAiBf,OAAQF,IAAOI,EAAMa,EAAiBjB,GAAQW,EAASG,QAAQV,IAAQ,GAAkBP,OAAOQ,UAAUa,qBAAqBX,KAAKJ,EAAQC,KAAgBL,EAAOK,GAAOD,EAAOC,GAAQ,CAAE,OAAOL,CAAQ,CAM3e,IAAIqF,IAAWhE,EAAAA,EAAAA,aAAW,SAAU9B,EAAM+B,GACxC,IAAIC,EAAahC,EAAKiC,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAYlC,EAAKmC,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,GAAyBpB,EAAM,CAAC,QAAS,SAEpD,OAAoBqC,EAAAA,cAAoB,MAAO/B,GAAS,CACtDyB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,OAAQ,CACjDuD,EAAG,6CACYvD,EAAAA,cAAoB,OAAQ,CAC3CuD,EAAG,+CAEP,IACAE,GAASvC,UAAY,CACnBtB,MAAOuB,IAAAA,OACPrB,KAAMqB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/CsC,GAASrC,YAAc,WACvB,YCrCA,SAASnD,KAA2Q,OAA9PA,GAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUH,GAASY,MAAMC,KAAMR,UAAY,CAE5T,SAASS,GAAyBP,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAAkEC,EAAKJ,EAAnED,EAEzF,SAAuCI,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOa,EAAaf,OAAOgB,KAAKV,GAAqB,IAAKH,EAAI,EAAGA,EAAIY,EAAWV,OAAQF,IAAOI,EAAMQ,EAAWZ,GAAQW,EAASG,QAAQV,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAFhNgB,CAA8BZ,EAAQQ,GAAuB,GAAId,OAAOmB,sBAAuB,CAAE,IAAIC,EAAmBpB,OAAOmB,sBAAsBb,GAAS,IAAKH,EAAI,EAAGA,EAAIiB,EAAiBf,OAAQF,IAAOI,EAAMa,EAAiBjB,GAAQW,EAASG,QAAQV,IAAQ,GAAkBP,OAAOQ,UAAUa,qBAAqBX,KAAKJ,EAAQC,KAAgBL,EAAOK,GAAOD,EAAOC,GAAQ,CAAE,OAAOL,CAAQ,CAM3e,IAAIsF,IAAQjE,EAAAA,EAAAA,aAAW,SAAU9B,EAAM+B,GACrC,IAAIC,EAAahC,EAAKiC,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAYlC,EAAKmC,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,GAAyBpB,EAAM,CAAC,QAAS,SAEpD,OAAoBqC,EAAAA,cAAoB,MAAO/B,GAAS,CACtDyB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,OAAQ,CACjDuD,EAAG,8CACYvD,EAAAA,cAAoB,SAAU,CAC7CU,GAAI,IACJC,GAAI,IACJC,EAAG,MACYZ,EAAAA,cAAoB,OAAQ,CAC3CuD,EAAG,+BACYvD,EAAAA,cAAoB,OAAQ,CAC3CuD,EAAG,8BAEP,IACAG,GAAMxC,UAAY,CAChBtB,MAAOuB,IAAAA,OACPrB,KAAMqB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/CuC,GAAMtC,YAAc,QACpB,YC3CA,SAASnD,KAA2Q,OAA9PA,GAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUH,GAASY,MAAMC,KAAMR,UAAY,CAE5T,SAASS,GAAyBP,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAAkEC,EAAKJ,EAAnED,EAEzF,SAAuCI,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOa,EAAaf,OAAOgB,KAAKV,GAAqB,IAAKH,EAAI,EAAGA,EAAIY,EAAWV,OAAQF,IAAOI,EAAMQ,EAAWZ,GAAQW,EAASG,QAAQV,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAFhNgB,CAA8BZ,EAAQQ,GAAuB,GAAId,OAAOmB,sBAAuB,CAAE,IAAIC,EAAmBpB,OAAOmB,sBAAsBb,GAAS,IAAKH,EAAI,EAAGA,EAAIiB,EAAiBf,OAAQF,IAAOI,EAAMa,EAAiBjB,GAAQW,EAASG,QAAQV,IAAQ,GAAkBP,OAAOQ,UAAUa,qBAAqBX,KAAKJ,EAAQC,KAAgBL,EAAOK,GAAOD,EAAOC,GAAQ,CAAE,OAAOL,CAAQ,CAM3e,IAAIuF,IAASlE,EAAAA,EAAAA,aAAW,SAAU9B,EAAM+B,GACtC,IAAIC,EAAahC,EAAKiC,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAYlC,EAAKmC,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,GAAyBpB,EAAM,CAAC,QAAS,SAEpD,OAAoBqC,EAAAA,cAAoB,MAAO/B,GAAS,CACtDyB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,OAAQ,CACjDuD,EAAG,6BACYvD,EAAAA,cAAoB,OAAQ,CAC3CuD,EAAG,+CACYvD,EAAAA,cAAoB,OAAQ,CAC3Cc,GAAI,IACJC,GAAI,IACJC,GAAI,IACJC,GAAI,MACWjB,EAAAA,cAAoB,OAAQ,CAC3Cc,GAAI,KACJC,GAAI,IACJC,GAAI,KACJC,GAAI,MACWjB,EAAAA,cAAoB,OAAQ,CAC3Cc,GAAI,KACJC,GAAI,IACJC,GAAI,KACJC,GAAI,MAER,IACA0C,GAAOzC,UAAY,CACjBtB,MAAOuB,IAAAA,OACPrB,KAAMqB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/CwC,GAAOvC,YAAc,SACrB,YCpDA,SAASnD,KAA2Q,OAA9PA,GAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUH,GAASY,MAAMC,KAAMR,UAAY,CAE5T,SAASS,GAAyBP,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAAkEC,EAAKJ,EAAnED,EAEzF,SAAuCI,EAAQQ,GAAY,GAAc,MAAVR,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOa,EAAaf,OAAOgB,KAAKV,GAAqB,IAAKH,EAAI,EAAGA,EAAIY,EAAWV,OAAQF,IAAOI,EAAMQ,EAAWZ,GAAQW,EAASG,QAAQV,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAFhNgB,CAA8BZ,EAAQQ,GAAuB,GAAId,OAAOmB,sBAAuB,CAAE,IAAIC,EAAmBpB,OAAOmB,sBAAsBb,GAAS,IAAKH,EAAI,EAAGA,EAAIiB,EAAiBf,OAAQF,IAAOI,EAAMa,EAAiBjB,GAAQW,EAASG,QAAQV,IAAQ,GAAkBP,OAAOQ,UAAUa,qBAAqBX,KAAKJ,EAAQC,KAAgBL,EAAOK,GAAOD,EAAOC,GAAQ,CAAE,OAAOL,CAAQ,CAM3e,IAAIwF,IAAenE,EAAAA,EAAAA,aAAW,SAAU9B,EAAM+B,GAC5C,IAAIC,EAAahC,EAAKiC,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAYlC,EAAKmC,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,GAAyBpB,EAAM,CAAC,QAAS,SAEpD,OAAoBqC,EAAAA,cAAoB,MAAO/B,GAAS,CACtDyB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,SAAU,CACnDU,GAAI,IACJC,GAAI,KACJC,EAAG,MACYZ,EAAAA,cAAoB,SAAU,CAC7CU,GAAI,KACJC,GAAI,KACJC,EAAG,MACYZ,EAAAA,cAAoB,OAAQ,CAC3CuD,EAAG,oEAEP,IACAK,GAAa1C,UAAY,CACvBtB,MAAOuB,IAAAA,OACPrB,KAAMqB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/CyC,GAAaxC,YAAc,eAC3B,YCtBayC,GAAa,CACxB,CACEnG,IAAKoG,GAEP,CACEpG,IAAKqG,GAEP,CACErG,IAAKsG,GAEP,CACEtG,I,klUAEF,CACEA,I,8xPAEF,CACEA,IAAKuG,GAEP,CACEvG,IAAKwG,GAEP,CACExG,IAAKyG,GAEP,CACEzG,IAAK0G,GAEP,CACE1G,IAAK2G,GAEP,CACE3G,IAAK4G,GAEP,CACE5G,IAAK6G,IAIIC,GAAiB,CAC5B,CACEC,GAAI,EACJvB,QAAS,0BACTD,MAAMpF,EAAAA,EAAAA,KAACyF,EAAQ,IACf1F,KAAM,CACJ,CACE2D,MAAO,wBAET,CACEA,MAAO,gBAET,CACEA,MAAO,2BAET,CACEA,MAAO,gCAET,CACEA,MAAO,2BAET,CACEA,MAAO,sBAET,CACEA,MAAO,gCAET,CACEA,MAAO,0BAET,CACEA,MAAO,yBAET,CACEA,MAAO,gCAET,CACEA,MAAO,wBAET,CACEA,MAAO,uCAET,CACEA,MAAO,wBAET,CACEA,MAAO,kCAET,CACEA,MAAO,oBAET,CACEA,MAAO,oBAET,CACEA,MAAO,YAET,CACEA,MAAO,aAET,CACEA,MAAO,WAET,CACEA,MAAO,YAET,CACEA,MAAO,iBAET,CACEA,MAAO,SAET,CACEA,MAAO,yCAET,CACEA,MAAO,8BAET,CACEA,MAAO,yBAET,CACEA,MAAO,YAET,CACEA,MAAO,2BAET,CACEA,MAAO,YAET,CACEA,MAAO,gCAET,CACEA,MAAO,2BAET,CACEA,MAAO,gCAET,CACEA,MAAO,cAET,CACEA,MAAO,WAET,CACEA,MAAO,eAET,CACEA,MAAO,0BAET,CACEA,MAAO,UAET,CACEA,MAAO,8BAET,CACEA,MAAO,+BAET,CACEA,MAAO,6CAET,CACEA,MAAO,8BAET,CACEA,MAAO,WAET,CACEA,MAAO,yBAET,CACEA,MAAO,mCAET,CACEA,MAAO,mCAET,CACEA,MAAO,6BAET,CACEA,MAAO,eAET,CACEA,MAAO,6BAET,CACEA,MAAO,8CAET,CACEA,MAAO,uCAET,CACEA,MAAO,SAET,CACEA,MAAO,qCAET,CACEA,MAAO,kCAET,CACEA,MAAO,SAET,CACEA,MAAO,0BAET,CACEA,MAAO,6BAET,CACEA,MAAO,yBAET,CACEA,MAAO,SAET,CACEA,MAAO,OAET,CACEA,MAAO,YAET,CACEA,MAAO,kCAET,CACEA,MAAO,mCAET,CACEA,MAAO,UAET,CACEA,MAAO,mBAET,CACEA,MAAO,oCAET,CACEA,MAAO,YAET,CACEA,MAAO,YAET,CACEA,MAAO,wBAET,CACEA,MAAO,SAET,CACEA,MAAO,eAET,CACEA,MAAO,wBAET,CACEA,MAAO,UAET,CACEA,MAAO,gBAET,CACEA,MAAO,mBAET,CACEA,MAAO,OAET,CACEA,MAAO,sCAET,CACEA,MAAO,YAET,CACEA,MAAO,gBAET,CACEA,MAAO,qBAET,CACEA,MAAO,OAET,CACEA,MAAO,kBAET,CACEA,MAAO,WAET,CACEA,MAAO,kBAET,CACEA,MAAO,oCAET,CACEA,MAAO,iBAET,CACEA,MAAO,mBAET,CACEA,MAAO,mBAET,CACEA,MAAO,kBAET,CACEA,MAAO,uBAET,CACEA,MAAO,iBAET,CACEA,MAAO,4BAET,CACEA,MAAO,kCAIb,CACEkD,GAAI,EACJvB,QAAS,cACTD,MAAMpF,EAAAA,EAAAA,KAAC2F,GAAI,IACX5F,KAAM,CACJ,CACE2D,MAAO,sBAET,CACEA,MAAO,sBAET,CACEA,MAAO,kBAET,CACEA,MAAO,kBAET,CACEA,MAAO,wBAET,CACEA,MAAO,aAET,CACEA,MAAO,yBAET,CACEA,MAAO,uBAET,CACEA,MAAO,oBAET,CACEA,MAAO,eAET,CACEA,MAAO,eAET,CACEA,MAAO,iBAET,CACEA,MAAO,uBAET,CACEA,MAAO,UAET,CACEA,MAAO,mBAET,CACEA,MAAO,mBAET,CACEA,MAAO,eAET,CACEA,MAAO,gBAET,CACEA,MAAO,mBAET,CACEA,MAAO,cAET,CACEA,MAAO,iCAET,CACEA,MAAO,iCAET,CACEA,MAAO,gBAET,CACEA,MAAO,gCAET,CACEA,MAAO,oBAET,CACEA,MAAO,kBAET,CACEA,MAAO,yBAET,CACEA,MAAO,2BAET,CACEA,MAAO,sBAET,CACEA,MAAO,sBAET,CACEA,MAAO,aAET,CACEA,MAAO,yBAET,CACEA,MAAO,qBAET,CACEA,MAAO,2CAET,CACEA,MAAO,kCAET,CACEA,MAAO,gBAET,CACEA,MAAO,eAET,CACEA,MAAO,eAET,CACEA,MAAO,iBAET,CACEA,MAAO,0BAET,CACEA,MAAO,eAET,CACEA,MAAO,uBAET,CACEA,MAAO,mBAET,CACEA,MAAO,gBAET,CACEA,MAAO,kBAET,CACEA,MAAO,4BAET,CACEA,MAAO,sBAET,CACEA,MAAO,wBAET,CACEA,MAAO,2BAET,CACEA,MAAO,sBAET,CACEA,MAAO,sBAET,CACEA,MAAO,uBAET,CACEA,MAAO,sBAET,CACEA,MAAO,iBAET,CACEA,MAAO,oBAET,CACEA,MAAO,iBAET,CACEA,MAAO,qBAET,CACEA,MAAO,oBAET,CACEA,MAAO,gCAET,CACEA,MAAO,sBAET,CACEA,MAAO,uBAET,CACEA,MAAO,uCAET,CACEA,MAAO,mBAET,CACEA,MAAO,wBAET,CACEA,MAAO,kBAET,CACEA,MAAO,mBAET,CACEA,MAAO,iBAET,CACEA,MAAO,gBAET,CACEA,MAAO,uBAET,CACEA,MAAO,eAET,CACEA,MAAO,yBAET,CACEA,MAAO,wBAET,CACEA,MAAO,oBAET,CACEA,MAAO,sBAET,CACEA,MAAO,oBAET,CACEA,MAAO,wBAET,CACEA,MAAO,oBAET,CACEA,MAAO,gBAET,CACEA,MAAO,qCAET,CACEA,MAAO,oBAET,CACEA,MAAO,wBAET,CACEA,MAAO,wBAET,CACEA,MAAO,0BAET,CACEA,MAAO,kBAET,CACEA,MAAO,kCAET,CACEA,MAAO,kBAET,CACEA,MAAO,oBAET,CACEA,MAAO,sBAET,CACEA,MAAO,sBAET,CACEA,MAAO,oBAET,CACEA,MAAO,kBAET,CACEA,MAAO,oBAET,CACEA,MAAO,2BAET,CACEA,MAAO,oBAET,CACEA,MAAO,oBAET,CACEA,MAAO,sBAET,CACEA,MAAO,yBAET,CACEA,MAAO,kBAET,CACEA,MAAO,kBAET,CACEA,MAAO,cAET,CACEA,MAAO,yBAET,CACEA,MAAO,sBAET,CACEA,MAAO,kBAET,CACEA,MAAO,qBAET,CACEA,MAAO,yCAET,CACEA,MAAO,qBAET,CACEA,MAAO,oBAIb,CACEkD,GAAI,EACJvB,QAAS,mBACTD,MAAMpF,EAAAA,EAAAA,KAAC4F,GAAQ,IACf7F,KAAM,CACJ,CACE2D,MAAO,+BAET,CACEA,MAAO,oBAET,CACEA,MAAO,uCAET,CACEA,MAAO,2BAET,CACEA,MAAO,uCAIb,CACEkD,GAAI,EACJvB,QAAS,eACTD,MAAMpF,EAAAA,EAAAA,KAAC6F,GAAK,IACZ9F,KAAM,CACJ,CACE2D,MAAO,0BAET,CACEA,MAAO,mBAET,CACEA,MAAO,qBAET,CACEA,MAAO,qBAET,CACEA,MAAO,2BAET,CACEA,MAAO,iCAET,CACEA,MAAO,mCAIb,CACEkD,GAAI,EACJvB,QAAS,SACTD,MAAMpF,EAAAA,EAAAA,KAAC8F,GAAM,IACb/F,KAAM,CACJ,CACE2D,MAAO,yBAET,CACEA,MAAO,uBAET,CACEA,MAAO,sBAET,CACEA,MAAO,sBAET,CACEA,MAAO,uBAIb,CACEkD,GAAI,EACJvB,QAAS,QACTD,MAAMpF,EAAAA,EAAAA,KAAC+F,GAAY,IACnBhG,KAAM,CACJ,CACE2D,MAAO,sBAET,CACEA,MAAO,aAET,CACEA,MAAO,qBChmBf,GA1HgBmD,KAyDZ3B,EAAAA,EAAAA,MAACjF,EAAgB,CAAAC,SAAA,EACfgF,EAAAA,EAAAA,MAACjF,EAAiB,CAAAC,SAAA,EAChBF,EAAAA,EAAAA,KAAC8G,EAAAA,EAAe,CAACpD,MAAM,aACvB1D,EAAAA,EAAAA,KAAC+G,EAAAA,QAAQ,CACPC,WA5DW,CACjBC,kBAAmB,CAEjBC,WAAY,CAAEC,IAAK,IAAMC,IAAK,KAC9BC,MAAO,EACPC,cAAe,GAEjBC,QAAS,CACPL,WAAY,CAAEC,IAAK,IAAMC,IAAK,MAC9BC,MAAO,EACPC,cAAe,GAEjBE,OAAQ,CACNN,WAAY,CAAEC,IAAK,KAAMC,IAAK,KAC9BC,MAAO,EACPC,cAAe,GAEjBG,OAAQ,CACNP,WAAY,CAAEC,IAAK,IAAKC,IAAK,GAC7BC,MAAO,EACPC,cAAe,IAyCXI,UAAQ,EACRC,UAAQ,EACRC,cAAe,IACfN,cAAe,EACfO,mBAAoB,IAAM3H,SAEzB8F,GAAWV,KAAI,CAACC,EAAMC,KACdxF,EAAAA,EAAAA,KAAC8H,EAAU,CAAa/H,KAAMwF,GAAbC,WAI9BxF,EAAAA,EAAAA,KAACC,EAAoB,CAAAC,SAClByG,GAAerB,KAAI,CAACC,EAAMC,KAClBxF,EAAAA,EAAAA,KAAC+H,EAAQ,CAAahD,OAAQQ,GAAfC,UCtFhC,GARoBwC,KAEhBhI,EAAAA,EAAAA,KAAA,OAAKmF,UAAU,YAAWjF,UACxBF,EAAAA,EAAAA,KAAC6G,GAAO,K,0DCLP,MAAM1H,E,QAAYC,GAAOC,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,6GAIlBO,IAAA,IAAG0D,OAAO,KAAEC,IAAQ3D,EAAA,OAAK2D,EAAKwE,O,aCF/C,MAQA,EARwBnI,IAAgB,IAAf,MAAE4D,GAAO5D,EAC9B,OACIE,EAAAA,EAAAA,KAACC,EAAgB,CAAAC,SACZwD,I", "sources": ["components/clients/styles.js", "components/clients/item/styles.js", "components/clients/item/index.jsx", "../node_modules/react-feather/dist/icons/arrow-right-circle.js", "components/clients/name-card/styles.js", "components/clients/name-card/index.jsx", "../node_modules/react-feather/dist/icons/settings.js", "../node_modules/react-feather/dist/icons/home.js", "../node_modules/react-feather/dist/icons/book-open.js", "../node_modules/react-feather/dist/icons/users.js", "../node_modules/react-feather/dist/icons/coffee.js", "../node_modules/react-feather/dist/icons/shopping-cart.js", "components/clients/data.js", "components/clients/index.jsx", "pages/clients/index.js", "components/global/seaction-title/styles.js", "components/global/seaction-title/index.jsx"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  margin: 50px 0;\n\n  .react-multiple-carousel__arrow.react-multiple-carousel__arrow--left {\n    transform: translateX(-90%);\n  }\n  .react-multiple-carousel__arrow.react-multiple-carousel__arrow--right {\n    transform: translateX(90%);\n  }\n`;\n\nexport const ListHolder = styled.div`\n  /* display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  } */\n\n  margin: 50px 0;\n`;\n\nexport const AllListHolder = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n\n  margin: 50px 0;\n`;\n", "import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  background-color: #fefefe;\n\n  padding: 20px;\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  max-height: 160px;\n  min-height: 160px;\n\n  margin: 0 10px;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: #ffffff;\n  }\n\n  cursor: pointer;\n  transition: all 0.2 ease-in-out;\n`;\n\nexport const ImageHolder = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: scale-down;\n`;\n", "import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst ClientItem = ({ data }) => {\n  return (\n    <Styles.Container>\n      <Styles.ImageHolder src={data.img} />\n    </Styles.Container>\n  );\n};\n\nexport default ClientItem;\n", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar ArrowRightCircle = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"circle\", {\n    cx: \"12\",\n    cy: \"12\",\n    r: \"10\"\n  }), /*#__PURE__*/React.createElement(\"polyline\", {\n    points: \"12 16 16 12 12 8\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"8\",\n    y1: \"12\",\n    x2: \"16\",\n    y2: \"12\"\n  }));\n});\nArrowRightCircle.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nArrowRightCircle.displayName = 'ArrowRightCircle';\nexport default ArrowRightCircle;", "import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n\n  background-color: #fefefe;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  min-height: 200px;\n  max-height: 420px;\n\n  margin: 0 10px;\n\n  font-size: ${({ theme: { font } }) => font.title};\n  font-weight: 700;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ${({ theme: { colors } }) => `${colors.grey}01`};\n    transition: all 0.3 ease-in-out;\n\n\n    .title {\n      background-color: ${({ theme: { colors } }) => colors.white};\n    }\n  }\n\n  cursor: pointer;\n  transition: all 0.3 ease-in-out;\n`;\n\nexport const TitleWrapper = styled.div`\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  font-size: 25px;\n\n  padding: 20px 60px;\n`;\n\nexport const IconHolder = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  margin-right: 10px;\n\n  svg {\n    width: 30px;\n    height: 30px;\n  }\n`;\n\nexport const TextHolder = styled.span``;\n\nexport const ListHolder = styled.ul`\n  padding: 20px 60px;\n  width: 100%;\n\n  height: 100%;\n  overflow-y: auto;\n`;\n\nexport const ListItme = styled.li`\n  padding: 10px;\n\n  display: flex;\n  align-items: center;\n\n  svg {\n    margin-right: 10px;\n  }\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: #ffffff;\n  }\n`;\n", "import React from \"react\";\n\nimport { ArrowRightCircle } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst NameCard = ({ client }) => {\n  console.log({ client });\n  return (\n    <Styles.Container>\n      <Styles.TitleWrapper className=\"title\">\n        <Styles.IconHolder>{client?.icon}</Styles.IconHolder>\n        <Styles.TextHolder>{client?.heading}</Styles.TextHolder>\n      </Styles.TitleWrapper>\n      <Styles.ListHolder>\n        {client?.data?.map((item, index) => {\n          return (\n            <Styles.ListItme className=\"list-item\" key={index}>\n              <ArrowRightCircle />\n              <Styles.TextHolder>{item?.title}</Styles.TextHolder>\n            </Styles.ListItme>\n          );\n        })}\n      </Styles.ListHolder>\n    </Styles.Container>\n  );\n};\n\nexport default NameCard;\n", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar Settings = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"circle\", {\n    cx: \"12\",\n    cy: \"12\",\n    r: \"3\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"\n  }));\n});\nSettings.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nSettings.displayName = 'Settings';\nexport default Settings;", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar Home = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"\n  }), /*#__PURE__*/React.createElement(\"polyline\", {\n    points: \"9 22 9 12 15 12 15 22\"\n  }));\n});\nHome.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nHome.displayName = 'Home';\nexport default Home;", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar BookOpen = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z\"\n  }));\n});\nBookOpen.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nBookOpen.displayName = 'BookOpen';\nexport default BookOpen;", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar Users = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"\n  }), /*#__PURE__*/React.createElement(\"circle\", {\n    cx: \"9\",\n    cy: \"7\",\n    r: \"4\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23 21v-2a4 4 0 0 0-3-3.87\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n  }));\n});\nUsers.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nUsers.displayName = 'Users';\nexport default Users;", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar Coffee = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M18 8h1a4 4 0 0 1 0 8h-1\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"1\",\n    x2: \"6\",\n    y2: \"4\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"10\",\n    y1: \"1\",\n    x2: \"10\",\n    y2: \"4\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"14\",\n    y1: \"1\",\n    x2: \"14\",\n    y2: \"4\"\n  }));\n});\nCoffee.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nCoffee.displayName = 'Coffee';\nexport default Coffee;", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar ShoppingCart = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"circle\", {\n    cx: \"9\",\n    cy: \"21\",\n    r: \"1\"\n  }), /*#__PURE__*/React.createElement(\"circle\", {\n    cx: \"20\",\n    cy: \"21\",\n    r: \"1\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6\"\n  }));\n});\nShoppingCart.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nShoppingCart.displayName = 'ShoppingCart';\nexport default ShoppingCart;", "import fourSeason from \"../../assets/clients/4season.png\";\nimport Capacitelogo from \"../../assets/clients/capacitelogo.png\";\nimport Damodar from \"../../assets/clients/damodar.jpeg\";\nimport HawareLogo from \"../../assets/clients/haware-logo.png\";\nimport InoxLogo from \"../../assets/clients/inox_logo.png\";\nimport JNJ from \"../../assets/clients/jnj.jpeg\";\nimport Kalpataru from \"../../assets/clients/kalpataru.jpeg\";\nimport MahaVitaran from \"../../assets/clients/mahaVitaran.png\";\nimport <PERSON><PERSON> from \"../../assets/clients/raviraj.png\";\nimport SerumLogo from \"../../assets/clients/serum.png\";\nimport Technova from \"../../assets/clients/technova.png\";\nimport Trustlogo from \"../../assets/clients/trustlogo.jpeg\";\nimport {\n  Settings,\n  Home,\n  BookOpen,\n  Users,\n  Coffee,\n  ShoppingCart,\n} from \"react-feather\";\n\nexport const clientdata = [\n  {\n    img: fourSeason,\n  },\n  {\n    img: Capacitelogo,\n  },\n  {\n    img: Damodar,\n  },\n  {\n    img: HawareLogo,\n  },\n  {\n    img: InoxLogo,\n  },\n  {\n    img: JNJ,\n  },\n  {\n    img: Kalpataru,\n  },\n  {\n    img: MahaVitaran,\n  },\n  {\n    img: Raviraj,\n  },\n  {\n    img: SerumLogo,\n  },\n  {\n    img: Technova,\n  },\n  {\n    img: Trustlogo,\n  },\n];\n\nexport const allClientsData = [\n  {\n    id: 1,\n    heading: \"Industrial / Commercial\",\n    icon: <Settings />,\n    data: [\n      {\n        title: \"Bayer Vapi Pvt. Ltd.\",\n      },\n      {\n        title: \"Embassy 24x7\",\n      },\n      {\n        title: \"Siyaram Silk Mill, Ltd.\",\n      },\n      {\n        title: \"Damodar Silk Mills Pvt. Ltd.\",\n      },\n      {\n        title: \"Damodar Industries Ltd.\",\n      },\n      {\n        title: \"Platinum Logistics\",\n      },\n      {\n        title: \"Pantaloon Retails India Ltd.\",\n      },\n      {\n        title: \"Donear Industries Ltd.\",\n      },\n      {\n        title: \"Pal Fashion Pvt. Ltd.\",\n      },\n      {\n        title: \"Sunrise Containers Pvt. Ltd.\",\n      },\n      {\n        title: \"G M Syntex Pvt. Ltd.\",\n      },\n      {\n        title: \"D' Décor Homes fabrics Pvt. Ltd.\",\n      },\n      {\n        title: \"Rukshmani Synthetics\",\n      },\n      {\n        title: \"Shree NM Electricals Pvt. Ltd.\",\n      },\n      {\n        title: \"Platinum Fabrics\",\n      },\n      {\n        title: \"Aarti Drugs Ltd.\",\n      },\n      {\n        title: \"Oxemberg\",\n      },\n      {\n        title: \"Samosaran\",\n      },\n      {\n        title: \"Konarak\",\n      },\n      {\n        title: \"Sushitex\",\n      },\n      {\n        title: \"Jai Corp Ltd.\",\n      },\n      {\n        title: \"Naari\",\n      },\n      {\n        title: \"ASM Industries – India Pvt. Ltd.\",\n      },\n      {\n        title: \"United Santosh Enterprises\",\n      },\n      {\n        title: \"Balkrishna Paper Mill\",\n      },\n      {\n        title: \"Mandhana\",\n      },\n      {\n        title: \"Nahar Textile Pvt. Ltd.\",\n      },\n      {\n        title: \"Narain's\",\n      },\n      {\n        title: \"Raj Rajendra Industries Ltd.\",\n      },\n      {\n        title: \"Unitec Fibres Pvt. Ltd.\",\n      },\n      {\n        title: \"Kriplon Synthetics Pvt. Ltd.\",\n      },\n      {\n        title: \"Madhusudan\",\n      },\n      {\n        title: \"Sunayaa\",\n      },\n      {\n        title: \"N G Nextgen\",\n      },\n      {\n        title: \"Sanaa Syntex Pvt. Ltd.\",\n      },\n      {\n        title: \"KayKay\",\n      },\n      {\n        title: \"Mukat Tanks & Vessels Ltd.\",\n      },\n      {\n        title: \"Vionod Intelligent Cookware\",\n      },\n      {\n        title: \"Silomec drilling and foundation equipment\",\n      },\n      {\n        title: \"G.R. Engineering Pvt. Ltd.\",\n      },\n      {\n        title: \"Modison\",\n      },\n      {\n        title: \"Vinod Stainless Steel\",\n      },\n      {\n        title: \"High Volt Electricals Pvt. Ltd.\",\n      },\n      {\n        title: \"Karamtara Engineering Pvt. Ltd.\",\n      },\n      {\n        title: \"Zenith Birla (india) Ltd.\",\n      },\n      {\n        title: \"Loba Chemie\",\n      },\n      {\n        title: \"Nirbhay Rasayan Pvt. Ltd.\",\n      },\n      {\n        title: \"Suru Chemicals & Pharmaceuticals Pvt. Ltd.\",\n      },\n      {\n        title: \"Mitsui Chemicals Group •MOMCPL\",\n      },\n      {\n        title: \"Astik\",\n      },\n      {\n        title: \"Rank organics chemicals Pvt. Ltd.\",\n      },\n      {\n        title: \"SS Astra Formulation Pvt. Ltd.\",\n      },\n      {\n        title: \"Sarex\",\n      },\n      {\n        title: \"Ganesh Benzoplast Ltd.\",\n      },\n      {\n        title: \"Mohini organics Pvt. Ltd.\",\n      },\n      {\n        title: \"Bhagat Aromatics Ltd.\",\n      },\n      {\n        title: \"Crown\",\n      },\n      {\n        title: \"SNA\",\n      },\n      {\n        title: \"Siramaxo\",\n      },\n      {\n        title: \"Maxwell Life Science Pvt. Ltd.\",\n      },\n      {\n        title: \"Mehta Pharmaceuticals Pvt. Ltd.\",\n      },\n      {\n        title: \"Medley\",\n      },\n      {\n        title: \"Salasar Plastic\",\n      },\n      {\n        title: \"Sunrise Containers Ltd. (Sunpet)\",\n      },\n      {\n        title: \"Champion\",\n      },\n      {\n        title: \"Shree NM\",\n      },\n      {\n        title: \"Moonlight Industries\",\n      },\n      {\n        title: \"Fibro\",\n      },\n      {\n        title: \"Ramky Group\",\n      },\n      {\n        title: \"SKG Refractories Ltd\",\n      },\n      {\n        title: \"Haware\",\n      },\n      {\n        title: \"Dosti Realty\",\n      },\n      {\n        title: \"Provenance Land\",\n      },\n      {\n        title: \"MRF\",\n      },\n      {\n        title: \"Technova Imagine Systems Pvt. Ltd.\",\n      },\n      {\n        title: \"Capacite\",\n      },\n      {\n        title: \"ACC Concrete\",\n      },\n      {\n        title: \"Inox Air Products\",\n      },\n      {\n        title: \"RNA\",\n      },\n      {\n        title: \"Kasam Builders\",\n      },\n      {\n        title: \"Siyaram\",\n      },\n      {\n        title: \"Raviraj Realty\",\n      },\n      {\n        title: \"Panexcell Clinical Lab Pvt. Ltd.\",\n      },\n      {\n        title: \"Shree Ahinant\",\n      },\n      {\n        title: \"Cyrus Poonawala\",\n      },\n      {\n        title: \"Cyrum Institute\",\n      },\n      {\n        title: \"Millenium Star\",\n      },\n      {\n        title: \"Synthetic Pvt. Ltd.\",\n      },\n      {\n        title: \"NOCIL Limited\",\n      },\n      {\n        title: \"Gold Fingerest Pvt. Ltd.\",\n      },\n      {\n        title: \"Trimity Developers Pvt. Ltd.\",\n      },\n    ],\n  },\n  {\n    id: 2,\n    heading: \"Residential\",\n    icon: <Home />,\n    data: [\n      {\n        title: \"Krishna Green Park\",\n      },\n      {\n        title: \"Swagat Agro Agency\",\n      },\n      {\n        title: \"Athar Building\",\n      },\n      {\n        title: \"Padmavati CHS,\",\n      },\n      {\n        title: \"Boomerang Chandivali\",\n      },\n      {\n        title: \"Borla CHS\",\n      },\n      {\n        title: \"OM Siddharaj CHS ltd.\",\n      },\n      {\n        title: \"Dreamland Building,\",\n      },\n      {\n        title: \"Charni Road East\",\n      },\n      {\n        title: \"Garden View\",\n      },\n      {\n        title: \"Silco House\",\n      },\n      {\n        title: \"S. K. Academy\",\n      },\n      {\n        title: \"Shri Shivakrupa CHS\",\n      },\n      {\n        title: \"Shelar\",\n      },\n      {\n        title: \"Jai Ganaraj CHS\",\n      },\n      {\n        title: \"Akash Vihar CHS\",\n      },\n      {\n        title: \"Parvati CHS\",\n      },\n      {\n        title: \"Vasant Vihar\",\n      },\n      {\n        title: \"Rajnigandha CHS\",\n      },\n      {\n        title: \"Cosmos CHS\",\n      },\n      {\n        title: \"Balkrishna Quarters, Dombivli\",\n      },\n      {\n        title: \"Raymond, Ten X Habitat, Thane\",\n      },\n      {\n        title: \"Gangotri CHS\",\n      },\n      {\n        title: \"Shree Samarth Kuti Apartment\",\n      },\n      {\n        title: \"Krishna Kunj CHS\",\n      },\n      {\n        title: \"Manusmruti CHS\",\n      },\n      {\n        title: \"Krishna Saraswati CHS\",\n      },\n      {\n        title: \"Gokuldham Vrundavan CHS\",\n      },\n      {\n        title: \"Dhanlaxmi Chambers\",\n      },\n      {\n        title: \"Vijay Nagari Annex\",\n      },\n      {\n        title: \"Sobat CHS\",\n      },\n      {\n        title: \"Adarsh Peace Building\",\n      },\n      {\n        title: \"Kasturi Plaza CHS\",\n      },\n      {\n        title: \"Al-Jamiah-Al-Islamiyyah Noor Bagh Kausa\",\n      },\n      {\n        title: \"Vrindavan Dham, D Building CHS\",\n      },\n      {\n        title: \"Bhavan Mahal\",\n      },\n      {\n        title: \"Agarwal CHS\",\n      },\n      {\n        title: \"Kamla Niwas\",\n      },\n      {\n        title: \"Gangatara CHS\",\n      },\n      {\n        title: \"Shree Sidhivinayak CHS\",\n      },\n      {\n        title: \"Om Chambers\",\n      },\n      {\n        title: \"Kemps Cornor MUMBAI\",\n      },\n      {\n        title: \"Coral crest CHS\",\n      },\n      {\n        title: \"Parinita CHS\",\n      },\n      {\n        title: \"Gyan Kutir CHS\",\n      },\n      {\n        title: \"Harasiddh Park A3-A4 CHS\",\n      },\n      {\n        title: \"Highway Shirin CHS\",\n      },\n      {\n        title: \"Panchawati Apartment\",\n      },\n      {\n        title: \"Konark Indraprastha CHS\",\n      },\n      {\n        title: \"Mangalyakushma CHS\",\n      },\n      {\n        title: \"Siddhivinayak CHS,\",\n      },\n      {\n        title: \"Har Har Mahadev CHS\",\n      },\n      {\n        title: \"Bhagwan Walmki CHS\",\n      },\n      {\n        title: \"Tarang C.H.S.\",\n      },\n      {\n        title: \"Anand Palace CHS\",\n      },\n      {\n        title: \"Rameshwar CHS\",\n      },\n      {\n        title: \"Neelkanth Heights\",\n      },\n      {\n        title: \"Mahavir Heritage\",\n      },\n      {\n        title: \"Shree Ganesh Krupa Apartment\",\n      },\n      {\n        title: \"Nand Apartment CHS\",\n      },\n      {\n        title: \"Oshiwara Link Plaza\",\n      },\n      {\n        title: \"Royal Shalibhadra Complex No 1. CHS\",\n      },\n      {\n        title: \"Shyamkutir CHS,\",\n      },\n      {\n        title: \"Om Namha Shivay CHS,\",\n      },\n      {\n        title: \"Golden Express\",\n      },\n      {\n        title: \"Shriniketan CHS\",\n      },\n      {\n        title: \"Kailas Avenue\",\n      },\n      {\n        title: \"JS Infratech\",\n      },\n      {\n        title: \"Saraswati Nagar CHS\",\n      },\n      {\n        title: \"Vaibhav CHS\",\n      },\n      {\n        title: \"Revati Akashganga CHS\",\n      },\n      {\n        title: \"Anubhav Building CHS\",\n      },\n      {\n        title: \"Rahat Manzil CHS\",\n      },\n      {\n        title: \"Shree Dharshan CHS\",\n      },\n      {\n        title: \"Mahavir Dham CHS\",\n      },\n      {\n        title: \"Sai Ashish Apartment\",\n      },\n      {\n        title: \"Shweta Apartment\",\n      },\n      {\n        title: \"Surabhi CHSL\",\n      },\n      {\n        title: \"400 KV Substation Colony Kharghar\",\n      },\n      {\n        title: \"Sewa Samiti CHS,\",\n      },\n      {\n        title: \"Sion Koliwada Mumbai\",\n      },\n      {\n        title: \"Nilkanth Darshan CHS\",\n      },\n      {\n        title: \"Brahmand Phase-IV CHSL\",\n      },\n      {\n        title: \"Sindhi Society\",\n      },\n      {\n        title: \"Sidharth Nagar Bldg No- 4 CHSL\",\n      },\n      {\n        title: \"Amar Joyti CHS\",\n      },\n      {\n        title: \"Dosti Apartment,\",\n      },\n      {\n        title: \"Navsai Prasad CHSL\",\n      },\n      {\n        title: \"Chamunda Jewel CHS\",\n      },\n      {\n        title: \"Shiv Smruti CHSL\",\n      },\n      {\n        title: \"Wimbledon Park\",\n      },\n      {\n        title: \"Purohit Building\",\n      },\n      {\n        title: \"Rajani Gandha Apartment\",\n      },\n      {\n        title: \"Ananta Apartment\",\n      },\n      {\n        title: \"Ganesh Krupa CHS\",\n      },\n      {\n        title: \"Pearl Mansion CHSL\",\n      },\n      {\n        title: \"Building Marine Lines\",\n      },\n      {\n        title: \"Mayur Park CHS\",\n      },\n      {\n        title: \"Shreeniwas CHS\",\n      },\n      {\n        title: \"Shiram CHS\",\n      },\n      {\n        title: \"Rashmi Drashtant CHSL\",\n      },\n      {\n        title: \"Sai Dharshan B CHS\",\n      },\n      {\n        title: \"Sai Snehal CHS\",\n      },\n      {\n        title: \"Shivanand Society\",\n      },\n      {\n        title: \"Shree Arihant Compound Kalher Village\",\n      },\n      {\n        title: \"Shri Sai Baba CHS\",\n      },\n      {\n        title: \"Arundhati CHSL\",\n      },\n    ],\n  },\n  {\n    id: 3,\n    heading: \"School / College\",\n    icon: <BookOpen />,\n    data: [\n      {\n        title: \"Thakur Engineering College,\",\n      },\n      {\n        title: \"Somaiya College,\",\n      },\n      {\n        title: \"Guru Nanak Dev Engineering College,\",\n      },\n      {\n        title: \"Jhun Jhun wala College,\",\n      },\n      {\n        title: \"Rafiuddin Fakih boys High School,\",\n      },\n    ],\n  },\n  {\n    id: 4,\n    heading: \"Public Trust\",\n    icon: <Users />,\n    data: [\n      {\n        title: \"Maharashtra Seva Sangh\",\n      },\n      {\n        title: \"Orthodox Church\",\n      },\n      {\n        title: \"St. Joseph Church\",\n      },\n      {\n        title: \"St. John's Church\",\n      },\n      {\n        title: \"Sharma Shikshan Sanstha\",\n      },\n      {\n        title: \"Maharashtra Jivan Pradhikaran\",\n      },\n      {\n        title: \"Navabharat Education Socienty\",\n      },\n    ],\n  },\n  {\n    id: 5,\n    heading: \"Hotels\",\n    icon: <Coffee />,\n    data: [\n      {\n        title: \"Nariman House, Mumbai\",\n      },\n      {\n        title: \"Four Season, Mumbai\",\n      },\n      {\n        title: \"Hotel Ashok, Thane\",\n      },\n      {\n        title: \"Lokesh Hotel, Pune\",\n      },\n      {\n        title: \"Ram Mahal, Mumbai\",\n      },\n    ],\n  },\n  {\n    id: 6,\n    heading: \"Malls\",\n    icon: <ShoppingCart />,\n    data: [\n      {\n        title: \"Metro Mall, Kalyan\",\n      },\n      {\n        title: \"Mega Mall\",\n      },\n      {\n        title: \"Millenium Mall\",\n      },\n    ],\n  },\n];\n\nexport const industrialCommercial = [\n  {\n    title: \"Bayer Vapi Pvt. Ltd.\",\n  },\n  {\n    title: \"Embassy 24x7\",\n  },\n  {\n    title: \"Siyaram Silk Mill, Ltd.\",\n  },\n  {\n    title: \"Damodar Silk Mills Pvt. Ltd.\",\n  },\n  {\n    title: \"Damodar Industries Ltd.\",\n  },\n  {\n    title: \"Platinum Logistics\",\n  },\n  {\n    title: \"Pantaloon Retails India Ltd.\",\n  },\n  {\n    title: \"Donear Industries Ltd.\",\n  },\n  {\n    title: \"Pal Fashion Pvt. Ltd.\",\n  },\n  {\n    title: \"Sunrise Containers Pvt. Ltd.\",\n  },\n  {\n    title: \"G M Syntex Pvt. Ltd.\",\n  },\n  {\n    title: \"D' Décor Homes fabrics Pvt. Ltd.\",\n  },\n  {\n    title: \"Rukshmani Synthetics\",\n  },\n  {\n    title: \"Shree NM Electricals Pvt. Ltd.\",\n  },\n  {\n    title: \"Platinum Fabrics\",\n  },\n  {\n    title: \"Aarti Drugs Ltd.\",\n  },\n  {\n    title: \"Oxemberg\",\n  },\n  {\n    title: \"Samosaran\",\n  },\n  {\n    title: \"Konarak\",\n  },\n  {\n    title: \"Sushitex\",\n  },\n  {\n    title: \"Jai Corp Ltd.\",\n  },\n  {\n    title: \"Naari\",\n  },\n  {\n    title: \"ASM Industries – India Pvt. Ltd.\",\n  },\n  {\n    title: \"United Santosh Enterprises\",\n  },\n  {\n    title: \"Balkrishna Paper Mill\",\n  },\n  {\n    title: \"Mandhana\",\n  },\n  {\n    title: \"Nahar Textile Pvt. Ltd.\",\n  },\n  {\n    title: \"Narain's\",\n  },\n  {\n    title: \"Raj Rajendra Industries Ltd.\",\n  },\n  {\n    title: \"Unitec Fibres Pvt. Ltd.\",\n  },\n  {\n    title: \"Kriplon Synthetics Pvt. Ltd.\",\n  },\n  {\n    title: \"Madhusudan\",\n  },\n  {\n    title: \"Sunayaa\",\n  },\n  {\n    title: \"N G Nextgen\",\n  },\n  {\n    title: \"Sanaa Syntex Pvt. Ltd.\",\n  },\n  {\n    title: \"KayKay\",\n  },\n  {\n    title: \"Mukat Tanks & Vessels Ltd.\",\n  },\n  {\n    title: \"Vionod Intelligent Cookware\",\n  },\n  {\n    title: \"Silomec drilling and foundation equipment\",\n  },\n  {\n    title: \"G.R. Engineering Pvt. Ltd.\",\n  },\n  {\n    title: \"Modison\",\n  },\n  {\n    title: \"Vinod Stainless Steel\",\n  },\n  {\n    title: \"High Volt Electricals Pvt. Ltd.\",\n  },\n  {\n    title: \"Karamtara Engineering Pvt. Ltd.\",\n  },\n  {\n    title: \"Zenith Birla (india) Ltd.\",\n  },\n  {\n    title: \"Loba Chemie\",\n  },\n  {\n    title: \"Nirbhay Rasayan Pvt. Ltd.\",\n  },\n  {\n    title: \"Suru Chemicals & Pharmaceuticals Pvt. Ltd.\",\n  },\n  {\n    title: \"Mitsui Chemicals Group •MOMCPL\",\n  },\n  {\n    title: \"Astik\",\n  },\n  {\n    title: \"Rank organics chemicals Pvt. Ltd.\",\n  },\n  {\n    title: \"SS Astra Formulation Pvt. Ltd.\",\n  },\n  {\n    title: \"Sarex\",\n  },\n  {\n    title: \"Ganesh Benzoplast Ltd.\",\n  },\n  {\n    title: \"Mohini organics Pvt. Ltd.\",\n  },\n  {\n    title: \"Bhagat Aromatics Ltd.\",\n  },\n  {\n    title: \"Crown\",\n  },\n  {\n    title: \"SNA\",\n  },\n  {\n    title: \"Siramaxo\",\n  },\n  {\n    title: \"Maxwell Life Science Pvt. Ltd.\",\n  },\n  {\n    title: \"Mehta Pharmaceuticals Pvt. Ltd.\",\n  },\n  {\n    title: \"Medley\",\n  },\n  {\n    title: \"Salasar Plastic\",\n  },\n  {\n    title: \"Sunrise Containers Ltd. (Sunpet)\",\n  },\n  {\n    title: \"Champion\",\n  },\n  {\n    title: \"Shree NM\",\n  },\n  {\n    title: \"Moonlight Industries\",\n  },\n  {\n    title: \"Fibro\",\n  },\n  {\n    title: \"Ramky Group\",\n  },\n  {\n    title: \"SKG Refractories Ltd\",\n  },\n  {\n    title: \"Haware\",\n  },\n  {\n    title: \"Dosti Realty\",\n  },\n  {\n    title: \"Provenance Land\",\n  },\n  {\n    title: \"MRF\",\n  },\n  {\n    title: \"Technova Imagine Systems Pvt. Ltd.\",\n  },\n  {\n    title: \"Capacite\",\n  },\n  {\n    title: \"ACC Concrete\",\n  },\n  {\n    title: \"Inox Air Products\",\n  },\n  {\n    title: \"RNA\",\n  },\n  {\n    title: \"Kasam Builders\",\n  },\n  {\n    title: \"Siyaram\",\n  },\n  {\n    title: \"Raviraj Realty\",\n  },\n  {\n    title: \"Panexcell Clinical Lab Pvt. Ltd.\",\n  },\n  {\n    title: \"Shree Ahinant\",\n  },\n  {\n    title: \"Cyrus Poonawala\",\n  },\n  {\n    title: \"Cyrum Institute\",\n  },\n  {\n    title: \"Millenium Star\",\n  },\n  {\n    title: \"Synthetic Pvt. Ltd.\",\n  },\n  {\n    title: \"NOCIL Limited\",\n  },\n  {\n    title: \"Gold Fingerest Pvt. Ltd.\",\n  },\n  {\n    title: \"Trimity Developers Pvt. Ltd.\",\n  },\n];\n\nexport const residentialData = [\n  {\n    title: \"Krishna Green Park\",\n  },\n  {\n    title: \"Swagat Agro Agency\",\n  },\n  {\n    title: \"Athar Building\",\n  },\n  {\n    title: \"Padmavati CHS,\",\n  },\n  {\n    title: \"Boomerang Chandivali\",\n  },\n  {\n    title: \"Borla CHS\",\n  },\n  {\n    title: \"OM Siddharaj CHS ltd.\",\n  },\n  {\n    title: \"Dreamland Building,\",\n  },\n  {\n    title: \"Charni Road East\",\n  },\n  {\n    title: \"Garden View\",\n  },\n  {\n    title: \"Silco House\",\n  },\n  {\n    title: \"S. K. Academy\",\n  },\n  {\n    title: \"Shri Shivakrupa CHS\",\n  },\n  {\n    title: \"Shelar\",\n  },\n  {\n    title: \"Jai Ganaraj CHS\",\n  },\n  {\n    title: \"Akash Vihar CHS\",\n  },\n  {\n    title: \"Parvati CHS\",\n  },\n  {\n    title: \"Vasant Vihar\",\n  },\n  {\n    title: \"Rajnigandha CHS\",\n  },\n  {\n    title: \"Cosmos CHS\",\n  },\n  {\n    title: \"Balkrishna Quarters, Dombivli\",\n  },\n  {\n    title: \"Raymond, Ten X Habitat, Thane\",\n  },\n  {\n    title: \"Gangotri CHS\",\n  },\n  {\n    title: \"Shree Samarth Kuti Apartment\",\n  },\n  {\n    title: \"Krishna Kunj CHS\",\n  },\n  {\n    title: \"Manusmruti CHS\",\n  },\n  {\n    title: \"Krishna Saraswati CHS\",\n  },\n  {\n    title: \"Gokuldham Vrundavan CHS\",\n  },\n  {\n    title: \"Dhanlaxmi Chambers\",\n  },\n  {\n    title: \"Vijay Nagari Annex\",\n  },\n  {\n    title: \"Sobat CHS\",\n  },\n  {\n    title: \"Adarsh Peace Building\",\n  },\n  {\n    title: \"Kasturi Plaza CHS\",\n  },\n  {\n    title: \"Al-Jamiah-Al-Islamiyyah Noor Bagh Kausa\",\n  },\n  {\n    title: \"Vrindavan Dham, D Building CHS\",\n  },\n  {\n    title: \"Bhavan Mahal\",\n  },\n  {\n    title: \"Agarwal CHS\",\n  },\n  {\n    title: \"Kamla Niwas\",\n  },\n  {\n    title: \"Gangatara CHS\",\n  },\n  {\n    title: \"Shree Sidhivinayak CHS\",\n  },\n  {\n    title: \"Om Chambers\",\n  },\n  {\n    title: \"Kemps Cornor MUMBAI\",\n  },\n  {\n    title: \"Coral crest CHS\",\n  },\n  {\n    title: \"Parinita CHS\",\n  },\n  {\n    title: \"Gyan Kutir CHS\",\n  },\n  {\n    title: \"Harasiddh Park A3-A4 CHS\",\n  },\n  {\n    title: \"Highway Shirin CHS\",\n  },\n  {\n    title: \"Panchawati Apartment\",\n  },\n  {\n    title: \"Konark Indraprastha CHS\",\n  },\n  {\n    title: \"Mangalyakushma CHS\",\n  },\n  {\n    title: \"Siddhivinayak CHS,\",\n  },\n  {\n    title: \"Har Har Mahadev CHS\",\n  },\n  {\n    title: \"Bhagwan Walmki CHS\",\n  },\n  {\n    title: \"Tarang C.H.S.\",\n  },\n  {\n    title: \"Anand Palace CHS\",\n  },\n  {\n    title: \"Rameshwar CHS\",\n  },\n  {\n    title: \"Neelkanth Heights\",\n  },\n  {\n    title: \"Mahavir Heritage\",\n  },\n  {\n    title: \"Shree Ganesh Krupa Apartment\",\n  },\n  {\n    title: \"Nand Apartment CHS\",\n  },\n  {\n    title: \"Oshiwara Link Plaza\",\n  },\n  {\n    title: \"Royal Shalibhadra Complex No 1. CHS\",\n  },\n  {\n    title: \"Shyamkutir CHS,\",\n  },\n  {\n    title: \"Om Namha Shivay CHS,\",\n  },\n  {\n    title: \"Golden Express\",\n  },\n  {\n    title: \"Shriniketan CHS\",\n  },\n  {\n    title: \"Kailas Avenue\",\n  },\n  {\n    title: \"JS Infratech\",\n  },\n  {\n    title: \"Saraswati Nagar CHS\",\n  },\n  {\n    title: \"Vaibhav CHS\",\n  },\n  {\n    title: \"Revati Akashganga CHS\",\n  },\n  {\n    title: \"Anubhav Building CHS\",\n  },\n  {\n    title: \"Rahat Manzil CHS\",\n  },\n  {\n    title: \"Shree Dharshan CHS\",\n  },\n  {\n    title: \"Mahavir Dham CHS\",\n  },\n  {\n    title: \"Sai Ashish Apartment\",\n  },\n  {\n    title: \"Shweta Apartment\",\n  },\n  {\n    title: \"Surabhi CHSL\",\n  },\n  {\n    title: \"400 KV Substation Colony Kharghar\",\n  },\n  {\n    title: \"Sewa Samiti CHS,\",\n  },\n  {\n    title: \"Sion Koliwada Mumbai\",\n  },\n  {\n    title: \"Nilkanth Darshan CHS\",\n  },\n  {\n    title: \"Brahmand Phase-IV CHSL\",\n  },\n  {\n    title: \"Sindhi Society\",\n  },\n  {\n    title: \"Sidharth Nagar Bldg No- 4 CHSL\",\n  },\n  {\n    title: \"Amar Joyti CHS\",\n  },\n  {\n    title: \"Dosti Apartment,\",\n  },\n  {\n    title: \"Navsai Prasad CHSL\",\n  },\n  {\n    title: \"Chamunda Jewel CHS\",\n  },\n  {\n    title: \"Shiv Smruti CHSL\",\n  },\n  {\n    title: \"Wimbledon Park\",\n  },\n  {\n    title: \"Purohit Building\",\n  },\n  {\n    title: \"Rajani Gandha Apartment\",\n  },\n  {\n    title: \"Ananta Apartment\",\n  },\n  {\n    title: \"Ganesh Krupa CHS\",\n  },\n  {\n    title: \"Pearl Mansion CHSL\",\n  },\n  {\n    title: \"Building Marine Lines\",\n  },\n  {\n    title: \"Mayur Park CHS\",\n  },\n  {\n    title: \"Shreeniwas CHS\",\n  },\n  {\n    title: \"Shiram CHS\",\n  },\n  {\n    title: \"Rashmi Drashtant CHSL\",\n  },\n  {\n    title: \"Sai Dharshan B CHS\",\n  },\n  {\n    title: \"Sai Snehal CHS\",\n  },\n  {\n    title: \"Shivanand Society\",\n  },\n  {\n    title: \"Shree Arihant Compound Kalher Village\",\n  },\n  {\n    title: \"Shri Sai Baba CHS\",\n  },\n  {\n    title: \"Arundhati CHSL\",\n  },\n];\n\nexport const collegeData = [\n  {\n    title: \"Thakur Engineering College,\",\n  },\n  {\n    title: \"Somaiya College,\",\n  },\n  {\n    title: \"Guru Nanak Dev Engineering College,\",\n  },\n  {\n    title: \"Jhun Jhun wala College,\",\n  },\n  {\n    title: \"Rafiuddin Fakih boys High School,\",\n  },\n];\n\nexport const publicTrustData = [\n  {\n    title: \"Maharashtra Seva Sangh\",\n  },\n  {\n    title: \"Orthodox Church\",\n  },\n  {\n    title: \"St. Joseph Church\",\n  },\n  {\n    title: \"St. John's Church\",\n  },\n  {\n    title: \"Sharma Shikshan Sanstha\",\n  },\n  {\n    title: \"Maharashtra Jivan Pradhikaran\",\n  },\n  {\n    title: \"Navabharat Education Socienty\",\n  },\n];\n\nexport const hotelData = [\n  {\n    title: \"Nariman House, Mumbai\",\n  },\n  {\n    title: \"Four Season, Mumbai\",\n  },\n  {\n    title: \"Hotel Ashok, Thane\",\n  },\n  {\n    title: \"Lokesh Hotel, Pune\",\n  },\n  {\n    title: \"Ram Mahal, Mumbai\",\n  },\n];\n\nexport const mallsData = [\n  {\n    title: \"Metro Mall, Kalyan\",\n  },\n  {\n    title: \"Mega Mall\",\n  },\n  {\n    title: \"Millenium Mall\",\n  },\n];\n", "import React from \"react\";\n\nimport Carousel from \"react-multi-carousel\";\nimport \"react-multi-carousel/lib/styles.css\";\n\nimport SeactionHeading from \"../global/seaction-title\";\n\nimport * as Styles from \"./styles\";\n\nimport ClientItem from \"./item\";\nimport NameCard from \"./name-card\";\n\nimport {\n  clientdata,\n  industrialCommercial,\n  residentialData,\n  collegeData,\n  publicTrustData,\n  hotelData,\n  mallsData,\n  allClientsData,\n} from \"./data\";\n\nconst Clients = () => {\n  const responsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: { max: 4000, min: 3000 },\n      items: 4,\n      slidesToSlide: 4,\n    },\n    desktop: {\n      breakpoint: { max: 3000, min: 1024 },\n      items: 4,\n      slidesToSlide: 4,\n    },\n    tablet: {\n      breakpoint: { max: 1024, min: 664 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    mobile: {\n      breakpoint: { max: 664, min: 0 },\n      items: 1,\n      slidesToSlide: 1,\n    },\n  };\n\n  const allResponsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: { max: 4000, min: 3000 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    desktop: {\n      breakpoint: { max: 3000, min: 1024 },\n      items: 2,\n      slidesToSlide: 2,\n    },\n    tablet: {\n      breakpoint: { max: 1024, min: 664 },\n      items: 2,\n      slidesToSlide: 2,\n    },\n    mobile: {\n      breakpoint: { max: 664, min: 0 },\n      items: 1,\n      slidesToSlide: 1,\n    },\n  };\n\n  // const hey = residentialData.map((item) => item?.split(\"•\"));\n  // const hey2 = hey[0].map((item) => {\n  //   return { title: item.trim() };\n  // });\n\n  // console.log({ hey2 });\n\n  return (\n    <Styles.Container>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Clients\" />\n        <Carousel\n          responsive={responsive}\n          autoPlay\n          infinite\n          autoPlaySpeed={6000}\n          slidesToSlide={1}\n          transitionDuration={30000}\n        >\n          {clientdata.map((item, index) => {\n            return <ClientItem key={index} data={item} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.AllListHolder>\n        {allClientsData.map((item, index) => {\n          return <NameCard key={index} client={item} />;\n        })}\n      </Styles.AllListHolder>\n      {/* <Styles.ListHolder>\n        <SeactionHeading title=\"Residential\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {residentialData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"School / College\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {collegeData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Public Trust\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {publicTrustData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Hotels\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {hotelData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Malls\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {mallsData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder> */}\n    </Styles.Container>\n  );\n};\n\nexport default Clients;\n", "import React from \"react\";\n\nimport Clients from \"../../components/clients\";\n\nconst ClientsPage = () => {\n  return (\n    <div className=\"container\">\n      <Clients />\n    </div>\n  );\n};\n\nexport default ClientsPage;\n", "import styled from 'styled-components';\n\nexport const Container = styled.div`\n    text-align: center;\n    padding: 20px 0 30px;\n\n    font-size: ${({ theme: { font } }) => font.big};\n    font-weight: bold;\n`;", "import React from 'react'\n\nimport * as Styles from './styles';\n\nconst SeactionHeading = ({ title }) => {\n    return (\n        <Styles.Container>\n            {title}\n        </Styles.Container>\n    )\n}\n\nexport default SeactionHeading"], "names": ["Container", "styled", "div", "_templateObject", "_taggedTemplateLiteral", "ListHolder", "_templateObject2", "AllListHolder", "_templateObject3", "ImageHolder", "img", "_ref", "data", "_jsx", "Styles", "children", "src", "_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "this", "_objectWithoutProperties", "excluded", "sourceKeys", "keys", "indexOf", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "propertyIsEnumerable", "ArrowRightCircle", "forwardRef", "ref", "_ref$color", "color", "_ref$size", "size", "rest", "React", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r", "points", "x1", "y1", "x2", "y2", "propTypes", "PropTypes", "displayName", "theme", "font", "title", "_ref2", "colors", "concat", "grey", "_ref3", "white", "TitleWrapper", "IconHolder", "TextHolder", "span", "_templateObject4", "ul", "_templateObject5", "ListItme", "li", "_templateObject6", "_ref4", "main", "blue", "_client$data", "client", "console", "log", "_jsxs", "className", "icon", "heading", "map", "item", "index", "Settings", "d", "Home", "BookOpen", "Users", "Coffee", "ShoppingCart", "clientdata", "fourSeason", "Capacitelogo", "Damodar", "JNJ", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Technova", "Trustlogo", "allClientsData", "id", "Clients", "SeactionHeading", "Carousel", "responsive", "superLargeDesktop", "breakpoint", "max", "min", "items", "slidesToSlide", "desktop", "tablet", "mobile", "autoPlay", "infinite", "autoPlaySpeed", "transitionDuration", "ClientItem", "NameCard", "ClientsPage", "big"], "sourceRoot": ""}