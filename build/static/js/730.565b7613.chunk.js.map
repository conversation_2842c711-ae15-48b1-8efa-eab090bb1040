{"version": 3, "file": "static/js/730.565b7613.chunk.js", "mappings": "mJAEO,MAAMA,E,QAAUC,GAAOC,QAAOC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,uDACxBC,IAAA,IAAC,KAAEC,GAAMD,EAAA,OAAMC,EAAO,QAAU,W,aCC7C,MAQA,EARiBD,IAAyB,IAAxB,SAAEE,EAAQ,KAAED,GAAMD,EAClC,OACEG,EAAAA,EAAAA,KAACC,EAAc,CAACC,UAAU,OAAOJ,KAAMA,EAAKC,SACzCA,I,wFCLA,MAAMI,E,QAAYV,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,yQ,aCMnC,MAiBA,EAjBoBS,KAEZL,EAAAA,EAAAA,KAACC,EAAgB,CAACC,UAAU,2BAA0BH,SAE9CO,EAAAA,EAAYC,MAAM,EAAG,GAAGC,KAAI,CAACC,EAAMC,KAE3BV,EAAAA,EAAAA,KAACW,EAAAA,EAAW,CAERF,KAAMA,GADDC,OCDjC,EATiBE,KAETC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAf,SAAA,EACIC,EAAAA,EAAAA,KAACe,EAAAA,EAAe,CAACC,MAAM,uBACvBhB,EAAAA,EAAAA,KAACK,EAAW,M,uFCTxB,SAASY,IAA2Q,OAA9PA,EAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAIX,KAAOc,EAAcN,OAAOO,UAAUC,eAAeC,KAAKH,EAAQd,KAAQU,EAAOV,GAAOc,EAAOd,GAAU,CAAE,OAAOU,CAAQ,EAAUH,EAASW,MAAMC,KAAMP,UAAY,CAE5T,SAASQ,EAAyBN,EAAQO,GAAY,GAAc,MAAVP,EAAgB,MAAO,CAAC,EAAG,IAAkEd,EAAKW,EAAnED,EAEzF,SAAuCI,EAAQO,GAAY,GAAc,MAAVP,EAAgB,MAAO,CAAC,EAAG,IAA2Dd,EAAKW,EAA5DD,EAAS,CAAC,EAAOY,EAAad,OAAOe,KAAKT,GAAqB,IAAKH,EAAI,EAAGA,EAAIW,EAAWT,OAAQF,IAAOX,EAAMsB,EAAWX,GAAQU,EAASG,QAAQxB,IAAQ,IAAaU,EAAOV,GAAOc,EAAOd,IAAQ,OAAOU,CAAQ,CAFhNe,CAA8BX,EAAQO,GAAuB,GAAIb,OAAOkB,sBAAuB,CAAE,IAAIC,EAAmBnB,OAAOkB,sBAAsBZ,GAAS,IAAKH,EAAI,EAAGA,EAAIgB,EAAiBd,OAAQF,IAAOX,EAAM2B,EAAiBhB,GAAQU,EAASG,QAAQxB,IAAQ,GAAkBQ,OAAOO,UAAUa,qBAAqBX,KAAKH,EAAQd,KAAgBU,EAAOV,GAAOc,EAAOd,GAAQ,CAAE,OAAOU,CAAQ,CAM3e,IAAImB,GAASC,EAAAA,EAAAA,aAAW,SAAU3C,EAAM4C,GACtC,IAAIC,EAAa7C,EAAK8C,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAY/C,EAAKgD,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,EAAyBjC,EAAM,CAAC,QAAS,SAEpD,OAAoBkD,EAAAA,cAAoB,MAAO9B,EAAS,CACtDwB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,OAAQ,CACjDU,EAAG,mDACYV,EAAAA,cAAoB,SAAU,CAC7CW,GAAI,KACJC,GAAI,KACJC,EAAG,MAEP,IACArB,EAAOsB,UAAY,CACjBlB,MAAOmB,IAAAA,OACPjB,KAAMiB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/CvB,EAAOwB,YAAc,SACrB,UCvCA,SAAS9C,IAA2Q,OAA9PA,EAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAIX,KAAOc,EAAcN,OAAOO,UAAUC,eAAeC,KAAKH,EAAQd,KAAQU,EAAOV,GAAOc,EAAOd,GAAU,CAAE,OAAOU,CAAQ,EAAUH,EAASW,MAAMC,KAAMP,UAAY,CAE5T,SAASQ,EAAyBN,EAAQO,GAAY,GAAc,MAAVP,EAAgB,MAAO,CAAC,EAAG,IAAkEd,EAAKW,EAAnED,EAEzF,SAAuCI,EAAQO,GAAY,GAAc,MAAVP,EAAgB,MAAO,CAAC,EAAG,IAA2Dd,EAAKW,EAA5DD,EAAS,CAAC,EAAOY,EAAad,OAAOe,KAAKT,GAAqB,IAAKH,EAAI,EAAGA,EAAIW,EAAWT,OAAQF,IAAOX,EAAMsB,EAAWX,GAAQU,EAASG,QAAQxB,IAAQ,IAAaU,EAAOV,GAAOc,EAAOd,IAAQ,OAAOU,CAAQ,CAFhNe,CAA8BX,EAAQO,GAAuB,GAAIb,OAAOkB,sBAAuB,CAAE,IAAIC,EAAmBnB,OAAOkB,sBAAsBZ,GAAS,IAAKH,EAAI,EAAGA,EAAIgB,EAAiBd,OAAQF,IAAOX,EAAM2B,EAAiBhB,GAAQU,EAASG,QAAQxB,IAAQ,GAAkBQ,OAAOO,UAAUa,qBAAqBX,KAAKH,EAAQd,KAAgBU,EAAOV,GAAOc,EAAOd,GAAQ,CAAE,OAAOU,CAAQ,CAM3e,IAAI4C,GAAaxB,EAAAA,EAAAA,aAAW,SAAU3C,EAAM4C,GAC1C,IAAIC,EAAa7C,EAAK8C,MAClBA,OAAuB,IAAfD,EAAwB,eAAiBA,EACjDE,EAAY/C,EAAKgD,KACjBA,OAAqB,IAAdD,EAAuB,GAAKA,EACnCE,EAAOhB,EAAyBjC,EAAM,CAAC,QAAS,SAEpD,OAAoBkD,EAAAA,cAAoB,MAAO9B,EAAS,CACtDwB,IAAKA,EACLO,MAAO,6BACPC,MAAOJ,EACPK,OAAQL,EACRM,QAAS,YACTC,KAAM,OACNC,OAAQV,EACRW,YAAa,IACbC,cAAe,QACfC,eAAgB,SACfV,GAAoBC,EAAAA,cAAoB,OAAQ,CACjDkB,GAAI,IACJC,GAAI,KACJC,GAAI,KACJC,GAAI,OACWrB,EAAAA,cAAoB,WAAY,CAC/CsB,OAAQ,qBAEZ,IACAL,EAAWH,UAAY,CACrBlB,MAAOmB,IAAAA,OACPjB,KAAMiB,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAE/CE,EAAWD,YAAc,aACzB,U,8CCtCO,MAAM5D,EAAYV,EAAAA,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,ghBAkBpBC,IAAA,IAAGyE,OAAO,OAAEC,IAAU1E,EAAA,OAAK0E,EAAOC,KAAKC,QAIvCC,IAAA,IAAGJ,OAAO,OAAEC,IAAUG,EAAA,OAAKH,EAAOC,KAAKG,OAczCC,EAAenF,EAAAA,GAAOW,IAAGyE,IAAAA,GAAAjF,EAAAA,EAAAA,GAAA,iNAEhBkF,IAAA,IAAC,IAAEC,GAAKD,EAAA,aAAAE,OAAYD,EAAG,QAUhCE,EAAaxF,EAAAA,GAAOW,IAAG8E,IAAAA,GAAAtF,EAAAA,EAAAA,GAAA,iKAKduF,IAAA,IAAGb,OAAO,OAAEC,IAAUY,EAAA,OAAKZ,EAAOC,KAAKC,QAOhDW,EAAa3F,EAAAA,GAAO4F,KAAIC,IAAAA,GAAA1F,EAAAA,EAAAA,GAAA,0IACtB2F,IAAA,IAAC,IAAEC,EAAG,QAAEC,EAASnB,OAAO,KAAEoB,IAAQH,EAAA,OAC7CC,EAAME,EAAKF,IAAMC,EAAUC,EAAK1E,MAAQ0E,EAAKlB,QAChCmB,IAAA,IAAC,KAAEC,GAAMD,EAAA,OAAMC,EAAO,IAAM,OACzBC,IAAA,IAAC,MAAEC,GAAOD,EAAA,OAAMC,EAAQ,YAAc,UAO7CC,EAActG,EAAAA,GAAOW,IAAG4F,IAAAA,GAAApG,EAAAA,EAAAA,GAAA,oDAkBxBqG,GAbcxG,EAAAA,GAAOW,IAAG8F,IAAAA,GAAAtG,EAAAA,EAAAA,GAAA,8KAaXH,EAAAA,GAAOW,IAAG+F,IAAAA,GAAAvG,EAAAA,EAAAA,GAAA,sFASvBwG,EAAkB3G,EAAAA,GAAOW,IAAGiG,IAAAA,GAAAzG,EAAAA,EAAAA,GAAA,oDAK5B0G,EAAU7G,EAAAA,GAAOW,IAAGmG,IAAAA,GAAA3G,EAAAA,EAAAA,GAAA,mCAIpB4G,EAAc/G,EAAAA,GAAOW,IAAGqG,IAAAA,GAAA7G,EAAAA,EAAAA,GAAA,4HAG1B8G,IAAA,IAAGpC,OAAO,OAAEC,IAAUmC,EAAA,OAAKnC,EAAOC,KAAKC,QAOrCkC,EAAclH,EAAAA,GAAOW,IAAGwG,IAAAA,GAAAhH,EAAAA,EAAAA,GAAA,yH,aC9GrC,MA0CA,EA1CoBC,IAAe,IAAd,KAAEY,GAAMZ,EAE3B,OADAgH,QAAQC,IAAI,CAAErG,KAAMA,EAAKsG,OAAO,MAE9B/G,EAAAA,EAAAA,KAACC,EAAgB,CAAAF,UACfC,EAAAA,EAAAA,KAACgH,EAAAA,GAAI,CAACC,GAAE,aAAAjC,OAAevE,EAAKyG,IAAKnH,UAC/Bc,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAf,SAAA,EACEC,EAAAA,EAAAA,KAACC,EAAmB,CAAC8E,IAAS,OAAJtE,QAAI,IAAJA,OAAI,EAAJA,EAAM0G,UAAWjH,UAAU,QAAOH,UAC1DC,EAAAA,EAAAA,KAACC,EAAiB,CAAAF,UAChBC,EAAAA,EAAAA,KAACC,EAAiB,CAACuF,KAAG,EAACI,MAAI,EAACE,OAAK,EAAA/F,SAC9BU,EAAK2G,WAIZvG,EAAAA,EAAAA,MAACZ,EAAkB,CAAAF,SAAA,EAIjBc,EAAAA,EAAAA,MAACZ,EAAsB,CAAAF,SAAA,EACrBC,EAAAA,EAAAA,KAACC,EAAiB,CAAAF,UAChBC,EAAAA,EAAAA,KAACuC,EAAM,OAETvC,EAAAA,EAAAA,KAACC,EAAiB,CAACuF,KAAG,EAAAzF,SAAEU,EAAK4G,eAE/BrH,EAAAA,EAAAA,KAACC,EAAc,CAAAF,UACbC,EAAAA,EAAAA,KAACC,EAAiB,CAACC,UAAU,QAAQuF,SAAO,EAAA1F,SACzCU,EAAKO,WAGVhB,EAAAA,EAAAA,KAACC,EAAiB,CAACuF,KAAG,EAAAzF,SAAEU,EAAK6G,WAE/BzG,EAAAA,EAAAA,MAACZ,EAAkB,CAACC,UAAU,WAAUH,SAAA,EACtCC,EAAAA,EAAAA,KAACC,EAAiB,CAAC2F,MAAI,EAAA7F,SAAC,UACxBC,EAAAA,EAAAA,KAACC,EAAkB,CAACC,UAAU,QAAOH,UACnCC,EAAAA,EAAAA,KAACgE,EAAU,gB,0DCxClB,MAAM7D,E,QAAYV,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,6GAIlBC,IAAA,IAAGyE,OAAO,KAAEoB,IAAQ7F,EAAA,OAAK6F,EAAK6B,O,aCF/C,MAQA,EARwB1H,IAAgB,IAAf,MAAEmB,GAAOnB,EAC9B,OACIG,EAAAA,EAAAA,KAACC,EAAgB,CAAAF,SACZiB,I,gDCLN,MAAMV,EAAc,CACzB,CACE4G,GAAI,EACJlG,MAAO,gDACPoG,IAAK,aACLC,SAAU,oDACVG,SAAU,aACVL,UAAWM,EAAAA,EAAWC,SAASC,QAAQC,SACvCb,OAAQ,CACN,CACEc,SAAUJ,EAAAA,EAAWC,SAASC,QAAQG,UAExC,CACED,SAAUJ,EAAAA,EAAWC,SAASC,QAAQI,UAExC,CACEF,SAAUJ,EAAAA,EAAWC,SAASC,QAAQC,WAG1CI,YAAa,6DACbC,SAAU,CAAC,oBAAqB,eAAgB,aAElD,CACEf,GAAI,EACJlG,MACE,0EACFoG,IAAK,aACLC,SAAU,iBACVG,SAAU,aACVL,UAAWM,EAAAA,EAAWC,SAASQ,oBAAoBC,qBACnDpB,OAAQ,CACN,CACEc,SAAUJ,EAAAA,EAAWC,SAASQ,oBAAoBC,sBAEpD,CACEN,SAAUJ,EAAAA,EAAWC,SAASQ,oBAAoBE,sBAEpD,CACEP,SAAUJ,EAAAA,EAAWC,SAASQ,oBAAoBG,sBAEpD,CACER,SAAUJ,EAAAA,EAAWC,SAASQ,oBAAoBI,uBAGtDN,YACE,gNACFC,SAAU,CAAC,0BAA2B,6BAExC,CACEf,GAAI,EACJlG,MAAO,oEACPoG,IAAK,aACLC,SAAU,+BACVG,SAAU,aACVL,UAAWM,EAAAA,EAAWC,SAASa,QAAQC,SACvCzB,OAAQ,CACN,CACEc,SAAUJ,EAAAA,EAAWC,SAASa,QAAQC,UAExC,CACEX,SAAUJ,EAAAA,EAAWC,SAASa,QAAQE,UAExC,CACEZ,SAAUJ,EAAAA,EAAWC,SAASa,QAAQG,WAG1CV,YACE,wGACFC,SAAU,CAAC,iBAAkB,QAAS,gBAAiB,mBAEzD,CACEf,GAAI,EACJlG,MAAO,wDACPoG,IAAK,aACLC,SAAU,OACVG,SAAU,aACVL,UAAWM,EAAAA,EAAWC,SAASiB,cAAcC,eAC7C7B,OAAQ,CACN,CACEc,SAAUJ,EAAAA,EAAWC,SAASiB,cAAcE,gBAE9C,CACEhB,SAAUJ,EAAAA,EAAWC,SAASiB,cAAcG,gBAE9C,CACEjB,SAAUJ,EAAAA,EAAWC,SAASiB,cAAcC,gBAE9C,CACEf,SAAUJ,EAAAA,EAAWC,SAASiB,cAAcI,iBAGhDf,YAAa,8DACbC,SAAU,CAAC,iBAAkB,cAE/B,CACEf,GAAI,EACJlG,MAAO,qCACPoG,IAAK,cACLC,SAAU,eACVG,SAAU,aACVL,UAAWM,EAAAA,EAAWC,SAASsB,UAAUC,WACzClC,OAAQ,CACN,CACEc,SAAUJ,EAAAA,EAAWC,SAASsB,UAAUE,YAE1C,CACErB,SAAUJ,EAAAA,EAAWC,SAASsB,UAAUC,YAE1C,CACEpB,SAAUJ,EAAAA,EAAWC,SAASsB,UAAUG,aAG5CnB,YACE,kFACFC,SAAU,CAAC,oBAAqB,aAAc,gBAAiB,aAEjE,CACEf,GAAI,EACJlG,MAAO,iEACPoG,IAAK,aACLC,SAAU,eACVG,SAAU,aACVL,UAAWM,EAAAA,EAAWC,SAAS0B,QAAQC,SACvCtC,OAAQ,CACN,CACEc,SAAUJ,EAAAA,EAAWC,SAAS0B,QAAQC,UAExC,CACExB,SAAUJ,EAAAA,EAAWC,SAAS0B,QAAQE,UAExC,CACEzB,SAAUJ,EAAAA,EAAWC,SAAS0B,QAAQG,WAG1CvB,YACE,wHACFC,SAAU,CAAC,iBAAkB,kBAAmB,2BAElD,CACEf,GAAI,EACJlG,MAAO,yCACPoG,IAAK,aACLC,SAAU,SACVG,SAAU,aACVL,UAAWM,EAAAA,EAAWC,SAAS8B,YAAYC,aAC3C1C,OAAQ,CACN,CACEc,SAAUJ,EAAAA,EAAWC,SAAS8B,YAAYC,cAE5C,CACE5B,SAAUJ,EAAAA,EAAWC,SAAS8B,YAAYE,cAE5C,CACE7B,SAAUJ,EAAAA,EAAWC,SAAS8B,YAAYG,cAE5C,CACE9B,SAAUJ,EAAAA,EAAWC,SAAS8B,YAAYI,eAG9C5B,YAAa,4DACbC,SAAU,CAAC,QAAS,aAAc,aAEpC,CACEf,GAAI,EACJlG,MACE,mGACFoG,IAAK,aACLC,SAAU,gBACVG,SAAU,aACVL,UAAWM,EAAAA,EAAWC,SAASmC,UAAUC,WACzC/C,OAAQ,CACN,CACEc,SAAUJ,EAAAA,EAAWC,SAASmC,UAAUC,YAE1C,CACEjC,SAAUJ,EAAAA,EAAWC,SAASmC,UAAUE,YAE1C,CACElC,SAAUJ,EAAAA,EAAWC,SAASmC,UAAUG,YAE1C,CACEnC,SAAUJ,EAAAA,EAAWC,SAASmC,UAAUI,aAG5CjC,YACE,0EACFC,SAAU,CAAC,kBAAmB,eAAgB,kB,oIC1L3C,MAAMiC,EAAWzK,EAAAA,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,sQAeJH,EAAAA,GAAOW,IAAGyE,IAAAA,GAAAjF,EAAAA,EAAAA,GAAA,c,aCJxC,MA4BA,EA5BoBuK,KAEZnK,EAAAA,EAAAA,KAAA,OAAKE,UAAU,YAAWH,UACtBc,EAAAA,EAAAA,MAACuJ,EAAAA,EAAQ,CAAArK,SAAA,EACLC,EAAAA,EAAAA,KAACe,EAAAA,EAAe,CAACC,MAAM,0BACvBhB,EAAAA,EAAAA,KAACC,EAAe,CAACC,UAAU,eAAcH,SAEjCO,EAAAA,EAAYE,KAAI,CAACC,EAAMC,KAEfV,EAAAA,EAAAA,KAACW,EAAAA,EAAW,CAERF,KAAMA,GADDC,W", "sources": ["components/global/seaction/styles.js", "components/global/seaction/index.jsx", "components/projects/list/styles.js", "components/projects/list/index.jsx", "components/projects/index.jsx", "../node_modules/react-feather/dist/icons/map-pin.js", "../node_modules/react-feather/dist/icons/arrow-right.js", "components/projects/card/styles.js", "components/projects/card/index.jsx", "components/global/seaction-title/styles.js", "components/global/seaction-title/index.jsx", "components/projects/projectData.js", "pages/projects/styles.js", "pages/projects/index.jsx"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Section = styled.section`\n  display: ${({ flex }) => (flex ? \" flex\" : \"block\")};\n  align-items: flex-start;\n`;\n", "import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst Seaction = ({ children, flex }) => {\n  return (\n    <Styles.Section className=\"py-5\" flex={flex}>\n      {children}\n    </Styles.Section>\n  );\n};\n\nexport default Seaction;\n", "import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n\n  grid-gap: 20px;\n\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n`;\n", "import React from 'react'\n\nimport ProjectCard from '../card'\n\nimport { ProjectData } from '../projectData'\n\nimport * as Styles from './styles'\n\nconst ProjectList = () => {\n    return (\n        <Styles.Container className=\"flex-wrap flex-md-nowrap\">\n            {\n                ProjectData.slice(0, 4).map((item, key) => {\n                    return (\n                        <ProjectCard\n                            key={key}\n                            item={item}\n                        />\n                    )\n                })\n            }\n        </Styles.Container>\n    )\n}\n\nexport default ProjectList", "import React from 'react'\n\nimport SeactionHeading from '../global/seaction-title'\nimport ProjectList from './list'\n\nconst Projects = () => {\n    return (\n        <>\n            <SeactionHeading title=\"Featured Property\" />\n            <ProjectList />\n        </>\n    )\n}\n\nexport default Projects", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar MapPin = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"\n  }), /*#__PURE__*/React.createElement(\"circle\", {\n    cx: \"12\",\n    cy: \"10\",\n    r: \"3\"\n  }));\n});\nMapPin.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nMapPin.displayName = 'MapPin';\nexport default MapPin;", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar ArrowRight = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"5\",\n    y1: \"12\",\n    x2: \"19\",\n    y2: \"12\"\n  }), /*#__PURE__*/React.createElement(\"polyline\", {\n    points: \"12 5 19 12 12 19\"\n  }));\n});\nArrowRight.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nArrowRight.displayName = 'ArrowRight';\nexport default ArrowRight;", "import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  background-color: #fff;\n  border-radius: 10px;\n\n  overflow: hidden;\n\n  flex: 1 1 auto;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);\n  cursor: pointer;\n\n  &:hover {\n    box-shadow: 0 0 13px rgba(0, 0, 0, 0.3);\n    .arrow {\n      margin-left: 12px;\n    }\n\n    .title {\n      color: ${({ theme: { colors } }) => colors.main.blue};\n    }\n\n    .viewlink {\n      color: ${({ theme: { colors } }) => colors.main.red};\n    }\n\n    .image {\n      background-size: 110%;\n      transition: all 0.4s ease-in-out;\n    }\n  }\n\n  transition: all 0.3s ease-in-out;\n\n  position: relative;\n`;\n\nexport const ImgContainer = styled.div`\n  position: relative;\n  background-image: ${({ img }) => `url(${img})`};\n  background-size: 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n\n  transition: all 0.4s ease-in-out;\n\n  height: 180px;\n`;\n\nexport const TagWrapper = styled.div`\n  position: absolute;\n  right: 0;\n  top: 0;\n\n  background-color: ${({ theme: { colors } }) => colors.main.blue};\n\n  color: #fff;\n  padding: 5px 25px;\n  border-bottom-left-radius: 10px;\n`;\n\nexport const TextHolder = styled.span`\n  font-size: ${({ sub, heading, theme: { font } }) =>\n    sub ? font.sub : heading ? font.title : font.main};\n  font-weight: ${({ bold }) => (bold ? 600 : 400)};\n  text-transform: ${({ upper }) => (upper ? \"uppercase\" : \"none\")};\n\n  position: relative;\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const TextWrapper = styled.div`\n  padding: 20px;\n  margin-bottom: 30px;\n`;\n\nexport const SaveWrapper = styled.div`\n  background-color: #e5e5e6;\n  padding: 15px 16px;\n\n  position: absolute;\n  right: 10%;\n  top: 0;\n\n  transform: translateY(-50%);\n\n  border-radius: 10px;\n`;\n\nexport const IconHolder = styled.div`\n  margin-right: 8px;\n\n  svg {\n    width: 20px;\n    height: 20px;\n  }\n`;\n\nexport const LocationWrapper = styled.div`\n  display: flex;\n  align-items: center;\n`;\n\nexport const Padding = styled.div`\n  padding: 15px 0 10px;\n`;\n\nexport const LinkWrapper = styled.div`\n  display: flex;\n  align-items: center;\n  color: ${({ theme: { colors } }) => colors.main.blue};\n\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n`;\n\nexport const ArrowHolder = styled.div`\n  margin-left: 6px;\n\n  svg {\n    width: 20px;\n    height: 20px;\n  }\n  transition: all 0.3s ease-in-out;\n`;\n", "import React from \"react\";\n\nimport { Link } from \"react-router-dom\";\n\nimport { AssetsList } from \"../../elements/assetsList\";\nimport { ArrowRight, MapPin } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst ProjectCard = ({ item }) => {\n  console.log({ item: item.banner[0] });\n  return (\n    <Styles.Container>\n      <Link to={`/projects/${item.id}`}>\n        <>\n          <Styles.ImgContainer img={item?.thumbnail} className=\"image\">\n            <Styles.TagWrapper>\n              <Styles.TextHolder sub bold upper>\n                {item.tag}\n              </Styles.TextHolder>\n            </Styles.TagWrapper>\n          </Styles.ImgContainer>\n          <Styles.TextWrapper>\n            {/* <Styles.SaveWrapper>\n              <Styles.IconHolder src={AssetsList.save} />\n            </Styles.SaveWrapper> */}\n            <Styles.LocationWrapper>\n              <Styles.IconHolder>\n                <MapPin />\n              </Styles.IconHolder>\n              <Styles.TextHolder sub>{item.location}</Styles.TextHolder>\n            </Styles.LocationWrapper>\n            <Styles.Padding>\n              <Styles.TextHolder className=\"title\" heading>\n                {item.title}\n              </Styles.TextHolder>\n            </Styles.Padding>\n            <Styles.TextHolder sub>{item.info}</Styles.TextHolder>\n          </Styles.TextWrapper>\n          <Styles.LinkWrapper className=\"viewlink\">\n            <Styles.TextHolder bold>View</Styles.TextHolder>\n            <Styles.ArrowHolder className=\"arrow\">\n              <ArrowRight />\n            </Styles.ArrowHolder>\n          </Styles.LinkWrapper>\n        </>\n      </Link>\n    </Styles.Container>\n  );\n};\n\nexport default ProjectCard;\n", "import styled from 'styled-components';\n\nexport const Container = styled.div`\n    text-align: center;\n    padding: 20px 0 30px;\n\n    font-size: ${({ theme: { font } }) => font.big};\n    font-weight: bold;\n`;", "import React from 'react'\n\nimport * as Styles from './styles';\n\nconst SeactionHeading = ({ title }) => {\n    return (\n        <Styles.Container>\n            {title}\n        </Styles.Container>\n    )\n}\n\nexport default SeactionHeading", "import { AssetsList } from \"../elements/assetsList\";\n\nexport const ProjectData = [\n  {\n    id: 1,\n    title: \"Vikhroli Corporate Park Private LTD (Embassy)\",\n    tag: \"Commercial\",\n    location: \"247 Park, LBS Marg, Vikroli (West), Mumbai 400083\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.embassy.embassy3,\n    banner: [\n      {\n        original: AssetsList.projects.embassy.embassy1,\n      },\n      {\n        original: AssetsList.projects.embassy.embassy2,\n      },\n      {\n        original: AssetsList.projects.embassy.embassy3,\n      },\n    ],\n    description: \"Structural and Civil Repairs, Painting of Utility Building\",\n    services: [\"Structural Repair\", \"Civil Repair\", \"Painting\"],\n  },\n  {\n    id: 2,\n    title:\n      \"Column, Slab strengthening at four season at residential tower project.\",\n    tag: \"Commercial\",\n    location: \"Worli, Mumbai \",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy1,\n    banner: [\n      {\n        original: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy1,\n      },\n      {\n        original: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy2,\n      },\n      {\n        original: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy3,\n      },\n      {\n        original: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy4,\n      },\n    ],\n    description:\n      \"Column, Slab strengthening of Four Season residency tower project. strengthening of column by microconcrete jacketing, by using high grade steel plate for additional loading. i.e. additional floor included\",\n    services: [\"Microconcrete Jacketing\", \"High grade steel plating\"],\n  },\n  {\n    id: 3,\n    title: \"Structural strengthening of Vikas Industries And Chemical PVT LTD\",\n    tag: \"Industrial\",\n    location: \"MIDC Tarapur Industrial Area\",\n    category: \"Industrial\",\n    thumbnail: AssetsList.projects.tarapur.tarapur1,\n    banner: [\n      {\n        original: AssetsList.projects.tarapur.tarapur1,\n      },\n      {\n        original: AssetsList.projects.tarapur.tarapur2,\n      },\n      {\n        original: AssetsList.projects.tarapur.tarapur3,\n      },\n    ],\n    description:\n      \"strengthening of Blast Upgrade structure using epoxy grouting, P.M.M, Microconcrete & fibre wrapping.\",\n    services: [\"Epoxy Grouting\", \"P.M.M\", \"Microconcrete\", \"Fibre Wrapping\"],\n  },\n  {\n    id: 4,\n    title: \"Structural strengthening of columns at Millenium Star\",\n    tag: \"Commercial\",\n    location: \"Pune\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.milleniumMall.milleniumMall3,\n    banner: [\n      {\n        original: AssetsList.projects.milleniumMall.milleniumMall1,\n      },\n      {\n        original: AssetsList.projects.milleniumMall.milleniumMall2,\n      },\n      {\n        original: AssetsList.projects.milleniumMall.milleniumMall3,\n      },\n      {\n        original: AssetsList.projects.milleniumMall.milleniumMall4,\n      },\n    ],\n    description: \"strengthening columns by using fibre wrapping and Laminates\",\n    services: [\"Fibre Wrapping\", \"Laminates\"],\n  },\n  {\n    id: 5,\n    title: \"Structural Repair at Raghav C.H.S.\",\n    tag: \"Residential\",\n    location: \"Malad (East)\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.raghavChs.raghavChs2,\n    banner: [\n      {\n        original: AssetsList.projects.raghavChs.raghavChs1,\n      },\n      {\n        original: AssetsList.projects.raghavChs.raghavChs2,\n      },\n      {\n        original: AssetsList.projects.raghavChs.raghavChs3,\n      },\n    ],\n    description:\n      \"Structural repairing of R.C.C. members, plastering, waterproofing and painting.\",\n    services: [\"Structural repair\", \"Plastering\", \"Waterproofing\", \"Painting\"],\n  },\n  {\n    id: 6,\n    title: \"Column strengthening works of Tower D, E, J, K Raymond Project\",\n    tag: \"Commercial\",\n    location: \"Thane (West)\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.raymond.raymond1,\n    banner: [\n      {\n        original: AssetsList.projects.raymond.raymond1,\n      },\n      {\n        original: AssetsList.projects.raymond.raymond2,\n      },\n      {\n        original: AssetsList.projects.raymond.raymond3,\n      },\n    ],\n    description:\n      \"strengthening column by Fibre Wrapping, Micro Jacketing and high strength steel plate for additional floor included .\",\n    services: [\"Fibre Wrapping\", \"Micro Jacketing\", \"High grade steel plate\"],\n  },\n  {\n    id: 7,\n    title: \"Structural Repair at Siyaram Mill LTD.\",\n    tag: \"Commercial\",\n    location: \"Kalher\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.siyaramMill.siyaramMill1,\n    banner: [\n      {\n        original: AssetsList.projects.siyaramMill.siyaramMill1,\n      },\n      {\n        original: AssetsList.projects.siyaramMill.siyaramMill2,\n      },\n      {\n        original: AssetsList.projects.siyaramMill.siyaramMill3,\n      },\n      {\n        original: AssetsList.projects.siyaramMill.siyaramMill4,\n      },\n    ],\n    description: \"Structural Repair work using P.M.M, Plastering, Painting.\",\n    services: [\"P.M.M\", \"Plastering\", \"Painting\"],\n  },\n  {\n    id: 8,\n    title:\n      \"Providing and Carrying out beam strengthening work by fibre wrapping and laminate at Metro Mall \",\n    tag: \"Commercial\",\n    location: \"Kalyan (East)\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.metroMall.metroMall1,\n    banner: [\n      {\n        original: AssetsList.projects.metroMall.metroMall1,\n      },\n      {\n        original: AssetsList.projects.metroMall.metroMall2,\n      },\n      {\n        original: AssetsList.projects.metroMall.metroMall3,\n      },\n      {\n        original: AssetsList.projects.metroMall.metroMall4,\n      },\n    ],\n    description:\n      \"Beam strengthening using carbon Fibre, steel plating & carbon laminate.\",\n    services: [\"Carbon Laminate\", \"Carbon Fibre\", \"Steel Plating\"],\n  },\n];\n\n// export const ProjectData2 = [\n//   {\n//     id: 1,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n//   {\n//     id: 2,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n//   {\n//     id: 3,\n//     banner: AssetsList.bg,\n//     title: \"Embassy 247 Park\",\n//     location: \"Vikhroli West, Mumbai\",\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     tag: \"Industrial Commercial\",\n//   },\n//   {\n//     id: 4,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n//   {\n//     id: 5,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n//   {\n//     id: 6,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n// ];\n", "import styled from 'styled-components';\n\nexport const CardGrid = styled.div`\n    display: grid;\n    grid-template-columns: 1fr 1fr 1fr;\n\n    grid-gap: 40px;\n\n    @media screen and (max-width: 900px) {\n        grid-template-columns: 1fr 1fr;\n    }\n\n    @media screen and (max-width: 600px) {\n        grid: none;\n    }\n`;\n\nexport const VideoContainer = styled.div`\n  \n`;", "import React from 'react'\n\nimport Seaction from '../../components/global/seaction'\n\nimport SeactionHeading from '../../components/global/seaction-title'\n\nimport { ProjectData } from '../../components/projects/projectData'\n\nimport ProjectCard from '../../components/projects/card';\nimport Projects from '../../components/projects';\n\nimport * as Styles from './styles'\n\nconst ProjectPage = () => {\n    return (\n        <div className=\"container\">\n            <Seaction>\n                <SeactionHeading title=\"Explore New features\" />\n                <Styles.CardGrid className=\"grid-md-none\">\n                    {\n                        ProjectData.map((item, key) => {\n                            return (\n                                <ProjectCard\n                                    key={key}\n                                    item={item}\n                                />\n                            )\n                        })\n                    }\n                </Styles.CardGrid>\n            </Seaction>\n            {/* <Seaction>\n                <Styles.VideoContainer>\n                    <SeactionHeading title=\"Short Glimpse Of the Working\" />\n                    <iframe width=\"100%\" height=\"615\" src=\"https://www.youtube.com/embed/KbTjl1PNCzg\" title=\"YouTube video player\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>\n                </Styles.VideoContainer>\n            </Seaction> */}\n        </div>\n    )\n}\n\nexport default ProjectPage"], "names": ["Section", "styled", "section", "_templateObject", "_taggedTemplateLiteral", "_ref", "flex", "children", "_jsx", "Styles", "className", "Container", "div", "ProjectList", "ProjectData", "slice", "map", "item", "key", "ProjectCard", "Projects", "_jsxs", "_Fragment", "SeactionHeading", "title", "_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "prototype", "hasOwnProperty", "call", "apply", "this", "_objectWithoutProperties", "excluded", "sourceKeys", "keys", "indexOf", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "propertyIsEnumerable", "MapPin", "forwardRef", "ref", "_ref$color", "color", "_ref$size", "size", "rest", "React", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "cx", "cy", "r", "propTypes", "PropTypes", "displayName", "ArrowRight", "x1", "y1", "x2", "y2", "points", "theme", "colors", "main", "blue", "_ref2", "red", "ImgContainer", "_templateObject2", "_ref3", "img", "concat", "TagWrapper", "_templateObject3", "_ref4", "TextHolder", "span", "_templateObject4", "_ref5", "sub", "heading", "font", "_ref6", "bold", "_ref7", "upper", "TextWrapper", "_templateObject5", "IconHolder", "_templateObject6", "_templateObject7", "LocationWrapper", "_templateObject8", "Padding", "_templateObject9", "LinkWrapper", "_templateObject0", "_ref8", "ArrowHolder", "_templateObject1", "console", "log", "banner", "Link", "to", "id", "thumbnail", "tag", "location", "info", "big", "category", "AssetsList", "projects", "embassy", "embassy3", "original", "embassy1", "embassy2", "description", "services", "fourSeasonResidancy", "fourSeasonResidancy1", "fourSeasonResidancy2", "fourSeasonResidancy3", "fourSeasonResidancy4", "tarapur", "tarapur1", "tarapur2", "tarapur3", "milleniumMall", "milleniumMall3", "milleniumMall1", "milleniumMall2", "milleniumMall4", "raghavChs", "raghavChs2", "raghavChs1", "raghavChs3", "<PERSON><PERSON>", "raymond1", "raymond2", "raymond3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "siyaramMill1", "siyaramMill2", "siyaramMill3", "siyaramMill4", "metroMall", "metroMall1", "metroMall2", "metroMall3", "metroMall4", "CardGrid", "ProjectPage", "Seaction"], "sourceRoot": ""}