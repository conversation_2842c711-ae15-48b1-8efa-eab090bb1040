"use strict";(self.webpackChunkcapastrength=self.webpackChunkcapastrength||[]).push([[222],{222:(e,t,n)=>{n.d(t,{A:()=>lt});var i=n(5043),r={694:(e,t,n)=>{var i=n(925);function r(){}function a(){}a.resetWarningCache=r,e.exports=function(){function e(e,t,n,r,a,o){if(o!==i){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:r};return n.PropTypes=n,n}},556:(e,t,n)=>{e.exports=n(694)()},925:e=>{e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},115:e=>{var t="undefined"!=typeof Element,n="function"==typeof Map,i="function"==typeof Set,r="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function a(e,o){if(e===o)return!0;if(e&&o&&"object"==typeof e&&"object"==typeof o){if(e.constructor!==o.constructor)return!1;var s,l,u,c;if(Array.isArray(e)){if((s=e.length)!=o.length)return!1;for(l=s;0!=l--;)if(!a(e[l],o[l]))return!1;return!0}if(n&&e instanceof Map&&o instanceof Map){if(e.size!==o.size)return!1;for(c=e.entries();!(l=c.next()).done;)if(!o.has(l.value[0]))return!1;for(c=e.entries();!(l=c.next()).done;)if(!a(l.value[1],o.get(l.value[0])))return!1;return!0}if(i&&e instanceof Set&&o instanceof Set){if(e.size!==o.size)return!1;for(c=e.entries();!(l=c.next()).done;)if(!o.has(l.value[0]))return!1;return!0}if(r&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(o)){if((s=e.length)!=o.length)return!1;for(l=s;0!=l--;)if(e[l]!==o[l])return!1;return!0}if(e.constructor===RegExp)return e.source===o.source&&e.flags===o.flags;if(e.valueOf!==Object.prototype.valueOf&&"function"==typeof e.valueOf&&"function"==typeof o.valueOf)return e.valueOf()===o.valueOf();if(e.toString!==Object.prototype.toString&&"function"==typeof e.toString&&"function"==typeof o.toString)return e.toString()===o.toString();if((s=(u=Object.keys(e)).length)!==Object.keys(o).length)return!1;for(l=s;0!=l--;)if(!Object.prototype.hasOwnProperty.call(o,u[l]))return!1;if(t&&e instanceof Element)return!1;for(l=s;0!=l--;)if(("_owner"!==u[l]&&"__v"!==u[l]&&"__o"!==u[l]||!e.$$typeof)&&!a(e[u[l]],o[u[l]]))return!1;return!0}return e!=e&&o!=o}e.exports=function(e,t){try{return a(e,t)}catch(e){if((e.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw e}}}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var n=a[e]={exports:{}};return r[e](n,n.exports,o),n.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var s={};function l(e){var t,n,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=l(e[t]))&&(i&&(i+=" "),i+=n)}else for(n in e)e[n]&&(i&&(i+=" "),i+=n);return i}o.d(s,{A:()=>st});const u=function(){for(var e,t,n=0,i="",r=arguments.length;n<r;n++)(e=arguments[n])&&(t=l(e))&&(i&&(i+=" "),i+=t);return i},c=(h={default:()=>i,useMemo:()=>i.useMemo,useRef:()=>i.useRef},d={},o.d(d,h),d);var h,d;const f=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},p="object"==typeof global&&global&&global.Object===Object&&global;var m="object"==typeof self&&self&&self.Object===Object&&self;const b=p||m||Function("return this")(),g=function(){return b.Date.now()};var v=/\s/,y=/^\s+/;const w=b.Symbol;var S=Object.prototype,T=S.hasOwnProperty,O=S.toString,E=w?w.toStringTag:void 0,k=Object.prototype.toString,I=w?w.toStringTag:void 0;var P=/^[-+]0x[0-9a-f]+$/i,x=/^0b[01]+$/i,j=/^0o[0-7]+$/i,_=parseInt;const R=function(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return null!=e&&"object"==typeof e}(e)&&"[object Symbol]"==function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":I&&I in Object(e)?function(e){var t=T.call(e,E),n=e[E];try{e[E]=void 0;var i=!0}catch(e){}var r=O.call(e);return i&&(t?e[E]=n:delete e[E]),r}(e):function(e){return k.call(e)}(e)}(e)}(e))return NaN;if(f(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=f(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=function(e){return e?e.slice(0,function(e){for(var t=e.length;t--&&v.test(e.charAt(t)););return t}(e)+1).replace(y,""):e}(e);var n=x.test(e);return n||j.test(e)?_(e.slice(2),n?2:8):P.test(e)?NaN:+e};var L=Math.max,M=Math.min;const D=function(e,t,n){var i,r,a,o,s,l,u=0,c=!1,h=!1,d=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var n=i,a=r;return i=r=void 0,u=t,o=e.apply(a,n)}function m(e){var n=e-l;return void 0===l||n>=t||n<0||h&&e-u>=a}function b(){var e=g();if(m(e))return v(e);s=setTimeout(b,function(e){var n=t-(e-l);return h?M(n,a-(e-u)):n}(e))}function v(e){return s=void 0,d&&i?p(e):(i=r=void 0,o)}function y(){var e=g(),n=m(e);if(i=arguments,r=this,l=e,n){if(void 0===s)return function(e){return u=e,s=setTimeout(b,t),c?p(e):o}(l);if(h)return clearTimeout(s),s=setTimeout(b,t),p(l)}return void 0===s&&(s=setTimeout(b,t)),o}return t=R(t)||0,f(n)&&(c=!!n.leading,a=(h="maxWait"in n)?L(R(n.maxWait)||0,t):a,d="trailing"in n?!!n.trailing:d),y.cancel=function(){void 0!==s&&clearTimeout(s),u=0,i=l=r=s=void 0},y.flush=function(){return void 0===s?o:v(g())},y},C=function(e,t,n){var i=!0,r=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return f(n)&&(i="leading"in n?!!n.leading:i,r="trailing"in n?!!n.trailing:r),D(e,t,{leading:i,maxWait:t,trailing:r})};var W=o(115),N=o.n(W),F=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,i){return e[0]===t&&(n=i,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),i=this.__entries__[n];return i&&i[1]},t.prototype.set=function(t,n){var i=e(this.__entries__,t);~i?this.__entries__[i][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,i=e(n,t);~i&&n.splice(i,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,i=this.__entries__;n<i.length;n++){var r=i[n];e.call(t,r[1],r[0])}},t}()}(),z="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,B=void 0!==o.g&&o.g.Math===Math?o.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),A="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(B):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)},U=["top","right","bottom","left","width","height","size","weight"],q="undefined"!=typeof MutationObserver,G=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e){var t=!1,n=!1,i=0;function r(){t&&(t=!1,e()),n&&o()}function a(){A(r)}function o(){var e=Date.now();if(t){if(e-i<2)return;n=!0}else t=!0,n=!1,setTimeout(a,20);i=e}return o}(this.refresh.bind(this))}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){z&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),q?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){z&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;U.some((function(e){return!!~n.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),H=function(e,t){for(var n=0,i=Object.keys(t);n<i.length;n++){var r=i[n];Object.defineProperty(e,r,{value:t[r],enumerable:!1,writable:!1,configurable:!0})}return e},V=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||B},K=J(0,0,0,0);function X(e){return parseFloat(e)||0}function Y(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){return t+X(e["border-"+n+"-width"])}),0)}var $="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof V(e).SVGGraphicsElement}:function(e){return e instanceof V(e).SVGElement&&"function"==typeof e.getBBox};function J(e,t,n,i){return{x:e,y:t,width:n,height:i}}var Q=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=J(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=function(e){return z?$(e)?function(e){var t=e.getBBox();return J(0,0,t.width,t.height)}(e):function(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return K;var i=V(e).getComputedStyle(e),r=function(e){for(var t={},n=0,i=["top","right","bottom","left"];n<i.length;n++){var r=i[n],a=e["padding-"+r];t[r]=X(a)}return t}(i),a=r.left+r.right,o=r.top+r.bottom,s=X(i.width),l=X(i.height);if("border-box"===i.boxSizing&&(Math.round(s+a)!==t&&(s-=Y(i,"left","right")+a),Math.round(l+o)!==n&&(l-=Y(i,"top","bottom")+o)),!function(e){return e===V(e).document.documentElement}(e)){var u=Math.round(s+a)-t,c=Math.round(l+o)-n;1!==Math.abs(u)&&(s-=u),1!==Math.abs(c)&&(l-=c)}return J(r.left,r.top,s,l)}(e):K}(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),Z=function(e,t){var n=function(e){var t=e.x,n=e.y,i=e.width,r=e.height,a="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,o=Object.create(a.prototype);return H(o,{x:t,y:n,width:i,height:r,top:n,right:t+i,bottom:r+n,left:t}),o}(t);H(this,{target:e,contentRect:n})},ee=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new F,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof V(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new Q(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof V(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new Z(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),te="undefined"!=typeof WeakMap?new WeakMap:new F,ne=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=G.getInstance(),i=new ee(t,n,this);te.set(this,i)};["observe","unobserve","disconnect"].forEach((function(e){ne.prototype[e]=function(){var t;return(t=te.get(this))[e].apply(t,arguments)}}));const ie=void 0!==B.ResizeObserver?B.ResizeObserver:ne,re="Left",ae="Right",oe="Up",se="Down",le={delta:10,preventScrollOnSwipe:!1,rotationAngle:0,trackMouse:!1,trackTouch:!0,swipeDuration:1/0,touchEventOptions:{passive:!0}},ue={first:!0,initial:[0,0],start:0,swiping:!1,xy:[0,0]},ce="mousemove",he="mouseup";function de(e,t){if(0===t)return e;const n=Math.PI/180*t;return[e[0]*Math.cos(n)+e[1]*Math.sin(n),e[1]*Math.cos(n)-e[0]*Math.sin(n)]}var fe=o(556);function pe(e){return pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pe(e)}function me(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function be(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?me(Object(n),!0).forEach((function(t){ge(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):me(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ge(e,t,n){return(t=function(e){var t=function(e){if("object"!=pe(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=pe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==pe(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ve={description:"",fullscreen:"",isFullscreen:!1,originalAlt:"",originalHeight:"",originalWidth:"",originalTitle:"",sizes:"",srcSet:"",loading:"eager"},ye=c.default.memo((function(e){var t=be(be({},ve),e),n=t.description,i=t.fullscreen,r=t.handleImageLoaded,a=t.isFullscreen,o=t.onImageError,s=t.original,l=t.originalAlt,u=t.originalHeight,h=t.originalWidth,d=t.originalTitle,f=t.sizes,p=t.srcSet,m=t.loading,b=a&&i||s;return c.default.createElement(c.default.Fragment,null,c.default.createElement("img",{className:"image-gallery-image",src:b,alt:l,srcSet:p,height:u,width:h,sizes:f,title:d,onLoad:function(e){return r(e,s)},onError:o,loading:m}),n&&c.default.createElement("span",{className:"image-gallery-description"},n))}));ye.displayName="Item",ye.propTypes={description:fe.string,fullscreen:fe.string,handleImageLoaded:fe.func.isRequired,isFullscreen:fe.bool,onImageError:fe.func.isRequired,original:fe.string.isRequired,originalAlt:fe.string,originalHeight:fe.string,originalWidth:fe.string,originalTitle:fe.string,sizes:fe.string,srcSet:fe.string,loading:fe.string};const we=ye;function Se(e){return Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Se(e)}function Te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function Oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Te(Object(n),!0).forEach((function(t){Ee(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ee(e,t,n){return(t=function(e){var t=function(e){if("object"!=Se(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Se(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Se(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ke={left:c.default.createElement("polyline",{points:"15 18 9 12 15 6"}),right:c.default.createElement("polyline",{points:"9 18 15 12 9 6"}),top:c.default.createElement("polyline",{points:"6 15 12 9 18 15"}),bottom:c.default.createElement("polyline",{points:"6 9 12 15 18 9"}),maximize:c.default.createElement("path",{d:"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"}),minimize:c.default.createElement("path",{d:"M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"}),play:c.default.createElement("polygon",{points:"5 3 19 12 5 21 5 3"}),pause:c.default.createElement(c.default.Fragment,null,c.default.createElement("rect",{x:"6",y:"4",width:"4",height:"16"}),c.default.createElement("rect",{x:"14",y:"4",width:"4",height:"16"}))},Ie={strokeWidth:1,viewBox:"0 0 24 24"},Pe=function(e){var t=Oe(Oe({},Ie),e),n=t.strokeWidth,i=t.viewBox,r=t.icon;return c.default.createElement("svg",{className:"image-gallery-svg",xmlns:"http://www.w3.org/2000/svg",viewBox:i,fill:"none",stroke:"currentColor",strokeWidth:n,strokeLinecap:"round",strokeLinejoin:"round"},ke[r])};Pe.propTypes={strokeWidth:fe.number,viewBox:fe.string,icon:(0,fe.oneOf)(["left","right","top","bottom","maximize","minimize","play","pause"]).isRequired};const xe=Pe;var je=c.default.memo((function(e){var t=e.isFullscreen,n=e.onClick;return c.default.createElement("button",{type:"button",className:"image-gallery-icon image-gallery-fullscreen-button",onClick:n,"aria-label":"Open Fullscreen"},c.default.createElement(xe,{strokeWidth:2,icon:t?"minimize":"maximize"}))}));je.displayName="Fullscreen",je.propTypes={isFullscreen:fe.bool.isRequired,onClick:fe.func.isRequired};const _e=je;var Re=c.default.memo((function(e){var t=e.disabled,n=e.onClick;return c.default.createElement("button",{type:"button",className:"image-gallery-icon image-gallery-left-nav",disabled:t,onClick:n,"aria-label":"Previous Slide"},c.default.createElement(xe,{icon:"left",viewBox:"6 0 12 24"}))}));Re.displayName="LeftNav",Re.propTypes={disabled:fe.bool.isRequired,onClick:fe.func.isRequired};const Le=Re;var Me=c.default.memo((function(e){var t=e.disabled,n=e.onClick;return c.default.createElement("button",{type:"button",className:"image-gallery-icon image-gallery-right-nav",disabled:t,onClick:n,"aria-label":"Next Slide"},c.default.createElement(xe,{icon:"right",viewBox:"6 0 12 24"}))}));Me.displayName="RightNav",Me.propTypes={disabled:fe.bool.isRequired,onClick:fe.func.isRequired};const De=Me;var Ce=c.default.memo((function(e){var t=e.isPlaying,n=e.onClick;return c.default.createElement("button",{type:"button",className:"image-gallery-icon image-gallery-play-button",onClick:n,"aria-label":"Play or Pause Slideshow"},c.default.createElement(xe,{strokeWidth:2,icon:t?"pause":"play"}))}));Ce.displayName="PlayPause",Ce.propTypes={isPlaying:fe.bool.isRequired,onClick:fe.func.isRequired};const We=Ce;function Ne(e){return Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ne(e)}function Fe(){return Fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)({}).hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},Fe.apply(null,arguments)}function ze(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function Be(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ze(Object(n),!0).forEach((function(t){Ae(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ze(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ae(e,t,n){return(t=function(e){var t=function(e){if("object"!=Ne(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Ne(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ne(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ue={className:"",delta:0,onSwiping:function(){},onSwiped:function(){}},qe=function(e){var t=Be(Be({},Ue),e),n=t.children,i=t.className,r=function(e){const{trackMouse:t}=e,n=c.useRef(Object.assign({},ue)),i=c.useRef(Object.assign({},le)),r=c.useRef(Object.assign({},i.current));let a;for(a in r.current=Object.assign({},i.current),i.current=Object.assign(Object.assign({},le),e),le)void 0===i.current[a]&&(i.current[a]=le[a]);const[o,s]=c.useMemo((()=>function(e,t){const n=t=>{const n="touches"in t;n&&t.touches.length>1||e(((e,r)=>{r.trackMouse&&!n&&(document.addEventListener(ce,i),document.addEventListener(he,a));const{clientX:o,clientY:s}=n?t.touches[0]:t,l=de([o,s],r.rotationAngle);return r.onTouchStartOrOnMouseDown&&r.onTouchStartOrOnMouseDown({event:t}),Object.assign(Object.assign(Object.assign({},e),ue),{initial:l.slice(),xy:l,start:t.timeStamp||0})}))},i=t=>{e(((e,n)=>{const i="touches"in t;if(i&&t.touches.length>1)return e;if(t.timeStamp-e.start>n.swipeDuration)return e.swiping?Object.assign(Object.assign({},e),{swiping:!1}):e;const{clientX:r,clientY:a}=i?t.touches[0]:t,[o,s]=de([r,a],n.rotationAngle),l=o-e.xy[0],u=s-e.xy[1],c=Math.abs(l),h=Math.abs(u),d=(t.timeStamp||0)-e.start,f=Math.sqrt(c*c+h*h)/(d||1),p=[l/(d||1),u/(d||1)],m=function(e,t,n,i){return e>t?n>0?ae:re:i>0?se:oe}(c,h,l,u),b="number"==typeof n.delta?n.delta:n.delta[m.toLowerCase()]||le.delta;if(c<b&&h<b&&!e.swiping)return e;const g={absX:c,absY:h,deltaX:l,deltaY:u,dir:m,event:t,first:e.first,initial:e.initial,velocity:f,vxvy:p};g.first&&n.onSwipeStart&&n.onSwipeStart(g),n.onSwiping&&n.onSwiping(g);let v=!1;return(n.onSwiping||n.onSwiped||n["onSwiped".concat(m)])&&(v=!0),v&&n.preventScrollOnSwipe&&n.trackTouch&&t.cancelable&&t.preventDefault(),Object.assign(Object.assign({},e),{first:!1,eventData:g,swiping:!0})}))},r=t=>{e(((e,n)=>{let i;if(e.swiping&&e.eventData){if(t.timeStamp-e.start<n.swipeDuration){i=Object.assign(Object.assign({},e.eventData),{event:t}),n.onSwiped&&n.onSwiped(i);const r=n["onSwiped".concat(i.dir)];r&&r(i)}}else n.onTap&&n.onTap({event:t});return n.onTouchEndOrOnMouseUp&&n.onTouchEndOrOnMouseUp({event:t}),Object.assign(Object.assign(Object.assign({},e),ue),{eventData:i})}))},a=e=>{document.removeEventListener(ce,i),document.removeEventListener(he,a),r(e)},o=(e,t)=>{let a=()=>{};if(e&&e.addEventListener){const o=Object.assign(Object.assign({},le.touchEventOptions),t.touchEventOptions),s=[["touchstart",n,o],["touchmove",i,Object.assign(Object.assign({},o),t.preventScrollOnSwipe?{passive:!1}:{})],["touchend",r,o]];s.forEach((t=>{let[n,i,r]=t;return e.addEventListener(n,i,r)})),a=()=>s.forEach((t=>{let[n,i]=t;return e.removeEventListener(n,i)}))}return a},s={ref:t=>{null!==t&&e(((e,n)=>{if(e.el===t)return e;const i={};return e.el&&e.el!==t&&e.cleanUpTouch&&(e.cleanUpTouch(),i.cleanUpTouch=void 0),n.trackTouch&&t&&(i.cleanUpTouch=o(t,n)),Object.assign(Object.assign(Object.assign({},e),{el:t}),i)}))}};return t.trackMouse&&(s.onMouseDown=n),[s,o]}((e=>n.current=e(n.current,i.current)),{trackMouse:t})),[t]);return n.current=function(e,t,n,i){return t.trackTouch&&e.el?e.cleanUpTouch?t.preventScrollOnSwipe!==n.preventScrollOnSwipe||t.touchEventOptions.passive!==n.touchEventOptions.passive?(e.cleanUpTouch(),Object.assign(Object.assign({},e),{cleanUpTouch:i(e.el,t)})):e:Object.assign(Object.assign({},e),{cleanUpTouch:i(e.el,t)}):(e.cleanUpTouch&&e.cleanUpTouch(),Object.assign(Object.assign({},e),{cleanUpTouch:void 0}))}(n.current,i.current,r.current,s),o}({delta:t.delta,onSwiping:t.onSwiping,onSwiped:t.onSwiped});return c.default.createElement("div",Fe({},r,{className:i}),n)};qe.propTypes={children:fe.node.isRequired,className:fe.string,delta:fe.number,onSwiped:fe.func,onSwiping:fe.func};const Ge=qe;var He=c.default.memo((function(e){var t=e.disabled,n=e.onClick;return c.default.createElement("button",{type:"button",className:"image-gallery-icon image-gallery-top-nav",disabled:t,onClick:n,"aria-label":"Previous Slide"},c.default.createElement(xe,{icon:"top",viewBox:"6 0 12 24"}))}));He.displayName="TopNav",He.propTypes={disabled:fe.bool.isRequired,onClick:fe.func.isRequired};const Ve=He;var Ke=c.default.memo((function(e){var t=e.disabled,n=e.onClick;return c.default.createElement("button",{type:"button",className:"image-gallery-icon image-gallery-bottom-nav",disabled:t,onClick:n,"aria-label":"Next Slide"},c.default.createElement(xe,{icon:"bottom",viewBox:"6 0 12 24"}))}));Ke.displayName="BottomNav",Ke.propTypes={disabled:fe.bool.isRequired,onClick:fe.func.isRequired};const Xe=Ke;function Ye(e){return Ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ye(e)}function $e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function Je(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$e(Object(n),!0).forEach((function(t){tt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Qe(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Qe=function(){return!!e})()}function Ze(e){return Ze=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ze(e)}function et(e,t){return et=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},et(e,t)}function tt(e,t,n){return(t=nt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function nt(e){var t=function(e){if("object"!=Ye(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Ye(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ye(t)?t:t+""}var it=["fullscreenchange","MSFullscreenChange","mozfullscreenchange","webkitfullscreenchange"],rt=(0,fe.arrayOf)((0,fe.shape)({srcSet:fe.string,media:fe.string}));function at(e){var t=parseInt(e.keyCode||e.which||0,10);return 66===t||62===t}var ot=function(){function e(t){var n,i,r,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),tt((i=this,a=[t],r=Ze(r=e),n=function(e,t){if(t&&("object"==Ye(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(i,Qe()?Reflect.construct(r,a||[],Ze(i).constructor):r.apply(i,a))),"onBulletClick",(function(e,t){var i=n.props,r=i.onBulletClick,a=i.items,o=n.state.currentIndex;e.target.blur(),o!==t&&(2===a.length?n.slideToIndexWithStyleReset(t,e):n.slideToIndex(t,e)),r&&r(e,t)})),n.state={currentIndex:t.startIndex,thumbsTranslate:0,thumbsSwipedTranslate:0,currentSlideOffset:0,galleryWidth:0,galleryHeight:0,thumbnailsWrapperWidth:0,thumbnailsWrapperHeight:0,thumbsStyle:{transition:"all ".concat(t.slideDuration,"ms ease-out")},isFullscreen:!1,isSwipingThumbnail:!1,isPlaying:!1},n.loadedImages={},n.imageGallery=c.default.createRef(),n.thumbnailsWrapper=c.default.createRef(),n.thumbnails=c.default.createRef(),n.imageGallerySlideWrapper=c.default.createRef(),n.handleImageLoaded=n.handleImageLoaded.bind(n),n.handleKeyDown=n.handleKeyDown.bind(n),n.handleMouseDown=n.handleMouseDown.bind(n),n.handleResize=n.handleResize.bind(n),n.handleOnSwiped=n.handleOnSwiped.bind(n),n.handleScreenChange=n.handleScreenChange.bind(n),n.handleSwiping=n.handleSwiping.bind(n),n.handleThumbnailSwiping=n.handleThumbnailSwiping.bind(n),n.handleOnThumbnailSwiped=n.handleOnThumbnailSwiped.bind(n),n.onThumbnailMouseLeave=n.onThumbnailMouseLeave.bind(n),n.handleImageError=n.handleImageError.bind(n),n.pauseOrPlay=n.pauseOrPlay.bind(n),n.renderThumbInner=n.renderThumbInner.bind(n),n.renderItem=n.renderItem.bind(n),n.slideLeft=n.slideLeft.bind(n),n.slideRight=n.slideRight.bind(n),n.toggleFullScreen=n.toggleFullScreen.bind(n),n.togglePlay=n.togglePlay.bind(n),n.unthrottledSlideToIndex=n.slideToIndex,n.slideToIndex=C(n.unthrottledSlideToIndex,t.slideDuration,{trailing:!1}),t.lazyLoad&&(n.lazyLoaded=[]),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&et(e,t)}(e,c.default.Component),function(e,t){return t&&function(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,nt(i.key),i)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}(e,[{key:"componentDidMount",value:function(){var e=this.props,t=e.autoPlay,n=e.useWindowKeyDown;t&&this.play(),n?window.addEventListener("keydown",this.handleKeyDown):this.imageGallery.current.addEventListener("keydown",this.handleKeyDown),window.addEventListener("mousedown",this.handleMouseDown),this.initSlideWrapperResizeObserver(this.imageGallerySlideWrapper),this.initThumbnailWrapperResizeObserver(this.thumbnailsWrapper),this.addScreenChangeEvent()}},{key:"componentDidUpdate",value:function(e,t){var n=this.props,i=n.items,r=n.lazyLoad,a=n.slideDuration,o=n.slideInterval,s=n.startIndex,l=n.thumbnailPosition,u=n.showThumbnails,c=n.useWindowKeyDown,h=this.state,d=h.currentIndex,f=h.isPlaying,p=e.items.length!==i.length,m=!N()(e.items,i),b=e.startIndex!==s,g=e.thumbnailPosition!==l,v=e.showThumbnails!==u;o===e.slideInterval&&a===e.slideDuration||f&&(this.pause(),this.play()),g&&(this.removeResizeObserver(),this.initSlideWrapperResizeObserver(this.imageGallerySlideWrapper),this.initThumbnailWrapperResizeObserver(this.thumbnailsWrapper)),v&&u&&this.initThumbnailWrapperResizeObserver(this.thumbnailsWrapper),v&&!u&&this.removeThumbnailsResizeObserver(),(p||v)&&this.handleResize(),t.currentIndex!==d&&this.slideThumbnailBar(),e.slideDuration!==a&&(this.slideToIndex=C(this.unthrottledSlideToIndex,a,{trailing:!1})),!r||e.lazyLoad&&!m||(this.lazyLoaded=[]),c!==e.useWindowKeyDown&&(c?(this.imageGallery.current.removeEventListener("keydown",this.handleKeyDown),window.addEventListener("keydown",this.handleKeyDown)):(window.removeEventListener("keydown",this.handleKeyDown),this.imageGallery.current.addEventListener("keydown",this.handleKeyDown))),(b||m)&&this.setState({currentIndex:s,slideStyle:{transition:"none"}})}},{key:"componentWillUnmount",value:function(){var e=this.props.useWindowKeyDown;window.removeEventListener("mousedown",this.handleMouseDown),this.removeScreenChangeEvent(),this.removeResizeObserver(),this.playPauseIntervalId&&(window.clearInterval(this.playPauseIntervalId),this.playPauseIntervalId=null),this.transitionTimer&&window.clearTimeout(this.transitionTimer),e?window.removeEventListener("keydown",this.handleKeyDown):this.imageGallery.current.removeEventListener("keydown",this.handleKeyDown)}},{key:"onSliding",value:function(){var e=this,t=this.state,n=t.currentIndex,i=t.isTransitioning,r=this.props,a=r.onSlide,o=r.slideDuration;this.transitionTimer=window.setTimeout((function(){i&&(e.setState({isTransitioning:!i,isSwipingThumbnail:!1}),a&&a(n))}),o+50)}},{key:"onThumbnailClick",value:function(e,t){var n=this.props,i=n.onThumbnailClick,r=n.items,a=this.state.currentIndex;e.target.parentNode.parentNode.blur(),a!==t&&(2===r.length?this.slideToIndexWithStyleReset(t,e):this.slideToIndex(t,e)),i&&i(e,t)}},{key:"onThumbnailMouseOver",value:function(e,t){var n=this;this.thumbnailMouseOverTimer&&(window.clearTimeout(this.thumbnailMouseOverTimer),this.thumbnailMouseOverTimer=null),this.thumbnailMouseOverTimer=window.setTimeout((function(){n.slideToIndex(t),n.pause()}),300)}},{key:"onThumbnailMouseLeave",value:function(){if(this.thumbnailMouseOverTimer){var e=this.props.autoPlay;window.clearTimeout(this.thumbnailMouseOverTimer),this.thumbnailMouseOverTimer=null,e&&this.play()}}},{key:"setThumbsTranslate",value:function(e){this.setState({thumbsTranslate:e})}},{key:"setModalFullscreen",value:function(e){var t=this.props.onScreenChange;this.setState({modalFullscreen:e}),t&&t(e)}},{key:"getThumbsTranslate",value:function(e){var t,n=this.props,i=n.disableThumbnailScroll,r=n.items,a=this.state,o=a.thumbnailsWrapperWidth,s=a.thumbnailsWrapperHeight,l=this.thumbnails&&this.thumbnails.current;if(i)return 0;if(l){if(this.isThumbnailVertical()){if(l.scrollHeight<=s)return 0;t=l.scrollHeight-s}else{if(l.scrollWidth<=o||o<=0)return 0;t=l.scrollWidth-o}return e*(t/(r.length-1))}return 0}},{key:"getThumbnailPositionClassName",value:function(e){switch(e){case"left":e=" ".concat("image-gallery-thumbnails-left");break;case"right":e=" ".concat("image-gallery-thumbnails-right");break;case"bottom":e=" ".concat("image-gallery-thumbnails-bottom");break;case"top":e=" ".concat("image-gallery-thumbnails-top")}return e}},{key:"getAlignmentClassName",value:function(e){var t=this.state.currentIndex,n=this.props,i=n.infinite,r=n.items,a="",o="image-gallery-left",s="image-gallery-right";switch(e){case t-1:a=" ".concat(o);break;case t:a=" ".concat("image-gallery-center");break;case t+1:a=" ".concat(s)}return r.length>=3&&i&&(0===e&&t===r.length-1?a=" ".concat(s):e===r.length-1&&0===t&&(a=" ".concat(o))),a}},{key:"getTranslateXForTwoSlide",value:function(e){var t=this.state,n=t.currentIndex,i=t.currentSlideOffset,r=t.previousIndex,a=n!==r,o=0===e&&0===r,s=1===e&&1===r,l=0===e&&1===n,u=1===e&&0===n,c=0===i,h=-100*n+100*e+i;return i>0?this.direction="left":i<0&&(this.direction="right"),u&&i>0&&(h=-100+i),l&&i<0&&(h=100+i),a?o&&c&&"left"===this.direction?h=100:s&&c&&"right"===this.direction&&(h=-100):(u&&c&&"left"===this.direction&&(h=-100),l&&c&&"right"===this.direction&&(h=100)),h}},{key:"getThumbnailBarHeight",value:function(){return this.isThumbnailVertical()?{height:this.state.gallerySlideWrapperHeight}:{}}},{key:"getSlideStyle",value:function(e){var t=this.state,n=t.currentIndex,i=t.currentSlideOffset,r=t.slideStyle,a=this.props,o=a.infinite,s=a.items,l=a.useTranslate3D,u=a.isRTL,c=a.slideVertically,h=-100*n,d=s.length-1,f=(h+100*e)*(u?-1:1)+i;o&&s.length>2&&(0===n&&e===d?f=-100*(u?-1:1)+i:n===d&&0===e&&(f=100*(u?-1:1)+i)),o&&2===s.length&&(f=this.getTranslateXForTwoSlide(e));var p=c?"translate(0, ".concat(f,"%)"):"translate(".concat(f,"%, 0)");return l&&(p=c?"translate3d(0, ".concat(f,"%, 0)"):"translate3d(".concat(f,"%, 0, 0)")),Je({display:this.isSlideVisible(e)?"inherit":"none",WebkitTransform:p,MozTransform:p,msTransform:p,OTransform:p,transform:p},r)}},{key:"getCurrentIndex",value:function(){return this.state.currentIndex}},{key:"getThumbnailStyle",value:function(){var e,t=this.props,n=t.useTranslate3D,i=t.isRTL,r=this.state,a=r.thumbsTranslate,o=r.thumbsStyle,s=i?-1*a:a;return this.isThumbnailVertical()?(e="translate(0, ".concat(a,"px)"),n&&(e="translate3d(0, ".concat(a,"px, 0)"))):(e="translate(".concat(s,"px, 0)"),n&&(e="translate3d(".concat(s,"px, 0, 0)"))),Je({WebkitTransform:e,MozTransform:e,msTransform:e,OTransform:e,transform:e},o)}},{key:"getSlideItems",value:function(){var e=this,t=this.state.currentIndex,n=this.props,i=n.items,r=n.slideOnThumbnailOver,a=n.onClick,o=n.lazyLoad,s=n.onTouchMove,l=n.onTouchEnd,h=n.onTouchStart,d=n.onMouseOver,f=n.onMouseLeave,p=n.renderItem,m=n.renderThumbInner,b=n.showThumbnails,g=n.showBullets,v=[],y=[],w=[];return i.forEach((function(n,i){var S=e.getAlignmentClassName(i),T=n.originalClass?" ".concat(n.originalClass):"",O=n.thumbnailClass?" ".concat(n.thumbnailClass):"",E=n.renderItem||p||e.renderItem,k=n.renderThumbInner||m||e.renderThumbInner,I=!o||S||e.lazyLoaded[i];I&&o&&!e.lazyLoaded[i]&&(e.lazyLoaded[i]=!0);var P=e.getSlideStyle(i),x=c.default.createElement("div",{"aria-label":"Go to Slide ".concat(i+1),key:"slide-".concat(i),tabIndex:"-1",className:"image-gallery-slide ".concat(S," ").concat(T),style:P,onClick:a,onKeyUp:e.handleSlideKeyUp,onTouchMove:s,onTouchEnd:l,onTouchStart:h,onMouseOver:d,onFocus:d,onMouseLeave:f,role:"button"},I?E(n):c.default.createElement("div",{style:{height:"100%"}}));if(v.push(x),b&&n.thumbnail){var j=u("image-gallery-thumbnail",O,{active:t===i});y.push(c.default.createElement("button",{key:"thumbnail-".concat(i),type:"button",tabIndex:"0","aria-pressed":t===i?"true":"false","aria-label":"Go to Slide ".concat(i+1),className:j,onMouseLeave:r?e.onThumbnailMouseLeave:null,onMouseOver:function(t){return e.handleThumbnailMouseOver(t,i)},onFocus:function(t){return e.handleThumbnailMouseOver(t,i)},onKeyUp:function(t){return e.handleThumbnailKeyUp(t,i)},onClick:function(t){return e.onThumbnailClick(t,i)}},k(n)))}if(g){var _=u("image-gallery-bullet",n.bulletClass,{active:t===i});w.push(c.default.createElement("button",{type:"button",key:"bullet-".concat(i),className:_,onClick:function(t){return e.onBulletClick(t,i)},"aria-pressed":t===i?"true":"false","aria-label":"Go to Slide ".concat(i+1)}))}})),{slides:v,thumbnails:y,bullets:w}}},{key:"ignoreIsTransitioning",value:function(){var e=this.props.items,t=this.state,n=t.previousIndex,i=t.currentIndex,r=e.length-1;return Math.abs(n-i)>1&&!(0===n&&i===r)&&!(n===r&&0===i)}},{key:"isFirstOrLastSlide",value:function(e){return e===this.props.items.length-1||0===e}},{key:"slideIsTransitioning",value:function(e){var t=this.state,n=t.isTransitioning,i=t.previousIndex,r=t.currentIndex;return n&&!(e===i||e===r)}},{key:"isSlideVisible",value:function(e){return!this.slideIsTransitioning(e)||this.ignoreIsTransitioning()&&!this.isFirstOrLastSlide(e)}},{key:"slideThumbnailBar",value:function(){var e=this.state,t=e.currentIndex,n=e.isSwipingThumbnail,i=-this.getThumbsTranslate(t);n||(0===t?this.setState({thumbsTranslate:0,thumbsSwipedTranslate:0}):this.setState({thumbsTranslate:i,thumbsSwipedTranslate:i}))}},{key:"canSlide",value:function(){return this.props.items.length>=2}},{key:"canSlideLeft",value:function(){var e=this.props,t=e.infinite,n=e.isRTL;return t||(n?this.canSlideNext():this.canSlidePrevious())}},{key:"canSlideRight",value:function(){var e=this.props,t=e.infinite,n=e.isRTL;return t||(n?this.canSlidePrevious():this.canSlideNext())}},{key:"canSlidePrevious",value:function(){return this.state.currentIndex>0}},{key:"canSlideNext",value:function(){return this.state.currentIndex<this.props.items.length-1}},{key:"handleSwiping",value:function(e){var t=e.event,n=e.absX,i=e.absY,r=e.dir,a=this.props,o=a.disableSwipe,s=a.stopPropagation,l=a.swipingTransitionDuration,u=this.state,c=u.galleryWidth,h=u.galleryHeight,d=u.isTransitioning,f=u.swipingUpDown,p=u.swipingLeftRight,m=this.props.slideVertically;if((r!==oe&&r!==se&&!f||p||(f||this.setState({swipingUpDown:!0}),m))&&(r!==re&&r!==ae||p||this.setState({swipingLeftRight:!0}),!o))if(s&&t.preventDefault(),d)this.setState({currentSlideOffset:0});else{if((r===re||r===ae)&&m)return;if((r===oe||r===se)&&!m)return;var b=tt(tt(tt(tt({},re,-1),ae,1),oe,-1),se,1)[r],g=n/c*100;m&&(g=i/h*100),Math.abs(g)>=100&&(g=100);var v={transition:"transform ".concat(l,"ms ease-out")};this.setState({currentSlideOffset:b*g,slideStyle:v})}}},{key:"handleThumbnailSwiping",value:function(e){var t=e.event,n=e.absX,i=e.absY,r=e.dir,a=this.props,o=a.stopPropagation,s=a.swipingThumbnailTransitionDuration,l=this.state,u=l.thumbsSwipedTranslate,c=l.thumbnailsWrapperHeight,h=l.thumbnailsWrapperWidth,d=l.swipingUpDown,f=l.swipingLeftRight;if(this.isThumbnailVertical()){if((r===re||r===ae||f)&&!d)return void(f||this.setState({swipingLeftRight:!0}));r!==oe&&r!==se||d||this.setState({swipingUpDown:!0})}else{if((r===oe||r===se||d)&&!f)return void(d||this.setState({swipingUpDown:!0}));r!==re&&r!==ae||f||this.setState({swipingLeftRight:!0})}var p,m,b,g,v,y=this.thumbnails&&this.thumbnails.current;if(this.isThumbnailVertical()?(p=u+(r===se?i:-i),m=y.scrollHeight-c+20,b=Math.abs(p)>m,g=p>20,v=y.scrollHeight<=c):(p=u+(r===ae?n:-n),m=y.scrollWidth-h+20,b=Math.abs(p)>m,g=p>20,v=y.scrollWidth<=h),!v&&(r!==re&&r!==oe||!b)&&(r!==ae&&r!==se||!g)){o&&t.stopPropagation();var w={transition:"transform ".concat(s,"ms ease-out")};this.setState({thumbsTranslate:p,thumbsStyle:w})}}},{key:"handleOnThumbnailSwiped",value:function(){var e=this.state.thumbsTranslate,t=this.props.slideDuration;this.resetSwipingDirection(),this.setState({isSwipingThumbnail:!0,thumbsSwipedTranslate:e,thumbsStyle:{transition:"all ".concat(t,"ms ease-out")}})}},{key:"sufficientSwipe",value:function(){var e=this.state.currentSlideOffset,t=this.props.swipeThreshold;return Math.abs(e)>t}},{key:"resetSwipingDirection",value:function(){var e=this.state,t=e.swipingUpDown,n=e.swipingLeftRight;t&&this.setState({swipingUpDown:!1}),n&&this.setState({swipingLeftRight:!1})}},{key:"handleOnSwiped",value:function(e){var t=e.event,n=e.dir,i=e.velocity,r=this.props,a=r.disableSwipe,o=r.stopPropagation,s=r.flickThreshold,l=this.props.slideVertically;if(!a){var u=this.props.isRTL;o&&t.stopPropagation(),this.resetSwipingDirection();var c=(n===re?1:-1)*(u?-1:1);l&&(c=n===oe?1:-1);var h=l?i>s&&!(n===re||n===ae):i>s&&!(n===oe||n===se);this.handleOnSwipedTo(c,h)}}},{key:"handleOnSwipedTo",value:function(e,t){var n=this.state,i=n.currentIndex,r=n.isTransitioning,a=i;!this.sufficientSwipe()&&!t||r||(a+=e),(-1===e&&!this.canSlideLeft()||1===e&&!this.canSlideRight())&&(a=i),this.unthrottledSlideToIndex(a)}},{key:"handleMouseDown",value:function(){this.imageGallery.current.classList.add("image-gallery-using-mouse")}},{key:"handleKeyDown",value:function(e){var t=this.props,n=t.disableKeyDown,i=t.useBrowserFullscreen,r=this.state.isFullscreen;if(this.imageGallery.current.classList.remove("image-gallery-using-mouse"),!n)switch(parseInt(e.keyCode||e.which||0,10)){case 37:this.canSlideLeft()&&!this.playPauseIntervalId&&this.slideLeft(e);break;case 39:this.canSlideRight()&&!this.playPauseIntervalId&&this.slideRight(e);break;case 27:r&&!i&&this.exitFullScreen()}}},{key:"handleImageError",value:function(e){var t=this.props.onErrorImageURL;t&&-1===e.target.src.indexOf(t)&&(e.target.src=t)}},{key:"removeThumbnailsResizeObserver",value:function(){this.resizeThumbnailWrapperObserver&&this.thumbnailsWrapper&&this.thumbnailsWrapper.current&&(this.resizeThumbnailWrapperObserver.unobserve(this.thumbnailsWrapper.current),this.resizeThumbnailWrapperObserver=null)}},{key:"removeResizeObserver",value:function(){this.resizeSlideWrapperObserver&&this.imageGallerySlideWrapper&&this.imageGallerySlideWrapper.current&&(this.resizeSlideWrapperObserver.unobserve(this.imageGallerySlideWrapper.current),this.resizeSlideWrapperObserver=null),this.removeThumbnailsResizeObserver()}},{key:"handleResize",value:function(){var e=this.state.currentIndex;this.imageGallery&&(this.imageGallery&&this.imageGallery.current&&this.setState({galleryWidth:this.imageGallery.current.offsetWidth,galleryHeight:this.imageGallery.current.offsetHeight}),this.imageGallerySlideWrapper&&this.imageGallerySlideWrapper.current&&this.setState({gallerySlideWrapperHeight:this.imageGallerySlideWrapper.current.offsetHeight}),this.setThumbsTranslate(-this.getThumbsTranslate(e)))}},{key:"initSlideWrapperResizeObserver",value:function(e){var t=this;e&&!e.current||(this.resizeSlideWrapperObserver=new ie(D((function(e){e&&e.forEach((function(e){t.setState({thumbnailsWrapperWidth:e.contentRect.width},t.handleResize)}))}),50)),this.resizeSlideWrapperObserver.observe(e.current))}},{key:"initThumbnailWrapperResizeObserver",value:function(e){var t=this;e&&!e.current||(this.resizeThumbnailWrapperObserver=new ie(D((function(e){e&&e.forEach((function(e){t.setState({thumbnailsWrapperHeight:e.contentRect.height},t.handleResize)}))}),50)),this.resizeThumbnailWrapperObserver.observe(e.current))}},{key:"toggleFullScreen",value:function(){this.state.isFullscreen?this.exitFullScreen():this.fullScreen()}},{key:"togglePlay",value:function(){this.playPauseIntervalId?this.pause():this.play()}},{key:"handleScreenChange",value:function(){var e=this.props,t=e.onScreenChange,n=e.useBrowserFullscreen,i=document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement,r=this.imageGallery.current===i;t&&t(r),n&&this.setState({isFullscreen:r})}},{key:"slideToIndex",value:function(e,t){var n=this.state,i=n.currentIndex,r=n.isTransitioning,a=this.props,o=a.items,s=a.slideDuration,l=a.onBeforeSlide;if(!r){t&&this.playPauseIntervalId&&(this.pause(!1),this.play(!1));var u=o.length-1,c=e;e<0?c=u:e>u&&(c=0),l&&c!==i&&l(c),this.setState({previousIndex:i,currentIndex:c,isTransitioning:c!==i,currentSlideOffset:0,slideStyle:{transition:"all ".concat(s,"ms ease-out")}},this.onSliding)}}},{key:"slideLeft",value:function(e){var t=this.props.isRTL;this.slideTo(e,t?"right":"left")}},{key:"slideRight",value:function(e){var t=this.props.isRTL;this.slideTo(e,t?"left":"right")}},{key:"slideTo",value:function(e,t){var n=this.state,i=n.currentIndex,r=n.isTransitioning,a=this.props.items,o=i+("left"===t?-1:1);r||(2===a.length?this.slideToIndexWithStyleReset(o,e):this.slideToIndex(o,e))}},{key:"slideToIndexWithStyleReset",value:function(e,t){var n=this,i=this.state,r=i.currentIndex,a=i.currentSlideOffset;this.setState({currentSlideOffset:a+(r>e?.001:-.001),slideStyle:{transition:"none"}},(function(){window.setTimeout((function(){return n.slideToIndex(e,t)}),25)}))}},{key:"handleThumbnailMouseOver",value:function(e,t){this.props.slideOnThumbnailOver&&this.onThumbnailMouseOver(e,t)}},{key:"handleThumbnailKeyUp",value:function(e,t){at(e)&&this.onThumbnailClick(e,t)}},{key:"handleSlideKeyUp",value:function(e){at(e)&&(0,this.props.onClick)(e)}},{key:"isThumbnailVertical",value:function(){var e=this.props.thumbnailPosition;return"left"===e||"right"===e}},{key:"addScreenChangeEvent",value:function(){var e=this;it.forEach((function(t){document.addEventListener(t,e.handleScreenChange)}))}},{key:"removeScreenChangeEvent",value:function(){var e=this;it.forEach((function(t){document.removeEventListener(t,e.handleScreenChange)}))}},{key:"fullScreen",value:function(){var e=this.props.useBrowserFullscreen,t=this.imageGallery.current;e?t.requestFullscreen?t.requestFullscreen():t.msRequestFullscreen?t.msRequestFullscreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen():this.setModalFullscreen(!0):this.setModalFullscreen(!0),this.setState({isFullscreen:!0})}},{key:"exitFullScreen",value:function(){var e=this.state.isFullscreen,t=this.props.useBrowserFullscreen;e&&(t?document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():this.setModalFullscreen(!1):this.setModalFullscreen(!1),this.setState({isFullscreen:!1}))}},{key:"pauseOrPlay",value:function(){var e=this.props.infinite,t=this.state.currentIndex;e||this.canSlideRight()?this.slideToIndex(t+1):this.pause()}},{key:"play",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=this.props,n=t.onPlay,i=t.slideInterval,r=t.slideDuration,a=this.state.currentIndex;this.playPauseIntervalId||(this.setState({isPlaying:!0}),this.playPauseIntervalId=window.setInterval(this.pauseOrPlay,Math.max(i,r)),n&&e&&n(a))}},{key:"pause",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=this.props.onPause,n=this.state.currentIndex;this.playPauseIntervalId&&(window.clearInterval(this.playPauseIntervalId),this.playPauseIntervalId=null,this.setState({isPlaying:!1}),t&&e&&t(n))}},{key:"isImageLoaded",value:function(e){return!!this.loadedImages[e.original]||(this.loadedImages[e.original]=!0,!1)}},{key:"handleImageLoaded",value:function(e,t){var n=this.props.onImageLoad;!this.loadedImages[t]&&n&&(this.loadedImages[t]=!0,n(e))}},{key:"renderItem",value:function(e){var t=this.state.isFullscreen,n=this.props.onImageError||this.handleImageError;return c.default.createElement(we,{description:e.description,fullscreen:e.fullscreen,handleImageLoaded:this.handleImageLoaded,isFullscreen:t,onImageError:n,original:e.original,originalAlt:e.originalAlt,originalHeight:e.originalHeight,originalWidth:e.originalWidth,originalTitle:e.originalTitle,sizes:e.sizes,loading:e.loading,srcSet:e.srcSet})}},{key:"renderThumbInner",value:function(e){var t=this.props.onThumbnailError||this.handleImageError;return c.default.createElement("span",{className:"image-gallery-thumbnail-inner"},c.default.createElement("img",{className:"image-gallery-thumbnail-image",src:e.thumbnail,height:e.thumbnailHeight,width:e.thumbnailWidth,alt:e.thumbnailAlt,title:e.thumbnailTitle,loading:e.thumbnailLoading,onError:t}),e.thumbnailLabel&&c.default.createElement("div",{className:"image-gallery-thumbnail-label"},e.thumbnailLabel))}},{key:"render",value:function(){var e=this.state,t=e.currentIndex,n=e.isFullscreen,i=e.modalFullscreen,r=e.isPlaying,a=this.props,o=a.additionalClass,s=a.disableThumbnailSwipe,l=a.indexSeparator,h=a.isRTL,d=a.items,f=a.thumbnailPosition,p=a.renderFullscreenButton,m=a.renderCustomControls,b=a.renderLeftNav,g=a.renderRightNav,v=a.renderTopNav,y=a.renderBottomNav,w=a.showBullets,S=a.showFullscreenButton,T=a.showIndex,O=a.showThumbnails,E=a.showNav,k=a.showPlayButton,I=a.slideVertically,P=a.renderPlayPauseButton,x=this.getThumbnailStyle(),j=this.getSlideItems(),_=j.slides,R=j.thumbnails,L=j.bullets,M=u("image-gallery-slide-wrapper",this.getThumbnailPositionClassName(f),{"image-gallery-rtl":h}),D=u("image-gallery-bullets",{"image-gallery-bullets-vertical":I}),C=c.default.createElement("div",{ref:this.imageGallerySlideWrapper,className:M},m&&m(),this.canSlide()?c.default.createElement(c.default.Fragment,null,E&&c.default.createElement(c.default.Fragment,null,I?v(this.slideLeft,!this.canSlideLeft()):b(this.slideLeft,!this.canSlideLeft()),I?y(this.slideRight,!this.canSlideRight()):g(this.slideRight,!this.canSlideRight())),c.default.createElement(Ge,{className:"image-gallery-swipe",delta:0,onSwiping:this.handleSwiping,onSwiped:this.handleOnSwiped},c.default.createElement("div",{className:"image-gallery-slides"},_))):c.default.createElement("div",{className:"image-gallery-slides"},_),k&&P(this.togglePlay,r),w&&c.default.createElement("div",{className:D},c.default.createElement("div",{className:"image-gallery-bullets-container",role:"navigation","aria-label":"Bullet Navigation"},L)),S&&p(this.toggleFullScreen,n),T&&c.default.createElement("div",{className:"image-gallery-index"},c.default.createElement("span",{className:"image-gallery-index-current"},t+1),c.default.createElement("span",{className:"image-gallery-index-separator"},l),c.default.createElement("span",{className:"image-gallery-index-total"},d.length))),W=u("image-gallery",o,{"fullscreen-modal":i}),N=u("image-gallery-content",this.getThumbnailPositionClassName(f),{fullscreen:n}),F=u("image-gallery-thumbnails-wrapper",this.getThumbnailPositionClassName(f),{"thumbnails-wrapper-rtl":!this.isThumbnailVertical()&&h},{"thumbnails-swipe-horizontal":!this.isThumbnailVertical()&&!s},{"thumbnails-swipe-vertical":this.isThumbnailVertical()&&!s});return c.default.createElement("div",{ref:this.imageGallery,className:W,"aria-live":"polite"},c.default.createElement("div",{className:N},("bottom"===f||"right"===f)&&C,O&&R.length>0?c.default.createElement(Ge,{className:F,delta:0,onSwiping:!s&&this.handleThumbnailSwiping,onSwiped:!s&&this.handleOnThumbnailSwiped},c.default.createElement("div",{className:"image-gallery-thumbnails",ref:this.thumbnailsWrapper,style:this.getThumbnailBarHeight()},c.default.createElement("nav",{ref:this.thumbnails,className:"image-gallery-thumbnails-container",style:x,"aria-label":"Thumbnail Navigation"},R))):null,("top"===f||"left"===f)&&C))}}])}();ot.propTypes={flickThreshold:fe.number,items:(0,fe.arrayOf)((0,fe.shape)({bulletClass:fe.string,bulletOnClick:fe.func,description:fe.string,original:fe.string,originalHeight:fe.number,originalWidth:fe.number,loading:fe.string,thumbnailHeight:fe.number,thumbnailWidth:fe.number,thumbnailLoading:fe.string,fullscreen:fe.string,originalAlt:fe.string,originalTitle:fe.string,thumbnail:fe.string,thumbnailAlt:fe.string,thumbnailLabel:fe.string,thumbnailTitle:fe.string,originalClass:fe.string,thumbnailClass:fe.string,renderItem:fe.func,renderThumbInner:fe.func,imageSet:rt,srcSet:fe.string,sizes:fe.string})).isRequired,showNav:fe.bool,autoPlay:fe.bool,lazyLoad:fe.bool,infinite:fe.bool,showIndex:fe.bool,showBullets:fe.bool,showThumbnails:fe.bool,showPlayButton:fe.bool,showFullscreenButton:fe.bool,disableThumbnailScroll:fe.bool,disableKeyDown:fe.bool,disableSwipe:fe.bool,disableThumbnailSwipe:fe.bool,useBrowserFullscreen:fe.bool,onErrorImageURL:fe.string,indexSeparator:fe.string,thumbnailPosition:(0,fe.oneOf)(["top","bottom","left","right"]),startIndex:fe.number,slideDuration:fe.number,slideInterval:fe.number,slideOnThumbnailOver:fe.bool,swipeThreshold:fe.number,swipingTransitionDuration:fe.number,swipingThumbnailTransitionDuration:fe.number,onSlide:fe.func,onBeforeSlide:fe.func,onScreenChange:fe.func,onPause:fe.func,onPlay:fe.func,onClick:fe.func,onImageLoad:fe.func,onImageError:fe.func,onTouchMove:fe.func,onTouchEnd:fe.func,onTouchStart:fe.func,onMouseOver:fe.func,onMouseLeave:fe.func,onBulletClick:fe.func,onThumbnailError:fe.func,onThumbnailClick:fe.func,renderCustomControls:fe.func,renderLeftNav:fe.func,renderRightNav:fe.func,renderTopNav:fe.func,renderBottomNav:fe.func,renderPlayPauseButton:fe.func,renderFullscreenButton:fe.func,renderItem:fe.func,renderThumbInner:fe.func,stopPropagation:fe.bool,additionalClass:fe.string,useTranslate3D:fe.bool,isRTL:fe.bool,useWindowKeyDown:fe.bool,slideVertically:fe.bool},ot.defaultProps={onErrorImageURL:"",additionalClass:"",showNav:!0,autoPlay:!1,lazyLoad:!1,infinite:!0,showIndex:!1,showBullets:!1,showThumbnails:!0,showPlayButton:!0,showFullscreenButton:!0,disableThumbnailScroll:!1,disableKeyDown:!1,disableSwipe:!1,disableThumbnailSwipe:!1,useTranslate3D:!0,isRTL:!1,useBrowserFullscreen:!0,flickThreshold:.4,stopPropagation:!1,indexSeparator:" / ",thumbnailPosition:"bottom",startIndex:0,slideDuration:450,swipingTransitionDuration:0,swipingThumbnailTransitionDuration:0,onSlide:null,onBeforeSlide:null,onScreenChange:null,onPause:null,onPlay:null,onClick:null,onImageLoad:null,onImageError:null,onTouchMove:null,onTouchEnd:null,onTouchStart:null,onMouseOver:null,onMouseLeave:null,onBulletClick:null,onThumbnailError:null,onThumbnailClick:null,renderCustomControls:null,renderThumbInner:null,renderItem:null,slideInterval:3e3,slideOnThumbnailOver:!1,swipeThreshold:30,slideVertically:!1,renderLeftNav:function(e,t){return c.default.createElement(Le,{onClick:e,disabled:t})},renderRightNav:function(e,t){return c.default.createElement(De,{onClick:e,disabled:t})},renderTopNav:function(e,t){return c.default.createElement(Ve,{onClick:e,disabled:t})},renderBottomNav:function(e,t){return c.default.createElement(Xe,{onClick:e,disabled:t})},renderPlayPauseButton:function(e,t){return c.default.createElement(We,{onClick:e,isPlaying:t})},renderFullscreenButton:function(e,t){return c.default.createElement(_e,{onClick:e,isFullscreen:t})},useWindowKeyDown:!0};const st=ot;var lt=s.A}}]);
//# sourceMappingURL=222.1438c3c2.chunk.js.map