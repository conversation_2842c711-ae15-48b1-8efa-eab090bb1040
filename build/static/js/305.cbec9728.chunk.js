"use strict";(self.webpackChunkcapastrength=self.webpackChunkcapastrength||[]).push([[305],{216:(n,e,r)=>{r.d(e,{A:()=>l});r(5043);var i,t=r(7528);const a=r(1779).Ay.section(i||(i=(0,t.A)(["\n  display: ",";\n  align-items: flex-start;\n"])),(n=>{let{flex:e}=n;return e?" flex":"block"}));var o=r(579);const l=n=>{let{children:e,flex:r}=n;return(0,o.jsx)(a,{className:"py-5",flex:r,children:e})}},2305:(n,e,r)=>{r.r(e),r.d(e,{default:()=>z});r(5043);var i=r(216),t=r(6689),a=r(7483),o=r(1066);const l=[{name:"Retrofitting / Structural Strengthening",icon:o.E.services.Retro},{name:"Waterproofing",icon:o.E.services.Waterproofing},{name:"Environmental Coatings",icon:o.E.services.Envcoating},{name:"Industrial Buildings",icon:o.E.services.Industrial},{name:"Public Buildings",icon:o.E.services.Publicbuildings},{name:"Water Tanks",icon:o.E.services.Watertank}];var s,c,d,g,p=r(7528),m=r(1779);m.Ay.div(s||(s=(0,p.A)(["\n  flex: 1;\n  text-align: center;\n\n  cursor: pointer;\n\n  &:hover {\n    .text {\n      color: ",";\n      font-weight: 600;\n    }\n\n    .image {\n      background-color: ",";\n      box-shadow: 0 0 20px 20px rgba(0, 0, 0, 0.1);\n    }\n  }\n"])),(n=>{let{theme:{colors:e}}=n;return e.main.red}),(n=>{let{theme:{colors:e}}=n;return e.main.blue})),m.Ay.div(c||(c=(0,p.A)(["\n  background-color: #fff;\n  box-shadow: 0 0 20px 20px rgba(0, 0, 0, 0.05);\n  border-radius: 100px;\n\n  width: 130px;\n  height: 130px;\n\n  margin: 0 auto;\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  transition: all 0.3s ease-in-out;\n"]))),m.Ay.img(d||(d=(0,p.A)(["\n  width: 60px;\n  height: 60px;\n"]))),m.Ay.p(g||(g=(0,p.A)(["\n  margin: 20px 0;\n  font-weight: 400;\n  transition: all 0.3s ease-in-out;\n"])));var u=r(579);var h,x=r(8719);const f=m.Ay.div(h||(h=(0,p.A)(["\n    display: flex;\n    align-items: flex-start;\n    justify-content: space-between;\n\n    flex-wrap: wrap;\n\n    gap: 20px;\n"]))),y=()=>(0,u.jsx)(f,{children:l.map(((n,e)=>(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(x.A,{title:null===n||void 0===n?void 0:n.name})})))}),b=()=>(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(a.A,{title:"Our Services"}),(0,u.jsx)(y,{})]});var v,j,w,A,E,M,k=r(5475),C=r(3447);const S=m.Ay.div(v||(v=(0,p.A)(["\n    display: flex;\n    align-items: center;\n\n    gap: 20px;\n"]))),P=m.Ay.img(j||(j=(0,p.A)(["\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n"]))),O=m.Ay.div(w||(w=(0,p.A)(["\n    margin-bottom: 30px;\n"]))),R=m.Ay.div(A||(A=(0,p.A)(["\n    font-size: 25px;\n    font-weight: bold;\n"]))),N=m.Ay.p(E||(E=(0,p.A)(["\n    font-size: ",";\n"])),(n=>{let{theme:{font:e}}=n;return e.main})),W=(m.Ay.button(M||(M=(0,p.A)(["\n    background-color: ",";\n    padding: 5px 10px;\n    border-radius: 5px;\n    border: none;\n"])),(n=>{let{theme:{colors:e}}=n;return e.main.yellow})),()=>(0,u.jsxs)(S,{className:"flex-wrap flex-md-nowrap",children:[(0,u.jsx)("div",{className:"col col-12 col-md-7",children:(0,u.jsx)(P,{src:o.E.client})}),(0,u.jsxs)("div",{className:"col col-12 col-md-5 ps-md-4",children:[(0,u.jsx)(O,{children:(0,u.jsx)(R,{children:"Our Client"})}),(0,u.jsx)(N,{className:"pe-md-5",children:"Capastrength is one of the India\u2019s leading service providers of Retrofitting, Structural Strengthening services for structures of many regions key public and private organisations. We have worked closely and successfully with Organisations and major companies, and pride ourselves on meeting and exceeding our clients high expectations. Here, you can find a selection of the many client organisations we already work closely alongside."}),(0,u.jsx)(k.N_,{to:"/clients",children:(0,u.jsx)(C.A,{className:"my-3",children:"See Clients"})})]})]})),z=()=>(0,u.jsxs)("div",{className:"container",children:[(0,u.jsx)(i.A,{children:(0,u.jsx)(t.A,{})}),(0,u.jsx)(i.A,{children:(0,u.jsx)(b,{})}),(0,u.jsx)(i.A,{children:(0,u.jsx)(W,{})})]})},6689:(n,e,r)=>{r.d(e,{A:()=>g});r(5043);var i,t=r(7483),a=r(6816),o=r(8317),l=r(7528);const s=r(1779).Ay.div(i||(i=(0,l.A)(["\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n\n  grid-gap: 20px;\n\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n"])));var c=r(579);const d=()=>(0,c.jsx)(s,{className:"flex-wrap flex-md-nowrap",children:o.w.slice(0,4).map(((n,e)=>(0,c.jsx)(a.A,{item:n},e)))}),g=()=>(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(t.A,{title:"Featured Property"}),(0,c.jsx)(d,{})]})},6816:(n,e,r)=>{r.d(e,{A:()=>B});var i=r(5043),t=r(5475),a=(r(1066),r(5173)),o=r.n(a);function l(){return l=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])}return n},l.apply(this,arguments)}function s(n,e){if(null==n)return{};var r,i,t=function(n,e){if(null==n)return{};var r,i,t={},a=Object.keys(n);for(i=0;i<a.length;i++)r=a[i],e.indexOf(r)>=0||(t[r]=n[r]);return t}(n,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);for(i=0;i<a.length;i++)r=a[i],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(n,r)&&(t[r]=n[r])}return t}var c=(0,i.forwardRef)((function(n,e){var r=n.color,t=void 0===r?"currentColor":r,a=n.size,o=void 0===a?24:a,c=s(n,["color","size"]);return i.createElement("svg",l({ref:e,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},c),i.createElement("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),i.createElement("circle",{cx:"12",cy:"10",r:"3"}))}));c.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},c.displayName="MapPin";const d=c;function g(){return g=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])}return n},g.apply(this,arguments)}function p(n,e){if(null==n)return{};var r,i,t=function(n,e){if(null==n)return{};var r,i,t={},a=Object.keys(n);for(i=0;i<a.length;i++)r=a[i],e.indexOf(r)>=0||(t[r]=n[r]);return t}(n,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);for(i=0;i<a.length;i++)r=a[i],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(n,r)&&(t[r]=n[r])}return t}var m=(0,i.forwardRef)((function(n,e){var r=n.color,t=void 0===r?"currentColor":r,a=n.size,o=void 0===a?24:a,l=p(n,["color","size"]);return i.createElement("svg",g({ref:e,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),i.createElement("line",{x1:"5",y1:"12",x2:"19",y2:"12"}),i.createElement("polyline",{points:"12 5 19 12 12 19"}))}));m.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},m.displayName="ArrowRight";const u=m;var h,x,f,y,b,v,j,w,A,E,M,k=r(7528),C=r(1779);const S=C.Ay.div(h||(h=(0,k.A)(["\n  background-color: #fff;\n  border-radius: 10px;\n\n  overflow: hidden;\n\n  flex: 1 1 auto;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);\n  cursor: pointer;\n\n  &:hover {\n    box-shadow: 0 0 13px rgba(0, 0, 0, 0.3);\n    .arrow {\n      margin-left: 12px;\n    }\n\n    .title {\n      color: ",";\n    }\n\n    .viewlink {\n      color: ",";\n    }\n\n    .image {\n      background-size: 110%;\n      transition: all 0.4s ease-in-out;\n    }\n  }\n\n  transition: all 0.3s ease-in-out;\n\n  position: relative;\n"])),(n=>{let{theme:{colors:e}}=n;return e.main.blue}),(n=>{let{theme:{colors:e}}=n;return e.main.red})),P=C.Ay.div(x||(x=(0,k.A)(["\n  position: relative;\n  background-image: ",";\n  background-size: 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n\n  transition: all 0.4s ease-in-out;\n\n  height: 180px;\n"])),(n=>{let{img:e}=n;return"url(".concat(e,")")})),O=C.Ay.div(f||(f=(0,k.A)(["\n  position: absolute;\n  right: 0;\n  top: 0;\n\n  background-color: ",";\n\n  color: #fff;\n  padding: 5px 25px;\n  border-bottom-left-radius: 10px;\n"])),(n=>{let{theme:{colors:e}}=n;return e.main.blue})),R=C.Ay.span(y||(y=(0,k.A)(["\n  font-size: ",";\n  font-weight: ",";\n  text-transform: ",";\n\n  position: relative;\n\n  transition: all 0.3s ease-in-out;\n"])),(n=>{let{sub:e,heading:r,theme:{font:i}}=n;return e?i.sub:r?i.title:i.main}),(n=>{let{bold:e}=n;return e?600:400}),(n=>{let{upper:e}=n;return e?"uppercase":"none"})),N=C.Ay.div(b||(b=(0,k.A)(["\n  padding: 20px;\n  margin-bottom: 30px;\n"]))),W=(C.Ay.div(v||(v=(0,k.A)(["\n  background-color: #e5e5e6;\n  padding: 15px 16px;\n\n  position: absolute;\n  right: 10%;\n  top: 0;\n\n  transform: translateY(-50%);\n\n  border-radius: 10px;\n"]))),C.Ay.div(j||(j=(0,k.A)(["\n  margin-right: 8px;\n\n  svg {\n    width: 20px;\n    height: 20px;\n  }\n"])))),z=C.Ay.div(w||(w=(0,k.A)(["\n  display: flex;\n  align-items: center;\n"]))),F=C.Ay.div(A||(A=(0,k.A)(["\n  padding: 15px 0 10px;\n"]))),T=C.Ay.div(E||(E=(0,k.A)(["\n  display: flex;\n  align-items: center;\n  color: ",";\n\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n"])),(n=>{let{theme:{colors:e}}=n;return e.main.blue})),L=C.Ay.div(M||(M=(0,k.A)(["\n  margin-left: 6px;\n\n  svg {\n    width: 20px;\n    height: 20px;\n  }\n  transition: all 0.3s ease-in-out;\n"])));var I=r(579);const B=n=>{let{item:e}=n;return console.log({item:e.banner[0]}),(0,I.jsx)(S,{children:(0,I.jsx)(t.N_,{to:"/projects/".concat(e.id),children:(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(P,{img:null===e||void 0===e?void 0:e.thumbnail,className:"image",children:(0,I.jsx)(O,{children:(0,I.jsx)(R,{sub:!0,bold:!0,upper:!0,children:e.tag})})}),(0,I.jsxs)(N,{children:[(0,I.jsxs)(z,{children:[(0,I.jsx)(W,{children:(0,I.jsx)(d,{})}),(0,I.jsx)(R,{sub:!0,children:e.location})]}),(0,I.jsx)(F,{children:(0,I.jsx)(R,{className:"title",heading:!0,children:e.title})}),(0,I.jsx)(R,{sub:!0,children:e.info})]}),(0,I.jsxs)(T,{className:"viewlink",children:[(0,I.jsx)(R,{bold:!0,children:"View"}),(0,I.jsx)(L,{className:"arrow",children:(0,I.jsx)(u,{})})]})]})})})}},7483:(n,e,r)=>{r.d(e,{A:()=>l});r(5043);var i,t=r(7528);const a=r(1779).Ay.div(i||(i=(0,t.A)(["\n    text-align: center;\n    padding: 20px 0 30px;\n\n    font-size: ",";\n    font-weight: bold;\n"])),(n=>{let{theme:{font:e}}=n;return e.big}));var o=r(579);const l=n=>{let{title:e}=n;return(0,o.jsx)(a,{children:e})}},8317:(n,e,r)=>{r.d(e,{w:()=>t});var i=r(1066);const t=[{id:1,title:"Vikhroli Corporate Park Private LTD (Embassy)",tag:"Commercial",location:"247 Park, LBS Marg, Vikroli (West), Mumbai 400083",category:"Commercial",thumbnail:i.E.projects.embassy.embassy3,banner:[{original:i.E.projects.embassy.embassy1},{original:i.E.projects.embassy.embassy2},{original:i.E.projects.embassy.embassy3}],description:"Structural and Civil Repairs, Painting of Utility Building",services:["Structural Repair","Civil Repair","Painting"]},{id:2,title:"Column, Slab strengthening at four season at residential tower project.",tag:"Commercial",location:"Worli, Mumbai ",category:"Commercial",thumbnail:i.E.projects.fourSeasonResidancy.fourSeasonResidancy1,banner:[{original:i.E.projects.fourSeasonResidancy.fourSeasonResidancy1},{original:i.E.projects.fourSeasonResidancy.fourSeasonResidancy2},{original:i.E.projects.fourSeasonResidancy.fourSeasonResidancy3},{original:i.E.projects.fourSeasonResidancy.fourSeasonResidancy4}],description:"Column, Slab strengthening of Four Season residency tower project. strengthening of column by microconcrete jacketing, by using high grade steel plate for additional loading. i.e. additional floor included",services:["Microconcrete Jacketing","High grade steel plating"]},{id:3,title:"Structural strengthening of Vikas Industries And Chemical PVT LTD",tag:"Industrial",location:"MIDC Tarapur Industrial Area",category:"Industrial",thumbnail:i.E.projects.tarapur.tarapur1,banner:[{original:i.E.projects.tarapur.tarapur1},{original:i.E.projects.tarapur.tarapur2},{original:i.E.projects.tarapur.tarapur3}],description:"strengthening of Blast Upgrade structure using epoxy grouting, P.M.M, Microconcrete & fibre wrapping.",services:["Epoxy Grouting","P.M.M","Microconcrete","Fibre Wrapping"]},{id:4,title:"Structural strengthening of columns at Millenium Star",tag:"Commercial",location:"Pune",category:"Commercial",thumbnail:i.E.projects.milleniumMall.milleniumMall3,banner:[{original:i.E.projects.milleniumMall.milleniumMall1},{original:i.E.projects.milleniumMall.milleniumMall2},{original:i.E.projects.milleniumMall.milleniumMall3},{original:i.E.projects.milleniumMall.milleniumMall4}],description:"strengthening columns by using fibre wrapping and Laminates",services:["Fibre Wrapping","Laminates"]},{id:5,title:"Structural Repair at Raghav C.H.S.",tag:"Residential",location:"Malad (East)",category:"Commercial",thumbnail:i.E.projects.raghavChs.raghavChs2,banner:[{original:i.E.projects.raghavChs.raghavChs1},{original:i.E.projects.raghavChs.raghavChs2},{original:i.E.projects.raghavChs.raghavChs3}],description:"Structural repairing of R.C.C. members, plastering, waterproofing and painting.",services:["Structural repair","Plastering","Waterproofing","Painting"]},{id:6,title:"Column strengthening works of Tower D, E, J, K Raymond Project",tag:"Commercial",location:"Thane (West)",category:"Commercial",thumbnail:i.E.projects.raymond.raymond1,banner:[{original:i.E.projects.raymond.raymond1},{original:i.E.projects.raymond.raymond2},{original:i.E.projects.raymond.raymond3}],description:"strengthening column by Fibre Wrapping, Micro Jacketing and high strength steel plate for additional floor included .",services:["Fibre Wrapping","Micro Jacketing","High grade steel plate"]},{id:7,title:"Structural Repair at Siyaram Mill LTD.",tag:"Commercial",location:"Kalher",category:"Commercial",thumbnail:i.E.projects.siyaramMill.siyaramMill1,banner:[{original:i.E.projects.siyaramMill.siyaramMill1},{original:i.E.projects.siyaramMill.siyaramMill2},{original:i.E.projects.siyaramMill.siyaramMill3},{original:i.E.projects.siyaramMill.siyaramMill4}],description:"Structural Repair work using P.M.M, Plastering, Painting.",services:["P.M.M","Plastering","Painting"]},{id:8,title:"Providing and Carrying out beam strengthening work by fibre wrapping and laminate at Metro Mall ",tag:"Commercial",location:"Kalyan (East)",category:"Commercial",thumbnail:i.E.projects.metroMall.metroMall1,banner:[{original:i.E.projects.metroMall.metroMall1},{original:i.E.projects.metroMall.metroMall2},{original:i.E.projects.metroMall.metroMall3},{original:i.E.projects.metroMall.metroMall4}],description:"Beam strengthening using carbon Fibre, steel plating & carbon laminate.",services:["Carbon Laminate","Carbon Fibre","Steel Plating"]}]},8719:(n,e,r)=>{r.d(e,{A:()=>g});r(5043),r(1066);var i,t,a,o=r(7528),l=r(1779);const s=l.Ay.div(i||(i=(0,o.A)(["\n  background-color: ",";\n  padding: 15px 20px;\n\n  border-radius: 10px;\n\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\n\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  /* max-width: 150px; */\n  min-width: 140px;\n\n  &:hover {\n    background-color: #fff;\n\n    cursor: pointer;\n    transition: all 0.1s ease-in-out;\n  }\n"])),(n=>{let{theme:{colors:e}}=n;return e.main.yellow})),c=(l.Ay.img(t||(t=(0,o.A)(["\n  width: 50px;\n  height: 50px;\n  object-fit: contain;\n\n  margin: 10px 0;\n"]))),l.Ay.p(a||(a=(0,o.A)(["\n  margin-bottom: 0;\n  text-align: center;\n\n  font-weight: 600;\n\n  white-space: nowrap;\n  text-overflow: ellipsis;\n"]))));var d=r(579);const g=n=>{let{title:e}=n;return(0,d.jsx)(s,{children:(0,d.jsx)(c,{title:e,children:e})})}}}]);
//# sourceMappingURL=305.cbec9728.chunk.js.map