"use strict";(self.webpackChunkcapastrength=self.webpackChunkcapastrength||[]).push([[62],{216:(e,n,i)=>{i.d(n,{A:()=>o});i(5043);var t,r=i(7528);const a=i(1779).Ay.section(t||(t=(0,r.A)(["\n  display: ",";\n  align-items: flex-start;\n"])),(e=>{let{flex:n}=e;return n?" flex":"block"}));var s=i(579);const o=e=>{let{children:n,flex:i}=e;return(0,s.jsx)(a,{className:"py-5",flex:i,children:n})}},7483:(e,n,i)=>{i.d(n,{A:()=>o});i(5043);var t,r=i(7528);const a=i(1779).Ay.div(t||(t=(0,r.A)(["\n    text-align: center;\n    padding: 20px 0 30px;\n\n    font-size: ",";\n    font-weight: bold;\n"])),(e=>{let{theme:{font:n}}=e;return n.big}));var s=i(579);const o=e=>{let{title:n}=e;return(0,s.jsx)(a,{children:n})}},8968:(e,n,i)=>{i.r(n),i.d(n,{default:()=>L});var t=i(5043),r=i(216),a=i(7483),s=i(2488);i(4596);const o=[{name:"Mr.Swapnil D. Patil",words:"Mr. Swapnil D Patil has pursued his graduation in Civil engineering from Shivaji University. He is having 15 years of experience in High-rise construction as well as retrofitting, structural strengthening."},{name:"Mr. Sangram S.Kathole",words:"Mr. Sangram S Kathole graduated from Mumbai University is been with the firm for past 7 years. His hardworking nature and striving to accomplish the given work in time, is what very valued and inspires the firm.He is capable of catering the various Detailing requirements for industries. His expertise in billing of work on construction site."},{name:"Mr. S. S. Patil (Our Associate)",words:"Mr. S. S. Patil holds a M.E. (structure), from Sardar Patel college of Engineering, B.E. (civil) in Datta Megha College of Engineering from Mumbai. He also is an authorized Charted Engineer on board Institution of Engineers . He has in-depth knowledge of RCC Design, FRP Design, Civil construction, which includes Retrofitting, Structural Strengthening, Environmental coatings and rehabilitation of old/damaged structures."},{name:"Mr Chetan Mulaye",words:"Mr. Chetan Mulaye has pursued his graduation in Civil Engineering from Mumbai University. He has more than 15 years of experience in field of Fit-out, High Rise Construction, Restoration, Rehabilitation projects across India"}];var l,d,c,h,u,g,m=i(7528),p=i(1779);const f=p.Ay.div(l||(l=(0,m.A)(["\n  padding: 40px 30px;\n  background-color: #fff;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);\n\n  text-align: center;\n\n  border-radius: 20px;\n\n  margin: 0 10px;\n\n  flex: 1;\n  height: 100%;\n\n  &:hover {\n    background-color: ",";\n    color: #fff;\n  }\n\n  transition: all 0.2s ease-in-out;\n"])),(e=>{let{theme:{colors:n}}=e;return n.main.blue})),x=p.Ay.div(d||(d=(0,m.A)(["\n  display: flex;\n  flex-direction: column;\n"]))),v=p.Ay.h4(c||(c=(0,m.A)(["\n  font-weight: 600;\n"]))),b=p.Ay.p(h||(h=(0,m.A)(["\n  flex: 1;\n"]))),y=p.Ay.div(u||(u=(0,m.A)([""]))),j=p.Ay.div(g||(g=(0,m.A)(["\n  .react-multi-carousel-track {\n    /* gap: 20px; */\n  }\n"])));var w=i(579);const A=e=>{let{data:n}=e;return(0,w.jsx)(f,{children:(0,w.jsxs)(x,{children:[(0,w.jsx)(v,{children:n.name}),(0,w.jsx)(b,{children:n.words})]})})},S=()=>(0,w.jsxs)(y,{children:[(0,w.jsx)(a.A,{title:"Our Team"}),(0,w.jsx)(j,{children:(0,w.jsx)(s.default,{responsive:{superLargeDesktop:{breakpoint:{max:4e3,min:3e3},items:3,slidesToSlide:3},desktop:{breakpoint:{max:3e3,min:1024},items:3,slidesToSlide:3},tablet:{breakpoint:{max:1024,min:664},items:2,slidesToSlide:2},mobile:{breakpoint:{max:664,min:0},items:1,slidesToSlide:1}},children:o.map(((e,n)=>(0,w.jsx)(A,{data:e},n)))})})]});var C,k,M;const R=p.Ay.div(C||(C=(0,m.A)(["\n  /* text-align: center; */\n  flex: 1;\n\n  min-width: 50%;\n\n  height: 100%;\n\n"]))),H=(p.Ay.h2(k||(k=(0,m.A)([""]))),p.Ay.p(M||(M=(0,m.A)([""])))),P=e=>{let{title:n,text:i}=e;return(0,w.jsxs)(R,{className:"px-md-5",children:[n?(0,w.jsx)(a.A,{title:n}):null,(0,w.jsx)(H,{children:i})]})},E=["Low Concrete Strength","Additional Loading","Missing / Less Reinforcement Provided ","Vibration Control","Change in Code"," Blast / Seismic Upgrade","Fire Damaged Structure","Self-Healing Coatings, Anti-corrosive & Anti-carbonation Coatings","Acidic removal from concrete surface","Specialized Grouting","Seismic Upgrade","Waterproofing","Structural Steel And Fabrication","corrosion Repair"],T=["Retrofitting / Structural Strengthening work of Cement and Chemical Plants, Silo &  Chimney, Pharmaceutical companies, Jetties, Dams and Bridges.","Specialized Fiber wrapping systems","Specialized Waterproofing Works","Specialized Environmental Coatings","Repairs/Rehabilitation of Residential Buildings and Heritage Structures","Industrial Buildings - Factories, Plants and Warehouses","Commercial Buildings - Commercial Complexes, Retails and Multiplexes","Public Buildings - Health"];var q,B,N,D;const z=p.Ay.div(q||(q=(0,m.A)([""]))),F=p.Ay.div(B||(B=(0,m.A)(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n"]))),W=p.Ay.div(N||(N=(0,m.A)(["\n  background-color: #ffffff;\n\n  display: flex;\n  flex-direction: column;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);\n\n  min-height: 250px;\n  max-height: ",";\n\n  margin: 0 10px;\n\n  overflow: hidden;\n\n  padding: 20px;\n\n  font-size: ",";\n  font-weight: 700;\n\n  text-align: left;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ",";\n    color: #ffffff;\n\n    .paragraph-holder {\n      &::before {\n        top: 0;\n        left: 0;\n      }\n    }\n  }\n\n  position: relative;\n\n  transition: all 0.3s ease-in-out;\n"])),(e=>{let{hovered:n}=e;return n?"1000px":"465px"}),(e=>{let{theme:{font:n}}=e;return n.title}),(e=>{let{theme:{colors:n}}=e;return n.main.blue})),O=p.Ay.div(D||(D=(0,m.A)(['\n  height: 100%;\n\n  overflow-y: auto;\n\n  &::before {\n    content: "";\n    background-color: ',";\n    width: 50px;\n    height: 50px;\n    position: absolute;\n    top: -50px;\n    left: -50px;\n    transition: all 0.3s ease-in-out;\n    border-bottom-right-radius: 10px;\n  }\n\n  & > div {\n    height: 100%;\n    overflow-y: auto;\n  }\n"])),(e=>{let{theme:{colors:n}}=e;return n.main.yellow})),I=()=>{const[e,n]=(0,t.useState)(!1);return(0,w.jsxs)(z,{children:[(0,w.jsx)(r.A,{children:(0,w.jsx)(S,{})}),(0,w.jsxs)(F,{children:[(0,w.jsxs)(W,{children:[(0,w.jsx)(a.A,{title:"Vision"}),(0,w.jsx)(O,{className:"paragraph-holder",children:(0,w.jsx)(P,{text:"To be a leader in providing quality civil engineering solutions by integrating state of the art  techniques by using eficient human resources by specialized training and quality material. To be a leader in providing quality civil engineering solutions by integrating state of the art  techniques by using eficient human resources by specialized training and quality material."})})]}),(0,w.jsxs)(W,{onMouseEnter:()=>n(!0),onMouseLeave:()=>n(!1),hovered:e,children:[(0,w.jsx)(a.A,{title:"About Us"}),(0,w.jsx)(O,{className:"paragraph-holder",children:(0,w.jsx)(P,{text:"CapaStrength is one of India's leading providers of Retrotting/Structural Strengthening  services for structures. The company delivers engineering solutions with a focus on  technical quality and eficiency while ensuring on-time completion of projects. We undertake a variety of projects for a wide range of cliental from small residential  societies to large commercial projects. It includes projects that come under diverse  Categories such as high-rise Residential Buildings, Commercial Buildings, Cement  Chemical Plants, Bridges, Power Plants, Jetties, Five Star Hotels, Temples, Heritage  structures etc. We have been focused on Rehabilitation of damaged structures i.e.  retroctting and structural strengthening works. Our unique and yexible project  management systems ensure that a positive outcome is achieved regardless of size or  nature of the project.  Our philosophy of 'No Compromise' has come a long way implanting our strong  commitment to highest standards of excellence and ethics. It motivates innovation  and people development, which in turn lead to superior quality and services. It has  also resulted in rehabilitating projects at par with top standards and maximum customer satisfaction.Our strong growth has been as a result of consistently delivering quality works both on  time, on budget and high level of safety. Over the years, we have developed and implemented anintegrated management  system that ensures our work, health and safety, quality and environmental  obligations which are not only met but also continue to be monitored and improved  upon."})})]}),(0,w.jsxs)(W,{children:[(0,w.jsx)(a.A,{title:"Founder Message"}),(0,w.jsx)(O,{className:"paragraph-holder",children:(0,w.jsx)(P,{text:"After providing service more than 10 years as \u201cRESCON\u201d now we reformed as a   \u201cCAPASTRENGTH\u201d. To undertake Retrofitting, Structural Strengthening of civil structures. Since then, numerous projects have been successfully undertaken and completed. We shall contribute towards creating sustainable solutions to Better, Safer and Healthier life for people. . . We constantly focus on the development of all our employees through training to make them capable enough to deliver their best while converti"})})]}),(0,w.jsxs)(W,{children:[(0,w.jsx)(a.A,{title:"Managemnet"}),(0,w.jsx)(O,{className:"paragraph-holder",children:(0,w.jsx)(P,{text:"We have in-depth knowledge of Structural Design, FRP Design, Civil construction, which includes Retrofitting, Structural Strengthening, Environmental coatings and rehabilitation of old/damaged structures waterproofing. This has allowed to lead Capastrength from the front. Capastrength has undertaken several successful projects for eminent entities such as Johnson and Johnson, Bayer, provinces Land Hotels, MRF, Embassy Siyaram, 24x7 Services. Capastrength continues to grow and flourish under gui"})})]}),(0,w.jsxs)(W,{children:[(0,w.jsx)(a.A,{title:"Service Provider"}),(0,w.jsx)(O,{className:"paragraph-holder",children:(0,w.jsx)("div",{style:{padding:"0 30px"},children:(0,w.jsx)("ul",{children:null===E||void 0===E?void 0:E.map(((e,n)=>(0,w.jsx)("li",{children:e},n)))})})})]}),(0,w.jsxs)(W,{children:[(0,w.jsx)(a.A,{title:"Our Service"}),(0,w.jsx)(O,{className:"paragraph-holder",children:(0,w.jsx)("div",{style:{padding:"0 30px"},children:(0,w.jsx)("ul",{children:null===T||void 0===T?void 0:T.map(((e,n)=>(0,w.jsx)("li",{children:e},n)))})})})]})]})]})},L=()=>(0,w.jsx)("div",{className:"container",children:(0,w.jsx)(I,{})})}}]);
//# sourceMappingURL=62.d66406c6.chunk.js.map