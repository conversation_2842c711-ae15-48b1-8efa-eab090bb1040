"use strict";(self.webpackChunkcapastrength=self.webpackChunkcapastrength||[]).push([[961],{216:(e,n,r)=>{r.d(n,{A:()=>s});r(5043);var l,t=r(7528);const o=r(1779).Ay.section(l||(l=(0,t.A)(["\n  display: ",";\n  align-items: flex-start;\n"])),(e=>{let{flex:n}=e;return n?" flex":"block"}));var i=r(579);const s=e=>{let{children:n,flex:r}=e;return(0,i.jsx)(o,{className:"py-5",flex:r,children:n})}},5961:(e,n,r)=>{r.r(n),r.d(n,{default:()=>B});var l=r(5043),t=r(1066),o=r(2555);const i={_origin:"https://api.emailjs.com"},s=(e,n,r)=>{if(!e)throw"The public key is required. Visit https://dashboard.emailjs.com/admin/account";if(!n)throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!r)throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates";return!0};class a{constructor(e){this.status=e?e.status:0,this.text=e?e.responseText:"Network Error"}}const d=function(e,n){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new Promise(((l,t)=>{const o=new XMLHttpRequest;o.addEventListener("load",(e=>{let{target:n}=e;const r=new a(n);200===r.status||"OK"===r.text?l(r):t(r)})),o.addEventListener("error",(e=>{let{target:n}=e;t(new a(n))})),o.open("POST",i._origin+e,!0),Object.keys(r).forEach((e=>{o.setRequestHeader(e,r[e])})),o.send(n)}))},c=(e,n,r,l)=>{const t=l||i._userID,o=(e=>{let n;if(n="string"===typeof e?document.querySelector(e):e,!n||"FORM"!==n.nodeName)throw"The 3rd parameter is expected to be the HTML form element or the style selector of form";return n})(r);s(t,e,n);const a=new FormData(o);return a.append("lib_version","3.12.1"),a.append("service_id",e),a.append("template_id",n),a.append("user_id",t),d("/api/v1.0/email/send-form",a)};var u,m,h,p,x,v,g,y,f=r(7528),b=r(1779);const j=b.Ay.div(u||(u=(0,f.A)([""]))),A=b.Ay.form(m||(m=(0,f.A)([""]))),w=b.Ay.div(h||(h=(0,f.A)(["\n  margin: 20px 0;\n"]))),k=b.Ay.label(p||(p=(0,f.A)(["\n  display: block;\n  margin-left: 10px;\n  margin-bottom: 10px;\n\n  color: ",";\n"])),(e=>{let{theme:{colors:n}}=e;return n.grey})),N=b.Ay.input(x||(x=(0,f.A)(["\n  border: 2px solid\n    ",";\n  padding: 10px 16px;\n  border-radius: 100px;\n\n  width: 100%;\n\n  &:focus {\n    outline: none;\n    border: 2px solid ",";\n  }\n"])),(e=>{let{error:n,theme:{colors:r}}=e;return n?r.main.red:r.grey}),(e=>{let{theme:{colors:n}}=e;return n.main.blue})),S=b.Ay.button(v||(v=(0,f.A)(["\n  background-color: ",";\n  border: none;\n  padding: 10px;\n  width: 100%;\n  font-weight: 600;\n\n  border-radius: 100px;\n\n  box-shadow: 0 0 20px 2px rgba(0, 0, 0, 0.1);\n\n  margin-top: 20px;\n\n  color: ",";\n\n  &:hover {\n    background-color: ",";\n    color: ",";\n  }\n\n  transition: all 0.3s ease-in-out;\n"])),(e=>{let{success:n,theme:{colors:r}}=e;return n?"#a2ff54":r.main.yellow}),(e=>{let{theme:{colors:n}}=e;return n.main.blue}),(e=>{let{theme:{colors:n}}=e;return n.main.blue}),(e=>{let{theme:{colors:n}}=e;return n.white})),q=b.Ay.p(g||(g=(0,f.A)(["\n  color: red;\n"]))),C=b.Ay.p(y||(y=(0,f.A)(["\n  color: green;\n  margin-top: 10px;\n  text-align: center;\n"])));var T=r(579);const _=()=>{let e=(0,l.useRef)([]);const n=(0,l.useRef)(),[r,t]=(0,l.useState)(null),[i,s]=(0,l.useState)(!1),[a,d]=(0,l.useState)({name:"",company:"",email:"",mobile:""}),{name:u,company:m,email:h,mobile:p}=a,[x,v]=(0,l.useState)(null),g=e.current;(0,l.useEffect)((()=>{v(e.current)}),[g]);const y=e=>{const n=e.target.value,r=e.target.name;d((0,o.A)((0,o.A)({},a),{},{[r]:n})),v((0,o.A)((0,o.A)({},x),{},{[r]:""}))};return(0,T.jsxs)(j,{children:[(0,T.jsx)("h2",{style:{color:"Black",font:"-moz-initial",textAlign:"center"},children:"Who are already onboard"}),(0,T.jsxs)(A,{ref:n,onSubmit:e=>(async e=>{e.preventDefault(),s(!0),console.log({inputValue:a}),console.log({form:n.current}),c("service_ifbucgn","template_gryehv4",n.current,"ye7UvAtvh7-vkICwy").then((e=>{console.log(e),s(!1)}),(e=>{console.log(e.text),s(!1)}))})(e),children:[(0,T.jsxs)(w,{children:[(0,T.jsx)(k,{children:"Name"}),(0,T.jsx)(N,{name:"name",type:"text",value:u,onChange:e=>y(e),error:null===x||void 0===x?void 0:x.name,required:!0}),null!==x&&void 0!==x&&x.name?(0,T.jsx)(q,{children:null===x||void 0===x?void 0:x.name}):null]}),(0,T.jsxs)(w,{children:[(0,T.jsx)(k,{children:"Company"}),(0,T.jsx)(N,{name:"company",type:"text",value:m,onChange:e=>y(e),error:null===x||void 0===x?void 0:x.company,required:!0}),null!==x&&void 0!==x&&x.company?(0,T.jsx)(q,{children:null===x||void 0===x?void 0:x.company}):null]}),(0,T.jsxs)(w,{children:[(0,T.jsx)(k,{children:"Email"}),(0,T.jsx)(N,{name:"email",type:"email",value:h,onChange:e=>y(e),error:null===x||void 0===x?void 0:x.email,required:!0}),null!==x&&void 0!==x&&x.email?(0,T.jsx)(q,{children:null===x||void 0===x?void 0:x.email}):null]}),(0,T.jsxs)(w,{children:[(0,T.jsx)(k,{children:"Contact Details"}),(0,T.jsx)(N,{name:"mobile",type:"tel",value:p,onChange:e=>y(e),error:null===x||void 0===x?void 0:x.mobile,required:!0}),null!==x&&void 0!==x&&x.mobile?(0,T.jsx)(q,{children:null===x||void 0===x?void 0:x.mobile}):null]}),(0,T.jsx)(S,{success:null===r||void 0===r?void 0:r.delivered,type:"submit",disabled:null===r||void 0===r?void 0:r.delivered,children:!i||null!==r&&void 0!==r&&r.delivered?!i&&null!==r&&void 0!==r&&r.delivered?"Sent Successfully":"Send Message":"Sending...."})]}),(0,T.jsx)(C,{children:null===r||void 0===r?void 0:r.msg})]})};var E,D,O,L,R;const F=b.Ay.div(E||(E=(0,f.A)(["\n    display: flex;\n    align-items: center;\n"]))),H=b.Ay.div(D||(D=(0,f.A)(["\n    \n"]))),I=b.Ay.img(O||(O=(0,f.A)(["\n    width: 100%;\n    height: 100%;\n\n    object-fit: contain;\n"]))),M=b.Ay.div(L||(L=(0,f.A)(["\n    width: 100%;\n"]))),P=b.Ay.div(R||(R=(0,f.A)(["\n    padding: 2rem 5rem;\n    background-color: #f8f9fa;\n    text-align: center;\n"]))),V=()=>(0,T.jsxs)(T.Fragment,{children:[(0,T.jsxs)(F,{className:"flex-wrap flex-md-nowrap",children:[(0,T.jsx)(H,{className:"col col-12 col-md p-5 ",children:(0,T.jsx)(I,{src:t.E.client})}),(0,T.jsx)(M,{className:"col col-12 col-md px-5 py-1 py-md-5",children:(0,T.jsx)(_,{})})]}),(0,T.jsxs)(P,{className:"col-12",children:[(0,T.jsx)("h1",{children:(0,T.jsx)("b",{children:"Office Location"})}),(0,T.jsx)("p",{children:"Office No. 201, 2nd Floor, Tarabaug CHS, Prashant Nagar, Near Naupada Police Station, Thane - 400602"})]})]});var z=r(216);const B=()=>(0,T.jsx)("div",{className:"container",children:(0,T.jsx)(z.A,{children:(0,T.jsx)(V,{})})})}}]);
//# sourceMappingURL=961.16fe04ff.chunk.js.map