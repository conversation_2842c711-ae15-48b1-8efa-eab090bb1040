"use strict";(self.webpackChunkcapastrength=self.webpackChunkcapastrength||[]).push([[286],{241:(t,e,i)=>{i.r(e),i.d(e,{default:()=>Ot});var n,a,r,l=i(5043),o=i(2488),s=(i(4596),i(7483)),c=i(7528),d=i(1779);const h=d.Ay.div(n||(n=(0,c.A)(["\n  margin: 50px 0;\n\n  .react-multiple-carousel__arrow.react-multiple-carousel__arrow--left {\n    transform: translateX(-90%);\n  }\n  .react-multiple-carousel__arrow.react-multiple-carousel__arrow--right {\n    transform: translateX(90%);\n  }\n"]))),m=d.Ay.div(a||(a=(0,c.A)(["\n  /* display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  } */\n\n  margin: 50px 0;\n"]))),u=d.Ay.div(r||(r=(0,c.A)(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n\n  margin: 50px 0;\n"])));var A,p;const g=d.Ay.div(A||(A=(0,c.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  background-color: #fefefe;\n\n  padding: 20px;\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  max-height: 160px;\n  min-height: 160px;\n\n  margin: 0 10px;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: #ffffff;\n  }\n\n  cursor: pointer;\n  transition: all 0.2 ease-in-out;\n"]))),v=d.Ay.img(p||(p=(0,c.A)(["\n  width: 100%;\n  height: 100%;\n  object-fit: scale-down;\n"])));var y=i(579);const b=t=>{let{data:e}=t;return(0,y.jsx)(g,{children:(0,y.jsx)(v,{src:e.img})})};var S=i(5173),f=i.n(S);function O(){return O=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},O.apply(this,arguments)}function w(t,e){if(null==t)return{};var i,n,a=function(t,e){if(null==t)return{};var i,n,a={},r=Object.keys(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||(a[i]=t[i]);return a}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(a[i]=t[i])}return a}var C=(0,l.forwardRef)((function(t,e){var i=t.color,n=void 0===i?"currentColor":i,a=t.size,r=void 0===a?24:a,o=w(t,["color","size"]);return l.createElement("svg",O({ref:e,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),l.createElement("circle",{cx:"12",cy:"12",r:"10"}),l.createElement("polyline",{points:"12 16 16 12 12 8"}),l.createElement("line",{x1:"8",y1:"12",x2:"16",y2:"12"}))}));C.propTypes={color:f().string,size:f().oneOfType([f().string,f().number])},C.displayName="ArrowRightCircle";const M=C;var x,j,I,L,H,P;const B=d.Ay.div(x||(x=(0,c.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n\n  background-color: #fefefe;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  min-height: 200px;\n  max-height: 420px;\n\n  margin: 0 10px;\n\n  font-size: ",";\n  font-weight: 700;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ",";\n    transition: all 0.3 ease-in-out;\n\n\n    .title {\n      background-color: ",";\n    }\n  }\n\n  cursor: pointer;\n  transition: all 0.3 ease-in-out;\n"])),(t=>{let{theme:{font:e}}=t;return e.title}),(t=>{let{theme:{colors:e}}=t;return"".concat(e.grey,"01")}),(t=>{let{theme:{colors:e}}=t;return e.white})),R=d.Ay.div(j||(j=(0,c.A)(["\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  font-size: 25px;\n\n  padding: 20px 60px;\n"]))),N=d.Ay.div(I||(I=(0,c.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  margin-right: 10px;\n\n  svg {\n    width: 30px;\n    height: 30px;\n  }\n"]))),D=d.Ay.span(L||(L=(0,c.A)([""]))),E=d.Ay.ul(H||(H=(0,c.A)(["\n  padding: 20px 60px;\n  width: 100%;\n\n  height: 100%;\n  overflow-y: auto;\n"]))),G=d.Ay.li(P||(P=(0,c.A)(["\n  padding: 10px;\n\n  display: flex;\n  align-items: center;\n\n  svg {\n    margin-right: 10px;\n  }\n\n  &:hover {\n    background-color: ",";\n    color: #ffffff;\n  }\n"])),(t=>{let{theme:{colors:e}}=t;return e.main.blue})),k=t=>{var e;let{client:i}=t;return console.log({client:i}),(0,y.jsxs)(B,{children:[(0,y.jsxs)(R,{className:"title",children:[(0,y.jsx)(N,{children:null===i||void 0===i?void 0:i.icon}),(0,y.jsx)(D,{children:null===i||void 0===i?void 0:i.heading})]}),(0,y.jsx)(E,{children:null===i||void 0===i||null===(e=i.data)||void 0===e?void 0:e.map(((t,e)=>(0,y.jsxs)(G,{className:"list-item",children:[(0,y.jsx)(M,{}),(0,y.jsx)(D,{children:null===t||void 0===t?void 0:t.title})]},e)))})]})},Z=i.p+"static/media/4season.74f41b931d45f160f996.png",T=i.p+"static/media/capacitelogo.e157b44ccf885aab5ff3.png",U=i.p+"static/media/damodar.5d6ae80e5960287fa267.jpeg",Y=i.p+"static/media/jnj.313ab0d69680e6db2698.jpeg",W=i.p+"static/media/kalpataru.95289a70af905bac9fc0.jpeg",J=i.p+"static/media/mahaVitaran.d0e628681a85f87338a7.png",Q=i.p+"static/media/raviraj.64c389522a9edfd7cbe1.png",z=i.p+"static/media/serum.556b5fa87905ebf94d47.png",K=i.p+"static/media/technova.016675946f307edf40bf.png",V=i.p+"static/media/trustlogo.7caf5dfe98029ce7270c.jpeg";function F(){return F=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},F.apply(this,arguments)}function X(t,e){if(null==t)return{};var i,n,a=function(t,e){if(null==t)return{};var i,n,a={},r=Object.keys(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||(a[i]=t[i]);return a}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(a[i]=t[i])}return a}var q=(0,l.forwardRef)((function(t,e){var i=t.color,n=void 0===i?"currentColor":i,a=t.size,r=void 0===a?24:a,o=X(t,["color","size"]);return l.createElement("svg",F({ref:e,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),l.createElement("circle",{cx:"12",cy:"12",r:"3"}),l.createElement("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"}))}));q.propTypes={color:f().string,size:f().oneOfType([f().string,f().number])},q.displayName="Settings";const _=q;function $(){return $=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},$.apply(this,arguments)}function tt(t,e){if(null==t)return{};var i,n,a=function(t,e){if(null==t)return{};var i,n,a={},r=Object.keys(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||(a[i]=t[i]);return a}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(a[i]=t[i])}return a}var et=(0,l.forwardRef)((function(t,e){var i=t.color,n=void 0===i?"currentColor":i,a=t.size,r=void 0===a?24:a,o=tt(t,["color","size"]);return l.createElement("svg",$({ref:e,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),l.createElement("path",{d:"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"}),l.createElement("polyline",{points:"9 22 9 12 15 12 15 22"}))}));et.propTypes={color:f().string,size:f().oneOfType([f().string,f().number])},et.displayName="Home";const it=et;function nt(){return nt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},nt.apply(this,arguments)}function at(t,e){if(null==t)return{};var i,n,a=function(t,e){if(null==t)return{};var i,n,a={},r=Object.keys(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||(a[i]=t[i]);return a}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(a[i]=t[i])}return a}var rt=(0,l.forwardRef)((function(t,e){var i=t.color,n=void 0===i?"currentColor":i,a=t.size,r=void 0===a?24:a,o=at(t,["color","size"]);return l.createElement("svg",nt({ref:e,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),l.createElement("path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}),l.createElement("path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"}))}));rt.propTypes={color:f().string,size:f().oneOfType([f().string,f().number])},rt.displayName="BookOpen";const lt=rt;function ot(){return ot=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},ot.apply(this,arguments)}function st(t,e){if(null==t)return{};var i,n,a=function(t,e){if(null==t)return{};var i,n,a={},r=Object.keys(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||(a[i]=t[i]);return a}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(a[i]=t[i])}return a}var ct=(0,l.forwardRef)((function(t,e){var i=t.color,n=void 0===i?"currentColor":i,a=t.size,r=void 0===a?24:a,o=st(t,["color","size"]);return l.createElement("svg",ot({ref:e,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),l.createElement("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),l.createElement("circle",{cx:"9",cy:"7",r:"4"}),l.createElement("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),l.createElement("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"}))}));ct.propTypes={color:f().string,size:f().oneOfType([f().string,f().number])},ct.displayName="Users";const dt=ct;function ht(){return ht=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},ht.apply(this,arguments)}function mt(t,e){if(null==t)return{};var i,n,a=function(t,e){if(null==t)return{};var i,n,a={},r=Object.keys(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||(a[i]=t[i]);return a}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(a[i]=t[i])}return a}var ut=(0,l.forwardRef)((function(t,e){var i=t.color,n=void 0===i?"currentColor":i,a=t.size,r=void 0===a?24:a,o=mt(t,["color","size"]);return l.createElement("svg",ht({ref:e,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),l.createElement("path",{d:"M18 8h1a4 4 0 0 1 0 8h-1"}),l.createElement("path",{d:"M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"}),l.createElement("line",{x1:"6",y1:"1",x2:"6",y2:"4"}),l.createElement("line",{x1:"10",y1:"1",x2:"10",y2:"4"}),l.createElement("line",{x1:"14",y1:"1",x2:"14",y2:"4"}))}));ut.propTypes={color:f().string,size:f().oneOfType([f().string,f().number])},ut.displayName="Coffee";const At=ut;function pt(){return pt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},pt.apply(this,arguments)}function gt(t,e){if(null==t)return{};var i,n,a=function(t,e){if(null==t)return{};var i,n,a={},r=Object.keys(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||(a[i]=t[i]);return a}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(a[i]=t[i])}return a}var vt=(0,l.forwardRef)((function(t,e){var i=t.color,n=void 0===i?"currentColor":i,a=t.size,r=void 0===a?24:a,o=gt(t,["color","size"]);return l.createElement("svg",pt({ref:e,xmlns:"http://www.w3.org/2000/svg",width:r,height:r,viewBox:"0 0 24 24",fill:"none",stroke:n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o),l.createElement("circle",{cx:"9",cy:"21",r:"1"}),l.createElement("circle",{cx:"20",cy:"21",r:"1"}),l.createElement("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"}))}));vt.propTypes={color:f().string,size:f().oneOfType([f().string,f().number])},vt.displayName="ShoppingCart";const yt=vt,bt=[{img:Z},{img:T},{img:U},{img:"data:image/png;base64,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"},{img:"data:image/png;base64,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"},{img:Y},{img:W},{img:J},{img:Q},{img:z},{img:K},{img:V}],St=[{id:1,heading:"Industrial / Commercial",icon:(0,y.jsx)(_,{}),data:[{title:"Bayer Vapi Pvt. Ltd."},{title:"Embassy 24x7"},{title:"Siyaram Silk Mill, Ltd."},{title:"Damodar Silk Mills Pvt. Ltd."},{title:"Damodar Industries Ltd."},{title:"Platinum Logistics"},{title:"Pantaloon Retails India Ltd."},{title:"Donear Industries Ltd."},{title:"Pal Fashion Pvt. Ltd."},{title:"Sunrise Containers Pvt. Ltd."},{title:"G M Syntex Pvt. Ltd."},{title:"D' D\xe9cor Homes fabrics Pvt. Ltd."},{title:"Rukshmani Synthetics"},{title:"Shree NM Electricals Pvt. Ltd."},{title:"Platinum Fabrics"},{title:"Aarti Drugs Ltd."},{title:"Oxemberg"},{title:"Samosaran"},{title:"Konarak"},{title:"Sushitex"},{title:"Jai Corp Ltd."},{title:"Naari"},{title:"ASM Industries \u2013 India Pvt. Ltd."},{title:"United Santosh Enterprises"},{title:"Balkrishna Paper Mill"},{title:"Mandhana"},{title:"Nahar Textile Pvt. Ltd."},{title:"Narain's"},{title:"Raj Rajendra Industries Ltd."},{title:"Unitec Fibres Pvt. Ltd."},{title:"Kriplon Synthetics Pvt. Ltd."},{title:"Madhusudan"},{title:"Sunayaa"},{title:"N G Nextgen"},{title:"Sanaa Syntex Pvt. Ltd."},{title:"KayKay"},{title:"Mukat Tanks & Vessels Ltd."},{title:"Vionod Intelligent Cookware"},{title:"Silomec drilling and foundation equipment"},{title:"G.R. Engineering Pvt. Ltd."},{title:"Modison"},{title:"Vinod Stainless Steel"},{title:"High Volt Electricals Pvt. Ltd."},{title:"Karamtara Engineering Pvt. Ltd."},{title:"Zenith Birla (india) Ltd."},{title:"Loba Chemie"},{title:"Nirbhay Rasayan Pvt. Ltd."},{title:"Suru Chemicals & Pharmaceuticals Pvt. Ltd."},{title:"Mitsui Chemicals Group \u2022MOMCPL"},{title:"Astik"},{title:"Rank organics chemicals Pvt. Ltd."},{title:"SS Astra Formulation Pvt. Ltd."},{title:"Sarex"},{title:"Ganesh Benzoplast Ltd."},{title:"Mohini organics Pvt. Ltd."},{title:"Bhagat Aromatics Ltd."},{title:"Crown"},{title:"SNA"},{title:"Siramaxo"},{title:"Maxwell Life Science Pvt. Ltd."},{title:"Mehta Pharmaceuticals Pvt. Ltd."},{title:"Medley"},{title:"Salasar Plastic"},{title:"Sunrise Containers Ltd. (Sunpet)"},{title:"Champion"},{title:"Shree NM"},{title:"Moonlight Industries"},{title:"Fibro"},{title:"Ramky Group"},{title:"SKG Refractories Ltd"},{title:"Haware"},{title:"Dosti Realty"},{title:"Provenance Land"},{title:"MRF"},{title:"Technova Imagine Systems Pvt. Ltd."},{title:"Capacite"},{title:"ACC Concrete"},{title:"Inox Air Products"},{title:"RNA"},{title:"Kasam Builders"},{title:"Siyaram"},{title:"Raviraj Realty"},{title:"Panexcell Clinical Lab Pvt. Ltd."},{title:"Shree Ahinant"},{title:"Cyrus Poonawala"},{title:"Cyrum Institute"},{title:"Millenium Star"},{title:"Synthetic Pvt. Ltd."},{title:"NOCIL Limited"},{title:"Gold Fingerest Pvt. Ltd."},{title:"Trimity Developers Pvt. Ltd."}]},{id:2,heading:"Residential",icon:(0,y.jsx)(it,{}),data:[{title:"Krishna Green Park"},{title:"Swagat Agro Agency"},{title:"Athar Building"},{title:"Padmavati CHS,"},{title:"Boomerang Chandivali"},{title:"Borla CHS"},{title:"OM Siddharaj CHS ltd."},{title:"Dreamland Building,"},{title:"Charni Road East"},{title:"Garden View"},{title:"Silco House"},{title:"S. K. Academy"},{title:"Shri Shivakrupa CHS"},{title:"Shelar"},{title:"Jai Ganaraj CHS"},{title:"Akash Vihar CHS"},{title:"Parvati CHS"},{title:"Vasant Vihar"},{title:"Rajnigandha CHS"},{title:"Cosmos CHS"},{title:"Balkrishna Quarters, Dombivli"},{title:"Raymond, Ten X Habitat, Thane"},{title:"Gangotri CHS"},{title:"Shree Samarth Kuti Apartment"},{title:"Krishna Kunj CHS"},{title:"Manusmruti CHS"},{title:"Krishna Saraswati CHS"},{title:"Gokuldham Vrundavan CHS"},{title:"Dhanlaxmi Chambers"},{title:"Vijay Nagari Annex"},{title:"Sobat CHS"},{title:"Adarsh Peace Building"},{title:"Kasturi Plaza CHS"},{title:"Al-Jamiah-Al-Islamiyyah Noor Bagh Kausa"},{title:"Vrindavan Dham, D Building CHS"},{title:"Bhavan Mahal"},{title:"Agarwal CHS"},{title:"Kamla Niwas"},{title:"Gangatara CHS"},{title:"Shree Sidhivinayak CHS"},{title:"Om Chambers"},{title:"Kemps Cornor MUMBAI"},{title:"Coral crest CHS"},{title:"Parinita CHS"},{title:"Gyan Kutir CHS"},{title:"Harasiddh Park A3-A4 CHS"},{title:"Highway Shirin CHS"},{title:"Panchawati Apartment"},{title:"Konark Indraprastha CHS"},{title:"Mangalyakushma CHS"},{title:"Siddhivinayak CHS,"},{title:"Har Har Mahadev CHS"},{title:"Bhagwan Walmki CHS"},{title:"Tarang C.H.S."},{title:"Anand Palace CHS"},{title:"Rameshwar CHS"},{title:"Neelkanth Heights"},{title:"Mahavir Heritage"},{title:"Shree Ganesh Krupa Apartment"},{title:"Nand Apartment CHS"},{title:"Oshiwara Link Plaza"},{title:"Royal Shalibhadra Complex No 1. CHS"},{title:"Shyamkutir CHS,"},{title:"Om Namha Shivay CHS,"},{title:"Golden Express"},{title:"Shriniketan CHS"},{title:"Kailas Avenue"},{title:"JS Infratech"},{title:"Saraswati Nagar CHS"},{title:"Vaibhav CHS"},{title:"Revati Akashganga CHS"},{title:"Anubhav Building CHS"},{title:"Rahat Manzil CHS"},{title:"Shree Dharshan CHS"},{title:"Mahavir Dham CHS"},{title:"Sai Ashish Apartment"},{title:"Shweta Apartment"},{title:"Surabhi CHSL"},{title:"400 KV Substation Colony Kharghar"},{title:"Sewa Samiti CHS,"},{title:"Sion Koliwada Mumbai"},{title:"Nilkanth Darshan CHS"},{title:"Brahmand Phase-IV CHSL"},{title:"Sindhi Society"},{title:"Sidharth Nagar Bldg No- 4 CHSL"},{title:"Amar Joyti CHS"},{title:"Dosti Apartment,"},{title:"Navsai Prasad CHSL"},{title:"Chamunda Jewel CHS"},{title:"Shiv Smruti CHSL"},{title:"Wimbledon Park"},{title:"Purohit Building"},{title:"Rajani Gandha Apartment"},{title:"Ananta Apartment"},{title:"Ganesh Krupa CHS"},{title:"Pearl Mansion CHSL"},{title:"Building Marine Lines"},{title:"Mayur Park CHS"},{title:"Shreeniwas CHS"},{title:"Shiram CHS"},{title:"Rashmi Drashtant CHSL"},{title:"Sai Dharshan B CHS"},{title:"Sai Snehal CHS"},{title:"Shivanand Society"},{title:"Shree Arihant Compound Kalher Village"},{title:"Shri Sai Baba CHS"},{title:"Arundhati CHSL"}]},{id:3,heading:"School / College",icon:(0,y.jsx)(lt,{}),data:[{title:"Thakur Engineering College,"},{title:"Somaiya College,"},{title:"Guru Nanak Dev Engineering College,"},{title:"Jhun Jhun wala College,"},{title:"Rafiuddin Fakih boys High School,"}]},{id:4,heading:"Public Trust",icon:(0,y.jsx)(dt,{}),data:[{title:"Maharashtra Seva Sangh"},{title:"Orthodox Church"},{title:"St. Joseph Church"},{title:"St. John's Church"},{title:"Sharma Shikshan Sanstha"},{title:"Maharashtra Jivan Pradhikaran"},{title:"Navabharat Education Socienty"}]},{id:5,heading:"Hotels",icon:(0,y.jsx)(At,{}),data:[{title:"Nariman House, Mumbai"},{title:"Four Season, Mumbai"},{title:"Hotel Ashok, Thane"},{title:"Lokesh Hotel, Pune"},{title:"Ram Mahal, Mumbai"}]},{id:6,heading:"Malls",icon:(0,y.jsx)(yt,{}),data:[{title:"Metro Mall, Kalyan"},{title:"Mega Mall"},{title:"Millenium Mall"}]}],ft=()=>(0,y.jsxs)(h,{children:[(0,y.jsxs)(m,{children:[(0,y.jsx)(s.A,{title:"Clients"}),(0,y.jsx)(o.default,{responsive:{superLargeDesktop:{breakpoint:{max:4e3,min:3e3},items:4,slidesToSlide:4},desktop:{breakpoint:{max:3e3,min:1024},items:4,slidesToSlide:4},tablet:{breakpoint:{max:1024,min:664},items:3,slidesToSlide:3},mobile:{breakpoint:{max:664,min:0},items:1,slidesToSlide:1}},autoPlay:!0,infinite:!0,autoPlaySpeed:6e3,slidesToSlide:1,transitionDuration:3e4,children:bt.map(((t,e)=>(0,y.jsx)(b,{data:t},e)))})]}),(0,y.jsx)(u,{children:St.map(((t,e)=>(0,y.jsx)(k,{client:t},e)))})]}),Ot=()=>(0,y.jsx)("div",{className:"container",children:(0,y.jsx)(ft,{})})},7483:(t,e,i)=>{i.d(e,{A:()=>o});i(5043);var n,a=i(7528);const r=i(1779).Ay.div(n||(n=(0,a.A)(["\n    text-align: center;\n    padding: 20px 0 30px;\n\n    font-size: ",";\n    font-weight: bold;\n"])),(t=>{let{theme:{font:e}}=t;return e.big}));var l=i(579);const o=t=>{let{title:e}=t;return(0,l.jsx)(r,{children:e})}}}]);
//# sourceMappingURL=286.2862d510.chunk.js.map