{"version": 3, "file": "static/js/184.62f5020f.chunk.js", "mappings": "wPAEO,MAAMA,EAAYC,EAAAA,GAAOC,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,6BAItBC,EAAoBJ,EAAAA,GAAOC,IAAGI,IAAAA,GAAAF,EAAAA,EAAAA,GAAA,oDAK9BG,EAAaN,EAAAA,GAAOO,IAAGC,IAAAA,GAAAL,EAAAA,EAAAA,GAAA,uIAWvBM,EAAaT,EAAAA,GAAOU,KAAIC,IAAAA,GAAAR,EAAAA,EAAAA,GAAA,qEACtBS,IAAA,IAAC,QAAEC,EAAO,IAAEC,EAAG,MAAEC,EAAOC,OAAO,KAAEC,IAAQL,EAAA,OACpDC,EAAUI,EAAKC,MAAQH,EAAQE,EAAKF,MAAQD,EAAMG,EAAKH,IAAMG,EAAKE,QACrDC,IAAA,IAAC,KAAEC,GAAMD,EAAA,OAAMC,EAAO,IAAM,OAIhCC,EAActB,EAAAA,GAAOC,IAAGsB,IAAAA,GAAApB,EAAAA,EAAAA,GAAA,kCAIxBqB,EAAiBxB,EAAAA,GAAOC,IAAGwB,IAAAA,GAAAtB,EAAAA,EAAAA,GAAA,iPAuB3BuB,GATc1B,EAAAA,GAAOO,IAAGoB,IAAAA,GAAAxB,EAAAA,EAAAA,GAAA,6FASPH,EAAAA,GAAOC,IAAG2B,IAAAA,GAAAzB,EAAAA,EAAAA,GAAA,oCAI3B0B,EAAoB7B,EAAAA,GAAOC,IAAG6B,IAAAA,GAAA3B,EAAAA,EAAAA,GAAA,6BAI9B4B,EAAqB/B,EAAAA,GAAOC,IAAG+B,IAAAA,GAAA7B,EAAAA,EAAAA,GAAA,iH,aClD5C,MA6DA,EA7DuB8B,KACrB,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,KAETC,EAAOC,EAAAA,EAAYC,QACtBC,GAASC,SAASD,EAAKL,MAAQM,SAASN,KACzC,GAEF,OACEO,EAAAA,EAAAA,MAACC,EAAgB,CAAAC,SAAA,EACfC,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CAACC,GAAG,YAAWH,UAClBF,EAAAA,EAAAA,MAACC,EAAwB,CAAAC,SAAA,EACvBC,EAAAA,EAAAA,KAACF,EAAiB,CAACK,IAAKC,EAAAA,EAAWC,QAAQC,QAC3CN,EAAAA,EAAAA,KAACF,EAAiB,CAACrB,MAAI,EAACR,SAAO,EAAA8B,SAC5BP,EAAKlB,cAIZuB,EAAAA,EAAAA,MAACC,EAAkB,CAAAC,SAAA,EACjBC,EAAAA,EAAAA,KAACF,EAAiB,CAACS,UAAU,OAAO9B,MAAI,EAACP,KAAG,EAAA6B,SAC1CP,EAAKgB,OAEPR,EAAAA,EAAAA,KAACF,EAAiB,CAAC3B,OAAK,EAAA4B,SAAEP,EAAKiB,eAEjCT,EAAAA,EAAAA,KAACF,EAAqB,CAAAC,UACpBC,EAAAA,EAAAA,KAACU,EAAAA,EAAY,CACXC,MAAOnB,EAAKoB,OACZC,eAAgB,OAChBC,cAAe,OACfC,gBAAgB,EAChBC,gBAAgB,OAIpBnB,EAAAA,EAAAA,MAACC,EAAqB,CAAAC,SAAA,EACpBF,EAAAA,EAAAA,MAACC,EAAwB,CAAAC,SAAA,EACvBC,EAAAA,EAAAA,KAACF,EAAiB,CAACrB,MAAI,EAACR,SAAO,EAAA8B,SAAC,gBAGhCC,EAAAA,EAAAA,KAACF,EAAiB,CAAC7B,SAAO,EAAA8B,SAAEP,EAAKiB,eAEnCZ,EAAAA,EAAAA,MAACC,EAAwB,CAAAC,SAAA,EACvBC,EAAAA,EAAAA,KAACF,EAAiB,CAACrB,MAAI,EAACR,SAAO,EAAA8B,SAAC,mBAGhCC,EAAAA,EAAAA,KAACF,EAAiB,CAAC7B,SAAO,EAAA8B,SAAEP,EAAKyB,kBAEnCpB,EAAAA,EAAAA,MAACC,EAAwB,CAAAC,SAAA,EACvBC,EAAAA,EAAAA,KAACF,EAAiB,CAACrB,MAAI,EAACR,SAAO,EAAA8B,SAAC,gBAGhCC,EAAAA,EAAAA,KAACF,EAAyB,CAAAC,SACvBP,EAAK0B,SAASC,KAAI,CAACxB,EAAMyB,KACjBpB,EAAAA,EAAAA,KAACqB,EAAAA,EAAW,CAAW/C,MAAOqB,GAAZyB,iB,MChEhC,MAAMjE,EAAYC,EAAAA,GAAOC,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,cCYnC,EARoB+D,KAEZtB,EAAAA,EAAAA,KAACF,EAAgB,CAACS,UAAU,YAAWR,UACnCC,EAAAA,EAAAA,KAACX,EAAc,K,gDCPpB,MAAMI,EAAc,CACzB,CACEH,GAAI,EACJhB,MAAO,gDACPkC,IAAK,aACLC,SAAU,oDACVc,SAAU,aACVC,UAAWpB,EAAAA,EAAWqB,SAASC,QAAQC,SACvCf,OAAQ,CACN,CACEgB,SAAUxB,EAAAA,EAAWqB,SAASC,QAAQG,UAExC,CACED,SAAUxB,EAAAA,EAAWqB,SAASC,QAAQI,UAExC,CACEF,SAAUxB,EAAAA,EAAWqB,SAASC,QAAQC,WAG1CV,YAAa,6DACbC,SAAU,CAAC,oBAAqB,eAAgB,aAElD,CACE5B,GAAI,EACJhB,MACE,0EACFkC,IAAK,aACLC,SAAU,iBACVc,SAAU,aACVC,UAAWpB,EAAAA,EAAWqB,SAASM,oBAAoBC,qBACnDpB,OAAQ,CACN,CACEgB,SAAUxB,EAAAA,EAAWqB,SAASM,oBAAoBC,sBAEpD,CACEJ,SAAUxB,EAAAA,EAAWqB,SAASM,oBAAoBE,sBAEpD,CACEL,SAAUxB,EAAAA,EAAWqB,SAASM,oBAAoBG,sBAEpD,CACEN,SAAUxB,EAAAA,EAAWqB,SAASM,oBAAoBI,uBAGtDlB,YACE,gNACFC,SAAU,CAAC,0BAA2B,6BAExC,CACE5B,GAAI,EACJhB,MAAO,oEACPkC,IAAK,aACLC,SAAU,+BACVc,SAAU,aACVC,UAAWpB,EAAAA,EAAWqB,SAASW,QAAQC,SACvCzB,OAAQ,CACN,CACEgB,SAAUxB,EAAAA,EAAWqB,SAASW,QAAQC,UAExC,CACET,SAAUxB,EAAAA,EAAWqB,SAASW,QAAQE,UAExC,CACEV,SAAUxB,EAAAA,EAAWqB,SAASW,QAAQG,WAG1CtB,YACE,wGACFC,SAAU,CAAC,iBAAkB,QAAS,gBAAiB,mBAEzD,CACE5B,GAAI,EACJhB,MAAO,wDACPkC,IAAK,aACLC,SAAU,OACVc,SAAU,aACVC,UAAWpB,EAAAA,EAAWqB,SAASe,cAAcC,eAC7C7B,OAAQ,CACN,CACEgB,SAAUxB,EAAAA,EAAWqB,SAASe,cAAcE,gBAE9C,CACEd,SAAUxB,EAAAA,EAAWqB,SAASe,cAAcG,gBAE9C,CACEf,SAAUxB,EAAAA,EAAWqB,SAASe,cAAcC,gBAE9C,CACEb,SAAUxB,EAAAA,EAAWqB,SAASe,cAAcI,iBAGhD3B,YAAa,8DACbC,SAAU,CAAC,iBAAkB,cAE/B,CACE5B,GAAI,EACJhB,MAAO,qCACPkC,IAAK,cACLC,SAAU,eACVc,SAAU,aACVC,UAAWpB,EAAAA,EAAWqB,SAASoB,UAAUC,WACzClC,OAAQ,CACN,CACEgB,SAAUxB,EAAAA,EAAWqB,SAASoB,UAAUE,YAE1C,CACEnB,SAAUxB,EAAAA,EAAWqB,SAASoB,UAAUC,YAE1C,CACElB,SAAUxB,EAAAA,EAAWqB,SAASoB,UAAUG,aAG5C/B,YACE,kFACFC,SAAU,CAAC,oBAAqB,aAAc,gBAAiB,aAEjE,CACE5B,GAAI,EACJhB,MAAO,iEACPkC,IAAK,aACLC,SAAU,eACVc,SAAU,aACVC,UAAWpB,EAAAA,EAAWqB,SAASwB,QAAQC,SACvCtC,OAAQ,CACN,CACEgB,SAAUxB,EAAAA,EAAWqB,SAASwB,QAAQC,UAExC,CACEtB,SAAUxB,EAAAA,EAAWqB,SAASwB,QAAQE,UAExC,CACEvB,SAAUxB,EAAAA,EAAWqB,SAASwB,QAAQG,WAG1CnC,YACE,wHACFC,SAAU,CAAC,iBAAkB,kBAAmB,2BAElD,CACE5B,GAAI,EACJhB,MAAO,yCACPkC,IAAK,aACLC,SAAU,SACVc,SAAU,aACVC,UAAWpB,EAAAA,EAAWqB,SAAS4B,YAAYC,aAC3C1C,OAAQ,CACN,CACEgB,SAAUxB,EAAAA,EAAWqB,SAAS4B,YAAYC,cAE5C,CACE1B,SAAUxB,EAAAA,EAAWqB,SAAS4B,YAAYE,cAE5C,CACE3B,SAAUxB,EAAAA,EAAWqB,SAAS4B,YAAYG,cAE5C,CACE5B,SAAUxB,EAAAA,EAAWqB,SAAS4B,YAAYI,eAG9CxC,YAAa,4DACbC,SAAU,CAAC,QAAS,aAAc,aAEpC,CACE5B,GAAI,EACJhB,MACE,mGACFkC,IAAK,aACLC,SAAU,gBACVc,SAAU,aACVC,UAAWpB,EAAAA,EAAWqB,SAASiC,UAAUC,WACzC/C,OAAQ,CACN,CACEgB,SAAUxB,EAAAA,EAAWqB,SAASiC,UAAUC,YAE1C,CACE/B,SAAUxB,EAAAA,EAAWqB,SAASiC,UAAUE,YAE1C,CACEhC,SAAUxB,EAAAA,EAAWqB,SAASiC,UAAUG,YAE1C,CACEjC,SAAUxB,EAAAA,EAAWqB,SAASiC,UAAUI,aAG5C7C,YACE,0EACFC,SAAU,CAAC,kBAAmB,eAAgB,kB,gFC1L3C,MAAM/D,EAAYC,EAAAA,GAAOC,IAAGC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,wYACbS,IAAA,IAAGI,OAAO,OAAE2F,IAAU/F,EAAA,OAAK+F,EAAOxF,KAAKyF,UA+BhDC,GARc7G,EAAAA,GAAOO,IAAGF,IAAAA,GAAAF,EAAAA,EAAAA,GAAA,wFAQVH,EAAAA,GAAO8G,EAACtG,IAAAA,GAAAL,EAAAA,EAAAA,GAAA,oI,aC5BnC,MASA,EAToBS,IAAgB,IAAf,MAAEM,GAAON,EAC5B,OACEgC,EAAAA,EAAAA,KAACF,EAAgB,CAAAC,UAEfC,EAAAA,EAAAA,KAACF,EAAkB,CAACxB,MAAOA,EAAMyB,SAAEzB,M", "sources": ["components/projects/project-details/styles.js", "components/projects/project-details/index.jsx", "pages/projects/details-page/styles.js", "pages/projects/details-page/index.jsx", "components/projects/projectData.js", "components/projects/project-details/service-item/styles.js", "components/projects/project-details/service-item/index.jsx"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  margin: 50px 0;\n`;\n\nexport const BackButtonWrapper = styled.div`\n  display: flex;\n  align-items: center;\n`;\n\nexport const IconHolder = styled.img`\n  width: 25px;\n  height: 25px;\n\n  object-fit: contain;\n\n  margin-right: 10px;\n\n  /* transform: translateX(-150%); */\n`;\n\nexport const TextHolder = styled.span`\n  font-size: ${({ heading, big, small, theme: { font } }) =>\n    heading ? font.title : small ? font.small : big ? font.big : font.main};\n  font-weight: ${({ bold }) => (bold ? 600 : 400)};\n  display: block;\n`;\n\nexport const TitleHolder = styled.div`\n  margin: 20px 0 40px;\n`;\n\nexport const ImageContainer = styled.div`\n  /* margin: 50px 0 70px; */\n\n  border-radius: 20px;\n  /* height: 600px; */\n\n  display: block;\n  margin-bottom: 50px;\n\n  /* .image-gallery-content .image-gallery-slide .image-gallery-image {\n    max-height: 100%;\n  } */\n`;\n\nexport const ImageHolder = styled.img`\n  width: 100%;\n  height: 100%;\n\n  border-radius: 20px;\n\n  object-fit: cover;\n`;\n\nexport const DetailsWrapper = styled.div`\n  margin: 20px 0 100px;\n`;\n\nexport const DetailsItemHolder = styled.div`\n  margin: 20px 0;\n`;\n\nexport const ServiceListWrapper = styled.div`\n  display: flex;\n  align-items: flex-start;\n\n  flex-wrap: wrap;\n  gap: 20px;\n\n  margin: 10px 0;\n`;\n", "import React from \"react\";\n\nimport { usePara<PERSON>, Link } from \"react-router-dom\";\n\nimport ImageGallery from \"react-image-gallery\";\n\nimport { AssetsList } from \"../../elements/assetsList\";\n\nimport { ProjectData } from \"../projectData\";\n\nimport ServiceItem from \"./service-item\";\n\nimport * as Styles from \"./styles\";\n\nconst ProjectDetails = () => {\n  const { id } = useParams();\n\n  const data = ProjectData.filter(\n    (item) => parseInt(item.id) === parseInt(id)\n  )[0];\n\n  return (\n    <Styles.Container>\n      <Link to=\"/projects\">\n        <Styles.BackButtonWrapper>\n          <Styles.IconHolder src={AssetsList.chevron.left} />\n          <Styles.TextHolder bold heading>\n            {data.title}\n          </Styles.TextHolder>\n        </Styles.BackButtonWrapper>\n      </Link>\n      <Styles.TitleHolder>\n        <Styles.TextHolder className=\"mb-2\" bold big>\n         {data.tag}\n        </Styles.TextHolder>\n        <Styles.TextHolder small>{data.location}</Styles.TextHolder>\n      </Styles.TitleHolder>\n      <Styles.ImageContainer>\n        <ImageGallery\n          items={data.banner}\n          originalHeight={\"100%\"}\n          originalWidth={\"100%\"}\n          showThumbnails={false}\n          showPlayButton={false}\n        />\n        {/* <Styles.ImageHolder src={data.banner} /> */}\n      </Styles.ImageContainer>\n      <Styles.DetailsWrapper>\n        <Styles.DetailsItemHolder>\n          <Styles.TextHolder bold heading>\n            Location :\n          </Styles.TextHolder>\n          <Styles.TextHolder heading>{data.location}</Styles.TextHolder>\n        </Styles.DetailsItemHolder>\n        <Styles.DetailsItemHolder>\n          <Styles.TextHolder bold heading>\n            Description :\n          </Styles.TextHolder>\n          <Styles.TextHolder heading>{data.description}</Styles.TextHolder>\n        </Styles.DetailsItemHolder>\n        <Styles.DetailsItemHolder>\n          <Styles.TextHolder bold heading>\n            Services :\n          </Styles.TextHolder>\n          <Styles.ServiceListWrapper>\n            {data.services.map((item, key) => {\n              return <ServiceItem key={key} title={item} />;\n            })}\n          </Styles.ServiceListWrapper>\n        </Styles.DetailsItemHolder>\n      </Styles.DetailsWrapper>\n    </Styles.Container>\n  );\n};\n\nexport default ProjectDetails;\n", "import styled from 'styled-components';\n\nexport const Container = styled.div`\n  \n`;", "import React from 'react'\n\nimport ProjectDetails from '../../../components/projects/project-details'\n\nimport * as Styles from './styles'\n\nconst DetailsPage = () => {\n    return (\n        <Styles.Container className=\"container\">\n            <ProjectDetails />\n        </Styles.Container>\n    )\n}\n\nexport default DetailsPage", "import { AssetsList } from \"../elements/assetsList\";\n\nexport const ProjectData = [\n  {\n    id: 1,\n    title: \"Vikhroli Corporate Park Private LTD (Embassy)\",\n    tag: \"Commercial\",\n    location: \"247 Park, LBS Marg, Vikroli (West), Mumbai 400083\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.embassy.embassy3,\n    banner: [\n      {\n        original: AssetsList.projects.embassy.embassy1,\n      },\n      {\n        original: AssetsList.projects.embassy.embassy2,\n      },\n      {\n        original: AssetsList.projects.embassy.embassy3,\n      },\n    ],\n    description: \"Structural and Civil Repairs, Painting of Utility Building\",\n    services: [\"Structural Repair\", \"Civil Repair\", \"Painting\"],\n  },\n  {\n    id: 2,\n    title:\n      \"Column, Slab strengthening at four season at residential tower project.\",\n    tag: \"Commercial\",\n    location: \"Worli, Mumbai \",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy1,\n    banner: [\n      {\n        original: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy1,\n      },\n      {\n        original: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy2,\n      },\n      {\n        original: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy3,\n      },\n      {\n        original: AssetsList.projects.fourSeasonResidancy.fourSeasonResidancy4,\n      },\n    ],\n    description:\n      \"Column, Slab strengthening of Four Season residency tower project. strengthening of column by microconcrete jacketing, by using high grade steel plate for additional loading. i.e. additional floor included\",\n    services: [\"Microconcrete Jacketing\", \"High grade steel plating\"],\n  },\n  {\n    id: 3,\n    title: \"Structural strengthening of Vikas Industries And Chemical PVT LTD\",\n    tag: \"Industrial\",\n    location: \"MIDC Tarapur Industrial Area\",\n    category: \"Industrial\",\n    thumbnail: AssetsList.projects.tarapur.tarapur1,\n    banner: [\n      {\n        original: AssetsList.projects.tarapur.tarapur1,\n      },\n      {\n        original: AssetsList.projects.tarapur.tarapur2,\n      },\n      {\n        original: AssetsList.projects.tarapur.tarapur3,\n      },\n    ],\n    description:\n      \"strengthening of Blast Upgrade structure using epoxy grouting, P.M.M, Microconcrete & fibre wrapping.\",\n    services: [\"Epoxy Grouting\", \"P.M.M\", \"Microconcrete\", \"Fibre Wrapping\"],\n  },\n  {\n    id: 4,\n    title: \"Structural strengthening of columns at Millenium Star\",\n    tag: \"Commercial\",\n    location: \"Pune\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.milleniumMall.milleniumMall3,\n    banner: [\n      {\n        original: AssetsList.projects.milleniumMall.milleniumMall1,\n      },\n      {\n        original: AssetsList.projects.milleniumMall.milleniumMall2,\n      },\n      {\n        original: AssetsList.projects.milleniumMall.milleniumMall3,\n      },\n      {\n        original: AssetsList.projects.milleniumMall.milleniumMall4,\n      },\n    ],\n    description: \"strengthening columns by using fibre wrapping and Laminates\",\n    services: [\"Fibre Wrapping\", \"Laminates\"],\n  },\n  {\n    id: 5,\n    title: \"Structural Repair at Raghav C.H.S.\",\n    tag: \"Residential\",\n    location: \"Malad (East)\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.raghavChs.raghavChs2,\n    banner: [\n      {\n        original: AssetsList.projects.raghavChs.raghavChs1,\n      },\n      {\n        original: AssetsList.projects.raghavChs.raghavChs2,\n      },\n      {\n        original: AssetsList.projects.raghavChs.raghavChs3,\n      },\n    ],\n    description:\n      \"Structural repairing of R.C.C. members, plastering, waterproofing and painting.\",\n    services: [\"Structural repair\", \"Plastering\", \"Waterproofing\", \"Painting\"],\n  },\n  {\n    id: 6,\n    title: \"Column strengthening works of Tower D, E, J, K Raymond Project\",\n    tag: \"Commercial\",\n    location: \"Thane (West)\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.raymond.raymond1,\n    banner: [\n      {\n        original: AssetsList.projects.raymond.raymond1,\n      },\n      {\n        original: AssetsList.projects.raymond.raymond2,\n      },\n      {\n        original: AssetsList.projects.raymond.raymond3,\n      },\n    ],\n    description:\n      \"strengthening column by Fibre Wrapping, Micro Jacketing and high strength steel plate for additional floor included .\",\n    services: [\"Fibre Wrapping\", \"Micro Jacketing\", \"High grade steel plate\"],\n  },\n  {\n    id: 7,\n    title: \"Structural Repair at Siyaram Mill LTD.\",\n    tag: \"Commercial\",\n    location: \"Kalher\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.siyaramMill.siyaramMill1,\n    banner: [\n      {\n        original: AssetsList.projects.siyaramMill.siyaramMill1,\n      },\n      {\n        original: AssetsList.projects.siyaramMill.siyaramMill2,\n      },\n      {\n        original: AssetsList.projects.siyaramMill.siyaramMill3,\n      },\n      {\n        original: AssetsList.projects.siyaramMill.siyaramMill4,\n      },\n    ],\n    description: \"Structural Repair work using P.M.M, Plastering, Painting.\",\n    services: [\"P.M.M\", \"Plastering\", \"Painting\"],\n  },\n  {\n    id: 8,\n    title:\n      \"Providing and Carrying out beam strengthening work by fibre wrapping and laminate at Metro Mall \",\n    tag: \"Commercial\",\n    location: \"Kalyan (East)\",\n    category: \"Commercial\",\n    thumbnail: AssetsList.projects.metroMall.metroMall1,\n    banner: [\n      {\n        original: AssetsList.projects.metroMall.metroMall1,\n      },\n      {\n        original: AssetsList.projects.metroMall.metroMall2,\n      },\n      {\n        original: AssetsList.projects.metroMall.metroMall3,\n      },\n      {\n        original: AssetsList.projects.metroMall.metroMall4,\n      },\n    ],\n    description:\n      \"Beam strengthening using carbon Fibre, steel plating & carbon laminate.\",\n    services: [\"Carbon Laminate\", \"Carbon Fibre\", \"Steel Plating\"],\n  },\n];\n\n// export const ProjectData2 = [\n//   {\n//     id: 1,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n//   {\n//     id: 2,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n//   {\n//     id: 3,\n//     banner: AssetsList.bg,\n//     title: \"Embassy 247 Park\",\n//     location: \"Vikhroli West, Mumbai\",\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     tag: \"Industrial Commercial\",\n//   },\n//   {\n//     id: 4,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n//   {\n//     id: 5,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n//   {\n//     id: 6,\n//     title: \"Embassy 247 Park\",\n//     tag: \"Industrial Commercial\",\n//     location: \"Vikhroli West, Mumbai\",\n//     banner: AssetsList.bg,\n//     description:\n//       \"Strengthening and civil work including Grouting, Steel Treatment, Polymer modified mortar application, etc\",\n//     services: [\"Steel Treatment\", \"External Plaster\", \"Grouting\"],\n//   },\n// ];\n", "import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  background-color: ${({ theme: { colors } }) => colors.main.yellow};\n  padding: 15px 20px;\n\n  border-radius: 10px;\n\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\n\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  /* max-width: 150px; */\n  min-width: 140px;\n\n  &:hover {\n    background-color: #fff;\n\n    cursor: pointer;\n    transition: all 0.1s ease-in-out;\n  }\n`;\n\nexport const ImageHolder = styled.img`\n  width: 50px;\n  height: 50px;\n  object-fit: contain;\n\n  margin: 10px 0;\n`;\n\nexport const TextWrapper = styled.p`\n  margin-bottom: 0;\n  text-align: center;\n\n  font-weight: 600;\n\n  white-space: nowrap;\n  text-overflow: ellipsis;\n`;\n", "import React from \"react\";\n\nimport { AssetsList } from \"../../../elements/assetsList\";\n\nimport * as Styles from \"./styles\";\n\nconst ServiceItem = ({ title }) => {\n  return (\n    <Styles.Container>\n      {/* <Styles.ImageHolder src={AssetsList.logo} /> */}\n      <Styles.TextWrapper title={title}>{title}</Styles.TextWrapper>\n    </Styles.Container>\n  );\n};\n\nexport default ServiceItem;\n"], "names": ["Container", "styled", "div", "_templateObject", "_taggedTemplateLiteral", "BackButtonWrapper", "_templateObject2", "IconHolder", "img", "_templateObject3", "TextHolder", "span", "_templateObject4", "_ref", "heading", "big", "small", "theme", "font", "title", "main", "_ref2", "bold", "TitleHolder", "_templateObject5", "ImageContainer", "_templateObject6", "DetailsWrapper", "_templateObject7", "_templateObject8", "DetailsItemHolder", "_templateObject9", "ServiceListWrapper", "_templateObject0", "ProjectDetails", "id", "useParams", "data", "ProjectData", "filter", "item", "parseInt", "_jsxs", "Styles", "children", "_jsx", "Link", "to", "src", "AssetsList", "chevron", "left", "className", "tag", "location", "ImageGallery", "items", "banner", "originalHeight", "originalWidth", "showThumbnails", "showPlayButton", "description", "services", "map", "key", "ServiceItem", "DetailsPage", "category", "thumbnail", "projects", "embassy", "embassy3", "original", "embassy1", "embassy2", "fourSeasonResidancy", "fourSeasonResidancy1", "fourSeasonResidancy2", "fourSeasonResidancy3", "fourSeasonResidancy4", "tarapur", "tarapur1", "tarapur2", "tarapur3", "milleniumMall", "milleniumMall3", "milleniumMall1", "milleniumMall2", "milleniumMall4", "raghavChs", "raghavChs2", "raghavChs1", "raghavChs3", "<PERSON><PERSON>", "raymond1", "raymond2", "raymond3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "siyaramMill1", "siyaramMill2", "siyaramMill3", "siyaramMill4", "metroMall", "metroMall1", "metroMall2", "metroMall3", "metroMall4", "colors", "yellow", "TextWrapper", "p"], "sourceRoot": ""}