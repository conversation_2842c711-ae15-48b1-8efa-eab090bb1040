{"version": 3, "file": "static/js/961.16fe04ff.chunk.js", "mappings": "mJAEO,MAAMA,E,QAAUC,GAAOC,QAAOC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,uDACxBC,IAAA,IAAC,KAAEC,GAAMD,EAAA,OAAMC,EAAO,QAAU,W,aCC7C,MAQA,EARiBD,IAAyB,IAAxB,SAAEE,EAAQ,KAAED,GAAMD,EAClC,OACEG,EAAAA,EAAAA,KAACC,EAAc,CAACC,UAAU,OAAOJ,KAAMA,EAAKC,SACzCA,I,iFCPA,MAAMI,EAAQ,CACjBC,QAAS,2BCDAC,EAAiBA,CAACC,EAAWC,EAAWC,KACjD,IAAKF,EACD,KAAM,gFAEV,IAAKC,EACD,KAAM,wEAEV,IAAKC,EACD,KAAM,mFAEV,OAAO,GCVJ,MAAMC,EACTC,WAAAA,CAAYC,GACRC,KAAKC,OAASF,EAAeA,EAAaE,OAAS,EACnDD,KAAKE,KAAOH,EAAeA,EAAaI,aAAe,eAC3D,ECFG,MAAMC,EAAW,SAACC,EAAKC,GAAuB,IAAjBC,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC3C,OAAO,IAAIG,SAAQ,CAACC,EAASC,KACzB,MAAMC,EAAM,IAAIC,eAChBD,EAAIE,iBAAiB,QAAQ/B,IAAgB,IAAf,OAAEgC,GAAQhC,EACpC,MAAMiC,EAAiB,IAAIrB,EAAsBoB,GACnB,MAA1BC,EAAejB,QAA0C,OAAxBiB,EAAehB,KAChDU,EAAQM,GAGRL,EAAOK,MAGfJ,EAAIE,iBAAiB,SAASG,IAAgB,IAAf,OAAEF,GAAQE,EACrCN,EAAO,IAAIhB,EAAsBoB,OAErCH,EAAIM,KAAK,OAAQ7B,EAAMC,QAAUa,GAAK,GACtCgB,OAAOC,KAAKf,GAASgB,SAASC,IAC1BV,EAAIW,iBAAiBD,EAAKjB,EAAQiB,OAEtCV,EAAIY,KAAKpB,KAEjB,ECnBA,ECoBwBqB,CAAChC,EAAWC,EAAYgC,EAAMlC,KAClD,MAAMmC,EAAMnC,GAAaH,EAAMuC,QACzBC,EAvBYH,KAClB,IAAIG,EAOJ,GALIA,EADgB,kBAATH,EACOI,SAASC,cAAcL,GAGvBA,GAEbG,GAAwC,SAAzBA,EAAYG,SAC5B,KAAM,0FAEV,OAAOH,GAYaI,CAAaP,GACjCnC,EAAeoC,EAAKlC,EAAWC,GAC/B,MAAMwC,EAAW,IAAIC,SAASN,GAK9B,OAJAK,EAASE,OAAO,cAAe,UAC/BF,EAASE,OAAO,aAAc3C,GAC9ByC,EAASE,OAAO,cAAe1C,GAC/BwC,EAASE,OAAO,UAAWT,GACpBzB,EAAS,4BAA6BgC,I,wCC/B1C,MAAMG,EAAW1D,EAAAA,GAAO2D,IAAGzD,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,QAErByD,EAAa5D,EAAAA,GAAO+C,KAAIc,IAAAA,GAAA1D,EAAAA,EAAAA,GAAA,QAExB2D,EAAY9D,EAAAA,GAAO2D,IAAGI,IAAAA,GAAA5D,EAAAA,EAAAA,GAAA,6BAItB6D,EAAchE,EAAAA,GAAOiE,MAAKC,IAAAA,GAAA/D,EAAAA,EAAAA,GAAA,4FAK5BC,IAAA,IAAG+D,OAAO,OAAEC,IAAUhE,EAAA,OAAKgE,EAAOC,QAGhCC,EAActE,EAAAA,GAAOuE,MAAKC,IAAAA,GAAArE,EAAAA,EAAAA,GAAA,+KAEjCmC,IAAA,IAAC,MAAEmC,EAAON,OAAO,OAAEC,IAAU9B,EAAA,OAAMmC,EAAQL,EAAOM,KAAKC,IAAMP,EAAOC,QAQlDO,IAAA,IAAGT,OAAO,OAAEC,IAAUQ,EAAA,OAAKR,EAAOM,KAAKG,QAIlDC,EAAS9E,EAAAA,GAAO+E,OAAMC,IAAAA,GAAA7E,EAAAA,EAAAA,GAAA,yUACb8E,IAAA,IAAC,QAAEC,EAASf,OAAO,OAAEC,IAAUa,EAAA,OACjDC,EAAU,UAAYd,EAAOM,KAAKS,UAY3BC,IAAA,IAAGjB,OAAO,OAAEC,IAAUgB,EAAA,OAAKhB,EAAOM,KAAKG,QAG1BQ,IAAA,IAAGlB,OAAO,OAAEC,IAAUiB,EAAA,OAAKjB,EAAOM,KAAKG,QAClDS,IAAA,IAAGnB,OAAO,OAAEC,IAAUkB,EAAA,OAAKlB,EAAOmB,SAMlCC,EAAYxF,EAAAA,GAAOyF,EAACC,IAAAA,GAAAvF,EAAAA,EAAAA,GAAA,yBAIpBwF,EAAc3F,EAAAA,GAAOyF,EAACG,IAAAA,GAAAzF,EAAAA,EAAAA,GAAA,uE,aCrDnC,MA2JA,EA3JoB0F,KAClB,IAAIC,GAASC,EAAAA,EAAAA,QAAO,IACpB,MAAMhD,GAAOgD,EAAAA,EAAAA,WAENb,EAASc,IAAcC,EAAAA,EAAAA,UAAS,OAChCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,CAE3CK,KAAM,GACNC,QAAS,GACTC,MAAO,GACPC,OAAQ,MAGJ,KAAEH,EAAI,QAAEC,EAAO,MAAEC,EAAK,OAAEC,GAAWL,GAElCM,EAAaC,IAAkBV,EAAAA,EAAAA,UAAS,MAEzCW,EAAYd,EAAOe,SAEzBC,EAAAA,EAAAA,YAAU,KACRH,EAAeb,EAAOe,WACrB,CAACD,IAEJ,MAAMG,EAAgBC,IACpB,MAAMC,EAAQD,EAAE5E,OAAO6E,MACjBX,EAAOU,EAAE5E,OAAOkE,KAEtBD,GAAaa,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAId,GAAU,IAAE,CAACE,GAAOW,KACvCN,GAAcO,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIR,GAAW,IAAE,CAACJ,GAAO,OAgD3C,OACEa,EAAAA,EAAAA,MAAC3G,EAAe,CAAAF,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,MAAI6G,MAAO,CAAEC,MAAO,QAAQC,KAAM,eAAgBC,UAAW,UAAWjH,SAAC,6BACnE6G,EAAAA,EAAAA,MAAC3G,EAAiB,CAACgH,IAAKzE,EAAM0E,SAAWT,GAjDxBU,WACnBV,EAAEW,iBACFxB,GAAW,GACXyB,QAAQC,IAAI,CAAEzB,eACdwB,QAAQC,IAAI,CAAE9E,KAAMA,EAAK8D,UACzBiB,EAEI,kBACA,mBACA/E,EAAK8D,QACL,qBAEDkB,MACEC,IACCJ,QAAQC,IAAIG,GACZ7B,GAAW,MAEZ1B,IACCmD,QAAQC,IAAIpD,EAAMpD,MAClB8E,GAAW,OA8BgC8B,CAAajB,GAAG1G,SAAA,EAC7D6G,EAAAA,EAAAA,MAAC3G,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAACC,EAAkB,CAAAF,SAAC,UACpBC,EAAAA,EAAAA,KAACC,EAAkB,CACjB8F,KAAK,OACL4B,KAAK,OACLjB,MAAOX,EACP6B,SAAWnB,GAAMD,EAAaC,GAC9BvC,MAAkB,OAAXiC,QAAW,IAAXA,OAAW,EAAXA,EAAaJ,KACpB8B,UAAQ,IAEE,OAAX1B,QAAW,IAAXA,GAAAA,EAAaJ,MACZ/F,EAAAA,EAAAA,KAACC,EAAgB,CAAAF,SAAa,OAAXoG,QAAW,IAAXA,OAAW,EAAXA,EAAaJ,OAC9B,SAENa,EAAAA,EAAAA,MAAC3G,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAACC,EAAkB,CAAAF,SAAC,aACpBC,EAAAA,EAAAA,KAACC,EAAkB,CACjB8F,KAAK,UACL4B,KAAK,OACLjB,MAAOV,EACP4B,SAAWnB,GAAMD,EAAaC,GAC9BvC,MAAkB,OAAXiC,QAAW,IAAXA,OAAW,EAAXA,EAAaH,QACpB6B,UAAQ,IAEE,OAAX1B,QAAW,IAAXA,GAAAA,EAAaH,SACZhG,EAAAA,EAAAA,KAACC,EAAgB,CAAAF,SAAa,OAAXoG,QAAW,IAAXA,OAAW,EAAXA,EAAaH,UAC9B,SAENY,EAAAA,EAAAA,MAAC3G,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAACC,EAAkB,CAAAF,SAAC,WACpBC,EAAAA,EAAAA,KAACC,EAAkB,CACjB8F,KAAK,QACL4B,KAAK,QACLjB,MAAOT,EACP2B,SAAWnB,GAAMD,EAAaC,GAC9BvC,MAAkB,OAAXiC,QAAW,IAAXA,OAAW,EAAXA,EAAaF,MACpB4B,UAAQ,IAEE,OAAX1B,QAAW,IAAXA,GAAAA,EAAaF,OACZjG,EAAAA,EAAAA,KAACC,EAAgB,CAAAF,SAAa,OAAXoG,QAAW,IAAXA,OAAW,EAAXA,EAAaF,QAC9B,SAENW,EAAAA,EAAAA,MAAC3G,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAACC,EAAkB,CAAAF,SAAC,qBACpBC,EAAAA,EAAAA,KAACC,EAAkB,CACjB8F,KAAK,SACL4B,KAAK,MACLjB,MAAOR,EACP0B,SAAWnB,GAAMD,EAAaC,GAC9BvC,MAAkB,OAAXiC,QAAW,IAAXA,OAAW,EAAXA,EAAaD,OACpB2B,UAAQ,IAEE,OAAX1B,QAAW,IAAXA,GAAAA,EAAaD,QACZlG,EAAAA,EAAAA,KAACC,EAAgB,CAAAF,SAAa,OAAXoG,QAAW,IAAXA,OAAW,EAAXA,EAAaD,SAC9B,SAENlG,EAAAA,EAAAA,KAACC,EAAa,CACZ0E,QAAgB,OAAPA,QAAO,IAAPA,OAAO,EAAPA,EAASmD,UAClBH,KAAK,SACLI,SAAiB,OAAPpD,QAAO,IAAPA,OAAO,EAAPA,EAASmD,UAAU/H,UAE5B4F,GAAmB,OAAPhB,QAAO,IAAPA,GAAAA,EAASmD,WAEjBnC,GAAkB,OAAPhB,QAAO,IAAPA,GAAAA,EAASmD,UACrB,oBACA,eAHA,oBAMR9H,EAAAA,EAAAA,KAACC,EAAkB,CAAAF,SAAS,OAAP4E,QAAO,IAAPA,OAAO,EAAPA,EAASqD,U,cC3J7B,MAAM7E,EAAW1D,EAAAA,GAAO2D,IAAGzD,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,wDAKrBqI,EAAexI,EAAAA,GAAO2D,IAAGE,IAAAA,GAAA1D,EAAAA,EAAAA,GAAA,gBAIzBsI,EAAczI,EAAAA,GAAO0I,IAAG3E,IAAAA,GAAA5D,EAAAA,EAAAA,GAAA,2EAOxBwI,EAAc3I,EAAAA,GAAO2D,IAAGO,IAAAA,GAAA/D,EAAAA,EAAAA,GAAA,4BAIxByI,EAAiB5I,EAAAA,GAAO2D,IAAGa,IAAAA,GAAArE,EAAAA,EAAAA,GAAA,4FCSxC,EAvBkB0I,KAEV1B,EAAAA,EAAAA,MAAA2B,EAAAA,SAAA,CAAAxI,SAAA,EACA6G,EAAAA,EAAAA,MAAC3G,EAAe,CAACC,UAAU,2BAA0BH,SAAA,EAEjDC,EAAAA,EAAAA,KAACC,EAAmB,CAACC,UAAU,yBAAwBH,UACnDC,EAAAA,EAAAA,KAACC,EAAkB,CACfuI,IAAKC,EAAAA,EAAWC,YAGxB1I,EAAAA,EAAAA,KAACC,EAAkB,CAACC,UAAU,sCAAqCH,UAC/DC,EAAAA,EAAAA,KAACsF,EAAW,UAIpBsB,EAAAA,EAAAA,MAAC3G,EAAqB,CAACC,UAAU,SAAQH,SAAA,EACzCC,EAAAA,EAAAA,KAAA,MAAAD,UAAIC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,uBACPC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,+G,aCpBX,MAUA,EAVoB4I,KAEZ3I,EAAAA,EAAAA,KAAA,OAAKE,UAAU,YAAWH,UACtBC,EAAAA,EAAAA,KAAC4I,EAAAA,EAAQ,CAAA7I,UACLC,EAAAA,EAAAA,KAACsI,EAAS,O", "sources": ["components/global/seaction/styles.js", "components/global/seaction/index.jsx", "../node_modules/@emailjs/browser/es/store/store.js", "../node_modules/@emailjs/browser/es/utils/validateParams.js", "../node_modules/@emailjs/browser/es/models/EmailJSResponseStatus.js", "../node_modules/@emailjs/browser/es/api/sendPost.js", "../node_modules/@emailjs/browser/es/index.js", "../node_modules/@emailjs/browser/es/methods/sendForm/sendForm.js", "components/contact-us/form/styles.js", "components/contact-us/form/index.jsx", "components/contact-us/styles.js", "components/contact-us/index.jsx", "pages/contact-us/index.jsx"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Section = styled.section`\n  display: ${({ flex }) => (flex ? \" flex\" : \"block\")};\n  align-items: flex-start;\n`;\n", "import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst Seaction = ({ children, flex }) => {\n  return (\n    <Styles.Section className=\"py-5\" flex={flex}>\n      {children}\n    </Styles.Section>\n  );\n};\n\nexport default Seaction;\n", "export const store = {\n    _origin: 'https://api.emailjs.com',\n};\n", "export const validateParams = (publicKey, serviceID, templateID) => {\n    if (!publicKey) {\n        throw 'The public key is required. Visit https://dashboard.emailjs.com/admin/account';\n    }\n    if (!serviceID) {\n        throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n    }\n    if (!templateID) {\n        throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n    }\n    return true;\n};\n", "export class EmailJSResponseStatus {\n    constructor(httpResponse) {\n        this.status = httpResponse ? httpResponse.status : 0;\n        this.text = httpResponse ? httpResponse.responseText : 'Network Error';\n    }\n}\n", "import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = (url, data, headers = {}) => {\n    return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest();\n        xhr.addEventListener('load', ({ target }) => {\n            const responseStatus = new EmailJSResponseStatus(target);\n            if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n                resolve(responseStatus);\n            }\n            else {\n                reject(responseStatus);\n            }\n        });\n        xhr.addEventListener('error', ({ target }) => {\n            reject(new EmailJSResponseStatus(target));\n        });\n        xhr.open('POST', store._origin + url, true);\n        Object.keys(headers).forEach((key) => {\n            xhr.setRequestHeader(key, headers[key]);\n        });\n        xhr.send(data);\n    });\n};\n", "import { init } from './methods/init/init';\nimport { send } from './methods/send/send';\nimport { sendForm } from './methods/sendForm/sendForm';\nexport { init, send, sendForm };\nexport default {\n    init,\n    send,\n    sendForm,\n};\n", "import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\nconst findHTMLForm = (form) => {\n    let currentForm;\n    if (typeof form === 'string') {\n        currentForm = document.querySelector(form);\n    }\n    else {\n        currentForm = form;\n    }\n    if (!currentForm || currentForm.nodeName !== 'FORM') {\n        throw 'The 3rd parameter is expected to be the HTML form element or the style selector of form';\n    }\n    return currentForm;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {string} publicKey - the EmailJS public key\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const sendForm = (serviceID, templateID, form, publicKey) => {\n    const uID = publicKey || store._userID;\n    const currentForm = findHTMLForm(form);\n    validateParams(uID, serviceID, templateID);\n    const formData = new FormData(currentForm);\n    formData.append('lib_version', '3.12.1');\n    formData.append('service_id', serviceID);\n    formData.append('template_id', templateID);\n    formData.append('user_id', uID);\n    return sendPost('/api/v1.0/email/send-form', formData);\n};\n", "import styled from \"styled-components\";\n\nexport const Contaier = styled.div``;\n\nexport const FormHolder = styled.form``;\n\nexport const RowHolder = styled.div`\n  margin: 20px 0;\n`;\n\nexport const LabelHolder = styled.label`\n  display: block;\n  margin-left: 10px;\n  margin-bottom: 10px;\n\n  color: ${({ theme: { colors } }) => colors.grey};\n`;\n\nexport const InputHolder = styled.input`\n  border: 2px solid\n    ${({ error, theme: { colors } }) => (error ? colors.main.red : colors.grey)};\n  padding: 10px 16px;\n  border-radius: 100px;\n\n  width: 100%;\n\n  &:focus {\n    outline: none;\n    border: 2px solid ${({ theme: { colors } }) => colors.main.blue};\n  }\n`;\n\nexport const Button = styled.button`\n  background-color: ${({ success, theme: { colors } }) =>\n    success ? \"#a2ff54\" : colors.main.yellow};\n  border: none;\n  padding: 10px;\n  width: 100%;\n  font-weight: 600;\n\n  border-radius: 100px;\n\n  box-shadow: 0 0 20px 2px rgba(0, 0, 0, 0.1);\n\n  margin-top: 20px;\n\n  color: ${({ theme: { colors } }) => colors.main.blue};\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: ${({ theme: { colors } }) => colors.white};\n  }\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const ShowError = styled.p`\n  color: red;\n`;\n\nexport const ShowSuccess = styled.p`\n  color: green;\n  margin-top: 10px;\n  text-align: center;\n`;\n", "import { useState, useEffect, useRef } from \"react\";\n\n// import { sendMail } from \"../../../api\";\nimport emailjs from \"@emailjs/browser\";\n\nimport * as Styles from \"./styles\";\n\nconst ContactForm = () => {\n  let errors = useRef([]);\n  const form = useRef();\n\n  const [success, setSuccess] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [inputValue, setInputValue] = useState({\n\n    name: \"\",\n    company: \"\",\n    email: \"\",\n    mobile: \"\",\n  });\n\n  const { name, company, email, mobile } = inputValue;\n\n  const [inputErrors, setInputErrors] = useState(null);\n\n  const errChange = errors.current;\n\n  useEffect(() => {\n    setInputErrors(errors.current);\n  }, [errChange]);\n\n  const handleChange = (e) => {\n    const value = e.target.value;\n    const name = e.target.name;\n\n    setInputValue({ ...inputValue, [name]: value });\n    setInputErrors({ ...inputErrors, [name]: \"\" });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    console.log({ inputValue });\n    console.log({ form: form.current });\n    emailjs\n      .sendForm(\n        \"service_ifbucgn\",\n        \"template_gryehv4\",\n        form.current,\n        \"ye7UvAtvh7-vkICwy\"\n      )\n      .then(\n        (result) => {\n          console.log(result);\n          setLoading(false);\n        },\n        (error) => {\n          console.log(error.text);\n          setLoading(false);\n        }\n      );\n    // const res = await sendMail(inputValue);\n    // if (res.success) {\n    //   setSuccess(res.data);\n    //   setInputValue({ name: \"\", company: \"\", email: \"\", mobile: \"\" });\n    //   setLoading(false);\n\n    //   setTimeout(() => {\n    //     setSuccess(null);\n    //   }, 6000);\n    // } else if (!res.success) {\n    //   let i = 0;\n    //   while (i < res.data.length) {\n    //     errors.current[res.data[i].param] = res.data[i].msg;\n    //     setInputErrors({\n    //       ...inputErrors,\n    //       [res.data[i].param]: res.data[i].msg,\n    //     });\n    //     i++;\n    //   }\n    //   setLoading(false);\n    // }\n  };\n\n  return (\n    <Styles.Contaier>\n\n<h2 style={{ color: \"Black\",font: \"-moz-initial\", textAlign: \"center\" }}>Who are already onboard</h2>\n      <Styles.FormHolder ref={form} onSubmit={(e) => handleSubmit(e)}>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Name</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"name\"\n            type=\"text\"\n            value={name}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.name}\n            required\n          />\n          {inputErrors?.name ? (\n            <Styles.ShowError>{inputErrors?.name}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Company</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"company\"\n            type=\"text\"\n            value={company}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.company}\n            required\n          />\n          {inputErrors?.company ? (\n            <Styles.ShowError>{inputErrors?.company}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Email</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"email\"\n            type=\"email\"\n            value={email}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.email}\n            required\n          />\n          {inputErrors?.email ? (\n            <Styles.ShowError>{inputErrors?.email}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Contact Details</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"mobile\"\n            type=\"tel\"\n            value={mobile}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.mobile}\n            required\n          />\n          {inputErrors?.mobile ? (\n            <Styles.ShowError>{inputErrors?.mobile}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.Button\n          success={success?.delivered}\n          type=\"submit\"\n          disabled={success?.delivered}\n        >\n          {loading && !success?.delivered\n            ? \"Sending....\"\n            : !loading && success?.delivered\n            ? \"Sent Successfully\"\n            : \"Send Message\"}\n        </Styles.Button>\n      </Styles.FormHolder>\n      <Styles.ShowSuccess>{success?.msg}</Styles.ShowSuccess>\n    </Styles.Contaier>\n  );\n};\n\nexport default ContactForm;\n", "import styled from 'styled-components';\n\nexport const Contaier = styled.div`\n    display: flex;\n    align-items: center;\n`;\n\nexport const ImageWrapper = styled.div`\n    \n`;\n\nexport const ImageHolder = styled.img`\n    width: 100%;\n    height: 100%;\n\n    object-fit: contain;\n`;\n\nexport const FormWrapper = styled.div`\n    width: 100%;\n`;\n\nexport const AddressWrapper = styled.div`\n    padding: 2rem 5rem;\n    background-color: #f8f9fa;\n    text-align: center;\n`;", "import React from 'react'\n\nimport { AssetsList } from '../../components/elements/assetsList';\n\nimport ContactForm from './form';\n\nimport * as Styles from './styles'\n\nconst ContactUs = () => {\n    return (\n        <>\n        <Styles.Contaier className=\"flex-wrap flex-md-nowrap\">\n            \n            <Styles.ImageWrapper className=\"col col-12 col-md p-5 \">\n                <Styles.ImageHolder\n                    src={AssetsList.client}\n                />\n            </Styles.ImageWrapper>\n            <Styles.FormWrapper className=\"col col-12 col-md px-5 py-1 py-md-5\">\n                <ContactForm />\n            </Styles.FormWrapper>\n            \n        </Styles.Contaier>\n        <Styles.AddressWrapper className=\"col-12\">\n        <h1><b>Office Location</b></h1>\n        <p>Office No. 201, 2nd Floor, Tarabaug CHS, Prashant Nagar, Near Naupada Police Station, Thane - 400602</p>\n        </Styles.AddressWrapper>\n        </>\n    )\n}\n\nexport default ContactUs", "import React from 'react'\nimport ContactUs from '../../components/contact-us';\n\nimport Seaction from '../../components/global/seaction'\n\nconst ContactPage = () => {\n    return (\n        <div className=\"container\">\n            <Seaction>\n                <ContactUs />\n            </Seaction>\n        </div>\n    )\n}\n\nexport default ContactPage"], "names": ["Section", "styled", "section", "_templateObject", "_taggedTemplateLiteral", "_ref", "flex", "children", "_jsx", "Styles", "className", "store", "_origin", "validateParams", "public<PERSON>ey", "serviceID", "templateID", "EmailJSResponseStatus", "constructor", "httpResponse", "this", "status", "text", "responseText", "sendPost", "url", "data", "headers", "arguments", "length", "undefined", "Promise", "resolve", "reject", "xhr", "XMLHttpRequest", "addEventListener", "target", "responseStatus", "_ref2", "open", "Object", "keys", "for<PERSON>ach", "key", "setRequestHeader", "send", "sendForm", "form", "uID", "_userID", "currentForm", "document", "querySelector", "nodeName", "findHTMLForm", "formData", "FormData", "append", "<PERSON><PERSON><PERSON>", "div", "FormHolder", "_templateObject2", "RowHolder", "_templateObject3", "LabelHolder", "label", "_templateObject4", "theme", "colors", "grey", "InputHolder", "input", "_templateObject5", "error", "main", "red", "_ref3", "blue", "<PERSON><PERSON>", "button", "_templateObject6", "_ref4", "success", "yellow", "_ref5", "_ref6", "_ref7", "white", "ShowError", "p", "_templateObject7", "ShowSuccess", "_templateObject8", "ContactForm", "errors", "useRef", "setSuccess", "useState", "loading", "setLoading", "inputValue", "setInputValue", "name", "company", "email", "mobile", "inputErrors", "setInputErrors", "err<PERSON><PERSON><PERSON>", "current", "useEffect", "handleChange", "e", "value", "_objectSpread", "_jsxs", "style", "color", "font", "textAlign", "ref", "onSubmit", "async", "preventDefault", "console", "log", "emailjs", "then", "result", "handleSubmit", "type", "onChange", "required", "delivered", "disabled", "msg", "ImageWrapper", "ImageHolder", "img", "FormWrapper", "AddressWrapper", "ContactUs", "_Fragment", "src", "AssetsList", "client", "ContactPage", "Seaction"], "sourceRoot": ""}