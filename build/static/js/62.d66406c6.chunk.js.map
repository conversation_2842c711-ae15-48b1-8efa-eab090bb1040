{"version": 3, "file": "static/js/62.d66406c6.chunk.js", "mappings": "kJAEO,MAAMA,E,QAAUC,GAAOC,QAAOC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,uDACxBC,IAAA,IAAC,KAAEC,GAAMD,EAAA,OAAMC,EAAO,QAAU,W,aCC7C,MAQA,EARiBD,IAAyB,IAAxB,SAAEE,EAAQ,KAAED,GAAMD,EAClC,OACEG,EAAAA,EAAAA,KAACC,EAAc,CAACC,UAAU,OAAOJ,KAAMA,EAAKC,SACzCA,I,0DCLA,MAAMI,E,QAAYV,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,6GAIlBC,IAAA,IAAGQ,OAAO,KAAEC,IAAQT,EAAA,OAAKS,EAAKC,O,aCF/C,MAQA,EARwBV,IAAgB,IAAf,MAAEW,GAAOX,EAC9B,OACIG,EAAAA,EAAAA,KAACC,EAAgB,CAAAF,SACZS,I,kGCPN,MAAMC,EAAW,CACtB,CACEC,KAAM,sBACNC,MACE,iNAEJ,CACED,KAAM,wBACNC,MACE,0VAEJ,CACED,KAAM,kCACNC,MACE,0aAEJ,CACED,KAAM,mBACNC,MACE,qO,oCCjBC,MAAMC,EAAgBnB,EAAAA,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,0TAgBfC,IAAA,IAAGQ,OAAO,OAAEQ,IAAUhB,EAAA,OAAKgB,EAAOC,KAAKC,QAOlDC,EAAUvB,EAAAA,GAAOW,IAAGa,IAAAA,GAAArB,EAAAA,EAAAA,GAAA,uDAKpBsB,EAAYzB,EAAAA,GAAO0B,GAAEC,IAAAA,GAAAxB,EAAAA,EAAAA,GAAA,+BAIrByB,EAAa5B,EAAAA,GAAO6B,EAACC,IAAAA,GAAA3B,EAAAA,EAAAA,GAAA,sBAIrB4B,EAAgB/B,EAAAA,GAAOW,IAAGqB,IAAAA,GAAA7B,EAAAA,EAAAA,GAAA,QAE1B8B,EAAcjC,EAAAA,GAAOW,IAAGuB,IAAAA,GAAA/B,EAAAA,EAAAA,GAAA,sE,aCpCrC,MAWA,EAXkBC,IAAe,IAAd,KAAE+B,GAAM/B,EACzB,OACEG,EAAAA,EAAAA,KAACC,EAAoB,CAAAF,UACnB8B,EAAAA,EAAAA,MAAC5B,EAAc,CAAAF,SAAA,EACbC,EAAAA,EAAAA,KAACC,EAAgB,CAAAF,SAAE6B,EAAKlB,QACxBV,EAAAA,EAAAA,KAACC,EAAiB,CAAAF,SAAE6B,EAAKjB,cCyCjC,EAvCiBmB,KA0BbD,EAAAA,EAAAA,MAAC5B,EAAoB,CAAAF,SAAA,EACnBC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAACvB,MAAM,cACvBR,EAAAA,EAAAA,KAACC,EAAkB,CAAAF,UACjBC,EAAAA,EAAAA,KAACgC,EAAAA,QAAQ,CAACC,WA5BG,CACjBC,kBAAmB,CAEjBC,WAAY,CAAEC,IAAK,IAAMC,IAAK,KAC9BC,MAAO,EACPC,cAAe,GAEjBC,QAAS,CACPL,WAAY,CAAEC,IAAK,IAAMC,IAAK,MAC9BC,MAAO,EACPC,cAAe,GAEjBE,OAAQ,CACNN,WAAY,CAAEC,IAAK,KAAMC,IAAK,KAC9BC,MAAO,EACPC,cAAe,GAEjBG,OAAQ,CACNP,WAAY,CAAEC,IAAK,IAAKC,IAAK,GAC7BC,MAAO,EACPC,cAAe,IAQoBxC,SAC9BU,EAASkC,KAAI,CAACC,EAAMC,KACZ7C,EAAAA,EAAAA,KAAC8C,EAAS,CAAalB,KAAMgB,GAAbC,Y,UCxC5B,MAAM1C,EAAYV,EAAAA,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,6FAYtByB,GAFc5B,EAAAA,GAAOsD,GAAE9B,IAAAA,GAAArB,EAAAA,EAAAA,GAAA,QAEVH,EAAAA,GAAO6B,EAACF,IAAAA,GAAAxB,EAAAA,EAAAA,GAAA,SCClC,EATkBC,IAAsB,IAArB,MAAEW,EAAK,KAAEwC,GAAMnD,EAChC,OACEgC,EAAAA,EAAAA,MAAC5B,EAAgB,CAACC,UAAU,UAASH,SAAA,CAClCS,GAAQR,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAACvB,MAAOA,IAAY,MAC7CR,EAAAA,EAAAA,KAACC,EAAiB,CAAAF,SAAEiD,QCVbC,EAAwB,CACnC,wBACA,qBACA,yCACA,oBACA,iBACA,2BACA,yBACA,oEACA,uCACA,uBACA,kBACA,gBACA,mCACA,oBAGWC,EAAe,CAC1B,oJACA,qCACA,kCACA,qCACA,0EACA,0DACA,uEACA,6B,YCvBK,MAAM/C,EAAYV,EAAAA,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,QACtBoB,EAAUvB,EAAAA,GAAOW,IAAGa,IAAAA,GAAArB,EAAAA,EAAAA,GAAA,0OAcpBkD,EAAYrD,EAAAA,GAAOW,IAAGgB,IAAAA,GAAAxB,EAAAA,EAAAA,GAAA,omBAWnBC,IAAA,IAAC,QAAEsD,GAAStD,EAAA,OAAMsD,EAAU,SAAW,WAQxCC,IAAA,IAAG/C,OAAO,KAAEC,IAAQ8C,EAAA,OAAK9C,EAAKE,SAOrB6C,IAAA,IAAGhD,OAAO,OAAEQ,IAAUwC,EAAA,OAAKxC,EAAOC,KAAKC,QAgBlDuC,EAAmB7D,EAAAA,GAAOW,IAAGmB,IAAAA,GAAA3B,EAAAA,EAAAA,GAAA,mWAOlB2D,IAAA,IAAGlD,OAAO,OAAEQ,IAAU0C,EAAA,OAAK1C,EAAOC,KAAK0C,UC4C/D,EAlGcC,KACZ,MAAOC,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,GAG3C,OACE/B,EAAAA,EAAAA,MAAC5B,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAAC6D,EAAAA,EAAQ,CAAA9D,UACPC,EAAAA,EAAAA,KAAC8B,EAAQ,OAEXD,EAAAA,EAAAA,MAAC5B,EAAc,CAAAF,SAAA,EACb8B,EAAAA,EAAAA,MAAC5B,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAACvB,MAAM,YACvBR,EAAAA,EAAAA,KAACC,EAAuB,CAACC,UAAU,mBAAkBH,UACnDC,EAAAA,EAAAA,KAAC8D,EAAS,CACRd,KAAK,kYAMXnB,EAAAA,EAAAA,MAAC5B,EAAgB,CACf8D,aAAcA,IAAMJ,GAAa,GACjCK,aAAcA,IAAML,GAAa,GACjCR,QAASO,EAAU3D,SAAA,EAEnBC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAACvB,MAAM,cACvBR,EAAAA,EAAAA,KAACC,EAAuB,CAACC,UAAU,mBAAkBH,UACnDC,EAAAA,EAAAA,KAAC8D,EAAS,CACRd,KAAK,0jDAsBXnB,EAAAA,EAAAA,MAAC5B,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAACvB,MAAM,qBACvBR,EAAAA,EAAAA,KAACC,EAAuB,CAACC,UAAU,mBAAkBH,UACnDC,EAAAA,EAAAA,KAAC8D,EAAS,CACRd,KAAK,ihBAMXnB,EAAAA,EAAAA,MAAC5B,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAACvB,MAAM,gBACvBR,EAAAA,EAAAA,KAACC,EAAuB,CAACC,UAAU,mBAAkBH,UACnDC,EAAAA,EAAAA,KAAC8D,EAAS,CACRd,KAAK,8fAKXnB,EAAAA,EAAAA,MAAC5B,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAACvB,MAAM,sBACvBR,EAAAA,EAAAA,KAACC,EAAuB,CAACC,UAAU,mBAAkBH,UACnDC,EAAAA,EAAAA,KAAA,OAAKiE,MAAO,CAAEC,QAAS,UAAWnE,UAChCC,EAAAA,EAAAA,KAAA,MAAAD,SACwB,OAArBkD,QAAqB,IAArBA,OAAqB,EAArBA,EAAuBN,KAAI,CAACC,EAAMC,KAC1B7C,EAAAA,EAAAA,KAAA,MAAAD,SAAiB6C,GAARC,eAM1BhB,EAAAA,EAAAA,MAAC5B,EAAgB,CAAAF,SAAA,EACfC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAACvB,MAAM,iBACvBR,EAAAA,EAAAA,KAACC,EAAuB,CAACC,UAAU,mBAAkBH,UACnDC,EAAAA,EAAAA,KAAA,OAAKiE,MAAO,CAAEC,QAAS,UAAWnE,UAChCC,EAAAA,EAAAA,KAAA,MAAAD,SACe,OAAZmD,QAAY,IAAZA,OAAY,EAAZA,EAAcP,KAAI,CAACC,EAAMC,KACjB7C,EAAAA,EAAAA,KAAA,MAAAD,SAAiB6C,GAARC,qBCvFlC,EARkBsB,KAEdnE,EAAAA,EAAAA,KAAA,OAAKE,UAAU,YAAWH,UACxBC,EAAAA,EAAAA,KAACyD,EAAK,K", "sources": ["components/global/seaction/styles.js", "components/global/seaction/index.jsx", "components/global/seaction-title/styles.js", "components/global/seaction-title/index.jsx", "components/about/card/data.js", "components/about/card/styles.js", "components/about/card/card.jsx", "components/about/card/list.jsx", "components/about/paragraph/styles.js", "components/about/paragraph/index.jsx", "components/about/data.js", "components/about/styles.js", "components/about/index.jsx", "pages/about/index.jsx"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Section = styled.section`\n  display: ${({ flex }) => (flex ? \" flex\" : \"block\")};\n  align-items: flex-start;\n`;\n", "import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst Seaction = ({ children, flex }) => {\n  return (\n    <Styles.Section className=\"py-5\" flex={flex}>\n      {children}\n    </Styles.Section>\n  );\n};\n\nexport default Seaction;\n", "import styled from 'styled-components';\n\nexport const Container = styled.div`\n    text-align: center;\n    padding: 20px 0 30px;\n\n    font-size: ${({ theme: { font } }) => font.big};\n    font-weight: bold;\n`;", "import React from 'react'\n\nimport * as Styles from './styles';\n\nconst SeactionHeading = ({ title }) => {\n    return (\n        <Styles.Container>\n            {title}\n        </Styles.Container>\n    )\n}\n\nexport default SeactionHeading", "export const cardData = [\n  {\n    name: \"Mr.<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    words:\n      \"Mr. <PERSON><PERSON><PERSON><PERSON><PERSON> has pursued his graduation in Civil engineering from Shivaji University. He is having 15 years of experience in High-rise construction as well as retrofitting, structural strengthening.\",\n  },\n  {\n    name: \"Mr. <PERSON><PERSON>\",\n    words:\n      \"Mr. <PERSON> graduated from Mumbai University is been with the firm for past 7 years. His hardworking nature and striving to accomplish the given work in time, is what very valued and inspires the firm.He is capable of catering the various Detailing requirements for industries. His expertise in billing of work on construction site.\",\n  },\n  {\n    name: \"Mr. <PERSON><PERSON> <PERSON><PERSON> (Our Associate)\",\n    words:\n      \"Mr. <PERSON><PERSON> <PERSON><PERSON> holds a M.E. (structure), from Sardar Patel college of Engineering, B.E. (civil) in Datta Megha College of Engineering from Mumbai. He also is an authorized Charted Engineer on board Institution of Engineers . He has in-depth knowledge of RCC Design, FRP Design, Civil construction, which includes Retrofitting, Structural Strengthening, Environmental coatings and rehabilitation of old/damaged structures.\",\n  },\n  {\n    name: \"Mr <PERSON><PERSON><PERSON>\",\n    words:\n      \"Mr. <PERSON><PERSON><PERSON> has pursued his graduation in Civil Engineering from Mumbai University. He has more than 15 years of experience in field of Fit-out, High Rise Construction, Restoration, Rehabilitation projects across India\",\n  },\n];\n", "import styled from \"styled-components\";\n\nexport const CardContainer = styled.div`\n  padding: 40px 30px;\n  background-color: #fff;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);\n\n  text-align: center;\n\n  border-radius: 20px;\n\n  margin: 0 10px;\n\n  flex: 1;\n  height: 100%;\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: #fff;\n  }\n\n  transition: all 0.2s ease-in-out;\n`;\n\nexport const Wrapper = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nexport const CardTitle = styled.h4`\n  font-weight: 600;\n`;\n\nexport const TextHolder = styled.p`\n  flex: 1;\n`;\n\nexport const ListContainer = styled.div``;\n\nexport const ListWrapper = styled.div`\n  .react-multi-carousel-track {\n    /* gap: 20px; */\n  }\n`;\n", "import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst AboutCard = ({ data }) => {\n  return (\n    <Styles.CardContainer>\n      <Styles.Wrapper>\n        <Styles.CardTitle>{data.name}</Styles.CardTitle>\n        <Styles.TextHolder>{data.words}</Styles.TextHolder>\n      </Styles.Wrapper>\n    </Styles.CardContainer>\n  );\n};\n\nexport default AboutCard;\n", "import React from \"react\";\n\nimport SeactionHeading from \"../../global/seaction-title\";\nimport Carousel from \"react-multi-carousel\";\nimport \"react-multi-carousel/lib/styles.css\";\nimport { cardData } from \"./data\";\n\nimport AboutCard from \"./card\";\n\nimport * as Styles from \"./styles\";\n\nconst CardList = () => {\n  const responsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: { max: 4000, min: 3000 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    desktop: {\n      breakpoint: { max: 3000, min: 1024 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    tablet: {\n      breakpoint: { max: 1024, min: 664 },\n      items: 2,\n      slidesToSlide: 2,\n    },\n    mobile: {\n      breakpoint: { max: 664, min: 0 },\n      items: 1,\n      slidesToSlide: 1,\n    },\n  };\n\n  return (\n    <Styles.ListContainer>\n      <SeactionHeading title=\"Our Team\" />\n      <Styles.ListWrapper>\n        <Carousel responsive={responsive}>\n          {cardData.map((item, index) => {\n            return <AboutCard key={index} data={item} />;\n          })}\n        </Carousel>\n      </Styles.ListWrapper>\n    </Styles.ListContainer>\n  );\n};\n\nexport default CardList;\n", "import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  /* text-align: center; */\n  flex: 1;\n\n  min-width: 50%;\n\n  height: 100%;\n\n`;\n\nexport const TitleHolder = styled.h2``;\n\nexport const TextHolder = styled.p``;\n", "import React from \"react\";\n\nimport SeactionHeading from \"../../global/seaction-title\";\n\nimport * as Styles from \"./styles\";\n\nconst ParaGraph = ({ title, text }) => {\n  return (\n    <Styles.Container className=\"px-md-5\">\n      {title ? <SeactionHeading title={title} /> : null}\n      <Styles.TextHolder>{text}</Styles.TextHolder>\n    </Styles.Container>\n  );\n};\n\nexport default ParaGraph;\n", "export const solutionProcidersData = [\n  \"Low Concrete Strength\",\n  \"Additional Loading\",\n  \"Missing / Less Reinforcement Provided \",\n  \"Vibration Control\",\n  \"Change in Code\",\n  \" Blast / Seismic Upgrade\",\n  \"Fire Damaged Structure\",\n  \"Self-Healing Coatings, Anti-corrosive & Anti-carbonation Coatings\",\n  \"Acidic removal from concrete surface\",\n  \"Specialized Grouting\",\n  \"Seismic Upgrade\",\n  \"Waterproofing\",\n  \"Structural Steel And Fabrication\",\n  \"corrosion Repair\",\n];\n\nexport const servicesData = [\n  \"Retrofitting / Structural Strengthening work of Cement and Chemical Plants, Silo &  Chimney, Pharmaceutical companies, Jetties, Dams and Bridges.\",\n  \"Specialized Fiber wrapping systems\",\n  \"Specialized Waterproofing Works\",\n  \"Specialized Environmental Coatings\",\n  \"Repairs/Rehabilitation of Residential Buildings and Heritage Structures\",\n  \"Industrial Buildings - Factories, Plants and Warehouses\",\n  \"Commercial Buildings - Commercial Complexes, Retails and Multiplexes\",\n  \"Public Buildings - Health\",\n];\n", "import styled from \"styled-components\";\n\nexport const Container = styled.div``;\nexport const Wrapper = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n`;\n\nexport const AboutCard = styled.div`\n  background-color: #ffffff;\n\n  display: flex;\n  flex-direction: column;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);\n\n  min-height: 250px;\n  max-height: ${({ hovered }) => (hovered ? \"1000px\" : \"465px\")};\n\n  margin: 0 10px;\n\n  overflow: hidden;\n\n  padding: 20px;\n\n  font-size: ${({ theme: { font } }) => font.title};\n  font-weight: 700;\n\n  text-align: left;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: #ffffff;\n\n    .paragraph-holder {\n      &::before {\n        top: 0;\n        left: 0;\n      }\n    }\n  }\n\n  position: relative;\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const ParagraphWrapper = styled.div`\n  height: 100%;\n\n  overflow-y: auto;\n\n  &::before {\n    content: \"\";\n    background-color: ${({ theme: { colors } }) => colors.main.yellow};\n    width: 50px;\n    height: 50px;\n    position: absolute;\n    top: -50px;\n    left: -50px;\n    transition: all 0.3s ease-in-out;\n    border-bottom-right-radius: 10px;\n  }\n\n  & > div {\n    height: 100%;\n    overflow-y: auto;\n  }\n`;\n", "import React, { useState } from \"react\";\n\nimport Seaction from \"../../components/global/seaction\";\nimport CardList from \"../../components/about/card/list\";\n\nimport SeactionHeading from \"../../components/global/seaction-title\";\n\nimport ParaGraph from \"../../components/about/paragraph\";\nimport { solutionProcidersData, servicesData } from \"./data\";\n\nimport * as Styles from \"./styles\";\n\nconst About = () => {\n  const [isHovered, setIsHovered] = useState(false);\n  \n\n  return (\n    <Styles.Container>\n      <Seaction>\n        <CardList />\n      </Seaction>\n      <Styles.Wrapper>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Vision\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <ParaGraph\n              text=\"To be a leader in providing quality civil engineering solutions by integrating state of the art \n                    techniques by using eficient human resources by specialized training and quality material. To be a leader in providing quality civil engineering solutions by integrating state of the art \n                    techniques by using eficient human resources by specialized training and quality material.\"\n            />\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          hovered={isHovered}\n        >\n          <SeactionHeading title=\"About Us\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <ParaGraph\n              text=\"CapaStrength is one of India's leading providers of Retrotting/Structural Strengthening \n                    services for structures. The company delivers engineering solutions with a focus on \n                    technical quality and eficiency while ensuring on-time completion of projects.\n                    We undertake a variety of projects for a wide range of cliental from small residential \n                    societies to large commercial projects. It includes projects that come under diverse \n                    Categories such as high-rise Residential Buildings, Commercial Buildings, Cement \n                    Chemical Plants, Bridges, Power Plants, Jetties, Five Star Hotels, Temples, Heritage \n                    structures etc. We have been focused on Rehabilitation of damaged structures i.e. \n                    retroctting and structural strengthening works. Our unique and yexible project \n                    management systems ensure that a positive outcome is achieved regardless of size or \n                    nature of the project. \n                    Our philosophy of 'No Compromise' has come a long way implanting our strong \n                    commitment to highest standards of excellence and ethics. It motivates innovation \n                    and people development, which in turn lead to superior quality and services. It has \n                    also resulted in rehabilitating projects at par with top standards and maximum customer satisfaction.Our strong growth has been as a result of consistently delivering quality works both on \n                    time, on budget and high level of safety. Over the years, we have developed and implemented anintegrated management \n                    system that ensures our work, health and safety, quality and environmental \n                    obligations which are not only met but also continue to be monitored and improved \n                    upon.\"\n            />\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Founder Message\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <ParaGraph\n              text=\"After providing service more than 10 years as “RESCON” now we reformed as a   “CAPASTRENGTH”. To undertake Retrofitting, Structural Strengthening of civil structures. Since then, numerous projects have been successfully undertaken and completed.\n                We shall contribute towards creating sustainable solutions to Better, Safer and Healthier life for people. . .\n                We constantly focus on the development of all our employees through training to make them capable enough to deliver their best while converti\"\n            />\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Managemnet\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <ParaGraph\n              text=\"We have in-depth knowledge of Structural Design, FRP Design, Civil construction, which includes Retrofitting, Structural Strengthening, Environmental coatings and rehabilitation of old/damaged structures waterproofing. This has allowed to lead Capastrength from the front.\n                Capastrength has undertaken several successful projects for eminent entities such as Johnson and Johnson, Bayer, provinces Land Hotels, MRF, Embassy Siyaram, 24x7 Services. Capastrength continues to grow and flourish under gui\"\n            />\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Service Provider\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <div style={{ padding: \"0 30px\" }}>\n              <ul>\n                {solutionProcidersData?.map((item, index) => {\n                  return <li key={index}>{item}</li>;\n                })}\n              </ul>\n            </div>\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Our Service\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <div style={{ padding: \"0 30px\" }}>\n              <ul>\n                {servicesData?.map((item, index) => {\n                  return <li key={index}>{item}</li>;\n                })}\n              </ul>\n            </div>\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n      </Styles.Wrapper>\n    </Styles.Container>\n  );\n};\n\nexport default About;\n", "import React from \"react\";\n\nimport About from \"../../components/about\";\n\nconst AboutPage = () => {\n  return (\n    <div className=\"container\">\n      <About />\n    </div>\n  );\n};\n\nexport default AboutPage;\n"], "names": ["Section", "styled", "section", "_templateObject", "_taggedTemplateLiteral", "_ref", "flex", "children", "_jsx", "Styles", "className", "Container", "div", "theme", "font", "big", "title", "cardData", "name", "words", "CardContainer", "colors", "main", "blue", "Wrapper", "_templateObject2", "CardTitle", "h4", "_templateObject3", "TextHolder", "p", "_templateObject4", "ListContainer", "_templateObject5", "ListWrapper", "_templateObject6", "data", "_jsxs", "CardList", "SeactionHeading", "Carousel", "responsive", "superLargeDesktop", "breakpoint", "max", "min", "items", "slidesToSlide", "desktop", "tablet", "mobile", "map", "item", "index", "AboutCard", "h2", "text", "solutionProcidersData", "servicesData", "hovered", "_ref2", "_ref3", "ParagraphWrapper", "_ref4", "yellow", "About", "isHovered", "setIsHovered", "useState", "Seaction", "ParaGraph", "onMouseEnter", "onMouseLeave", "style", "padding", "AboutPage"], "sourceRoot": ""}