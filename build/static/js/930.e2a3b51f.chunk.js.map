{"version": 3, "file": "static/js/930.e2a3b51f.chunk.js", "mappings": "mJAEO,MAAMA,E,QAAUC,GAAOC,QAAOC,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,uDACxBC,IAAA,IAAC,KAAEC,GAAMD,EAAA,OAAMC,EAAO,QAAU,W,aCC7C,MAQA,EARiBD,IAAyB,IAAxB,SAAEE,EAAQ,KAAED,GAAMD,EAClC,OACEG,EAAAA,EAAAA,KAACC,EAAc,CAACC,UAAU,OAAOJ,KAAMA,EAAKC,SACzCA,I,4GCLA,MAAMI,EAAYV,EAAAA,GAAOW,IAAGT,IAAAA,GAAAC,EAAAA,EAAAA,GAAA,gBAItBS,EAAcZ,EAAAA,GAAOa,EAACC,IAAAA,GAAAX,EAAAA,EAAAA,GAAA,cAItBY,EAAaf,EAAAA,GAAOgB,GAAEC,IAAAA,GAAAd,EAAAA,EAAAA,GAAA,sCAItBe,EAAWlB,EAAAA,GAAOmB,GAAEC,IAAAA,GAAAjB,EAAAA,EAAAA,GAAA,gCAIpBkB,EAASrB,EAAAA,GAAOsB,OAAMC,IAAAA,GAAApB,EAAAA,EAAAA,GAAA,uIACXC,IAAA,IAAGoB,OAAO,OAAEC,IAAUrB,EAAA,OAAKqB,EAAOC,KAAKC,U,aCb/D,MA+CA,EA/CcC,KAENC,EAAAA,EAAAA,MAACrB,EAAgB,CAACC,UAAU,UAASH,SAAA,EACjCC,EAAAA,EAAAA,KAACC,EAAkB,CAAAF,SAAC,kCAGpBC,EAAAA,EAAAA,KAACC,EAAiB,CAAAF,UACdC,EAAAA,EAAAA,KAACC,EAAe,CAAAF,SAAC,w5HA+BrBC,EAAAA,EAAAA,KAACC,EAAa,CAAAF,UACVC,EAAAA,EAAAA,KAACuB,EAAAA,GAAI,CAACC,GAAG,IAAGzB,SAAC,yBC7B7B,EAVkB0B,KAEVzB,EAAAA,EAAAA,KAAA,OAAKE,UAAU,YAAWH,UACtBC,EAAAA,EAAAA,KAAC0B,EAAAA,EAAQ,CAAA3B,UACLC,EAAAA,EAAAA,KAACqB,EAAK,O", "sources": ["components/global/seaction/styles.js", "components/global/seaction/index.jsx", "components/t&c/styles.js", "components/t&c/index.jsx", "pages/t&c/index.jsx"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Section = styled.section`\n  display: ${({ flex }) => (flex ? \" flex\" : \"block\")};\n  align-items: flex-start;\n`;\n", "import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst Seaction = ({ children, flex }) => {\n  return (\n    <Styles.Section className=\"py-5\" flex={flex}>\n      {children}\n    </Styles.Section>\n  );\n};\n\nexport default Seaction;\n", "import styled from 'styled-components';\n\nexport const Container = styled.div`\n    \n`;\n\nexport const TitleHolder = styled.p`\n  \n`;\n\nexport const ListHolder = styled.ul`\n    list-style-type: disc;\n`;\n\nexport const ListItem = styled.li`\n    padding: 20px 0;\n`;\n\nexport const Button = styled.button`\n    background-color: ${({ theme: { colors } }) => colors.main.yellow};\n    padding: 10px 20px;\n    border-radius: 5px;\n    border: none;\n\n    margin: 50px 0 20px;\n`;", "import React from 'react'\n\nimport { Link } from 'react-router-dom';\n\nimport * as Styles from './styles'\n\nconst TandC = () => {\n    return (\n        <Styles.Container className=\"px-md-5\">\n            <Styles.TitleHolder>\n                last updated : April 28,2024\n            </Styles.TitleHolder>\n            <Styles.ListHolder>\n                <Styles.ListItem>\n                **CapaStrength Terms and Conditions**\n\nWelcome to CapaStrength! These Terms and Conditions (\"Terms\") govern your use of the CapaStrength website (the \"Website\"), provided to you by CapaStrength (\"we\", \"us\", or \"our\"). By accessing or using the Website, you agree to be bound by these Terms. Please read them carefully before using the Website.\n\n2. **Use of the Website:** You agree to use the Website only for lawful purposes and in accordance with these Terms. You are solely responsible for all content you post, upload, or otherwise transmit through the Website.\n\n3. **Intellectual Property:** The Website and its original content, features, and functionality are owned by CapaStrength and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.\n\n4. **User Accounts:** In order to access certain features of the Website, you may be required to create an account. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer, and you agree to accept responsibility for all activities that occur under your account or password.\n\n5. **Privacy:** Your use of the Website is also subject to our Privacy Policy. Please review our Privacy Policy, which governs the use of personal information on the Website.\n\n6. **Links to Third-Party Websites:** The Website may contain links to third-party websites or services that are not owned or controlled by CapaStrength. CapaStrength has no control over, and assumes no responsibility for, the content, privacy policies, or practices of any third-party websites or services. You further acknowledge and agree that CapaStrength shall not be responsible or liable, directly or indirectly, for any damage or loss caused or alleged to be caused by or in connection with the use of or reliance on any such content, goods, or services available on or through any such websites or services.\n\n7. **Disclaimer of Warranties:** The Website is provided \"as is\" and \"as available\" without warranties of any kind, either express or implied, including, but not limited to, implied warranties of merchantability, fitness for a particular purpose, and non-infringement.\n\n8. **Limitation of Liability:** In no event shall CapaStrength, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from (i) your access to or use of or inability to access or use the Website; (ii) any conduct or content of any third party on the Website; (iii) any content obtained from the Website; and (iv) unauthorized access, use, or alteration of your transmissions or content, whether based on warranty, contract, tort (including negligence), or any other legal theory, whether or not we have been informed of the possibility of such damage, and even if a remedy set forth herein is found to have failed of its essential purpose.\n\n9. **Indemnification:** You agree to indemnify, defend, and hold harmless CapaStrength and its affiliates, officers, directors, employees, agents, licensors, and suppliers from and against any claims, liabilities, damages, judgments, awards, losses, costs, expenses, or fees (including reasonable attorneys' fees) arising out of or relating to your violation of these Terms or your use of the Website.\n\n10. **Governing Law:** These Terms shall be governed and construed in accordance with the laws of India, without regard to its conflict of law provisions.\n\n11. **Entire Agreement:** These Terms constitute the entire agreement between you and CapaStrength regarding the use of the Website, superseding any prior agreements between you and CapaStrength regarding the Website.\n\nIf you have any questions about these Terms, please contact us at [<EMAIL>]. \n\nLast updated: [28th April 2024]\n\n</Styles.ListItem>\n            </Styles.ListHolder>\n            <Styles.Button>\n                <Link to=\"/\">\n                    Go to Main Page\n                </Link>\n            </Styles.Button>\n        </Styles.Container>\n    )\n}\n\nexport default TandC", "import React from 'react'\n\nimport Seaction from '../../components/global/seaction'\n\nimport TandC from '../../components/t&c';\n\nconst TandCPage = () => {\n    return (\n        <div className=\"container\">\n            <Seaction>\n                <TandC />\n            </Seaction>\n        </div>\n    )\n}\n\nexport default TandCPage"], "names": ["Section", "styled", "section", "_templateObject", "_taggedTemplateLiteral", "_ref", "flex", "children", "_jsx", "Styles", "className", "Container", "div", "TitleHolder", "p", "_templateObject2", "ListHolder", "ul", "_templateObject3", "ListItem", "li", "_templateObject4", "<PERSON><PERSON>", "button", "_templateObject5", "theme", "colors", "main", "yellow", "TandC", "_jsxs", "Link", "to", "TandCPage", "Seaction"], "sourceRoot": ""}