{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/index.jsx\",\n    _s = $RefreshSig$();\n\nimport React from \"react\";\nimport { useParams, Link } from \"react-router-dom\";\nimport ImageGallery from \"react-image-gallery\";\nimport { AssetsList } from \"../../elements/assetsList\";\nimport { ProjectData } from \"../projectData\";\nimport ServiceItem from \"./service-item\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ProjectDetails = () => {\n  _s();\n\n  const {\n    id\n  } = useParams();\n  const data = ProjectData.filter(item => parseInt(item.id) === parseInt(id))[0];\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: \"/projects\",\n      children: /*#__PURE__*/_jsxDEV(Styles.BackButtonWrapper, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.IconHolder, {\n          src: AssetsList.chevron.left\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n          bold: true,\n          heading: true,\n          children: data.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.TitleHolder, {\n      children: [/*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n        className: \"mb-2\",\n        bold: true,\n        big: true,\n        children: data.tag\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n        small: true,\n        children: data.location\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.ImageContainer, {\n      children: /*#__PURE__*/_jsxDEV(ImageGallery, {\n        items: data.banner,\n        originalHeight: \"100%\",\n        originalWidth: \"100%\",\n        showThumbnails: false,\n        showPlayButton: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.DetailsWrapper, {\n      children: [/*#__PURE__*/_jsxDEV(Styles.DetailsItemHolder, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n          bold: true,\n          heading: true,\n          children: \"Location :\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n          heading: true,\n          children: data.location\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.DetailsItemHolder, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n          bold: true,\n          heading: true,\n          children: \"Description :\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n          heading: true,\n          children: data.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.DetailsItemHolder, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n          bold: true,\n          heading: true,\n          children: \"Services :\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.ServiceListWrapper, {\n          children: data.services.map((item, key) => {\n            return /*#__PURE__*/_jsxDEV(ServiceItem, {\n              title: item\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 22\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n\n_s(ProjectDetails, \"yQgCIz/jJfqV1l9s2yoba81MT5A=\", false, function () {\n  return [useParams];\n});\n\n_c = ProjectDetails;\nexport default ProjectDetails;\n\nvar _c;\n\n$RefreshReg$(_c, \"ProjectDetails\");", "map": {"version": 3, "names": ["React", "useParams", "Link", "ImageGallery", "AssetsList", "ProjectData", "ServiceItem", "Styles", "ProjectDetails", "id", "data", "filter", "item", "parseInt", "chevron", "left", "title", "tag", "location", "banner", "description", "services", "map", "key"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { usePara<PERSON>, Link } from \"react-router-dom\";\n\nimport ImageGallery from \"react-image-gallery\";\n\nimport { AssetsList } from \"../../elements/assetsList\";\n\nimport { ProjectData } from \"../projectData\";\n\nimport ServiceItem from \"./service-item\";\n\nimport * as Styles from \"./styles\";\n\nconst ProjectDetails = () => {\n  const { id } = useParams();\n\n  const data = ProjectData.filter(\n    (item) => parseInt(item.id) === parseInt(id)\n  )[0];\n\n  return (\n    <Styles.Container>\n      <Link to=\"/projects\">\n        <Styles.BackButtonWrapper>\n          <Styles.IconHolder src={AssetsList.chevron.left} />\n          <Styles.TextHolder bold heading>\n            {data.title}\n          </Styles.TextHolder>\n        </Styles.BackButtonWrapper>\n      </Link>\n      <Styles.TitleHolder>\n        <Styles.TextHolder className=\"mb-2\" bold big>\n         {data.tag}\n        </Styles.TextHolder>\n        <Styles.TextHolder small>{data.location}</Styles.TextHolder>\n      </Styles.TitleHolder>\n      <Styles.ImageContainer>\n        <ImageGallery\n          items={data.banner}\n          originalHeight={\"100%\"}\n          originalWidth={\"100%\"}\n          showThumbnails={false}\n          showPlayButton={false}\n        />\n        {/* <Styles.ImageHolder src={data.banner} /> */}\n      </Styles.ImageContainer>\n      <Styles.DetailsWrapper>\n        <Styles.DetailsItemHolder>\n          <Styles.TextHolder bold heading>\n            Location :\n          </Styles.TextHolder>\n          <Styles.TextHolder heading>{data.location}</Styles.TextHolder>\n        </Styles.DetailsItemHolder>\n        <Styles.DetailsItemHolder>\n          <Styles.TextHolder bold heading>\n            Description :\n          </Styles.TextHolder>\n          <Styles.TextHolder heading>{data.description}</Styles.TextHolder>\n        </Styles.DetailsItemHolder>\n        <Styles.DetailsItemHolder>\n          <Styles.TextHolder bold heading>\n            Services :\n          </Styles.TextHolder>\n          <Styles.ServiceListWrapper>\n            {data.services.map((item, key) => {\n              return <ServiceItem key={key} title={item} />;\n            })}\n          </Styles.ServiceListWrapper>\n        </Styles.DetailsItemHolder>\n      </Styles.DetailsWrapper>\n    </Styles.Container>\n  );\n};\n\nexport default ProjectDetails;\n"], "mappings": ";;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,SAAT,EAAoBC,IAApB,QAAgC,kBAAhC;AAEA,OAAOC,YAAP,MAAyB,qBAAzB;AAEA,SAASC,UAAT,QAA2B,2BAA3B;AAEA,SAASC,WAAT,QAA4B,gBAA5B;AAEA,OAAOC,WAAP,MAAwB,gBAAxB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,cAAc,GAAG,MAAM;EAAA;;EAC3B,MAAM;IAAEC;EAAF,IAASR,SAAS,EAAxB;EAEA,MAAMS,IAAI,GAAGL,WAAW,CAACM,MAAZ,CACVC,IAAD,IAAUC,QAAQ,CAACD,IAAI,CAACH,EAAN,CAAR,KAAsBI,QAAQ,CAACJ,EAAD,CAD7B,EAEX,CAFW,CAAb;EAIA,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,wBACE,QAAC,IAAD;MAAM,EAAE,EAAC,WAAT;MAAA,uBACE,QAAC,MAAD,CAAQ,iBAAR;QAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;UAAmB,GAAG,EAAEL,UAAU,CAACU,OAAX,CAAmBC;QAA3C;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,UAAR;UAAmB,IAAI,MAAvB;UAAwB,OAAO,MAA/B;UAAA,UACGL,IAAI,CAACM;QADR;UAAA;UAAA;UAAA;QAAA,QAFF;MAAA;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QADF,eASE,QAAC,MAAD,CAAQ,WAAR;MAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;QAAmB,SAAS,EAAC,MAA7B;QAAoC,IAAI,MAAxC;QAAyC,GAAG,MAA5C;QAAA,UACEN,IAAI,CAACO;MADP;QAAA;QAAA;QAAA;MAAA,QADF,eAIE,QAAC,MAAD,CAAQ,UAAR;QAAmB,KAAK,MAAxB;QAAA,UAA0BP,IAAI,CAACQ;MAA/B;QAAA;QAAA;QAAA;MAAA,QAJF;IAAA;MAAA;MAAA;MAAA;IAAA,QATF,eAeE,QAAC,MAAD,CAAQ,cAAR;MAAA,uBACE,QAAC,YAAD;QACE,KAAK,EAAER,IAAI,CAACS,MADd;QAEE,cAAc,EAAE,MAFlB;QAGE,aAAa,EAAE,MAHjB;QAIE,cAAc,EAAE,KAJlB;QAKE,cAAc,EAAE;MALlB;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QAfF,eAyBE,QAAC,MAAD,CAAQ,cAAR;MAAA,wBACE,QAAC,MAAD,CAAQ,iBAAR;QAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;UAAmB,IAAI,MAAvB;UAAwB,OAAO,MAA/B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QADF,eAIE,QAAC,MAAD,CAAQ,UAAR;UAAmB,OAAO,MAA1B;UAAA,UAA4BT,IAAI,CAACQ;QAAjC;UAAA;UAAA;UAAA;QAAA,QAJF;MAAA;QAAA;QAAA;QAAA;MAAA,QADF,eAOE,QAAC,MAAD,CAAQ,iBAAR;QAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;UAAmB,IAAI,MAAvB;UAAwB,OAAO,MAA/B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QADF,eAIE,QAAC,MAAD,CAAQ,UAAR;UAAmB,OAAO,MAA1B;UAAA,UAA4BR,IAAI,CAACU;QAAjC;UAAA;UAAA;UAAA;QAAA,QAJF;MAAA;QAAA;QAAA;QAAA;MAAA,QAPF,eAaE,QAAC,MAAD,CAAQ,iBAAR;QAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;UAAmB,IAAI,MAAvB;UAAwB,OAAO,MAA/B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QADF,eAIE,QAAC,MAAD,CAAQ,kBAAR;UAAA,UACGV,IAAI,CAACW,QAAL,CAAcC,GAAd,CAAkB,CAACV,IAAD,EAAOW,GAAP,KAAe;YAChC,oBAAO,QAAC,WAAD;cAAuB,KAAK,EAAEX;YAA9B,GAAkBW,GAAlB;cAAA;cAAA;cAAA;YAAA,QAAP;UACD,CAFA;QADH;UAAA;UAAA;UAAA;QAAA,QAJF;MAAA;QAAA;QAAA;QAAA;MAAA,QAbF;IAAA;MAAA;MAAA;MAAA;IAAA,QAzBF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAoDD,CA3DD;;GAAMf,c;UACWP,S;;;KADXO,c;AA6DN,eAAeA,cAAf"}, "metadata": {}, "sourceType": "module"}