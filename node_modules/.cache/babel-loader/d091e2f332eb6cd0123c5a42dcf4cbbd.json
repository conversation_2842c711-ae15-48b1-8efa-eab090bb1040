{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar React = require(\"react\"),\n    LeftArrow = function (_a) {\n  var customLeftArrow = _a.customLeftArrow,\n      getState = _a.getState,\n      previous = _a.previous,\n      disabled = _a.disabled,\n      rtl = _a.rtl;\n  if (customLeftArrow) return React.cloneElement(customLeftArrow, {\n    onClick: function () {\n      return previous();\n    },\n    carouselState: getState(),\n    disabled: disabled,\n    rtl: rtl\n  });\n  var rtlClassName = rtl ? \"rtl\" : \"\";\n  return React.createElement(\"button\", {\n    \"aria-label\": \"Go to previous slide\",\n    className: \"react-multiple-carousel__arrow react-multiple-carousel__arrow--left \" + rtlClassName,\n    onClick: function () {\n      return previous();\n    },\n    type: \"button\",\n    disabled: disabled\n  });\n};\n\nexports.LeftArrow = LeftArrow;\n\nvar RightArrow = function (_a) {\n  var customRightArrow = _a.customRightArrow,\n      getState = _a.getState,\n      next = _a.next,\n      disabled = _a.disabled,\n      rtl = _a.rtl;\n  if (customRightArrow) return React.cloneElement(customRightArrow, {\n    onClick: function () {\n      return next();\n    },\n    carouselState: getState(),\n    disabled: disabled,\n    rtl: rtl\n  });\n  var rtlClassName = rtl ? \"rtl\" : \"\";\n  return React.createElement(\"button\", {\n    \"aria-label\": \"Go to next slide\",\n    className: \"react-multiple-carousel__arrow react-multiple-carousel__arrow--right \" + rtlClassName,\n    onClick: function () {\n      return next();\n    },\n    type: \"button\",\n    disabled: disabled\n  });\n};\n\nexports.RightArrow = RightArrow;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "React", "require", "LeftArrow", "_a", "customLeftArrow", "getState", "previous", "disabled", "rtl", "cloneElement", "onClick", "carouselState", "rtlClassName", "createElement", "className", "type", "RightArrow", "customRightArrow", "next"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/Arrows.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var React=require(\"react\"),LeftArrow=function(_a){var customLeftArrow=_a.customLeftArrow,getState=_a.getState,previous=_a.previous,disabled=_a.disabled,rtl=_a.rtl;if(customLeftArrow)return React.cloneElement(customLeftArrow,{onClick:function(){return previous()},carouselState:getState(),disabled:disabled,rtl:rtl});var rtlClassName=rtl?\"rtl\":\"\";return React.createElement(\"button\",{\"aria-label\":\"Go to previous slide\",className:\"react-multiple-carousel__arrow react-multiple-carousel__arrow--left \"+rtlClassName,onClick:function(){return previous()},type:\"button\",disabled:disabled})};exports.LeftArrow=LeftArrow;var RightArrow=function(_a){var customRightArrow=_a.customRightArrow,getState=_a.getState,next=_a.next,disabled=_a.disabled,rtl=_a.rtl;if(customRightArrow)return React.cloneElement(customRightArrow,{onClick:function(){return next()},carouselState:getState(),disabled:disabled,rtl:rtl});var rtlClassName=rtl?\"rtl\":\"\";return React.createElement(\"button\",{\"aria-label\":\"Go to next slide\",className:\"react-multiple-carousel__arrow react-multiple-carousel__arrow--right \"+rtlClassName,onClick:function(){return next()},type:\"button\",disabled:disabled})};exports.RightArrow=RightArrow;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,KAAK,GAACC,OAAO,CAAC,OAAD,CAAjB;AAAA,IAA2BC,SAAS,GAAC,UAASC,EAAT,EAAY;EAAC,IAAIC,eAAe,GAACD,EAAE,CAACC,eAAvB;EAAA,IAAuCC,QAAQ,GAACF,EAAE,CAACE,QAAnD;EAAA,IAA4DC,QAAQ,GAACH,EAAE,CAACG,QAAxE;EAAA,IAAiFC,QAAQ,GAACJ,EAAE,CAACI,QAA7F;EAAA,IAAsGC,GAAG,GAACL,EAAE,CAACK,GAA7G;EAAiH,IAAGJ,eAAH,EAAmB,OAAOJ,KAAK,CAACS,YAAN,CAAmBL,eAAnB,EAAmC;IAACM,OAAO,EAAC,YAAU;MAAC,OAAOJ,QAAQ,EAAf;IAAkB,CAAtC;IAAuCK,aAAa,EAACN,QAAQ,EAA7D;IAAgEE,QAAQ,EAACA,QAAzE;IAAkFC,GAAG,EAACA;EAAtF,CAAnC,CAAP;EAAsI,IAAII,YAAY,GAACJ,GAAG,GAAC,KAAD,GAAO,EAA3B;EAA8B,OAAOR,KAAK,CAACa,aAAN,CAAoB,QAApB,EAA6B;IAAC,cAAa,sBAAd;IAAqCC,SAAS,EAAC,yEAAuEF,YAAtH;IAAmIF,OAAO,EAAC,YAAU;MAAC,OAAOJ,QAAQ,EAAf;IAAkB,CAAxK;IAAyKS,IAAI,EAAC,QAA9K;IAAuLR,QAAQ,EAACA;EAAhM,CAA7B,CAAP;AAA+O,CAAzkB;;AAA0kBT,OAAO,CAACI,SAAR,GAAkBA,SAAlB;;AAA4B,IAAIc,UAAU,GAAC,UAASb,EAAT,EAAY;EAAC,IAAIc,gBAAgB,GAACd,EAAE,CAACc,gBAAxB;EAAA,IAAyCZ,QAAQ,GAACF,EAAE,CAACE,QAArD;EAAA,IAA8Da,IAAI,GAACf,EAAE,CAACe,IAAtE;EAAA,IAA2EX,QAAQ,GAACJ,EAAE,CAACI,QAAvF;EAAA,IAAgGC,GAAG,GAACL,EAAE,CAACK,GAAvG;EAA2G,IAAGS,gBAAH,EAAoB,OAAOjB,KAAK,CAACS,YAAN,CAAmBQ,gBAAnB,EAAoC;IAACP,OAAO,EAAC,YAAU;MAAC,OAAOQ,IAAI,EAAX;IAAc,CAAlC;IAAmCP,aAAa,EAACN,QAAQ,EAAzD;IAA4DE,QAAQ,EAACA,QAArE;IAA8EC,GAAG,EAACA;EAAlF,CAApC,CAAP;EAAmI,IAAII,YAAY,GAACJ,GAAG,GAAC,KAAD,GAAO,EAA3B;EAA8B,OAAOR,KAAK,CAACa,aAAN,CAAoB,QAApB,EAA6B;IAAC,cAAa,kBAAd;IAAiCC,SAAS,EAAC,0EAAwEF,YAAnH;IAAgIF,OAAO,EAAC,YAAU;MAAC,OAAOQ,IAAI,EAAX;IAAc,CAAjK;IAAkKH,IAAI,EAAC,QAAvK;IAAgLR,QAAQ,EAACA;EAAzL,CAA7B,CAAP;AAAwO,CAApiB;;AAAqiBT,OAAO,CAACkB,UAAR,GAAmBA,UAAnB"}, "metadata": {}, "sourceType": "script"}