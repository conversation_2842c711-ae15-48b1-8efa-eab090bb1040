{"ast": null, "code": "/** @license React vundefined\n * react-refresh-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict'; // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n    // nor polyfill, then a plain number is used for performance.\n\n    var REACT_ELEMENT_TYPE = 0xeac7;\n    var REACT_PORTAL_TYPE = 0xeaca;\n    var REACT_FRAGMENT_TYPE = 0xeacb;\n    var REACT_STRICT_MODE_TYPE = 0xeacc;\n    var REACT_PROFILER_TYPE = 0xead2;\n    var REACT_PROVIDER_TYPE = 0xeacd;\n    var REACT_CONTEXT_TYPE = 0xeace;\n    var REACT_FORWARD_REF_TYPE = 0xead0;\n    var REACT_SUSPENSE_TYPE = 0xead1;\n    var REACT_SUSPENSE_LIST_TYPE = 0xead8;\n    var REACT_MEMO_TYPE = 0xead3;\n    var REACT_LAZY_TYPE = 0xead4;\n    var REACT_SCOPE_TYPE = 0xead7;\n    var REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\n    var REACT_OFFSCREEN_TYPE = 0xeae2;\n    var REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n    var REACT_CACHE_TYPE = 0xeae4;\n\n    if (typeof Symbol === 'function' && Symbol.for) {\n      var symbolFor = Symbol.for;\n      REACT_ELEMENT_TYPE = symbolFor('react.element');\n      REACT_PORTAL_TYPE = symbolFor('react.portal');\n      REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n      REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n      REACT_PROFILER_TYPE = symbolFor('react.profiler');\n      REACT_PROVIDER_TYPE = symbolFor('react.provider');\n      REACT_CONTEXT_TYPE = symbolFor('react.context');\n      REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n      REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n      REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n      REACT_MEMO_TYPE = symbolFor('react.memo');\n      REACT_LAZY_TYPE = symbolFor('react.lazy');\n      REACT_SCOPE_TYPE = symbolFor('react.scope');\n      REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n      REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n      REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n      REACT_CACHE_TYPE = symbolFor('react.cache');\n    }\n\n    var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map; // We never remove these associations.\n    // It's OK to reference families, but use WeakMap/Set for types.\n\n    var allFamiliesByID = new Map();\n    var allFamiliesByType = new PossiblyWeakMap();\n    var allSignaturesByType = new PossiblyWeakMap(); // This WeakMap is read by React, so we only put families\n    // that have actually been edited here. This keeps checks fast.\n    // $FlowIssue\n\n    var updatedFamiliesByType = new PossiblyWeakMap(); // This is cleared on every performReactRefresh() call.\n    // It is an array of [Family, NextType] tuples.\n\n    var pendingUpdates = []; // This is injected by the renderer via DevTools global hook.\n\n    var helpersByRendererID = new Map();\n    var helpersByRoot = new Map(); // We keep track of mounted roots so we can schedule updates.\n\n    var mountedRoots = new Set(); // If a root captures an error, we remember it so we can retry on edit.\n\n    var failedRoots = new Set(); // In environments that support WeakMap, we also remember the last element for every root.\n    // It needs to be weak because we do this even for roots that failed to mount.\n    // If there is no WeakMap, we won't attempt to do retrying.\n    // $FlowIssue\n\n    var rootElements = // $FlowIssue\n    typeof WeakMap === 'function' ? new WeakMap() : null;\n    var isPerformingRefresh = false;\n\n    function computeFullKey(signature) {\n      if (signature.fullKey !== null) {\n        return signature.fullKey;\n      }\n\n      var fullKey = signature.ownKey;\n      var hooks;\n\n      try {\n        hooks = signature.getCustomHooks();\n      } catch (err) {\n        // This can happen in an edge case, e.g. if expression like Foo.useSomething\n        // depends on Foo which is lazily initialized during rendering.\n        // In that case just assume we'll have to remount.\n        signature.forceReset = true;\n        signature.fullKey = fullKey;\n        return fullKey;\n      }\n\n      for (var i = 0; i < hooks.length; i++) {\n        var hook = hooks[i];\n\n        if (typeof hook !== 'function') {\n          // Something's wrong. Assume we need to remount.\n          signature.forceReset = true;\n          signature.fullKey = fullKey;\n          return fullKey;\n        }\n\n        var nestedHookSignature = allSignaturesByType.get(hook);\n\n        if (nestedHookSignature === undefined) {\n          // No signature means Hook wasn't in the source code, e.g. in a library.\n          // We'll skip it because we can assume it won't change during this session.\n          continue;\n        }\n\n        var nestedHookKey = computeFullKey(nestedHookSignature);\n\n        if (nestedHookSignature.forceReset) {\n          signature.forceReset = true;\n        }\n\n        fullKey += '\\n---\\n' + nestedHookKey;\n      }\n\n      signature.fullKey = fullKey;\n      return fullKey;\n    }\n\n    function haveEqualSignatures(prevType, nextType) {\n      var prevSignature = allSignaturesByType.get(prevType);\n      var nextSignature = allSignaturesByType.get(nextType);\n\n      if (prevSignature === undefined && nextSignature === undefined) {\n        return true;\n      }\n\n      if (prevSignature === undefined || nextSignature === undefined) {\n        return false;\n      }\n\n      if (computeFullKey(prevSignature) !== computeFullKey(nextSignature)) {\n        return false;\n      }\n\n      if (nextSignature.forceReset) {\n        return false;\n      }\n\n      return true;\n    }\n\n    function isReactClass(type) {\n      return type.prototype && type.prototype.isReactComponent;\n    }\n\n    function canPreserveStateBetween(prevType, nextType) {\n      if (isReactClass(prevType) || isReactClass(nextType)) {\n        return false;\n      }\n\n      if (haveEqualSignatures(prevType, nextType)) {\n        return true;\n      }\n\n      return false;\n    }\n\n    function resolveFamily(type) {\n      // Only check updated types to keep lookups fast.\n      return updatedFamiliesByType.get(type);\n    } // If we didn't care about IE11, we could use new Map/Set(iterable).\n\n\n    function cloneMap(map) {\n      var clone = new Map();\n      map.forEach(function (value, key) {\n        clone.set(key, value);\n      });\n      return clone;\n    }\n\n    function cloneSet(set) {\n      var clone = new Set();\n      set.forEach(function (value) {\n        clone.add(value);\n      });\n      return clone;\n    } // This is a safety mechanism to protect against rogue getters and Proxies.\n\n\n    function getProperty(object, property) {\n      try {\n        return object[property];\n      } catch (err) {\n        // Intentionally ignore.\n        return undefined;\n      }\n    }\n\n    function performReactRefresh() {\n      if (pendingUpdates.length === 0) {\n        return null;\n      }\n\n      if (isPerformingRefresh) {\n        return null;\n      }\n\n      isPerformingRefresh = true;\n\n      try {\n        var staleFamilies = new Set();\n        var updatedFamilies = new Set();\n        var updates = pendingUpdates;\n        pendingUpdates = [];\n        updates.forEach(function (_ref) {\n          var family = _ref[0],\n              nextType = _ref[1]; // Now that we got a real edit, we can create associations\n          // that will be read by the React reconciler.\n\n          var prevType = family.current;\n          updatedFamiliesByType.set(prevType, family);\n          updatedFamiliesByType.set(nextType, family);\n          family.current = nextType; // Determine whether this should be a re-render or a re-mount.\n\n          if (canPreserveStateBetween(prevType, nextType)) {\n            updatedFamilies.add(family);\n          } else {\n            staleFamilies.add(family);\n          }\n        }); // TODO: rename these fields to something more meaningful.\n\n        var update = {\n          updatedFamilies: updatedFamilies,\n          // Families that will re-render preserving state\n          staleFamilies: staleFamilies // Families that will be remounted\n\n        };\n        helpersByRendererID.forEach(function (helpers) {\n          // Even if there are no roots, set the handler on first update.\n          // This ensures that if *new* roots are mounted, they'll use the resolve handler.\n          helpers.setRefreshHandler(resolveFamily);\n        });\n        var didError = false;\n        var firstError = null; // We snapshot maps and sets that are mutated during commits.\n        // If we don't do this, there is a risk they will be mutated while\n        // we iterate over them. For example, trying to recover a failed root\n        // may cause another root to be added to the failed list -- an infinite loop.\n\n        var failedRootsSnapshot = cloneSet(failedRoots);\n        var mountedRootsSnapshot = cloneSet(mountedRoots);\n        var helpersByRootSnapshot = cloneMap(helpersByRoot);\n        failedRootsSnapshot.forEach(function (root) {\n          var helpers = helpersByRootSnapshot.get(root);\n\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n\n          if (!failedRoots.has(root)) {// No longer failed.\n          }\n\n          if (rootElements === null) {\n            return;\n          }\n\n          if (!rootElements.has(root)) {\n            return;\n          }\n\n          var element = rootElements.get(root);\n\n          try {\n            helpers.scheduleRoot(root, element);\n          } catch (err) {\n            if (!didError) {\n              didError = true;\n              firstError = err;\n            } // Keep trying other roots.\n\n          }\n        });\n        mountedRootsSnapshot.forEach(function (root) {\n          var helpers = helpersByRootSnapshot.get(root);\n\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n\n          if (!mountedRoots.has(root)) {// No longer mounted.\n          }\n\n          try {\n            helpers.scheduleRefresh(root, update);\n          } catch (err) {\n            if (!didError) {\n              didError = true;\n              firstError = err;\n            } // Keep trying other roots.\n\n          }\n        });\n\n        if (didError) {\n          throw firstError;\n        }\n\n        return update;\n      } finally {\n        isPerformingRefresh = false;\n      }\n    }\n\n    function register(type, id) {\n      {\n        if (type === null) {\n          return;\n        }\n\n        if (typeof type !== 'function' && typeof type !== 'object') {\n          return;\n        } // This can happen in an edge case, e.g. if we register\n        // return value of a HOC but it returns a cached component.\n        // Ignore anything but the first registration for each type.\n\n\n        if (allFamiliesByType.has(type)) {\n          return;\n        } // Create family or remember to update it.\n        // None of this bookkeeping affects reconciliation\n        // until the first performReactRefresh() call above.\n\n\n        var family = allFamiliesByID.get(id);\n\n        if (family === undefined) {\n          family = {\n            current: type\n          };\n          allFamiliesByID.set(id, family);\n        } else {\n          pendingUpdates.push([family, type]);\n        }\n\n        allFamiliesByType.set(type, family); // Visit inner types because we might not have registered them.\n\n        if (typeof type === 'object' && type !== null) {\n          switch (getProperty(type, '$$typeof')) {\n            case REACT_FORWARD_REF_TYPE:\n              register(type.render, id + '$render');\n              break;\n\n            case REACT_MEMO_TYPE:\n              register(type.type, id + '$type');\n              break;\n          }\n        }\n      }\n    }\n\n    function setSignature(type, key) {\n      var forceReset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var getCustomHooks = arguments.length > 3 ? arguments[3] : undefined;\n      {\n        if (!allSignaturesByType.has(type)) {\n          allSignaturesByType.set(type, {\n            forceReset: forceReset,\n            ownKey: key,\n            fullKey: null,\n            getCustomHooks: getCustomHooks || function () {\n              return [];\n            }\n          });\n        } // Visit inner types because we might not have signed them.\n\n\n        if (typeof type === 'object' && type !== null) {\n          switch (getProperty(type, '$$typeof')) {\n            case REACT_FORWARD_REF_TYPE:\n              setSignature(type.render, key, forceReset, getCustomHooks);\n              break;\n\n            case REACT_MEMO_TYPE:\n              setSignature(type.type, key, forceReset, getCustomHooks);\n              break;\n          }\n        }\n      }\n    } // This is lazily called during first render for a type.\n    // It captures Hook list at that time so inline requires don't break comparisons.\n\n\n    function collectCustomHooksForSignature(type) {\n      {\n        var signature = allSignaturesByType.get(type);\n\n        if (signature !== undefined) {\n          computeFullKey(signature);\n        }\n      }\n    }\n\n    function getFamilyByID(id) {\n      {\n        return allFamiliesByID.get(id);\n      }\n    }\n\n    function getFamilyByType(type) {\n      {\n        return allFamiliesByType.get(type);\n      }\n    }\n\n    function findAffectedHostInstances(families) {\n      {\n        var affectedInstances = new Set();\n        mountedRoots.forEach(function (root) {\n          var helpers = helpersByRoot.get(root);\n\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n\n          var instancesForRoot = helpers.findHostInstancesForRefresh(root, families);\n          instancesForRoot.forEach(function (inst) {\n            affectedInstances.add(inst);\n          });\n        });\n        return affectedInstances;\n      }\n    }\n\n    function injectIntoGlobalHook(globalObject) {\n      {\n        // For React Native, the global hook will be set up by require('react-devtools-core').\n        // That code will run before us. So we need to monkeypatch functions on existing hook.\n        // For React Web, the global hook will be set up by the extension.\n        // This will also run before us.\n        var hook = globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n\n        if (hook === undefined) {\n          // However, if there is no DevTools extension, we'll need to set up the global hook ourselves.\n          // Note that in this case it's important that renderer code runs *after* this method call.\n          // Otherwise, the renderer will think that there is no global hook, and won't do the injection.\n          var nextID = 0;\n          globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__ = hook = {\n            renderers: new Map(),\n            supportsFiber: true,\n            inject: function (injected) {\n              return nextID++;\n            },\n            onScheduleFiberRoot: function (id, root, children) {},\n            onCommitFiberRoot: function (id, root, maybePriorityLevel, didError) {},\n            onCommitFiberUnmount: function () {}\n          };\n        }\n\n        if (hook.isDisabled) {\n          // This isn't a real property on the hook, but it can be set to opt out\n          // of DevTools integration and associated warnings and logs.\n          // Using console['warn'] to evade Babel and ESLint\n          console['warn']('Something has shimmed the React DevTools global hook (__REACT_DEVTOOLS_GLOBAL_HOOK__). ' + 'Fast Refresh is not compatible with this shim and will be disabled.');\n          return;\n        } // Here, we just want to get a reference to scheduleRefresh.\n\n\n        var oldInject = hook.inject;\n\n        hook.inject = function (injected) {\n          var id = oldInject.apply(this, arguments);\n\n          if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n            // This version supports React Refresh.\n            helpersByRendererID.set(id, injected);\n          }\n\n          return id;\n        }; // Do the same for any already injected roots.\n        // This is useful if ReactDOM has already been initialized.\n        // https://github.com/facebook/react/issues/17626\n\n\n        hook.renderers.forEach(function (injected, id) {\n          if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n            // This version supports React Refresh.\n            helpersByRendererID.set(id, injected);\n          }\n        }); // We also want to track currently mounted roots.\n\n        var oldOnCommitFiberRoot = hook.onCommitFiberRoot;\n\n        var oldOnScheduleFiberRoot = hook.onScheduleFiberRoot || function () {};\n\n        hook.onScheduleFiberRoot = function (id, root, children) {\n          if (!isPerformingRefresh) {\n            // If it was intentionally scheduled, don't attempt to restore.\n            // This includes intentionally scheduled unmounts.\n            failedRoots.delete(root);\n\n            if (rootElements !== null) {\n              rootElements.set(root, children);\n            }\n          }\n\n          return oldOnScheduleFiberRoot.apply(this, arguments);\n        };\n\n        hook.onCommitFiberRoot = function (id, root, maybePriorityLevel, didError) {\n          var helpers = helpersByRendererID.get(id);\n\n          if (helpers !== undefined) {\n            helpersByRoot.set(root, helpers);\n            var current = root.current;\n            var alternate = current.alternate; // We need to determine whether this root has just (un)mounted.\n            // This logic is copy-pasted from similar logic in the DevTools backend.\n            // If this breaks with some refactoring, you'll want to update DevTools too.\n\n            if (alternate !== null) {\n              var wasMounted = alternate.memoizedState != null && alternate.memoizedState.element != null;\n              var isMounted = current.memoizedState != null && current.memoizedState.element != null;\n\n              if (!wasMounted && isMounted) {\n                // Mount a new root.\n                mountedRoots.add(root);\n                failedRoots.delete(root);\n              } else if (wasMounted && isMounted) ;else if (wasMounted && !isMounted) {\n                // Unmount an existing root.\n                mountedRoots.delete(root);\n\n                if (didError) {\n                  // We'll remount it on future edits.\n                  failedRoots.add(root);\n                } else {\n                  helpersByRoot.delete(root);\n                }\n              } else if (!wasMounted && !isMounted) {\n                if (didError) {\n                  // We'll remount it on future edits.\n                  failedRoots.add(root);\n                }\n              }\n            } else {\n              // Mount a new root.\n              mountedRoots.add(root);\n            }\n          } // Always call the decorated DevTools hook.\n\n\n          return oldOnCommitFiberRoot.apply(this, arguments);\n        };\n      }\n    }\n\n    function hasUnrecoverableErrors() {\n      // TODO: delete this after removing dependency in RN.\n      return false;\n    } // Exposed for testing.\n\n\n    function _getMountedRootCount() {\n      {\n        return mountedRoots.size;\n      }\n    } // This is a wrapper over more primitive functions for setting signature.\n    // Signatures let us decide whether the Hook order has changed on refresh.\n    //\n    // This function is intended to be used as a transform target, e.g.:\n    // var _s = createSignatureFunctionForTransform()\n    //\n    // function Hello() {\n    //   const [foo, setFoo] = useState(0);\n    //   const value = useCustomHook();\n    //   _s(); /* Call without arguments triggers collecting the custom Hook list.\n    //          * This doesn't happen during the module evaluation because we\n    //          * don't want to change the module order with inline requires.\n    //          * Next calls are noops. */\n    //   return <h1>Hi</h1>;\n    // }\n    //\n    // /* Call with arguments attaches the signature to the type: */\n    // _s(\n    //   Hello,\n    //   'useState{[foo, setFoo]}(0)',\n    //   () => [useCustomHook], /* Lazy to avoid triggering inline requires */\n    // );\n\n\n    function createSignatureFunctionForTransform() {\n      {\n        var savedType;\n        var hasCustomHooks;\n        var didCollectHooks = false;\n        return function (type, key, forceReset, getCustomHooks) {\n          if (typeof key === 'string') {\n            // We're in the initial phase that associates signatures\n            // with the functions. Note this may be called multiple times\n            // in HOC chains like _s(hoc1(_s(hoc2(_s(actualFunction))))).\n            if (!savedType) {\n              // We're in the innermost call, so this is the actual type.\n              savedType = type;\n              hasCustomHooks = typeof getCustomHooks === 'function';\n            } // Set the signature for all types (even wrappers!) in case\n            // they have no signatures of their own. This is to prevent\n            // problems like https://github.com/facebook/react/issues/20417.\n\n\n            if (type != null && (typeof type === 'function' || typeof type === 'object')) {\n              setSignature(type, key, forceReset, getCustomHooks);\n            }\n\n            return type;\n          } else {\n            // We're in the _s() call without arguments, which means\n            // this is the time to collect custom Hook signatures.\n            // Only do this once. This path is hot and runs *inside* every render!\n            if (!didCollectHooks && hasCustomHooks) {\n              didCollectHooks = true;\n              collectCustomHooksForSignature(savedType);\n            }\n          }\n        };\n      }\n    }\n\n    function isLikelyComponentType(type) {\n      {\n        switch (typeof type) {\n          case 'function':\n            {\n              // First, deal with classes.\n              if (type.prototype != null) {\n                if (type.prototype.isReactComponent) {\n                  // React class.\n                  return true;\n                }\n\n                var ownNames = Object.getOwnPropertyNames(type.prototype);\n\n                if (ownNames.length > 1 || ownNames[0] !== 'constructor') {\n                  // This looks like a class.\n                  return false;\n                } // eslint-disable-next-line no-proto\n\n\n                if (type.prototype.__proto__ !== Object.prototype) {\n                  // It has a superclass.\n                  return false;\n                } // Pass through.\n                // This looks like a regular function with empty prototype.\n\n              } // For plain functions and arrows, use name as a heuristic.\n\n\n              var name = type.name || type.displayName;\n              return typeof name === 'string' && /^[A-Z]/.test(name);\n            }\n\n          case 'object':\n            {\n              if (type != null) {\n                switch (getProperty(type, '$$typeof')) {\n                  case REACT_FORWARD_REF_TYPE:\n                  case REACT_MEMO_TYPE:\n                    // Definitely React components.\n                    return true;\n\n                  default:\n                    return false;\n                }\n              }\n\n              return false;\n            }\n\n          default:\n            {\n              return false;\n            }\n        }\n      }\n    }\n\n    exports._getMountedRootCount = _getMountedRootCount;\n    exports.collectCustomHooksForSignature = collectCustomHooksForSignature;\n    exports.createSignatureFunctionForTransform = createSignatureFunctionForTransform;\n    exports.findAffectedHostInstances = findAffectedHostInstances;\n    exports.getFamilyByID = getFamilyByID;\n    exports.getFamilyByType = getFamilyByType;\n    exports.hasUnrecoverableErrors = hasUnrecoverableErrors;\n    exports.injectIntoGlobalHook = injectIntoGlobalHook;\n    exports.isLikelyComponentType = isLikelyComponentType;\n    exports.performReactRefresh = performReactRefresh;\n    exports.register = register;\n    exports.setSignature = setSignature;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "REACT_ELEMENT_TYPE", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_SCOPE_TYPE", "REACT_DEBUG_TRACING_MODE_TYPE", "REACT_OFFSCREEN_TYPE", "REACT_LEGACY_HIDDEN_TYPE", "REACT_CACHE_TYPE", "Symbol", "for", "symbolFor", "PossiblyWeakMap", "WeakMap", "Map", "allFamiliesByID", "allFamiliesByType", "allSignaturesByType", "updatedFamiliesByType", "pendingUpdates", "helpersByRendererID", "helpersByRoot", "mountedRoots", "Set", "failedRoots", "rootElements", "isPerformingRefresh", "computeFullKey", "signature", "<PERSON><PERSON><PERSON>", "own<PERSON>ey", "hooks", "getCustomHooks", "err", "forceReset", "i", "length", "hook", "nestedHookSignature", "get", "undefined", "nested<PERSON><PERSON><PERSON><PERSON>", "haveEqualSignatures", "prevType", "nextType", "prevSignature", "nextSignature", "isReactClass", "type", "prototype", "isReactComponent", "canPreserveStateBetween", "resolveFamily", "cloneMap", "map", "clone", "for<PERSON>ach", "value", "key", "set", "cloneSet", "add", "getProperty", "object", "property", "performReactRefresh", "staleFamilies", "updatedFamilies", "updates", "_ref", "family", "current", "update", "helpers", "setRefreshHandler", "<PERSON><PERSON><PERSON><PERSON>", "firstError", "failedRootsSnapshot", "mountedRootsSnapshot", "helpersByRootSnapshot", "root", "Error", "has", "element", "scheduleRoot", "scheduleRefresh", "register", "id", "push", "render", "setSignature", "arguments", "collectCustomHooksForSignature", "getFamilyByID", "getFamilyByType", "findAffectedHostInstances", "families", "affectedInstances", "instancesForRoot", "findHostInstancesForRefresh", "inst", "injectIntoGlobalHook", "globalObject", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "nextID", "renderers", "supportsFiber", "inject", "injected", "onScheduleFiberRoot", "children", "onCommitFiberRoot", "maybePriorityLevel", "onCommitFiberUnmount", "isDisabled", "console", "oldInject", "apply", "oldOnCommitFiberRoot", "oldOnScheduleFiberRoot", "delete", "alternate", "wasMounted", "memoizedState", "isMounted", "hasUnrecoverableErrors", "_getMountedRootCount", "size", "createSignatureFunctionForTransform", "savedType", "hasCustomHooks", "didCollectHooks", "isLikelyComponentType", "ownNames", "Object", "getOwnPropertyNames", "__proto__", "name", "displayName", "test", "exports"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-refresh/cjs/react-refresh-runtime.development.js"], "sourcesContent": ["/** @license React vundefined\n * react-refresh-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nvar REACT_FRAGMENT_TYPE = 0xeacb;\nvar REACT_STRICT_MODE_TYPE = 0xeacc;\nvar REACT_PROFILER_TYPE = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nvar REACT_SUSPENSE_TYPE = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\nvar REACT_CACHE_TYPE = 0xeae4;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n  REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n  REACT_PROFILER_TYPE = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n  REACT_CACHE_TYPE = symbolFor('react.cache');\n}\n\nvar PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map; // We never remove these associations.\n// It's OK to reference families, but use WeakMap/Set for types.\n\nvar allFamiliesByID = new Map();\nvar allFamiliesByType = new PossiblyWeakMap();\nvar allSignaturesByType = new PossiblyWeakMap(); // This WeakMap is read by React, so we only put families\n// that have actually been edited here. This keeps checks fast.\n// $FlowIssue\n\nvar updatedFamiliesByType = new PossiblyWeakMap(); // This is cleared on every performReactRefresh() call.\n// It is an array of [Family, NextType] tuples.\n\nvar pendingUpdates = []; // This is injected by the renderer via DevTools global hook.\n\nvar helpersByRendererID = new Map();\nvar helpersByRoot = new Map(); // We keep track of mounted roots so we can schedule updates.\n\nvar mountedRoots = new Set(); // If a root captures an error, we remember it so we can retry on edit.\n\nvar failedRoots = new Set(); // In environments that support WeakMap, we also remember the last element for every root.\n// It needs to be weak because we do this even for roots that failed to mount.\n// If there is no WeakMap, we won't attempt to do retrying.\n// $FlowIssue\n\nvar rootElements = // $FlowIssue\ntypeof WeakMap === 'function' ? new WeakMap() : null;\nvar isPerformingRefresh = false;\n\nfunction computeFullKey(signature) {\n  if (signature.fullKey !== null) {\n    return signature.fullKey;\n  }\n\n  var fullKey = signature.ownKey;\n  var hooks;\n\n  try {\n    hooks = signature.getCustomHooks();\n  } catch (err) {\n    // This can happen in an edge case, e.g. if expression like Foo.useSomething\n    // depends on Foo which is lazily initialized during rendering.\n    // In that case just assume we'll have to remount.\n    signature.forceReset = true;\n    signature.fullKey = fullKey;\n    return fullKey;\n  }\n\n  for (var i = 0; i < hooks.length; i++) {\n    var hook = hooks[i];\n\n    if (typeof hook !== 'function') {\n      // Something's wrong. Assume we need to remount.\n      signature.forceReset = true;\n      signature.fullKey = fullKey;\n      return fullKey;\n    }\n\n    var nestedHookSignature = allSignaturesByType.get(hook);\n\n    if (nestedHookSignature === undefined) {\n      // No signature means Hook wasn't in the source code, e.g. in a library.\n      // We'll skip it because we can assume it won't change during this session.\n      continue;\n    }\n\n    var nestedHookKey = computeFullKey(nestedHookSignature);\n\n    if (nestedHookSignature.forceReset) {\n      signature.forceReset = true;\n    }\n\n    fullKey += '\\n---\\n' + nestedHookKey;\n  }\n\n  signature.fullKey = fullKey;\n  return fullKey;\n}\n\nfunction haveEqualSignatures(prevType, nextType) {\n  var prevSignature = allSignaturesByType.get(prevType);\n  var nextSignature = allSignaturesByType.get(nextType);\n\n  if (prevSignature === undefined && nextSignature === undefined) {\n    return true;\n  }\n\n  if (prevSignature === undefined || nextSignature === undefined) {\n    return false;\n  }\n\n  if (computeFullKey(prevSignature) !== computeFullKey(nextSignature)) {\n    return false;\n  }\n\n  if (nextSignature.forceReset) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction isReactClass(type) {\n  return type.prototype && type.prototype.isReactComponent;\n}\n\nfunction canPreserveStateBetween(prevType, nextType) {\n  if (isReactClass(prevType) || isReactClass(nextType)) {\n    return false;\n  }\n\n  if (haveEqualSignatures(prevType, nextType)) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction resolveFamily(type) {\n  // Only check updated types to keep lookups fast.\n  return updatedFamiliesByType.get(type);\n} // If we didn't care about IE11, we could use new Map/Set(iterable).\n\n\nfunction cloneMap(map) {\n  var clone = new Map();\n  map.forEach(function (value, key) {\n    clone.set(key, value);\n  });\n  return clone;\n}\n\nfunction cloneSet(set) {\n  var clone = new Set();\n  set.forEach(function (value) {\n    clone.add(value);\n  });\n  return clone;\n} // This is a safety mechanism to protect against rogue getters and Proxies.\n\n\nfunction getProperty(object, property) {\n  try {\n    return object[property];\n  } catch (err) {\n    // Intentionally ignore.\n    return undefined;\n  }\n}\n\nfunction performReactRefresh() {\n\n  if (pendingUpdates.length === 0) {\n    return null;\n  }\n\n  if (isPerformingRefresh) {\n    return null;\n  }\n\n  isPerformingRefresh = true;\n\n  try {\n    var staleFamilies = new Set();\n    var updatedFamilies = new Set();\n    var updates = pendingUpdates;\n    pendingUpdates = [];\n    updates.forEach(function (_ref) {\n      var family = _ref[0],\n          nextType = _ref[1];\n      // Now that we got a real edit, we can create associations\n      // that will be read by the React reconciler.\n      var prevType = family.current;\n      updatedFamiliesByType.set(prevType, family);\n      updatedFamiliesByType.set(nextType, family);\n      family.current = nextType; // Determine whether this should be a re-render or a re-mount.\n\n      if (canPreserveStateBetween(prevType, nextType)) {\n        updatedFamilies.add(family);\n      } else {\n        staleFamilies.add(family);\n      }\n    }); // TODO: rename these fields to something more meaningful.\n\n    var update = {\n      updatedFamilies: updatedFamilies,\n      // Families that will re-render preserving state\n      staleFamilies: staleFamilies // Families that will be remounted\n\n    };\n    helpersByRendererID.forEach(function (helpers) {\n      // Even if there are no roots, set the handler on first update.\n      // This ensures that if *new* roots are mounted, they'll use the resolve handler.\n      helpers.setRefreshHandler(resolveFamily);\n    });\n    var didError = false;\n    var firstError = null; // We snapshot maps and sets that are mutated during commits.\n    // If we don't do this, there is a risk they will be mutated while\n    // we iterate over them. For example, trying to recover a failed root\n    // may cause another root to be added to the failed list -- an infinite loop.\n\n    var failedRootsSnapshot = cloneSet(failedRoots);\n    var mountedRootsSnapshot = cloneSet(mountedRoots);\n    var helpersByRootSnapshot = cloneMap(helpersByRoot);\n    failedRootsSnapshot.forEach(function (root) {\n      var helpers = helpersByRootSnapshot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      if (!failedRoots.has(root)) {// No longer failed.\n      }\n\n      if (rootElements === null) {\n        return;\n      }\n\n      if (!rootElements.has(root)) {\n        return;\n      }\n\n      var element = rootElements.get(root);\n\n      try {\n        helpers.scheduleRoot(root, element);\n      } catch (err) {\n        if (!didError) {\n          didError = true;\n          firstError = err;\n        } // Keep trying other roots.\n\n      }\n    });\n    mountedRootsSnapshot.forEach(function (root) {\n      var helpers = helpersByRootSnapshot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      if (!mountedRoots.has(root)) {// No longer mounted.\n      }\n\n      try {\n        helpers.scheduleRefresh(root, update);\n      } catch (err) {\n        if (!didError) {\n          didError = true;\n          firstError = err;\n        } // Keep trying other roots.\n\n      }\n    });\n\n    if (didError) {\n      throw firstError;\n    }\n\n    return update;\n  } finally {\n    isPerformingRefresh = false;\n  }\n}\nfunction register(type, id) {\n  {\n    if (type === null) {\n      return;\n    }\n\n    if (typeof type !== 'function' && typeof type !== 'object') {\n      return;\n    } // This can happen in an edge case, e.g. if we register\n    // return value of a HOC but it returns a cached component.\n    // Ignore anything but the first registration for each type.\n\n\n    if (allFamiliesByType.has(type)) {\n      return;\n    } // Create family or remember to update it.\n    // None of this bookkeeping affects reconciliation\n    // until the first performReactRefresh() call above.\n\n\n    var family = allFamiliesByID.get(id);\n\n    if (family === undefined) {\n      family = {\n        current: type\n      };\n      allFamiliesByID.set(id, family);\n    } else {\n      pendingUpdates.push([family, type]);\n    }\n\n    allFamiliesByType.set(type, family); // Visit inner types because we might not have registered them.\n\n    if (typeof type === 'object' && type !== null) {\n      switch (getProperty(type, '$$typeof')) {\n        case REACT_FORWARD_REF_TYPE:\n          register(type.render, id + '$render');\n          break;\n\n        case REACT_MEMO_TYPE:\n          register(type.type, id + '$type');\n          break;\n      }\n    }\n  }\n}\nfunction setSignature(type, key) {\n  var forceReset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var getCustomHooks = arguments.length > 3 ? arguments[3] : undefined;\n\n  {\n    if (!allSignaturesByType.has(type)) {\n      allSignaturesByType.set(type, {\n        forceReset: forceReset,\n        ownKey: key,\n        fullKey: null,\n        getCustomHooks: getCustomHooks || function () {\n          return [];\n        }\n      });\n    } // Visit inner types because we might not have signed them.\n\n\n    if (typeof type === 'object' && type !== null) {\n      switch (getProperty(type, '$$typeof')) {\n        case REACT_FORWARD_REF_TYPE:\n          setSignature(type.render, key, forceReset, getCustomHooks);\n          break;\n\n        case REACT_MEMO_TYPE:\n          setSignature(type.type, key, forceReset, getCustomHooks);\n          break;\n      }\n    }\n  }\n} // This is lazily called during first render for a type.\n// It captures Hook list at that time so inline requires don't break comparisons.\n\nfunction collectCustomHooksForSignature(type) {\n  {\n    var signature = allSignaturesByType.get(type);\n\n    if (signature !== undefined) {\n      computeFullKey(signature);\n    }\n  }\n}\nfunction getFamilyByID(id) {\n  {\n    return allFamiliesByID.get(id);\n  }\n}\nfunction getFamilyByType(type) {\n  {\n    return allFamiliesByType.get(type);\n  }\n}\nfunction findAffectedHostInstances(families) {\n  {\n    var affectedInstances = new Set();\n    mountedRoots.forEach(function (root) {\n      var helpers = helpersByRoot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      var instancesForRoot = helpers.findHostInstancesForRefresh(root, families);\n      instancesForRoot.forEach(function (inst) {\n        affectedInstances.add(inst);\n      });\n    });\n    return affectedInstances;\n  }\n}\nfunction injectIntoGlobalHook(globalObject) {\n  {\n    // For React Native, the global hook will be set up by require('react-devtools-core').\n    // That code will run before us. So we need to monkeypatch functions on existing hook.\n    // For React Web, the global hook will be set up by the extension.\n    // This will also run before us.\n    var hook = globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n\n    if (hook === undefined) {\n      // However, if there is no DevTools extension, we'll need to set up the global hook ourselves.\n      // Note that in this case it's important that renderer code runs *after* this method call.\n      // Otherwise, the renderer will think that there is no global hook, and won't do the injection.\n      var nextID = 0;\n      globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__ = hook = {\n        renderers: new Map(),\n        supportsFiber: true,\n        inject: function (injected) {\n          return nextID++;\n        },\n        onScheduleFiberRoot: function (id, root, children) {},\n        onCommitFiberRoot: function (id, root, maybePriorityLevel, didError) {},\n        onCommitFiberUnmount: function () {}\n      };\n    }\n\n    if (hook.isDisabled) {\n      // This isn't a real property on the hook, but it can be set to opt out\n      // of DevTools integration and associated warnings and logs.\n      // Using console['warn'] to evade Babel and ESLint\n      console['warn']('Something has shimmed the React DevTools global hook (__REACT_DEVTOOLS_GLOBAL_HOOK__). ' + 'Fast Refresh is not compatible with this shim and will be disabled.');\n      return;\n    } // Here, we just want to get a reference to scheduleRefresh.\n\n\n    var oldInject = hook.inject;\n\n    hook.inject = function (injected) {\n      var id = oldInject.apply(this, arguments);\n\n      if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n        // This version supports React Refresh.\n        helpersByRendererID.set(id, injected);\n      }\n\n      return id;\n    }; // Do the same for any already injected roots.\n    // This is useful if ReactDOM has already been initialized.\n    // https://github.com/facebook/react/issues/17626\n\n\n    hook.renderers.forEach(function (injected, id) {\n      if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n        // This version supports React Refresh.\n        helpersByRendererID.set(id, injected);\n      }\n    }); // We also want to track currently mounted roots.\n\n    var oldOnCommitFiberRoot = hook.onCommitFiberRoot;\n\n    var oldOnScheduleFiberRoot = hook.onScheduleFiberRoot || function () {};\n\n    hook.onScheduleFiberRoot = function (id, root, children) {\n      if (!isPerformingRefresh) {\n        // If it was intentionally scheduled, don't attempt to restore.\n        // This includes intentionally scheduled unmounts.\n        failedRoots.delete(root);\n\n        if (rootElements !== null) {\n          rootElements.set(root, children);\n        }\n      }\n\n      return oldOnScheduleFiberRoot.apply(this, arguments);\n    };\n\n    hook.onCommitFiberRoot = function (id, root, maybePriorityLevel, didError) {\n      var helpers = helpersByRendererID.get(id);\n\n      if (helpers !== undefined) {\n        helpersByRoot.set(root, helpers);\n        var current = root.current;\n        var alternate = current.alternate; // We need to determine whether this root has just (un)mounted.\n        // This logic is copy-pasted from similar logic in the DevTools backend.\n        // If this breaks with some refactoring, you'll want to update DevTools too.\n\n        if (alternate !== null) {\n          var wasMounted = alternate.memoizedState != null && alternate.memoizedState.element != null;\n          var isMounted = current.memoizedState != null && current.memoizedState.element != null;\n\n          if (!wasMounted && isMounted) {\n            // Mount a new root.\n            mountedRoots.add(root);\n            failedRoots.delete(root);\n          } else if (wasMounted && isMounted) ; else if (wasMounted && !isMounted) {\n            // Unmount an existing root.\n            mountedRoots.delete(root);\n\n            if (didError) {\n              // We'll remount it on future edits.\n              failedRoots.add(root);\n            } else {\n              helpersByRoot.delete(root);\n            }\n          } else if (!wasMounted && !isMounted) {\n            if (didError) {\n              // We'll remount it on future edits.\n              failedRoots.add(root);\n            }\n          }\n        } else {\n          // Mount a new root.\n          mountedRoots.add(root);\n        }\n      } // Always call the decorated DevTools hook.\n\n\n      return oldOnCommitFiberRoot.apply(this, arguments);\n    };\n  }\n}\nfunction hasUnrecoverableErrors() {\n  // TODO: delete this after removing dependency in RN.\n  return false;\n} // Exposed for testing.\n\nfunction _getMountedRootCount() {\n  {\n    return mountedRoots.size;\n  }\n} // This is a wrapper over more primitive functions for setting signature.\n// Signatures let us decide whether the Hook order has changed on refresh.\n//\n// This function is intended to be used as a transform target, e.g.:\n// var _s = createSignatureFunctionForTransform()\n//\n// function Hello() {\n//   const [foo, setFoo] = useState(0);\n//   const value = useCustomHook();\n//   _s(); /* Call without arguments triggers collecting the custom Hook list.\n//          * This doesn't happen during the module evaluation because we\n//          * don't want to change the module order with inline requires.\n//          * Next calls are noops. */\n//   return <h1>Hi</h1>;\n// }\n//\n// /* Call with arguments attaches the signature to the type: */\n// _s(\n//   Hello,\n//   'useState{[foo, setFoo]}(0)',\n//   () => [useCustomHook], /* Lazy to avoid triggering inline requires */\n// );\n\nfunction createSignatureFunctionForTransform() {\n  {\n    var savedType;\n    var hasCustomHooks;\n    var didCollectHooks = false;\n    return function (type, key, forceReset, getCustomHooks) {\n      if (typeof key === 'string') {\n        // We're in the initial phase that associates signatures\n        // with the functions. Note this may be called multiple times\n        // in HOC chains like _s(hoc1(_s(hoc2(_s(actualFunction))))).\n        if (!savedType) {\n          // We're in the innermost call, so this is the actual type.\n          savedType = type;\n          hasCustomHooks = typeof getCustomHooks === 'function';\n        } // Set the signature for all types (even wrappers!) in case\n        // they have no signatures of their own. This is to prevent\n        // problems like https://github.com/facebook/react/issues/20417.\n\n\n        if (type != null && (typeof type === 'function' || typeof type === 'object')) {\n          setSignature(type, key, forceReset, getCustomHooks);\n        }\n\n        return type;\n      } else {\n        // We're in the _s() call without arguments, which means\n        // this is the time to collect custom Hook signatures.\n        // Only do this once. This path is hot and runs *inside* every render!\n        if (!didCollectHooks && hasCustomHooks) {\n          didCollectHooks = true;\n          collectCustomHooksForSignature(savedType);\n        }\n      }\n    };\n  }\n}\nfunction isLikelyComponentType(type) {\n  {\n    switch (typeof type) {\n      case 'function':\n        {\n          // First, deal with classes.\n          if (type.prototype != null) {\n            if (type.prototype.isReactComponent) {\n              // React class.\n              return true;\n            }\n\n            var ownNames = Object.getOwnPropertyNames(type.prototype);\n\n            if (ownNames.length > 1 || ownNames[0] !== 'constructor') {\n              // This looks like a class.\n              return false;\n            } // eslint-disable-next-line no-proto\n\n\n            if (type.prototype.__proto__ !== Object.prototype) {\n              // It has a superclass.\n              return false;\n            } // Pass through.\n            // This looks like a regular function with empty prototype.\n\n          } // For plain functions and arrows, use name as a heuristic.\n\n\n          var name = type.name || type.displayName;\n          return typeof name === 'string' && /^[A-Z]/.test(name);\n        }\n\n      case 'object':\n        {\n          if (type != null) {\n            switch (getProperty(type, '$$typeof')) {\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_MEMO_TYPE:\n                // Definitely React components.\n                return true;\n\n              default:\n                return false;\n            }\n          }\n\n          return false;\n        }\n\n      default:\n        {\n          return false;\n        }\n    }\n  }\n}\n\nexports._getMountedRootCount = _getMountedRootCount;\nexports.collectCustomHooksForSignature = collectCustomHooksForSignature;\nexports.createSignatureFunctionForTransform = createSignatureFunctionForTransform;\nexports.findAffectedHostInstances = findAffectedHostInstances;\nexports.getFamilyByID = getFamilyByID;\nexports.getFamilyByType = getFamilyByType;\nexports.hasUnrecoverableErrors = hasUnrecoverableErrors;\nexports.injectIntoGlobalHook = injectIntoGlobalHook;\nexports.isLikelyComponentType = isLikelyComponentType;\nexports.performReactRefresh = performReactRefresh;\nexports.register = register;\nexports.setSignature = setSignature;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA,IAAIA,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzC,CAAC,YAAW;IACd,aADc,CAGd;IACA;IACA;IACA;IACA;;IACA,IAAIC,kBAAkB,GAAG,MAAzB;IACA,IAAIC,iBAAiB,GAAG,MAAxB;IACA,IAAIC,mBAAmB,GAAG,MAA1B;IACA,IAAIC,sBAAsB,GAAG,MAA7B;IACA,IAAIC,mBAAmB,GAAG,MAA1B;IACA,IAAIC,mBAAmB,GAAG,MAA1B;IACA,IAAIC,kBAAkB,GAAG,MAAzB;IACA,IAAIC,sBAAsB,GAAG,MAA7B;IACA,IAAIC,mBAAmB,GAAG,MAA1B;IACA,IAAIC,wBAAwB,GAAG,MAA/B;IACA,IAAIC,eAAe,GAAG,MAAtB;IACA,IAAIC,eAAe,GAAG,MAAtB;IACA,IAAIC,gBAAgB,GAAG,MAAvB;IACA,IAAIC,6BAA6B,GAAG,MAApC;IACA,IAAIC,oBAAoB,GAAG,MAA3B;IACA,IAAIC,wBAAwB,GAAG,MAA/B;IACA,IAAIC,gBAAgB,GAAG,MAAvB;;IAEA,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;MAC9C,IAAIC,SAAS,GAAGF,MAAM,CAACC,GAAvB;MACAlB,kBAAkB,GAAGmB,SAAS,CAAC,eAAD,CAA9B;MACAlB,iBAAiB,GAAGkB,SAAS,CAAC,cAAD,CAA7B;MACAjB,mBAAmB,GAAGiB,SAAS,CAAC,gBAAD,CAA/B;MACAhB,sBAAsB,GAAGgB,SAAS,CAAC,mBAAD,CAAlC;MACAf,mBAAmB,GAAGe,SAAS,CAAC,gBAAD,CAA/B;MACAd,mBAAmB,GAAGc,SAAS,CAAC,gBAAD,CAA/B;MACAb,kBAAkB,GAAGa,SAAS,CAAC,eAAD,CAA9B;MACAZ,sBAAsB,GAAGY,SAAS,CAAC,mBAAD,CAAlC;MACAX,mBAAmB,GAAGW,SAAS,CAAC,gBAAD,CAA/B;MACAV,wBAAwB,GAAGU,SAAS,CAAC,qBAAD,CAApC;MACAT,eAAe,GAAGS,SAAS,CAAC,YAAD,CAA3B;MACAR,eAAe,GAAGQ,SAAS,CAAC,YAAD,CAA3B;MACAP,gBAAgB,GAAGO,SAAS,CAAC,aAAD,CAA5B;MACAN,6BAA6B,GAAGM,SAAS,CAAC,wBAAD,CAAzC;MACAL,oBAAoB,GAAGK,SAAS,CAAC,iBAAD,CAAhC;MACAJ,wBAAwB,GAAGI,SAAS,CAAC,qBAAD,CAApC;MACAH,gBAAgB,GAAGG,SAAS,CAAC,aAAD,CAA5B;IACD;;IAED,IAAIC,eAAe,GAAG,OAAOC,OAAP,KAAmB,UAAnB,GAAgCA,OAAhC,GAA0CC,GAAhE,CA/Cc,CA+CuD;IACrE;;IAEA,IAAIC,eAAe,GAAG,IAAID,GAAJ,EAAtB;IACA,IAAIE,iBAAiB,GAAG,IAAIJ,eAAJ,EAAxB;IACA,IAAIK,mBAAmB,GAAG,IAAIL,eAAJ,EAA1B,CApDc,CAoDmC;IACjD;IACA;;IAEA,IAAIM,qBAAqB,GAAG,IAAIN,eAAJ,EAA5B,CAxDc,CAwDqC;IACnD;;IAEA,IAAIO,cAAc,GAAG,EAArB,CA3Dc,CA2DW;;IAEzB,IAAIC,mBAAmB,GAAG,IAAIN,GAAJ,EAA1B;IACA,IAAIO,aAAa,GAAG,IAAIP,GAAJ,EAApB,CA9Dc,CA8DiB;;IAE/B,IAAIQ,YAAY,GAAG,IAAIC,GAAJ,EAAnB,CAhEc,CAgEgB;;IAE9B,IAAIC,WAAW,GAAG,IAAID,GAAJ,EAAlB,CAlEc,CAkEe;IAC7B;IACA;IACA;;IAEA,IAAIE,YAAY,GAAG;IACnB,OAAOZ,OAAP,KAAmB,UAAnB,GAAgC,IAAIA,OAAJ,EAAhC,GAAgD,IADhD;IAEA,IAAIa,mBAAmB,GAAG,KAA1B;;IAEA,SAASC,cAAT,CAAwBC,SAAxB,EAAmC;MACjC,IAAIA,SAAS,CAACC,OAAV,KAAsB,IAA1B,EAAgC;QAC9B,OAAOD,SAAS,CAACC,OAAjB;MACD;;MAED,IAAIA,OAAO,GAAGD,SAAS,CAACE,MAAxB;MACA,IAAIC,KAAJ;;MAEA,IAAI;QACFA,KAAK,GAAGH,SAAS,CAACI,cAAV,EAAR;MACD,CAFD,CAEE,OAAOC,GAAP,EAAY;QACZ;QACA;QACA;QACAL,SAAS,CAACM,UAAV,GAAuB,IAAvB;QACAN,SAAS,CAACC,OAAV,GAAoBA,OAApB;QACA,OAAOA,OAAP;MACD;;MAED,KAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,KAAK,CAACK,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;QACrC,IAAIE,IAAI,GAAGN,KAAK,CAACI,CAAD,CAAhB;;QAEA,IAAI,OAAOE,IAAP,KAAgB,UAApB,EAAgC;UAC9B;UACAT,SAAS,CAACM,UAAV,GAAuB,IAAvB;UACAN,SAAS,CAACC,OAAV,GAAoBA,OAApB;UACA,OAAOA,OAAP;QACD;;QAED,IAAIS,mBAAmB,GAAGrB,mBAAmB,CAACsB,GAApB,CAAwBF,IAAxB,CAA1B;;QAEA,IAAIC,mBAAmB,KAAKE,SAA5B,EAAuC;UACrC;UACA;UACA;QACD;;QAED,IAAIC,aAAa,GAAGd,cAAc,CAACW,mBAAD,CAAlC;;QAEA,IAAIA,mBAAmB,CAACJ,UAAxB,EAAoC;UAClCN,SAAS,CAACM,UAAV,GAAuB,IAAvB;QACD;;QAEDL,OAAO,IAAI,YAAYY,aAAvB;MACD;;MAEDb,SAAS,CAACC,OAAV,GAAoBA,OAApB;MACA,OAAOA,OAAP;IACD;;IAED,SAASa,mBAAT,CAA6BC,QAA7B,EAAuCC,QAAvC,EAAiD;MAC/C,IAAIC,aAAa,GAAG5B,mBAAmB,CAACsB,GAApB,CAAwBI,QAAxB,CAApB;MACA,IAAIG,aAAa,GAAG7B,mBAAmB,CAACsB,GAApB,CAAwBK,QAAxB,CAApB;;MAEA,IAAIC,aAAa,KAAKL,SAAlB,IAA+BM,aAAa,KAAKN,SAArD,EAAgE;QAC9D,OAAO,IAAP;MACD;;MAED,IAAIK,aAAa,KAAKL,SAAlB,IAA+BM,aAAa,KAAKN,SAArD,EAAgE;QAC9D,OAAO,KAAP;MACD;;MAED,IAAIb,cAAc,CAACkB,aAAD,CAAd,KAAkClB,cAAc,CAACmB,aAAD,CAApD,EAAqE;QACnE,OAAO,KAAP;MACD;;MAED,IAAIA,aAAa,CAACZ,UAAlB,EAA8B;QAC5B,OAAO,KAAP;MACD;;MAED,OAAO,IAAP;IACD;;IAED,SAASa,YAAT,CAAsBC,IAAtB,EAA4B;MAC1B,OAAOA,IAAI,CAACC,SAAL,IAAkBD,IAAI,CAACC,SAAL,CAAeC,gBAAxC;IACD;;IAED,SAASC,uBAAT,CAAiCR,QAAjC,EAA2CC,QAA3C,EAAqD;MACnD,IAAIG,YAAY,CAACJ,QAAD,CAAZ,IAA0BI,YAAY,CAACH,QAAD,CAA1C,EAAsD;QACpD,OAAO,KAAP;MACD;;MAED,IAAIF,mBAAmB,CAACC,QAAD,EAAWC,QAAX,CAAvB,EAA6C;QAC3C,OAAO,IAAP;MACD;;MAED,OAAO,KAAP;IACD;;IAED,SAASQ,aAAT,CAAuBJ,IAAvB,EAA6B;MAC3B;MACA,OAAO9B,qBAAqB,CAACqB,GAAtB,CAA0BS,IAA1B,CAAP;IACD,CAvKa,CAuKZ;;;IAGF,SAASK,QAAT,CAAkBC,GAAlB,EAAuB;MACrB,IAAIC,KAAK,GAAG,IAAIzC,GAAJ,EAAZ;MACAwC,GAAG,CAACE,OAAJ,CAAY,UAAUC,KAAV,EAAiBC,GAAjB,EAAsB;QAChCH,KAAK,CAACI,GAAN,CAAUD,GAAV,EAAeD,KAAf;MACD,CAFD;MAGA,OAAOF,KAAP;IACD;;IAED,SAASK,QAAT,CAAkBD,GAAlB,EAAuB;MACrB,IAAIJ,KAAK,GAAG,IAAIhC,GAAJ,EAAZ;MACAoC,GAAG,CAACH,OAAJ,CAAY,UAAUC,KAAV,EAAiB;QAC3BF,KAAK,CAACM,GAAN,CAAUJ,KAAV;MACD,CAFD;MAGA,OAAOF,KAAP;IACD,CAxLa,CAwLZ;;;IAGF,SAASO,WAAT,CAAqBC,MAArB,EAA6BC,QAA7B,EAAuC;MACrC,IAAI;QACF,OAAOD,MAAM,CAACC,QAAD,CAAb;MACD,CAFD,CAEE,OAAO/B,GAAP,EAAY;QACZ;QACA,OAAOO,SAAP;MACD;IACF;;IAED,SAASyB,mBAAT,GAA+B;MAE7B,IAAI9C,cAAc,CAACiB,MAAf,KAA0B,CAA9B,EAAiC;QAC/B,OAAO,IAAP;MACD;;MAED,IAAIV,mBAAJ,EAAyB;QACvB,OAAO,IAAP;MACD;;MAEDA,mBAAmB,GAAG,IAAtB;;MAEA,IAAI;QACF,IAAIwC,aAAa,GAAG,IAAI3C,GAAJ,EAApB;QACA,IAAI4C,eAAe,GAAG,IAAI5C,GAAJ,EAAtB;QACA,IAAI6C,OAAO,GAAGjD,cAAd;QACAA,cAAc,GAAG,EAAjB;QACAiD,OAAO,CAACZ,OAAR,CAAgB,UAAUa,IAAV,EAAgB;UAC9B,IAAIC,MAAM,GAAGD,IAAI,CAAC,CAAD,CAAjB;UAAA,IACIzB,QAAQ,GAAGyB,IAAI,CAAC,CAAD,CADnB,CAD8B,CAG9B;UACA;;UACA,IAAI1B,QAAQ,GAAG2B,MAAM,CAACC,OAAtB;UACArD,qBAAqB,CAACyC,GAAtB,CAA0BhB,QAA1B,EAAoC2B,MAApC;UACApD,qBAAqB,CAACyC,GAAtB,CAA0Bf,QAA1B,EAAoC0B,MAApC;UACAA,MAAM,CAACC,OAAP,GAAiB3B,QAAjB,CAR8B,CAQH;;UAE3B,IAAIO,uBAAuB,CAACR,QAAD,EAAWC,QAAX,CAA3B,EAAiD;YAC/CuB,eAAe,CAACN,GAAhB,CAAoBS,MAApB;UACD,CAFD,MAEO;YACLJ,aAAa,CAACL,GAAd,CAAkBS,MAAlB;UACD;QACF,CAfD,EALE,CAoBE;;QAEJ,IAAIE,MAAM,GAAG;UACXL,eAAe,EAAEA,eADN;UAEX;UACAD,aAAa,EAAEA,aAHJ,CAGkB;;QAHlB,CAAb;QAMA9C,mBAAmB,CAACoC,OAApB,CAA4B,UAAUiB,OAAV,EAAmB;UAC7C;UACA;UACAA,OAAO,CAACC,iBAAR,CAA0BtB,aAA1B;QACD,CAJD;QAKA,IAAIuB,QAAQ,GAAG,KAAf;QACA,IAAIC,UAAU,GAAG,IAAjB,CAlCE,CAkCqB;QACvB;QACA;QACA;;QAEA,IAAIC,mBAAmB,GAAGjB,QAAQ,CAACpC,WAAD,CAAlC;QACA,IAAIsD,oBAAoB,GAAGlB,QAAQ,CAACtC,YAAD,CAAnC;QACA,IAAIyD,qBAAqB,GAAG1B,QAAQ,CAAChC,aAAD,CAApC;QACAwD,mBAAmB,CAACrB,OAApB,CAA4B,UAAUwB,IAAV,EAAgB;UAC1C,IAAIP,OAAO,GAAGM,qBAAqB,CAACxC,GAAtB,CAA0ByC,IAA1B,CAAd;;UAEA,IAAIP,OAAO,KAAKjC,SAAhB,EAA2B;YACzB,MAAM,IAAIyC,KAAJ,CAAU,oEAAV,CAAN;UACD;;UAED,IAAI,CAACzD,WAAW,CAAC0D,GAAZ,CAAgBF,IAAhB,CAAL,EAA4B,CAAC;UAC5B;;UAED,IAAIvD,YAAY,KAAK,IAArB,EAA2B;YACzB;UACD;;UAED,IAAI,CAACA,YAAY,CAACyD,GAAb,CAAiBF,IAAjB,CAAL,EAA6B;YAC3B;UACD;;UAED,IAAIG,OAAO,GAAG1D,YAAY,CAACc,GAAb,CAAiByC,IAAjB,CAAd;;UAEA,IAAI;YACFP,OAAO,CAACW,YAAR,CAAqBJ,IAArB,EAA2BG,OAA3B;UACD,CAFD,CAEE,OAAOlD,GAAP,EAAY;YACZ,IAAI,CAAC0C,QAAL,EAAe;cACbA,QAAQ,GAAG,IAAX;cACAC,UAAU,GAAG3C,GAAb;YACD,CAJW,CAIV;;UAEH;QACF,CA7BD;QA8BA6C,oBAAoB,CAACtB,OAArB,CAA6B,UAAUwB,IAAV,EAAgB;UAC3C,IAAIP,OAAO,GAAGM,qBAAqB,CAACxC,GAAtB,CAA0ByC,IAA1B,CAAd;;UAEA,IAAIP,OAAO,KAAKjC,SAAhB,EAA2B;YACzB,MAAM,IAAIyC,KAAJ,CAAU,oEAAV,CAAN;UACD;;UAED,IAAI,CAAC3D,YAAY,CAAC4D,GAAb,CAAiBF,IAAjB,CAAL,EAA6B,CAAC;UAC7B;;UAED,IAAI;YACFP,OAAO,CAACY,eAAR,CAAwBL,IAAxB,EAA8BR,MAA9B;UACD,CAFD,CAEE,OAAOvC,GAAP,EAAY;YACZ,IAAI,CAAC0C,QAAL,EAAe;cACbA,QAAQ,GAAG,IAAX;cACAC,UAAU,GAAG3C,GAAb;YACD,CAJW,CAIV;;UAEH;QACF,CAnBD;;QAqBA,IAAI0C,QAAJ,EAAc;UACZ,MAAMC,UAAN;QACD;;QAED,OAAOJ,MAAP;MACD,CAlGD,SAkGU;QACR9C,mBAAmB,GAAG,KAAtB;MACD;IACF;;IACD,SAAS4D,QAAT,CAAkBtC,IAAlB,EAAwBuC,EAAxB,EAA4B;MAC1B;QACE,IAAIvC,IAAI,KAAK,IAAb,EAAmB;UACjB;QACD;;QAED,IAAI,OAAOA,IAAP,KAAgB,UAAhB,IAA8B,OAAOA,IAAP,KAAgB,QAAlD,EAA4D;UAC1D;QACD,CAPH,CAOI;QACF;QACA;;;QAGA,IAAIhC,iBAAiB,CAACkE,GAAlB,CAAsBlC,IAAtB,CAAJ,EAAiC;UAC/B;QACD,CAdH,CAcI;QACF;QACA;;;QAGA,IAAIsB,MAAM,GAAGvD,eAAe,CAACwB,GAAhB,CAAoBgD,EAApB,CAAb;;QAEA,IAAIjB,MAAM,KAAK9B,SAAf,EAA0B;UACxB8B,MAAM,GAAG;YACPC,OAAO,EAAEvB;UADF,CAAT;UAGAjC,eAAe,CAAC4C,GAAhB,CAAoB4B,EAApB,EAAwBjB,MAAxB;QACD,CALD,MAKO;UACLnD,cAAc,CAACqE,IAAf,CAAoB,CAAClB,MAAD,EAAStB,IAAT,CAApB;QACD;;QAEDhC,iBAAiB,CAAC2C,GAAlB,CAAsBX,IAAtB,EAA4BsB,MAA5B,EA9BF,CA8BuC;;QAErC,IAAI,OAAOtB,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,KAAK,IAAzC,EAA+C;UAC7C,QAAQc,WAAW,CAACd,IAAD,EAAO,UAAP,CAAnB;YACE,KAAKjD,sBAAL;cACEuF,QAAQ,CAACtC,IAAI,CAACyC,MAAN,EAAcF,EAAE,GAAG,SAAnB,CAAR;cACA;;YAEF,KAAKrF,eAAL;cACEoF,QAAQ,CAACtC,IAAI,CAACA,IAAN,EAAYuC,EAAE,GAAG,OAAjB,CAAR;cACA;UAPJ;QASD;MACF;IACF;;IACD,SAASG,YAAT,CAAsB1C,IAAtB,EAA4BU,GAA5B,EAAiC;MAC/B,IAAIxB,UAAU,GAAGyD,SAAS,CAACvD,MAAV,GAAmB,CAAnB,IAAwBuD,SAAS,CAAC,CAAD,CAAT,KAAiBnD,SAAzC,GAAqDmD,SAAS,CAAC,CAAD,CAA9D,GAAoE,KAArF;MACA,IAAI3D,cAAc,GAAG2D,SAAS,CAACvD,MAAV,GAAmB,CAAnB,GAAuBuD,SAAS,CAAC,CAAD,CAAhC,GAAsCnD,SAA3D;MAEA;QACE,IAAI,CAACvB,mBAAmB,CAACiE,GAApB,CAAwBlC,IAAxB,CAAL,EAAoC;UAClC/B,mBAAmB,CAAC0C,GAApB,CAAwBX,IAAxB,EAA8B;YAC5Bd,UAAU,EAAEA,UADgB;YAE5BJ,MAAM,EAAE4B,GAFoB;YAG5B7B,OAAO,EAAE,IAHmB;YAI5BG,cAAc,EAAEA,cAAc,IAAI,YAAY;cAC5C,OAAO,EAAP;YACD;UAN2B,CAA9B;QAQD,CAVH,CAUI;;;QAGF,IAAI,OAAOgB,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,KAAK,IAAzC,EAA+C;UAC7C,QAAQc,WAAW,CAACd,IAAD,EAAO,UAAP,CAAnB;YACE,KAAKjD,sBAAL;cACE2F,YAAY,CAAC1C,IAAI,CAACyC,MAAN,EAAc/B,GAAd,EAAmBxB,UAAnB,EAA+BF,cAA/B,CAAZ;cACA;;YAEF,KAAK9B,eAAL;cACEwF,YAAY,CAAC1C,IAAI,CAACA,IAAN,EAAYU,GAAZ,EAAiBxB,UAAjB,EAA6BF,cAA7B,CAAZ;cACA;UAPJ;QASD;MACF;IACF,CAjYa,CAiYZ;IACF;;;IAEA,SAAS4D,8BAAT,CAAwC5C,IAAxC,EAA8C;MAC5C;QACE,IAAIpB,SAAS,GAAGX,mBAAmB,CAACsB,GAApB,CAAwBS,IAAxB,CAAhB;;QAEA,IAAIpB,SAAS,KAAKY,SAAlB,EAA6B;UAC3Bb,cAAc,CAACC,SAAD,CAAd;QACD;MACF;IACF;;IACD,SAASiE,aAAT,CAAuBN,EAAvB,EAA2B;MACzB;QACE,OAAOxE,eAAe,CAACwB,GAAhB,CAAoBgD,EAApB,CAAP;MACD;IACF;;IACD,SAASO,eAAT,CAAyB9C,IAAzB,EAA+B;MAC7B;QACE,OAAOhC,iBAAiB,CAACuB,GAAlB,CAAsBS,IAAtB,CAAP;MACD;IACF;;IACD,SAAS+C,yBAAT,CAAmCC,QAAnC,EAA6C;MAC3C;QACE,IAAIC,iBAAiB,GAAG,IAAI1E,GAAJ,EAAxB;QACAD,YAAY,CAACkC,OAAb,CAAqB,UAAUwB,IAAV,EAAgB;UACnC,IAAIP,OAAO,GAAGpD,aAAa,CAACkB,GAAd,CAAkByC,IAAlB,CAAd;;UAEA,IAAIP,OAAO,KAAKjC,SAAhB,EAA2B;YACzB,MAAM,IAAIyC,KAAJ,CAAU,oEAAV,CAAN;UACD;;UAED,IAAIiB,gBAAgB,GAAGzB,OAAO,CAAC0B,2BAAR,CAAoCnB,IAApC,EAA0CgB,QAA1C,CAAvB;UACAE,gBAAgB,CAAC1C,OAAjB,CAAyB,UAAU4C,IAAV,EAAgB;YACvCH,iBAAiB,CAACpC,GAAlB,CAAsBuC,IAAtB;UACD,CAFD;QAGD,CAXD;QAYA,OAAOH,iBAAP;MACD;IACF;;IACD,SAASI,oBAAT,CAA8BC,YAA9B,EAA4C;MAC1C;QACE;QACA;QACA;QACA;QACA,IAAIjE,IAAI,GAAGiE,YAAY,CAACC,8BAAxB;;QAEA,IAAIlE,IAAI,KAAKG,SAAb,EAAwB;UACtB;UACA;UACA;UACA,IAAIgE,MAAM,GAAG,CAAb;UACAF,YAAY,CAACC,8BAAb,GAA8ClE,IAAI,GAAG;YACnDoE,SAAS,EAAE,IAAI3F,GAAJ,EADwC;YAEnD4F,aAAa,EAAE,IAFoC;YAGnDC,MAAM,EAAE,UAAUC,QAAV,EAAoB;cAC1B,OAAOJ,MAAM,EAAb;YACD,CALkD;YAMnDK,mBAAmB,EAAE,UAAUtB,EAAV,EAAcP,IAAd,EAAoB8B,QAApB,EAA8B,CAAE,CANF;YAOnDC,iBAAiB,EAAE,UAAUxB,EAAV,EAAcP,IAAd,EAAoBgC,kBAApB,EAAwCrC,QAAxC,EAAkD,CAAE,CAPpB;YAQnDsC,oBAAoB,EAAE,YAAY,CAAE;UARe,CAArD;QAUD;;QAED,IAAI5E,IAAI,CAAC6E,UAAT,EAAqB;UACnB;UACA;UACA;UACAC,OAAO,CAAC,MAAD,CAAP,CAAgB,4FAA4F,qEAA5G;UACA;QACD,CA9BH,CA8BI;;;QAGF,IAAIC,SAAS,GAAG/E,IAAI,CAACsE,MAArB;;QAEAtE,IAAI,CAACsE,MAAL,GAAc,UAAUC,QAAV,EAAoB;UAChC,IAAIrB,EAAE,GAAG6B,SAAS,CAACC,KAAV,CAAgB,IAAhB,EAAsB1B,SAAtB,CAAT;;UAEA,IAAI,OAAOiB,QAAQ,CAACvB,eAAhB,KAAoC,UAApC,IAAkD,OAAOuB,QAAQ,CAAClC,iBAAhB,KAAsC,UAA5F,EAAwG;YACtG;YACAtD,mBAAmB,CAACuC,GAApB,CAAwB4B,EAAxB,EAA4BqB,QAA5B;UACD;;UAED,OAAOrB,EAAP;QACD,CATD,CAnCF,CA4CK;QACH;QACA;;;QAGAlD,IAAI,CAACoE,SAAL,CAAejD,OAAf,CAAuB,UAAUoD,QAAV,EAAoBrB,EAApB,EAAwB;UAC7C,IAAI,OAAOqB,QAAQ,CAACvB,eAAhB,KAAoC,UAApC,IAAkD,OAAOuB,QAAQ,CAAClC,iBAAhB,KAAsC,UAA5F,EAAwG;YACtG;YACAtD,mBAAmB,CAACuC,GAApB,CAAwB4B,EAAxB,EAA4BqB,QAA5B;UACD;QACF,CALD,EAjDF,CAsDM;;QAEJ,IAAIU,oBAAoB,GAAGjF,IAAI,CAAC0E,iBAAhC;;QAEA,IAAIQ,sBAAsB,GAAGlF,IAAI,CAACwE,mBAAL,IAA4B,YAAY,CAAE,CAAvE;;QAEAxE,IAAI,CAACwE,mBAAL,GAA2B,UAAUtB,EAAV,EAAcP,IAAd,EAAoB8B,QAApB,EAA8B;UACvD,IAAI,CAACpF,mBAAL,EAA0B;YACxB;YACA;YACAF,WAAW,CAACgG,MAAZ,CAAmBxC,IAAnB;;YAEA,IAAIvD,YAAY,KAAK,IAArB,EAA2B;cACzBA,YAAY,CAACkC,GAAb,CAAiBqB,IAAjB,EAAuB8B,QAAvB;YACD;UACF;;UAED,OAAOS,sBAAsB,CAACF,KAAvB,CAA6B,IAA7B,EAAmC1B,SAAnC,CAAP;QACD,CAZD;;QAcAtD,IAAI,CAAC0E,iBAAL,GAAyB,UAAUxB,EAAV,EAAcP,IAAd,EAAoBgC,kBAApB,EAAwCrC,QAAxC,EAAkD;UACzE,IAAIF,OAAO,GAAGrD,mBAAmB,CAACmB,GAApB,CAAwBgD,EAAxB,CAAd;;UAEA,IAAId,OAAO,KAAKjC,SAAhB,EAA2B;YACzBnB,aAAa,CAACsC,GAAd,CAAkBqB,IAAlB,EAAwBP,OAAxB;YACA,IAAIF,OAAO,GAAGS,IAAI,CAACT,OAAnB;YACA,IAAIkD,SAAS,GAAGlD,OAAO,CAACkD,SAAxB,CAHyB,CAGU;YACnC;YACA;;YAEA,IAAIA,SAAS,KAAK,IAAlB,EAAwB;cACtB,IAAIC,UAAU,GAAGD,SAAS,CAACE,aAAV,IAA2B,IAA3B,IAAmCF,SAAS,CAACE,aAAV,CAAwBxC,OAAxB,IAAmC,IAAvF;cACA,IAAIyC,SAAS,GAAGrD,OAAO,CAACoD,aAAR,IAAyB,IAAzB,IAAiCpD,OAAO,CAACoD,aAAR,CAAsBxC,OAAtB,IAAiC,IAAlF;;cAEA,IAAI,CAACuC,UAAD,IAAeE,SAAnB,EAA8B;gBAC5B;gBACAtG,YAAY,CAACuC,GAAb,CAAiBmB,IAAjB;gBACAxD,WAAW,CAACgG,MAAZ,CAAmBxC,IAAnB;cACD,CAJD,MAIO,IAAI0C,UAAU,IAAIE,SAAlB,EAA6B,CAA7B,KAAoC,IAAIF,UAAU,IAAI,CAACE,SAAnB,EAA8B;gBACvE;gBACAtG,YAAY,CAACkG,MAAb,CAAoBxC,IAApB;;gBAEA,IAAIL,QAAJ,EAAc;kBACZ;kBACAnD,WAAW,CAACqC,GAAZ,CAAgBmB,IAAhB;gBACD,CAHD,MAGO;kBACL3D,aAAa,CAACmG,MAAd,CAAqBxC,IAArB;gBACD;cACF,CAV0C,MAUpC,IAAI,CAAC0C,UAAD,IAAe,CAACE,SAApB,EAA+B;gBACpC,IAAIjD,QAAJ,EAAc;kBACZ;kBACAnD,WAAW,CAACqC,GAAZ,CAAgBmB,IAAhB;gBACD;cACF;YACF,CAxBD,MAwBO;cACL;cACA1D,YAAY,CAACuC,GAAb,CAAiBmB,IAAjB;YACD;UACF,CAtCwE,CAsCvE;;;UAGF,OAAOsC,oBAAoB,CAACD,KAArB,CAA2B,IAA3B,EAAiC1B,SAAjC,CAAP;QACD,CA1CD;MA2CD;IACF;;IACD,SAASkC,sBAAT,GAAkC;MAChC;MACA,OAAO,KAAP;IACD,CApiBa,CAoiBZ;;;IAEF,SAASC,oBAAT,GAAgC;MAC9B;QACE,OAAOxG,YAAY,CAACyG,IAApB;MACD;IACF,CA1iBa,CA0iBZ;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IAEA,SAASC,mCAAT,GAA+C;MAC7C;QACE,IAAIC,SAAJ;QACA,IAAIC,cAAJ;QACA,IAAIC,eAAe,GAAG,KAAtB;QACA,OAAO,UAAUnF,IAAV,EAAgBU,GAAhB,EAAqBxB,UAArB,EAAiCF,cAAjC,EAAiD;UACtD,IAAI,OAAO0B,GAAP,KAAe,QAAnB,EAA6B;YAC3B;YACA;YACA;YACA,IAAI,CAACuE,SAAL,EAAgB;cACd;cACAA,SAAS,GAAGjF,IAAZ;cACAkF,cAAc,GAAG,OAAOlG,cAAP,KAA0B,UAA3C;YACD,CAR0B,CAQzB;YACF;YACA;;;YAGA,IAAIgB,IAAI,IAAI,IAAR,KAAiB,OAAOA,IAAP,KAAgB,UAAhB,IAA8B,OAAOA,IAAP,KAAgB,QAA/D,CAAJ,EAA8E;cAC5E0C,YAAY,CAAC1C,IAAD,EAAOU,GAAP,EAAYxB,UAAZ,EAAwBF,cAAxB,CAAZ;YACD;;YAED,OAAOgB,IAAP;UACD,CAlBD,MAkBO;YACL;YACA;YACA;YACA,IAAI,CAACmF,eAAD,IAAoBD,cAAxB,EAAwC;cACtCC,eAAe,GAAG,IAAlB;cACAvC,8BAA8B,CAACqC,SAAD,CAA9B;YACD;UACF;QACF,CA5BD;MA6BD;IACF;;IACD,SAASG,qBAAT,CAA+BpF,IAA/B,EAAqC;MACnC;QACE,QAAQ,OAAOA,IAAf;UACE,KAAK,UAAL;YACE;cACE;cACA,IAAIA,IAAI,CAACC,SAAL,IAAkB,IAAtB,EAA4B;gBAC1B,IAAID,IAAI,CAACC,SAAL,CAAeC,gBAAnB,EAAqC;kBACnC;kBACA,OAAO,IAAP;gBACD;;gBAED,IAAImF,QAAQ,GAAGC,MAAM,CAACC,mBAAP,CAA2BvF,IAAI,CAACC,SAAhC,CAAf;;gBAEA,IAAIoF,QAAQ,CAACjG,MAAT,GAAkB,CAAlB,IAAuBiG,QAAQ,CAAC,CAAD,CAAR,KAAgB,aAA3C,EAA0D;kBACxD;kBACA,OAAO,KAAP;gBACD,CAXyB,CAWxB;;;gBAGF,IAAIrF,IAAI,CAACC,SAAL,CAAeuF,SAAf,KAA6BF,MAAM,CAACrF,SAAxC,EAAmD;kBACjD;kBACA,OAAO,KAAP;gBACD,CAjByB,CAiBxB;gBACF;;cAED,CAtBH,CAsBI;;;cAGF,IAAIwF,IAAI,GAAGzF,IAAI,CAACyF,IAAL,IAAazF,IAAI,CAAC0F,WAA7B;cACA,OAAO,OAAOD,IAAP,KAAgB,QAAhB,IAA4B,SAASE,IAAT,CAAcF,IAAd,CAAnC;YACD;;UAEH,KAAK,QAAL;YACE;cACE,IAAIzF,IAAI,IAAI,IAAZ,EAAkB;gBAChB,QAAQc,WAAW,CAACd,IAAD,EAAO,UAAP,CAAnB;kBACE,KAAKjD,sBAAL;kBACA,KAAKG,eAAL;oBACE;oBACA,OAAO,IAAP;;kBAEF;oBACE,OAAO,KAAP;gBAPJ;cASD;;cAED,OAAO,KAAP;YACD;;UAEH;YACE;cACE,OAAO,KAAP;YACD;QAnDL;MAqDD;IACF;;IAED0I,OAAO,CAACd,oBAAR,GAA+BA,oBAA/B;IACAc,OAAO,CAAChD,8BAAR,GAAyCA,8BAAzC;IACAgD,OAAO,CAACZ,mCAAR,GAA8CA,mCAA9C;IACAY,OAAO,CAAC7C,yBAAR,GAAoCA,yBAApC;IACA6C,OAAO,CAAC/C,aAAR,GAAwBA,aAAxB;IACA+C,OAAO,CAAC9C,eAAR,GAA0BA,eAA1B;IACA8C,OAAO,CAACf,sBAAR,GAAiCA,sBAAjC;IACAe,OAAO,CAACvC,oBAAR,GAA+BA,oBAA/B;IACAuC,OAAO,CAACR,qBAAR,GAAgCA,qBAAhC;IACAQ,OAAO,CAAC3E,mBAAR,GAA8BA,mBAA9B;IACA2E,OAAO,CAACtD,QAAR,GAAmBA,QAAnB;IACAsD,OAAO,CAAClD,YAAR,GAAuBA,YAAvB;EACG,CA3qBD;AA4qBD"}, "metadata": {}, "sourceType": "script"}