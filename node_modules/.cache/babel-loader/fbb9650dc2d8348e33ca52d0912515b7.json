{"ast": null, "code": "import { AssetsList } from \"../elements/assetsList\";\nexport const ServicesData = [{\n  name: \"Retrofitting / Structural Strengthening\",\n  icon: AssetsList.services.Retro\n}, {\n  name: \"Waterproofing\",\n  icon: AssetsList.services.Waterproofing\n}, {\n  name: \"Environmental Coatings\",\n  icon: AssetsList.services.Envcoating\n}, {\n  name: \"Industrial Buildings\",\n  icon: AssetsList.services.Industrial\n}, {\n  name: \"Public Buildings\",\n  icon: AssetsList.services.Publicbuildings\n}, {\n  name: \"Water Tanks\",\n  icon: AssetsList.services.Watertank\n}];", "map": {"version": 3, "names": ["AssetsList", "ServicesData", "name", "icon", "services", "Retro", "Waterproofing", "Envcoating", "Industrial", "Publicbuildings", "Watertank"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/servicesData.js"], "sourcesContent": ["import { AssetsList } from \"../elements/assetsList\";\n\nexport const ServicesData = [\n  {\n    name: \"Retrofitting / Structural Strengthening\",\n    icon: AssetsList.services.Retro,\n  },\n  {\n    name: \"Waterproofing\",\n    icon: AssetsList.services.Waterproofing,\n  },\n  {\n    name: \"Environmental Coatings\",\n    icon: AssetsList.services.Envcoating,\n  },\n  {\n    name: \"Industrial Buildings\",\n    icon: AssetsList.services.Industrial,\n  },\n  {\n    name: \"Public Buildings\",\n    icon: AssetsList.services.Publicbuildings,\n  },\n  {\n    name: \"Water Tanks\",\n    icon: AssetsList.services.Watertank,\n  },\n];\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,wBAA3B;AAEA,OAAO,MAAMC,YAAY,GAAG,CAC1B;EACEC,IAAI,EAAE,yCADR;EAEEC,IAAI,EAAEH,UAAU,CAACI,QAAX,CAAoBC;AAF5B,CAD0B,EAK1B;EACEH,IAAI,EAAE,eADR;EAEEC,IAAI,EAAEH,UAAU,CAACI,QAAX,CAAoBE;AAF5B,CAL0B,EAS1B;EACEJ,IAAI,EAAE,wBADR;EAEEC,IAAI,EAAEH,UAAU,CAACI,QAAX,CAAoBG;AAF5B,CAT0B,EAa1B;EACEL,IAAI,EAAE,sBADR;EAEEC,IAAI,EAAEH,UAAU,CAACI,QAAX,CAAoBI;AAF5B,CAb0B,EAiB1B;EACEN,IAAI,EAAE,kBADR;EAEEC,IAAI,EAAEH,UAAU,CAACI,QAAX,CAAoBK;AAF5B,CAjB0B,EAqB1B;EACEP,IAAI,EAAE,aADR;EAEEC,IAAI,EAAEH,UAAU,CAACI,QAAX,CAAoBM;AAF5B,CArB0B,CAArB"}, "metadata": {}, "sourceType": "module"}