{"ast": null, "code": "import styled from \"styled-components\";\nexport const Header = styled.header`\n  background-color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.white;\n}};\n  padding: 15px 0;\n\n  box-shadow: 0 0 10px #00000010;\n\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n\n  height: 100vh;\n  width: 100vw;\n\n  z-index: 9999;\n\n  transform: ${_ref2 => {\n  let {\n    active\n  } = _ref2;\n  return active ? \"translateX(0)\" : \"translateX(-100%)\";\n}};\n\n  transition: all 0.3s ease-in-out;\n`;\nexport const Container = styled.div`\n  position: relative;\n  padding-top: 120px;\n`;\nexport const Wtapper = styled.div`\n  text-align: center;\n\n  .logo {\n    margin: auto;\n\n    transform: scale(2);\n  }\n`;\nexport const MenuContainer = styled.div`\n  text-align: center;\n\n  margin: 90px 0;\n`;\nexport const MenuListWrapper = styled.ul`\n  list-style-type: none;\n  margin-bottom: 0;\n  padding-left: 0;\n`;\nexport const MenuItem = styled.li`\n  display: block;\n  padding: 10px 15px;\n  font-size: ${_ref3 => {\n  let {\n    theme: {\n      font\n    }\n  } = _ref3;\n  return font.big;\n}};\n  font-weight: ${_ref4 => {\n  let {\n    active\n  } = _ref4;\n  return active ? \"bold\" : 400;\n}};\n  color: ${_ref5 => {\n  let {\n    active,\n    theme: {\n      colors\n    }\n  } = _ref5;\n  return active ? colors.main.red : colors.black;\n}};\n\n  &.button {\n    background-color: ${_ref6 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref6;\n  return colors.main.yellow;\n}};\n    padding: 5px 10px;\n    border-radius: 5px;\n    color: ${_ref7 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref7;\n  return colors.black;\n}};\n  }\n`;\nexport const MobileMenuContainer = styled.div`\n  position: absolute;\n  top: 15px;\n  right: 15px;\n\n  svg {\n    width: 40px;\n    height: 40px;\n  }\n`;", "map": {"version": 3, "names": ["styled", "Header", "header", "theme", "colors", "white", "active", "Container", "div", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MenuListWrapper", "ul", "MenuItem", "li", "font", "big", "main", "red", "black", "yellow", "MobileMenuContainer"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Header = styled.header`\n  background-color: ${({ theme: { colors } }) => colors.white};\n  padding: 15px 0;\n\n  box-shadow: 0 0 10px #00000010;\n\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n\n  height: 100vh;\n  width: 100vw;\n\n  z-index: 9999;\n\n  transform: ${({ active }) =>\n    active ? \"translateX(0)\" : \"translateX(-100%)\"};\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const Container = styled.div`\n  position: relative;\n  padding-top: 120px;\n`;\n\nexport const Wtapper = styled.div`\n  text-align: center;\n\n  .logo {\n    margin: auto;\n\n    transform: scale(2);\n  }\n`;\n\nexport const MenuContainer = styled.div`\n  text-align: center;\n\n  margin: 90px 0;\n`;\n\nexport const MenuListWrapper = styled.ul`\n  list-style-type: none;\n  margin-bottom: 0;\n  padding-left: 0;\n`;\n\nexport const MenuItem = styled.li`\n  display: block;\n  padding: 10px 15px;\n  font-size: ${({ theme: { font } }) => font.big};\n  font-weight: ${({ active }) => (active ? \"bold\" : 400)};\n  color: ${({ active, theme: { colors } }) =>\n    active ? colors.main.red : colors.black};\n\n  &.button {\n    background-color: ${({ theme: { colors } }) => colors.main.yellow};\n    padding: 5px 10px;\n    border-radius: 5px;\n    color: ${({ theme: { colors } }) => colors.black};\n  }\n`;\n\nexport const MobileMenuContainer = styled.div`\n  position: absolute;\n  top: 15px;\n  right: 15px;\n\n  svg {\n    width: 40px;\n    height: 40px;\n  }\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,MAAM,GAAGD,MAAM,CAACE,MAAO;AACpC,sBAAsB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,KAAlC;AAAA,CAAwC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EAAA,IAAC;IAAEC;EAAF,CAAD;EAAA,OACXA,MAAM,GAAG,eAAH,GAAqB,mBADhB;AAAA,CACoC;AACnD;AACA;AACA,CApBO;AAsBP,OAAO,MAAMC,SAAS,GAAGP,MAAM,CAACQ,GAAI;AACpC;AACA;AACA,CAHO;AAKP,OAAO,MAAMC,OAAO,GAAGT,MAAM,CAACQ,GAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CARO;AAUP,OAAO,MAAME,aAAa,GAAGV,MAAM,CAACQ,GAAI;AACxC;AACA;AACA;AACA,CAJO;AAMP,OAAO,MAAMG,eAAe,GAAGX,MAAM,CAACY,EAAG;AACzC;AACA;AACA;AACA,CAJO;AAMP,OAAO,MAAMC,QAAQ,GAAGb,MAAM,CAACc,EAAG;AAClC;AACA;AACA,eAAe;EAAA,IAAC;IAAEX,KAAK,EAAE;MAAEY;IAAF;EAAT,CAAD;EAAA,OAAyBA,IAAI,CAACC,GAA9B;AAAA,CAAkC;AACjD,iBAAiB;EAAA,IAAC;IAAEV;EAAF,CAAD;EAAA,OAAiBA,MAAM,GAAG,MAAH,GAAY,GAAnC;AAAA,CAAwC;AACzD,WAAW;EAAA,IAAC;IAAEA,MAAF;IAAUH,KAAK,EAAE;MAAEC;IAAF;EAAjB,CAAD;EAAA,OACPE,MAAM,GAAGF,MAAM,CAACa,IAAP,CAAYC,GAAf,GAAqBd,MAAM,CAACe,KAD3B;AAAA,CACiC;AAC5C;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEhB,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACa,IAAP,CAAYG,MAAvC;AAAA,CAA8C;AACtE;AACA;AACA,aAAa;EAAA,IAAC;IAAEjB,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACe,KAAlC;AAAA,CAAwC;AACrD;AACA,CAdO;AAgBP,OAAO,MAAME,mBAAmB,GAAGrB,MAAM,CAACQ,GAAI;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CATO"}, "metadata": {}, "sourceType": "module"}