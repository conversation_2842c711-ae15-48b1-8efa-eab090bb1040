{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/index.jsx\";\nimport React from \"react\";\nimport { AssetsList } from \"../../../elements/assetsList\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst InfoField = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    className: \"flex-wrap flex-lg-nowrap justify-content-center justify-content-md-start\",\n    children: [/*#__PURE__*/_jsxDEV(Styles.ImageHolder, {\n      src: AssetsList.signFormIcon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.TextWrapper, {\n      children: [/*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n        className: \"mb-3\",\n        heading: true,\n        children: \"Get In Touch With Us\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n        children: \"Put your email address and get started\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n\n_c = InfoField;\nexport default InfoField;\n\nvar _c;\n\n$RefreshReg$(_c, \"InfoField\");", "map": {"version": 3, "names": ["React", "AssetsList", "Styles", "InfoField", "signFormIcon"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { AssetsList } from \"../../../elements/assetsList\";\n\nimport * as Styles from \"./styles\";\n\nconst InfoField = () => {\n  return (\n    <Styles.Container className=\"flex-wrap flex-lg-nowrap justify-content-center justify-content-md-start\">\n      <Styles.ImageHolder src={AssetsList.signFormIcon} />\n      <Styles.TextWrapper>\n        <Styles.TextHolder className=\"mb-3\" heading>\n          Get In Touch With Us\n        </Styles.TextHolder>\n        <Styles.TextHolder>\n          Put your email address and get started\n        </Styles.TextHolder>\n      </Styles.TextWrapper>\n    </Styles.Container>\n  );\n};\n\nexport default InfoField;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,UAAT,QAA2B,8BAA3B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAkB,SAAS,EAAC,0EAA5B;IAAA,wBACE,QAAC,MAAD,CAAQ,WAAR;MAAoB,GAAG,EAAEF,UAAU,CAACG;IAApC;MAAA;MAAA;MAAA;IAAA,QADF,eAEE,QAAC,MAAD,CAAQ,WAAR;MAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;QAAmB,SAAS,EAAC,MAA7B;QAAoC,OAAO,MAA3C;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QADF,eAIE,QAAC,MAAD,CAAQ,UAAR;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAJF;IAAA;MAAA;MAAA;MAAA;IAAA,QAFF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAaD,CAdD;;KAAMD,S;AAgBN,eAAeA,SAAf"}, "metadata": {}, "sourceType": "module"}