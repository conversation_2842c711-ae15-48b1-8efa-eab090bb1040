{"ast": null, "code": "import styled from \"styled-components\";\nexport const CardContainer = styled.div`\n  padding: 40px 30px;\n  background-color: #fff;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);\n\n  text-align: center;\n\n  border-radius: 20px;\n\n  margin: 0 10px;\n\n  flex: 1;\n  height: 100%;\n\n  &:hover {\n    background-color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.main.blue;\n}};\n    color: #fff;\n  }\n\n  transition: all 0.2s ease-in-out;\n`;\nexport const Wrapper = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\nexport const CardTitle = styled.h4`\n  font-weight: 600;\n`;\nexport const TextHolder = styled.p`\n  flex: 1;\n`;\nexport const ListContainer = styled.div``;\nexport const ListWrapper = styled.div`\n  .react-multi-carousel-track {\n    /* gap: 20px; */\n  }\n`;", "map": {"version": 3, "names": ["styled", "CardContainer", "div", "theme", "colors", "main", "blue", "Wrapper", "CardTitle", "h4", "TextHolder", "p", "ListContainer", "ListWrapper"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const CardContainer = styled.div`\n  padding: 40px 30px;\n  background-color: #fff;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);\n\n  text-align: center;\n\n  border-radius: 20px;\n\n  margin: 0 10px;\n\n  flex: 1;\n  height: 100%;\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: #fff;\n  }\n\n  transition: all 0.2s ease-in-out;\n`;\n\nexport const Wrapper = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nexport const CardTitle = styled.h4`\n  font-weight: 600;\n`;\n\nexport const TextHolder = styled.p`\n  flex: 1;\n`;\n\nexport const ListContainer = styled.div``;\n\nexport const ListWrapper = styled.div`\n  .react-multi-carousel-track {\n    /* gap: 20px; */\n  }\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,aAAa,GAAGD,MAAM,CAACE,GAAI;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AACpE;AACA;AACA;AACA;AACA,CArBO;AAuBP,OAAO,MAAMC,OAAO,GAAGP,MAAM,CAACE,GAAI;AAClC;AACA;AACA,CAHO;AAKP,OAAO,MAAMM,SAAS,GAAGR,MAAM,CAACS,EAAG;AACnC;AACA,CAFO;AAIP,OAAO,MAAMC,UAAU,GAAGV,MAAM,CAACW,CAAE;AACnC;AACA,CAFO;AAIP,OAAO,MAAMC,aAAa,GAAGZ,MAAM,CAACE,GAAI,EAAjC;AAEP,OAAO,MAAMW,WAAW,GAAGb,MAAM,CAACE,GAAI;AACtC;AACA;AACA;AACA,CAJO"}, "metadata": {}, "sourceType": "module"}