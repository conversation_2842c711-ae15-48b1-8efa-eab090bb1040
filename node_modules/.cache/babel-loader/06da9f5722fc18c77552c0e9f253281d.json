{"ast": null, "code": "/**\n * @param {{ protocol?: string, auth?: string, hostname?: string, port?: string, pathname?: string, search?: string, hash?: string, slashes?: boolean }} objURL\n * @returns {string}\n */\nfunction format(objURL) {\n  var protocol = objURL.protocol || \"\";\n\n  if (protocol && protocol.substr(-1) !== \":\") {\n    protocol += \":\";\n  }\n\n  var auth = objURL.auth || \"\";\n\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, \":\");\n    auth += \"@\";\n  }\n\n  var host = \"\";\n\n  if (objURL.hostname) {\n    host = auth + (objURL.hostname.indexOf(\":\") === -1 ? objURL.hostname : \"[\".concat(objURL.hostname, \"]\"));\n\n    if (objURL.port) {\n      host += \":\".concat(objURL.port);\n    }\n  }\n\n  var pathname = objURL.pathname || \"\";\n\n  if (objURL.slashes) {\n    host = \"//\".concat(host || \"\");\n\n    if (pathname && pathname.charAt(0) !== \"/\") {\n      pathname = \"/\".concat(pathname);\n    }\n  } else if (!host) {\n    host = \"\";\n  }\n\n  var search = objURL.search || \"\";\n\n  if (search && search.charAt(0) !== \"?\") {\n    search = \"?\".concat(search);\n  }\n\n  var hash = objURL.hash || \"\";\n\n  if (hash && hash.charAt(0) !== \"#\") {\n    hash = \"#\".concat(hash);\n  }\n\n  pathname = pathname.replace(/[?#]/g,\n  /**\n   * @param {string} match\n   * @returns {string}\n   */\n  function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace(\"#\", \"%23\");\n  return \"\".concat(protocol).concat(host).concat(pathname).concat(search).concat(hash);\n}\n/**\n * @param {URL & { fromCurrentScript?: boolean }} parsedURL\n * @returns {string}\n */\n\n\nfunction createSocketURL(parsedURL) {\n  var hostname = parsedURL.hostname; // Node.js module parses it as `::`\n  // `new URL(urlString, [baseURLString])` parses it as '[::]'\n\n  var isInAddrAny = hostname === \"0.0.0.0\" || hostname === \"::\" || hostname === \"[::]\"; // why do we need this check?\n  // hostname n/a for file protocol (example, when using electron, ionic)\n  // see: https://github.com/webpack/webpack-dev-server/pull/384\n\n  if (isInAddrAny && self.location.hostname && self.location.protocol.indexOf(\"http\") === 0) {\n    hostname = self.location.hostname;\n  }\n\n  var socketURLProtocol = parsedURL.protocol || self.location.protocol; // When https is used in the app, secure web sockets are always necessary because the browser doesn't accept non-secure web sockets.\n\n  if (socketURLProtocol === \"auto:\" || hostname && isInAddrAny && self.location.protocol === \"https:\") {\n    socketURLProtocol = self.location.protocol;\n  }\n\n  socketURLProtocol = socketURLProtocol.replace(/^(?:http|.+-extension|file)/i, \"ws\");\n  var socketURLAuth = \"\"; // `new URL(urlString, [baseURLstring])` doesn't have `auth` property\n  // Parse authentication credentials in case we need them\n\n  if (parsedURL.username) {\n    socketURLAuth = parsedURL.username; // Since HTTP basic authentication does not allow empty username,\n    // we only include password if the username is not empty.\n\n    if (parsedURL.password) {\n      // Result: <username>:<password>\n      socketURLAuth = socketURLAuth.concat(\":\", parsedURL.password);\n    }\n  } // In case the host is a raw IPv6 address, it can be enclosed in\n  // the brackets as the brackets are needed in the final URL string.\n  // Need to remove those as url.format blindly adds its own set of brackets\n  // if the host string contains colons. That would lead to non-working\n  // double brackets (e.g. [[::]]) host\n  //\n  // All of these web socket url params are optionally passed in through resourceQuery,\n  // so we need to fall back to the default if they are not provided\n\n\n  var socketURLHostname = (hostname || self.location.hostname || \"localhost\").replace(/^\\[(.*)\\]$/, \"$1\");\n  var socketURLPort = parsedURL.port;\n\n  if (!socketURLPort || socketURLPort === \"0\") {\n    socketURLPort = self.location.port;\n  } // If path is provided it'll be passed in via the resourceQuery as a\n  // query param so it has to be parsed out of the querystring in order for the\n  // client to open the socket to the correct location.\n\n\n  var socketURLPathname = \"/ws\";\n\n  if (parsedURL.pathname && !parsedURL.fromCurrentScript) {\n    socketURLPathname = parsedURL.pathname;\n  }\n\n  return format({\n    protocol: socketURLProtocol,\n    auth: socketURLAuth,\n    hostname: socketURLHostname,\n    port: socketURLPort,\n    pathname: socketURLPathname,\n    slashes: true\n  });\n}\n\nexport default createSocketURL;", "map": {"version": 3, "names": ["format", "objURL", "protocol", "substr", "auth", "encodeURIComponent", "replace", "host", "hostname", "indexOf", "concat", "port", "pathname", "slashes", "char<PERSON>t", "search", "hash", "match", "createSocketURL", "parsedURL", "isInAddrAny", "self", "location", "socketURLProtocol", "socketURLAuth", "username", "password", "socketURLHostname", "socketURLPort", "socketURLPathname", "fromCurrentScript"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/webpack-dev-server/client/utils/createSocketURL.js"], "sourcesContent": ["/**\n * @param {{ protocol?: string, auth?: string, hostname?: string, port?: string, pathname?: string, search?: string, hash?: string, slashes?: boolean }} objURL\n * @returns {string}\n */\nfunction format(objURL) {\n  var protocol = objURL.protocol || \"\";\n\n  if (protocol && protocol.substr(-1) !== \":\") {\n    protocol += \":\";\n  }\n\n  var auth = objURL.auth || \"\";\n\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, \":\");\n    auth += \"@\";\n  }\n\n  var host = \"\";\n\n  if (objURL.hostname) {\n    host = auth + (objURL.hostname.indexOf(\":\") === -1 ? objURL.hostname : \"[\".concat(objURL.hostname, \"]\"));\n\n    if (objURL.port) {\n      host += \":\".concat(objURL.port);\n    }\n  }\n\n  var pathname = objURL.pathname || \"\";\n\n  if (objURL.slashes) {\n    host = \"//\".concat(host || \"\");\n\n    if (pathname && pathname.charAt(0) !== \"/\") {\n      pathname = \"/\".concat(pathname);\n    }\n  } else if (!host) {\n    host = \"\";\n  }\n\n  var search = objURL.search || \"\";\n\n  if (search && search.charAt(0) !== \"?\") {\n    search = \"?\".concat(search);\n  }\n\n  var hash = objURL.hash || \"\";\n\n  if (hash && hash.charAt(0) !== \"#\") {\n    hash = \"#\".concat(hash);\n  }\n\n  pathname = pathname.replace(/[?#]/g,\n  /**\n   * @param {string} match\n   * @returns {string}\n   */\n  function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace(\"#\", \"%23\");\n  return \"\".concat(protocol).concat(host).concat(pathname).concat(search).concat(hash);\n}\n/**\n * @param {URL & { fromCurrentScript?: boolean }} parsedURL\n * @returns {string}\n */\n\n\nfunction createSocketURL(parsedURL) {\n  var hostname = parsedURL.hostname; // Node.js module parses it as `::`\n  // `new URL(urlString, [baseURLString])` parses it as '[::]'\n\n  var isInAddrAny = hostname === \"0.0.0.0\" || hostname === \"::\" || hostname === \"[::]\"; // why do we need this check?\n  // hostname n/a for file protocol (example, when using electron, ionic)\n  // see: https://github.com/webpack/webpack-dev-server/pull/384\n\n  if (isInAddrAny && self.location.hostname && self.location.protocol.indexOf(\"http\") === 0) {\n    hostname = self.location.hostname;\n  }\n\n  var socketURLProtocol = parsedURL.protocol || self.location.protocol; // When https is used in the app, secure web sockets are always necessary because the browser doesn't accept non-secure web sockets.\n\n  if (socketURLProtocol === \"auto:\" || hostname && isInAddrAny && self.location.protocol === \"https:\") {\n    socketURLProtocol = self.location.protocol;\n  }\n\n  socketURLProtocol = socketURLProtocol.replace(/^(?:http|.+-extension|file)/i, \"ws\");\n  var socketURLAuth = \"\"; // `new URL(urlString, [baseURLstring])` doesn't have `auth` property\n  // Parse authentication credentials in case we need them\n\n  if (parsedURL.username) {\n    socketURLAuth = parsedURL.username; // Since HTTP basic authentication does not allow empty username,\n    // we only include password if the username is not empty.\n\n    if (parsedURL.password) {\n      // Result: <username>:<password>\n      socketURLAuth = socketURLAuth.concat(\":\", parsedURL.password);\n    }\n  } // In case the host is a raw IPv6 address, it can be enclosed in\n  // the brackets as the brackets are needed in the final URL string.\n  // Need to remove those as url.format blindly adds its own set of brackets\n  // if the host string contains colons. That would lead to non-working\n  // double brackets (e.g. [[::]]) host\n  //\n  // All of these web socket url params are optionally passed in through resourceQuery,\n  // so we need to fall back to the default if they are not provided\n\n\n  var socketURLHostname = (hostname || self.location.hostname || \"localhost\").replace(/^\\[(.*)\\]$/, \"$1\");\n  var socketURLPort = parsedURL.port;\n\n  if (!socketURLPort || socketURLPort === \"0\") {\n    socketURLPort = self.location.port;\n  } // If path is provided it'll be passed in via the resourceQuery as a\n  // query param so it has to be parsed out of the querystring in order for the\n  // client to open the socket to the correct location.\n\n\n  var socketURLPathname = \"/ws\";\n\n  if (parsedURL.pathname && !parsedURL.fromCurrentScript) {\n    socketURLPathname = parsedURL.pathname;\n  }\n\n  return format({\n    protocol: socketURLProtocol,\n    auth: socketURLAuth,\n    hostname: socketURLHostname,\n    port: socketURLPort,\n    pathname: socketURLPathname,\n    slashes: true\n  });\n}\n\nexport default createSocketURL;"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,MAAT,CAAgBC,MAAhB,EAAwB;EACtB,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAP,IAAmB,EAAlC;;EAEA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAT,CAAgB,CAAC,CAAjB,MAAwB,GAAxC,EAA6C;IAC3CD,QAAQ,IAAI,GAAZ;EACD;;EAED,IAAIE,IAAI,GAAGH,MAAM,CAACG,IAAP,IAAe,EAA1B;;EAEA,IAAIA,IAAJ,EAAU;IACRA,IAAI,GAAGC,kBAAkB,CAACD,IAAD,CAAzB;IACAA,IAAI,GAAGA,IAAI,CAACE,OAAL,CAAa,MAAb,EAAqB,GAArB,CAAP;IACAF,IAAI,IAAI,GAAR;EACD;;EAED,IAAIG,IAAI,GAAG,EAAX;;EAEA,IAAIN,MAAM,CAACO,QAAX,EAAqB;IACnBD,IAAI,GAAGH,IAAI,IAAIH,MAAM,CAACO,QAAP,CAAgBC,OAAhB,CAAwB,GAAxB,MAAiC,CAAC,CAAlC,GAAsCR,MAAM,CAACO,QAA7C,GAAwD,IAAIE,MAAJ,CAAWT,MAAM,CAACO,QAAlB,EAA4B,GAA5B,CAA5D,CAAX;;IAEA,IAAIP,MAAM,CAACU,IAAX,EAAiB;MACfJ,IAAI,IAAI,IAAIG,MAAJ,CAAWT,MAAM,CAACU,IAAlB,CAAR;IACD;EACF;;EAED,IAAIC,QAAQ,GAAGX,MAAM,CAACW,QAAP,IAAmB,EAAlC;;EAEA,IAAIX,MAAM,CAACY,OAAX,EAAoB;IAClBN,IAAI,GAAG,KAAKG,MAAL,CAAYH,IAAI,IAAI,EAApB,CAAP;;IAEA,IAAIK,QAAQ,IAAIA,QAAQ,CAACE,MAAT,CAAgB,CAAhB,MAAuB,GAAvC,EAA4C;MAC1CF,QAAQ,GAAG,IAAIF,MAAJ,CAAWE,QAAX,CAAX;IACD;EACF,CAND,MAMO,IAAI,CAACL,IAAL,EAAW;IAChBA,IAAI,GAAG,EAAP;EACD;;EAED,IAAIQ,MAAM,GAAGd,MAAM,CAACc,MAAP,IAAiB,EAA9B;;EAEA,IAAIA,MAAM,IAAIA,MAAM,CAACD,MAAP,CAAc,CAAd,MAAqB,GAAnC,EAAwC;IACtCC,MAAM,GAAG,IAAIL,MAAJ,CAAWK,MAAX,CAAT;EACD;;EAED,IAAIC,IAAI,GAAGf,MAAM,CAACe,IAAP,IAAe,EAA1B;;EAEA,IAAIA,IAAI,IAAIA,IAAI,CAACF,MAAL,CAAY,CAAZ,MAAmB,GAA/B,EAAoC;IAClCE,IAAI,GAAG,IAAIN,MAAJ,CAAWM,IAAX,CAAP;EACD;;EAEDJ,QAAQ,GAAGA,QAAQ,CAACN,OAAT,CAAiB,OAAjB;EACX;AACF;AACA;AACA;EACE,UAAUW,KAAV,EAAiB;IACf,OAAOZ,kBAAkB,CAACY,KAAD,CAAzB;EACD,CAPU,CAAX;EAQAF,MAAM,GAAGA,MAAM,CAACT,OAAP,CAAe,GAAf,EAAoB,KAApB,CAAT;EACA,OAAO,GAAGI,MAAH,CAAUR,QAAV,EAAoBQ,MAApB,CAA2BH,IAA3B,EAAiCG,MAAjC,CAAwCE,QAAxC,EAAkDF,MAAlD,CAAyDK,MAAzD,EAAiEL,MAAjE,CAAwEM,IAAxE,CAAP;AACD;AACD;AACA;AACA;AACA;;;AAGA,SAASE,eAAT,CAAyBC,SAAzB,EAAoC;EAClC,IAAIX,QAAQ,GAAGW,SAAS,CAACX,QAAzB,CADkC,CACC;EACnC;;EAEA,IAAIY,WAAW,GAAGZ,QAAQ,KAAK,SAAb,IAA0BA,QAAQ,KAAK,IAAvC,IAA+CA,QAAQ,KAAK,MAA9E,CAJkC,CAIoD;EACtF;EACA;;EAEA,IAAIY,WAAW,IAAIC,IAAI,CAACC,QAAL,CAAcd,QAA7B,IAAyCa,IAAI,CAACC,QAAL,CAAcpB,QAAd,CAAuBO,OAAvB,CAA+B,MAA/B,MAA2C,CAAxF,EAA2F;IACzFD,QAAQ,GAAGa,IAAI,CAACC,QAAL,CAAcd,QAAzB;EACD;;EAED,IAAIe,iBAAiB,GAAGJ,SAAS,CAACjB,QAAV,IAAsBmB,IAAI,CAACC,QAAL,CAAcpB,QAA5D,CAZkC,CAYoC;;EAEtE,IAAIqB,iBAAiB,KAAK,OAAtB,IAAiCf,QAAQ,IAAIY,WAAZ,IAA2BC,IAAI,CAACC,QAAL,CAAcpB,QAAd,KAA2B,QAA3F,EAAqG;IACnGqB,iBAAiB,GAAGF,IAAI,CAACC,QAAL,CAAcpB,QAAlC;EACD;;EAEDqB,iBAAiB,GAAGA,iBAAiB,CAACjB,OAAlB,CAA0B,8BAA1B,EAA0D,IAA1D,CAApB;EACA,IAAIkB,aAAa,GAAG,EAApB,CAnBkC,CAmBV;EACxB;;EAEA,IAAIL,SAAS,CAACM,QAAd,EAAwB;IACtBD,aAAa,GAAGL,SAAS,CAACM,QAA1B,CADsB,CACc;IACpC;;IAEA,IAAIN,SAAS,CAACO,QAAd,EAAwB;MACtB;MACAF,aAAa,GAAGA,aAAa,CAACd,MAAd,CAAqB,GAArB,EAA0BS,SAAS,CAACO,QAApC,CAAhB;IACD;EACF,CA9BiC,CA8BhC;EACF;EACA;EACA;EACA;EACA;EACA;EACA;;;EAGA,IAAIC,iBAAiB,GAAG,CAACnB,QAAQ,IAAIa,IAAI,CAACC,QAAL,CAAcd,QAA1B,IAAsC,WAAvC,EAAoDF,OAApD,CAA4D,YAA5D,EAA0E,IAA1E,CAAxB;EACA,IAAIsB,aAAa,GAAGT,SAAS,CAACR,IAA9B;;EAEA,IAAI,CAACiB,aAAD,IAAkBA,aAAa,KAAK,GAAxC,EAA6C;IAC3CA,aAAa,GAAGP,IAAI,CAACC,QAAL,CAAcX,IAA9B;EACD,CA7CiC,CA6ChC;EACF;EACA;;;EAGA,IAAIkB,iBAAiB,GAAG,KAAxB;;EAEA,IAAIV,SAAS,CAACP,QAAV,IAAsB,CAACO,SAAS,CAACW,iBAArC,EAAwD;IACtDD,iBAAiB,GAAGV,SAAS,CAACP,QAA9B;EACD;;EAED,OAAOZ,MAAM,CAAC;IACZE,QAAQ,EAAEqB,iBADE;IAEZnB,IAAI,EAAEoB,aAFM;IAGZhB,QAAQ,EAAEmB,iBAHE;IAIZhB,IAAI,EAAEiB,aAJM;IAKZhB,QAAQ,EAAEiB,iBALE;IAMZhB,OAAO,EAAE;EANG,CAAD,CAAb;AAQD;;AAED,eAAeK,eAAf"}, "metadata": {}, "sourceType": "module"}