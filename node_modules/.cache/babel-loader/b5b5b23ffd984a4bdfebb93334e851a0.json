{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/index.jsx\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { AssetsList } from \"../../elements/assetsList\";\nimport { ArrowRight, MapPin } from \"react-feather\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst ProjectCard = _ref => {\n  let {\n    item\n  } = _ref;\n  console.log({\n    item: item.banner[0]\n  });\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/projects/${item.id}`,\n      children: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.ImgContainer, {\n          img: item === null || item === void 0 ? void 0 : item.thumbnail,\n          className: \"image\",\n          children: /*#__PURE__*/_jsxDEV(Styles.TagWrapper, {\n            children: /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n              sub: true,\n              bold: true,\n              upper: true,\n              children: item.tag\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.TextWrapper, {\n          children: [/*#__PURE__*/_jsxDEV(Styles.LocationWrapper, {\n            children: [/*#__PURE__*/_jsxDEV(Styles.IconHolder, {\n              children: /*#__PURE__*/_jsxDEV(MapPin, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n              sub: true,\n              children: item.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Styles.Padding, {\n            children: /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n              className: \"title\",\n              heading: true,\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n            sub: true,\n            children: item.info\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.LinkWrapper, {\n          className: \"viewlink\",\n          children: [/*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n            bold: true,\n            children: \"View\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Styles.ArrowHolder, {\n            className: \"arrow\",\n            children: /*#__PURE__*/_jsxDEV(ArrowRight, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n\n_c = ProjectCard;\nexport default ProjectCard;\n\nvar _c;\n\n$RefreshReg$(_c, \"ProjectCard\");", "map": {"version": 3, "names": ["React", "Link", "AssetsList", "ArrowRight", "MapPin", "Styles", "ProjectCard", "item", "console", "log", "banner", "id", "thumbnail", "tag", "location", "title", "info"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { Link } from \"react-router-dom\";\n\nimport { AssetsList } from \"../../elements/assetsList\";\nimport { ArrowRight, MapPin } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst ProjectCard = ({ item }) => {\n  console.log({ item: item.banner[0] });\n  return (\n    <Styles.Container>\n      <Link to={`/projects/${item.id}`}>\n        <>\n          <Styles.ImgContainer img={item?.thumbnail} className=\"image\">\n            <Styles.TagWrapper>\n              <Styles.TextHolder sub bold upper>\n                {item.tag}\n              </Styles.TextHolder>\n            </Styles.TagWrapper>\n          </Styles.ImgContainer>\n          <Styles.TextWrapper>\n            {/* <Styles.SaveWrapper>\n              <Styles.IconHolder src={AssetsList.save} />\n            </Styles.SaveWrapper> */}\n            <Styles.LocationWrapper>\n              <Styles.IconHolder>\n                <MapPin />\n              </Styles.IconHolder>\n              <Styles.TextHolder sub>{item.location}</Styles.TextHolder>\n            </Styles.LocationWrapper>\n            <Styles.Padding>\n              <Styles.TextHolder className=\"title\" heading>\n                {item.title}\n              </Styles.TextHolder>\n            </Styles.Padding>\n            <Styles.TextHolder sub>{item.info}</Styles.TextHolder>\n          </Styles.TextWrapper>\n          <Styles.LinkWrapper className=\"viewlink\">\n            <Styles.TextHolder bold>View</Styles.TextHolder>\n            <Styles.ArrowHolder className=\"arrow\">\n              <ArrowRight />\n            </Styles.ArrowHolder>\n          </Styles.LinkWrapper>\n        </>\n      </Link>\n    </Styles.Container>\n  );\n};\n\nexport default ProjectCard;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,IAAT,QAAqB,kBAArB;AAEA,SAASC,UAAT,QAA2B,2BAA3B;AACA,SAASC,UAAT,EAAqBC,MAArB,QAAmC,eAAnC;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;;AAEA,MAAMC,WAAW,GAAG,QAAc;EAAA,IAAb;IAAEC;EAAF,CAAa;EAChCC,OAAO,CAACC,GAAR,CAAY;IAAEF,IAAI,EAAEA,IAAI,CAACG,MAAL,CAAY,CAAZ;EAAR,CAAZ;EACA,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,uBACE,QAAC,IAAD;MAAM,EAAE,EAAG,aAAYH,IAAI,CAACI,EAAG,EAA/B;MAAA,uBACE;QAAA,wBACE,QAAC,MAAD,CAAQ,YAAR;UAAqB,GAAG,EAAEJ,IAAF,aAAEA,IAAF,uBAAEA,IAAI,CAAEK,SAAhC;UAA2C,SAAS,EAAC,OAArD;UAAA,uBACE,QAAC,MAAD,CAAQ,UAAR;YAAA,uBACE,QAAC,MAAD,CAAQ,UAAR;cAAmB,GAAG,MAAtB;cAAuB,IAAI,MAA3B;cAA4B,KAAK,MAAjC;cAAA,UACGL,IAAI,CAACM;YADR;cAAA;cAAA;cAAA;YAAA;UADF;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QADF,eAQE,QAAC,MAAD,CAAQ,WAAR;UAAA,wBAIE,QAAC,MAAD,CAAQ,eAAR;YAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;cAAA,uBACE,QAAC,MAAD;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QADF,eAIE,QAAC,MAAD,CAAQ,UAAR;cAAmB,GAAG,MAAtB;cAAA,UAAwBN,IAAI,CAACO;YAA7B;cAAA;cAAA;cAAA;YAAA,QAJF;UAAA;YAAA;YAAA;YAAA;UAAA,QAJF,eAUE,QAAC,MAAD,CAAQ,OAAR;YAAA,uBACE,QAAC,MAAD,CAAQ,UAAR;cAAmB,SAAS,EAAC,OAA7B;cAAqC,OAAO,MAA5C;cAAA,UACGP,IAAI,CAACQ;YADR;cAAA;cAAA;cAAA;YAAA;UADF;YAAA;YAAA;YAAA;UAAA,QAVF,eAeE,QAAC,MAAD,CAAQ,UAAR;YAAmB,GAAG,MAAtB;YAAA,UAAwBR,IAAI,CAACS;UAA7B;YAAA;YAAA;YAAA;UAAA,QAfF;QAAA;UAAA;UAAA;UAAA;QAAA,QARF,eAyBE,QAAC,MAAD,CAAQ,WAAR;UAAoB,SAAS,EAAC,UAA9B;UAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;YAAmB,IAAI,MAAvB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QADF,eAEE,QAAC,MAAD,CAAQ,WAAR;YAAoB,SAAS,EAAC,OAA9B;YAAA,uBACE,QAAC,UAAD;cAAA;cAAA;cAAA;YAAA;UADF;YAAA;YAAA;YAAA;UAAA,QAFF;QAAA;UAAA;UAAA;UAAA;QAAA,QAzBF;MAAA;IADF;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAsCD,CAxCD;;KAAMV,W;AA0CN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}