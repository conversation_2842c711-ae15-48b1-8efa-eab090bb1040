{"ast": null, "code": "import styled from 'styled-components';\nexport const Container = styled.div`\n  \n`;\nexport const InputWrapper = styled.div`\n    display: flex;\n    align-items: center;\n\n    padding: 10px 15px;\n    border-radius: 10px;\n    background-color: #fff;\n`;\nexport const InputHolder = styled.input`\n    box-sizing: border-box;\n    width: 100%;\n\n    border: none;\n\n    &:focus{\n        outline: none;\n    }\n`;\nexport const Button = styled.button`\n    background-color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.main.yellow;\n}};\n    border: none;\n    border-radius: 10px;\n\n    padding: 7px 30px;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "InputWrapper", "InputHolder", "input", "<PERSON><PERSON>", "button", "theme", "colors", "main", "yellow"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Container = styled.div`\n  \n`;\n\nexport const InputWrapper = styled.div`\n    display: flex;\n    align-items: center;\n\n    padding: 10px 15px;\n    border-radius: 10px;\n    background-color: #fff;\n`;\n\nexport const InputHolder = styled.input`\n    box-sizing: border-box;\n    width: 100%;\n\n    border: none;\n\n    &:focus{\n        outline: none;\n    }\n`;\n\nexport const Button = styled.button`\n    background-color: ${({ theme: { colors } }) => colors.main.yellow};\n    border: none;\n    border-radius: 10px;\n\n    padding: 7px 30px;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA,CAFO;AAIP,OAAO,MAAMC,YAAY,GAAGH,MAAM,CAACE,GAAI;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,CAPO;AASP,OAAO,MAAME,WAAW,GAAGJ,MAAM,CAACK,KAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CATO;AAWP,OAAO,MAAMC,MAAM,GAAGN,MAAM,CAACO,MAAO;AACpC,wBAAwB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,MAAvC;AAAA,CAA8C;AACtE;AACA;AACA;AACA;AACA,CANO"}, "metadata": {}, "sourceType": "module"}