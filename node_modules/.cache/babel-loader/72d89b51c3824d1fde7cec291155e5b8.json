{"ast": null, "code": "/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    var React = require('react'); // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types.\n\n\n    var REACT_ELEMENT_TYPE = Symbol.for('react.element');\n    var REACT_PORTAL_TYPE = Symbol.for('react.portal');\n    var REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\n    var REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\n    var REACT_PROFILER_TYPE = Symbol.for('react.profiler');\n    var REACT_PROVIDER_TYPE = Symbol.for('react.provider');\n    var REACT_CONTEXT_TYPE = Symbol.for('react.context');\n    var REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\n    var REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\n    var REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\n    var REACT_MEMO_TYPE = Symbol.for('react.memo');\n    var REACT_LAZY_TYPE = Symbol.for('react.lazy');\n    var REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\n    var MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\n    var FAUX_ITERATOR_SYMBOL = '@@iterator';\n\n    function getIteratorFn(maybeIterable) {\n      if (maybeIterable === null || typeof maybeIterable !== 'object') {\n        return null;\n      }\n\n      var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n      if (typeof maybeIterator === 'function') {\n        return maybeIterator;\n      }\n\n      return null;\n    }\n\n    var ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\n    function error(format) {\n      {\n        {\n          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n            args[_key2 - 1] = arguments[_key2];\n          }\n\n          printWarning('error', format, args);\n        }\n      }\n    }\n\n    function printWarning(level, format, args) {\n      // When changing this logic, you might want to also\n      // update consoleWithStackDev.www.js as well.\n      {\n        var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n        var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n        if (stack !== '') {\n          format += '%s';\n          args = args.concat([stack]);\n        } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n        var argsWithFormat = args.map(function (item) {\n          return String(item);\n        }); // Careful: RN currently depends on this prefix\n\n        argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n        // breaks IE9: https://github.com/facebook/react/issues/13610\n        // eslint-disable-next-line react-internal/no-production-logging\n\n        Function.prototype.apply.call(console[level], console, argsWithFormat);\n      }\n    } // -----------------------------------------------------------------------------\n\n\n    var enableScopeAPI = false; // Experimental Create Event Handle API.\n\n    var enableCacheElement = false;\n    var enableTransitionTracing = false; // No known bugs, but needs performance testing\n\n    var enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n    // stuff. Intended to enable React core members to more easily debug scheduling\n    // issues in DEV builds.\n\n    var enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\n    var REACT_MODULE_REFERENCE;\n    {\n      REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n    }\n\n    function isValidElementType(type) {\n      if (typeof type === 'string' || typeof type === 'function') {\n        return true;\n      } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n      if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden || type === REACT_OFFSCREEN_TYPE || enableScopeAPI || enableCacheElement || enableTransitionTracing) {\n        return true;\n      }\n\n      if (typeof type === 'object' && type !== null) {\n        if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n        // types supported by any Flight configuration anywhere since\n        // we don't know which Flight build this will end up being used\n        // with.\n        type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n          return true;\n        }\n      }\n\n      return false;\n    }\n\n    function getWrappedName(outerType, innerType, wrapperName) {\n      var displayName = outerType.displayName;\n\n      if (displayName) {\n        return displayName;\n      }\n\n      var functionName = innerType.displayName || innerType.name || '';\n      return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n    } // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\n    function getContextName(type) {\n      return type.displayName || 'Context';\n    } // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\n    function getComponentNameFromType(type) {\n      if (type == null) {\n        // Host root, text node or just invalid type.\n        return null;\n      }\n\n      {\n        if (typeof type.tag === 'number') {\n          error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n        }\n      }\n\n      if (typeof type === 'function') {\n        return type.displayName || type.name || null;\n      }\n\n      if (typeof type === 'string') {\n        return type;\n      }\n\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return 'Fragment';\n\n        case REACT_PORTAL_TYPE:\n          return 'Portal';\n\n        case REACT_PROFILER_TYPE:\n          return 'Profiler';\n\n        case REACT_STRICT_MODE_TYPE:\n          return 'StrictMode';\n\n        case REACT_SUSPENSE_TYPE:\n          return 'Suspense';\n\n        case REACT_SUSPENSE_LIST_TYPE:\n          return 'SuspenseList';\n      }\n\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_CONTEXT_TYPE:\n            var context = type;\n            return getContextName(context) + '.Consumer';\n\n          case REACT_PROVIDER_TYPE:\n            var provider = type;\n            return getContextName(provider._context) + '.Provider';\n\n          case REACT_FORWARD_REF_TYPE:\n            return getWrappedName(type, type.render, 'ForwardRef');\n\n          case REACT_MEMO_TYPE:\n            var outerName = type.displayName || null;\n\n            if (outerName !== null) {\n              return outerName;\n            }\n\n            return getComponentNameFromType(type.type) || 'Memo';\n\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n\n              try {\n                return getComponentNameFromType(init(payload));\n              } catch (x) {\n                return null;\n              }\n            }\n          // eslint-disable-next-line no-fallthrough\n        }\n      }\n\n      return null;\n    }\n\n    var assign = Object.assign; // Helpers to patch console.logs to avoid logging during side-effect free\n    // replaying on render function. This currently only patches the object\n    // lazily which won't cover if the log function was extracted eagerly.\n    // We could also eagerly patch the method.\n\n    var disabledDepth = 0;\n    var prevLog;\n    var prevInfo;\n    var prevWarn;\n    var prevError;\n    var prevGroup;\n    var prevGroupCollapsed;\n    var prevGroupEnd;\n\n    function disabledLog() {}\n\n    disabledLog.__reactDisabledLog = true;\n\n    function disableLogs() {\n      {\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          prevLog = console.log;\n          prevInfo = console.info;\n          prevWarn = console.warn;\n          prevError = console.error;\n          prevGroup = console.group;\n          prevGroupCollapsed = console.groupCollapsed;\n          prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n          var props = {\n            configurable: true,\n            enumerable: true,\n            value: disabledLog,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            info: props,\n            log: props,\n            warn: props,\n            error: props,\n            group: props,\n            groupCollapsed: props,\n            groupEnd: props\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n\n        disabledDepth++;\n      }\n    }\n\n    function reenableLogs() {\n      {\n        disabledDepth--;\n\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          var props = {\n            configurable: true,\n            enumerable: true,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            log: assign({}, props, {\n              value: prevLog\n            }),\n            info: assign({}, props, {\n              value: prevInfo\n            }),\n            warn: assign({}, props, {\n              value: prevWarn\n            }),\n            error: assign({}, props, {\n              value: prevError\n            }),\n            group: assign({}, props, {\n              value: prevGroup\n            }),\n            groupCollapsed: assign({}, props, {\n              value: prevGroupCollapsed\n            }),\n            groupEnd: assign({}, props, {\n              value: prevGroupEnd\n            })\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n\n        if (disabledDepth < 0) {\n          error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n        }\n      }\n    }\n\n    var ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\n    var prefix;\n\n    function describeBuiltInComponentFrame(name, source, ownerFn) {\n      {\n        if (prefix === undefined) {\n          // Extract the VM specific prefix used by each line.\n          try {\n            throw Error();\n          } catch (x) {\n            var match = x.stack.trim().match(/\\n( *(at )?)/);\n            prefix = match && match[1] || '';\n          }\n        } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n        return '\\n' + prefix + name;\n      }\n    }\n\n    var reentry = false;\n    var componentFrameCache;\n    {\n      var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n      componentFrameCache = new PossiblyWeakMap();\n    }\n\n    function describeNativeComponentFrame(fn, construct) {\n      // If something asked for a stack inside a fake render, it should get ignored.\n      if (!fn || reentry) {\n        return '';\n      }\n\n      {\n        var frame = componentFrameCache.get(fn);\n\n        if (frame !== undefined) {\n          return frame;\n        }\n      }\n      var control;\n      reentry = true;\n      var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n      Error.prepareStackTrace = undefined;\n      var previousDispatcher;\n      {\n        previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n        // for warnings.\n\n        ReactCurrentDispatcher.current = null;\n        disableLogs();\n      }\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            }\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          }\n\n          fn();\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          // This extracts the first frame from the sample that isn't also in the control.\n          // Skipping one frame that we assume is the frame that calls the two.\n          var sampleLines = sample.stack.split('\\n');\n          var controlLines = control.stack.split('\\n');\n          var s = sampleLines.length - 1;\n          var c = controlLines.length - 1;\n\n          while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n            // We expect at least one stack frame to be shared.\n            // Typically this will be the root most one. However, stack frames may be\n            // cut off due to maximum stack limits. In this case, one maybe cut off\n            // earlier than the other. We assume that the sample is longer or the same\n            // and there for cut off earlier. So we should find the root most frame in\n            // the sample somewhere in the control.\n            c--;\n          }\n\n          for (; s >= 1 && c >= 0; s--, c--) {\n            // Next we find the first one that isn't the same which should be the\n            // frame that called our sample function and the control.\n            if (sampleLines[s] !== controlLines[c]) {\n              // In V8, the first line is describing the message but other VMs don't.\n              // If we're about to return the first line, and the control is also on the same\n              // line, that's a pretty good indicator that our sample threw at same line as\n              // the control. I.e. before we entered the sample frame. So we ignore this result.\n              // This can happen if you passed a class to function component, or non-function.\n              if (s !== 1 || c !== 1) {\n                do {\n                  s--;\n                  c--; // We may still have similar intermediate frames from the construct call.\n                  // The next one that isn't the same should be our match though.\n\n                  if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                    // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                    var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                    // but we have a user-provided \"displayName\"\n                    // splice it in to make the stack more readable.\n\n\n                    if (fn.displayName && _frame.includes('<anonymous>')) {\n                      _frame = _frame.replace('<anonymous>', fn.displayName);\n                    }\n\n                    {\n                      if (typeof fn === 'function') {\n                        componentFrameCache.set(fn, _frame);\n                      }\n                    } // Return the line we found.\n\n                    return _frame;\n                  }\n                } while (s >= 1 && c >= 0);\n              }\n\n              break;\n            }\n          }\n        }\n      } finally {\n        reentry = false;\n        {\n          ReactCurrentDispatcher.current = previousDispatcher;\n          reenableLogs();\n        }\n        Error.prepareStackTrace = previousPrepareStackTrace;\n      } // Fallback to just using the name if we couldn't make it throw.\n\n\n      var name = fn ? fn.displayName || fn.name : '';\n      var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n      {\n        if (typeof fn === 'function') {\n          componentFrameCache.set(fn, syntheticFrame);\n        }\n      }\n      return syntheticFrame;\n    }\n\n    function describeFunctionComponentFrame(fn, source, ownerFn) {\n      {\n        return describeNativeComponentFrame(fn, false);\n      }\n    }\n\n    function shouldConstruct(Component) {\n      var prototype = Component.prototype;\n      return !!(prototype && prototype.isReactComponent);\n    }\n\n    function describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n      if (type == null) {\n        return '';\n      }\n\n      if (typeof type === 'function') {\n        {\n          return describeNativeComponentFrame(type, shouldConstruct(type));\n        }\n      }\n\n      if (typeof type === 'string') {\n        return describeBuiltInComponentFrame(type);\n      }\n\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame('Suspense');\n\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame('SuspenseList');\n      }\n\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return describeFunctionComponentFrame(type.render);\n\n          case REACT_MEMO_TYPE:\n            // Memo may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n\n              try {\n                // Lazy may contain any component type so we recursively resolve it.\n                return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n              } catch (x) {}\n            }\n        }\n      }\n\n      return '';\n    }\n\n    var hasOwnProperty = Object.prototype.hasOwnProperty;\n    var loggedTypeFailures = {};\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\n    function setCurrentlyValidatingElement(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          ReactDebugCurrentFrame.setExtraStackFrame(stack);\n        } else {\n          ReactDebugCurrentFrame.setExtraStackFrame(null);\n        }\n      }\n    }\n\n    function checkPropTypes(typeSpecs, values, location, componentName, element) {\n      {\n        // $FlowFixMe This is okay but Flow doesn't know it.\n        var has = Function.call.bind(hasOwnProperty);\n\n        for (var typeSpecName in typeSpecs) {\n          if (has(typeSpecs, typeSpecName)) {\n            var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n            // fail the render phase where it didn't fail before. So we log it.\n            // After these have been cleaned up, we'll let them throw.\n\n            try {\n              // This is intentionally an invariant that gets caught. It's the same\n              // behavior as without this statement except with a better message.\n              if (typeof typeSpecs[typeSpecName] !== 'function') {\n                // eslint-disable-next-line react-internal/prod-error-codes\n                var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n                err.name = 'Invariant Violation';\n                throw err;\n              }\n\n              error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n            } catch (ex) {\n              error$1 = ex;\n            }\n\n            if (error$1 && !(error$1 instanceof Error)) {\n              setCurrentlyValidatingElement(element);\n              error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n              setCurrentlyValidatingElement(null);\n            }\n\n            if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n              // Only monitor this failure once because there tends to be a lot of the\n              // same error.\n              loggedTypeFailures[error$1.message] = true;\n              setCurrentlyValidatingElement(element);\n              error('Failed %s type: %s', location, error$1.message);\n              setCurrentlyValidatingElement(null);\n            }\n          }\n        }\n      }\n    }\n\n    var isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\n    function isArray(a) {\n      return isArrayImpl(a);\n    }\n    /*\n     * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n     * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n     *\n     * The functions in this module will throw an easier-to-understand,\n     * easier-to-debug exception with a clear errors message message explaining the\n     * problem. (Instead of a confusing exception thrown inside the implementation\n     * of the `value` object).\n     */\n    // $FlowFixMe only called in DEV, so void return is not possible.\n\n\n    function typeName(value) {\n      {\n        // toStringTag is needed for namespaced types like Temporal.Instant\n        var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n        var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n        return type;\n      }\n    } // $FlowFixMe only called in DEV, so void return is not possible.\n\n\n    function willCoercionThrow(value) {\n      {\n        try {\n          testStringCoercion(value);\n          return false;\n        } catch (e) {\n          return true;\n        }\n      }\n    }\n\n    function testStringCoercion(value) {\n      // If you ended up here by following an exception call stack, here's what's\n      // happened: you supplied an object or symbol value to React (as a prop, key,\n      // DOM attribute, CSS property, string ref, etc.) and when React tried to\n      // coerce it to a string using `'' + value`, an exception was thrown.\n      //\n      // The most common types that will cause this exception are `Symbol` instances\n      // and Temporal objects like `Temporal.Instant`. But any object that has a\n      // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n      // exception. (Library authors do this to prevent users from using built-in\n      // numeric operators like `+` or comparison operators like `>=` because custom\n      // methods are needed to perform accurate arithmetic or comparison.)\n      //\n      // To fix the problem, coerce this object or symbol value to a string before\n      // passing it to React. The most reliable way is usually `String(value)`.\n      //\n      // To find which value is throwing, check the browser or debugger console.\n      // Before this exception was thrown, there should be `console.error` output\n      // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n      // problem and how that type was used: key, atrribute, input value prop, etc.\n      // In most cases, this console output also shows the component and its\n      // ancestor components where the exception happened.\n      //\n      // eslint-disable-next-line react-internal/safe-string-coercion\n      return '' + value;\n    }\n\n    function checkKeyStringCoercion(value) {\n      {\n        if (willCoercionThrow(value)) {\n          error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n          return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n        }\n      }\n    }\n\n    var ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\n    var RESERVED_PROPS = {\n      key: true,\n      ref: true,\n      __self: true,\n      __source: true\n    };\n    var specialPropKeyWarningShown;\n    var specialPropRefWarningShown;\n    var didWarnAboutStringRefs;\n    {\n      didWarnAboutStringRefs = {};\n    }\n\n    function hasValidRef(config) {\n      {\n        if (hasOwnProperty.call(config, 'ref')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.ref !== undefined;\n    }\n\n    function hasValidKey(config) {\n      {\n        if (hasOwnProperty.call(config, 'key')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.key !== undefined;\n    }\n\n    function warnIfStringRefCannotBeAutoConverted(config, self) {\n      {\n        if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n          var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n          if (!didWarnAboutStringRefs[componentName]) {\n            error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n            didWarnAboutStringRefs[componentName] = true;\n          }\n        }\n      }\n    }\n\n    function defineKeyPropWarningGetter(props, displayName) {\n      {\n        var warnAboutAccessingKey = function () {\n          if (!specialPropKeyWarningShown) {\n            specialPropKeyWarningShown = true;\n            error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        };\n\n        warnAboutAccessingKey.isReactWarning = true;\n        Object.defineProperty(props, 'key', {\n          get: warnAboutAccessingKey,\n          configurable: true\n        });\n      }\n    }\n\n    function defineRefPropWarningGetter(props, displayName) {\n      {\n        var warnAboutAccessingRef = function () {\n          if (!specialPropRefWarningShown) {\n            specialPropRefWarningShown = true;\n            error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        };\n\n        warnAboutAccessingRef.isReactWarning = true;\n        Object.defineProperty(props, 'ref', {\n          get: warnAboutAccessingRef,\n          configurable: true\n        });\n      }\n    }\n    /**\n     * Factory method to create a new React element. This no longer adheres to\n     * the class pattern, so do not use new to call it. Also, instanceof check\n     * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n     * if something is a React Element.\n     *\n     * @param {*} type\n     * @param {*} props\n     * @param {*} key\n     * @param {string|object} ref\n     * @param {*} owner\n     * @param {*} self A *temporary* helper to detect places where `this` is\n     * different from the `owner` when React.createElement is called, so that we\n     * can warn. We want to get rid of owner and replace string `ref`s with arrow\n     * functions, and as long as `this` and owner are the same, there will be no\n     * change in behavior.\n     * @param {*} source An annotation object (added by a transpiler or otherwise)\n     * indicating filename, line number, and/or other information.\n     * @internal\n     */\n\n\n    var ReactElement = function (type, key, ref, self, source, owner, props) {\n      var element = {\n        // This tag allows us to uniquely identify this as a React Element\n        $$typeof: REACT_ELEMENT_TYPE,\n        // Built-in properties that belong on the element\n        type: type,\n        key: key,\n        ref: ref,\n        props: props,\n        // Record the component responsible for creating this element.\n        _owner: owner\n      };\n      {\n        // The validation flag is currently mutative. We put it on\n        // an external backing store so that we can freeze the whole object.\n        // This can be replaced with a WeakMap once they are implemented in\n        // commonly used development environments.\n        element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n        // the validation flag non-enumerable (where possible, which should\n        // include every environment we run tests in), so the test framework\n        // ignores it.\n\n        Object.defineProperty(element._store, 'validated', {\n          configurable: false,\n          enumerable: false,\n          writable: true,\n          value: false\n        }); // self and source are DEV only properties.\n\n        Object.defineProperty(element, '_self', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: self\n        }); // Two elements created in two different places should be considered\n        // equal for testing purposes and therefore we hide it from enumeration.\n\n        Object.defineProperty(element, '_source', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: source\n        });\n\n        if (Object.freeze) {\n          Object.freeze(element.props);\n          Object.freeze(element);\n        }\n      }\n      return element;\n    };\n    /**\n     * https://github.com/reactjs/rfcs/pull/107\n     * @param {*} type\n     * @param {object} props\n     * @param {string} key\n     */\n\n\n    function jsxDEV(type, config, maybeKey, source, self) {\n      {\n        var propName; // Reserved names are extracted\n\n        var props = {};\n        var key = null;\n        var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n        // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n        // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n        // but as an intermediary step, we will use jsxDEV for everything except\n        // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n        // key is explicitly declared to be undefined or not.\n\n        if (maybeKey !== undefined) {\n          {\n            checkKeyStringCoercion(maybeKey);\n          }\n          key = '' + maybeKey;\n        }\n\n        if (hasValidKey(config)) {\n          {\n            checkKeyStringCoercion(config.key);\n          }\n          key = '' + config.key;\n        }\n\n        if (hasValidRef(config)) {\n          ref = config.ref;\n          warnIfStringRefCannotBeAutoConverted(config, self);\n        } // Remaining properties are added to a new props object\n\n\n        for (propName in config) {\n          if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n            props[propName] = config[propName];\n          }\n        } // Resolve default props\n\n\n        if (type && type.defaultProps) {\n          var defaultProps = type.defaultProps;\n\n          for (propName in defaultProps) {\n            if (props[propName] === undefined) {\n              props[propName] = defaultProps[propName];\n            }\n          }\n        }\n\n        if (key || ref) {\n          var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n          if (key) {\n            defineKeyPropWarningGetter(props, displayName);\n          }\n\n          if (ref) {\n            defineRefPropWarningGetter(props, displayName);\n          }\n        }\n\n        return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n      }\n    }\n\n    var ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\n    var ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\n    function setCurrentlyValidatingElement$1(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n        } else {\n          ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n        }\n      }\n    }\n\n    var propTypesMisspellWarningShown;\n    {\n      propTypesMisspellWarningShown = false;\n    }\n    /**\n     * Verifies the object is a ReactElement.\n     * See https://reactjs.org/docs/react-api.html#isvalidelement\n     * @param {?object} object\n     * @return {boolean} True if `object` is a ReactElement.\n     * @final\n     */\n\n    function isValidElement(object) {\n      {\n        return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n      }\n    }\n\n    function getDeclarationErrorAddendum() {\n      {\n        if (ReactCurrentOwner$1.current) {\n          var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n          if (name) {\n            return '\\n\\nCheck the render method of `' + name + '`.';\n          }\n        }\n\n        return '';\n      }\n    }\n\n    function getSourceInfoErrorAddendum(source) {\n      {\n        if (source !== undefined) {\n          var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n          var lineNumber = source.lineNumber;\n          return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n        }\n\n        return '';\n      }\n    }\n    /**\n     * Warn if there's no key explicitly set on dynamic arrays of children or\n     * object keys are not valid. This allows us to keep track of children between\n     * updates.\n     */\n\n\n    var ownerHasKeyUseWarning = {};\n\n    function getCurrentComponentErrorInfo(parentType) {\n      {\n        var info = getDeclarationErrorAddendum();\n\n        if (!info) {\n          var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n          if (parentName) {\n            info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n          }\n        }\n\n        return info;\n      }\n    }\n    /**\n     * Warn if the element doesn't have an explicit key assigned to it.\n     * This element is in an array. The array could grow and shrink or be\n     * reordered. All children that haven't already been validated are required to\n     * have a \"key\" property assigned to it. Error statuses are cached so a warning\n     * will only be shown once.\n     *\n     * @internal\n     * @param {ReactElement} element Element that requires a key.\n     * @param {*} parentType element's parent's type.\n     */\n\n\n    function validateExplicitKey(element, parentType) {\n      {\n        if (!element._store || element._store.validated || element.key != null) {\n          return;\n        }\n\n        element._store.validated = true;\n        var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n        if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n          return;\n        }\n\n        ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n        // property, it may be the creator of the child that's responsible for\n        // assigning it a key.\n\n        var childOwner = '';\n\n        if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n          // Give the component that originally created this child.\n          childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n        }\n\n        setCurrentlyValidatingElement$1(element);\n        error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n        setCurrentlyValidatingElement$1(null);\n      }\n    }\n    /**\n     * Ensure that every element either is passed in a static location, in an\n     * array with an explicit keys property defined, or in an object literal\n     * with valid key property.\n     *\n     * @internal\n     * @param {ReactNode} node Statically passed child of any type.\n     * @param {*} parentType node's parent's type.\n     */\n\n\n    function validateChildKeys(node, parentType) {\n      {\n        if (typeof node !== 'object') {\n          return;\n        }\n\n        if (isArray(node)) {\n          for (var i = 0; i < node.length; i++) {\n            var child = node[i];\n\n            if (isValidElement(child)) {\n              validateExplicitKey(child, parentType);\n            }\n          }\n        } else if (isValidElement(node)) {\n          // This element was passed in a valid location.\n          if (node._store) {\n            node._store.validated = true;\n          }\n        } else if (node) {\n          var iteratorFn = getIteratorFn(node);\n\n          if (typeof iteratorFn === 'function') {\n            // Entry iterators used to provide implicit keys,\n            // but now we print a separate warning for them later.\n            if (iteratorFn !== node.entries) {\n              var iterator = iteratorFn.call(node);\n              var step;\n\n              while (!(step = iterator.next()).done) {\n                if (isValidElement(step.value)) {\n                  validateExplicitKey(step.value, parentType);\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    /**\n     * Given an element, validate that its props follow the propTypes definition,\n     * provided by the type.\n     *\n     * @param {ReactElement} element\n     */\n\n\n    function validatePropTypes(element) {\n      {\n        var type = element.type;\n\n        if (type === null || type === undefined || typeof type === 'string') {\n          return;\n        }\n\n        var propTypes;\n\n        if (typeof type === 'function') {\n          propTypes = type.propTypes;\n        } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n        // Inner props are checked in the reconciler.\n        type.$$typeof === REACT_MEMO_TYPE)) {\n          propTypes = type.propTypes;\n        } else {\n          return;\n        }\n\n        if (propTypes) {\n          // Intentionally inside to avoid triggering lazy initializers:\n          var name = getComponentNameFromType(type);\n          checkPropTypes(propTypes, element.props, 'prop', name, element);\n        } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n          propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n          var _name = getComponentNameFromType(type);\n\n          error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n        }\n\n        if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n          error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n        }\n      }\n    }\n    /**\n     * Given a fragment, validate that it can only be provided with fragment props\n     * @param {ReactElement} fragment\n     */\n\n\n    function validateFragmentProps(fragment) {\n      {\n        var keys = Object.keys(fragment.props);\n\n        for (var i = 0; i < keys.length; i++) {\n          var key = keys[i];\n\n          if (key !== 'children' && key !== 'key') {\n            setCurrentlyValidatingElement$1(fragment);\n            error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n            setCurrentlyValidatingElement$1(null);\n            break;\n          }\n        }\n\n        if (fragment.ref !== null) {\n          setCurrentlyValidatingElement$1(fragment);\n          error('Invalid attribute `ref` supplied to `React.Fragment`.');\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n\n    function jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n      {\n        var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n        // succeed and there will likely be errors in render.\n\n        if (!validType) {\n          var info = '';\n\n          if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n            info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n          }\n\n          var sourceInfo = getSourceInfoErrorAddendum(source);\n\n          if (sourceInfo) {\n            info += sourceInfo;\n          } else {\n            info += getDeclarationErrorAddendum();\n          }\n\n          var typeString;\n\n          if (type === null) {\n            typeString = 'null';\n          } else if (isArray(type)) {\n            typeString = 'array';\n          } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n            typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n            info = ' Did you accidentally export a JSX literal instead of a component?';\n          } else {\n            typeString = typeof type;\n          }\n\n          error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n        }\n\n        var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n        // TODO: Drop this when these are no longer allowed as the type argument.\n\n        if (element == null) {\n          return element;\n        } // Skip key warning if the type isn't valid since our key validation logic\n        // doesn't expect a non-string/function type and can throw confusing errors.\n        // We don't want exception behavior to differ between dev and prod.\n        // (Rendering will throw with a helpful message and as soon as the type is\n        // fixed, the key warnings will appear.)\n\n\n        if (validType) {\n          var children = props.children;\n\n          if (children !== undefined) {\n            if (isStaticChildren) {\n              if (isArray(children)) {\n                for (var i = 0; i < children.length; i++) {\n                  validateChildKeys(children[i], type);\n                }\n\n                if (Object.freeze) {\n                  Object.freeze(children);\n                }\n              } else {\n                error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n              }\n            } else {\n              validateChildKeys(children, type);\n            }\n          }\n        }\n\n        if (type === REACT_FRAGMENT_TYPE) {\n          validateFragmentProps(element);\n        } else {\n          validatePropTypes(element);\n        }\n\n        return element;\n      }\n    } // These two functions exist to still get child warnings in dev\n\n\n    var jsxDEV$1 = jsxWithValidation;\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = jsxDEV$1;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "React", "require", "REACT_ELEMENT_TYPE", "Symbol", "for", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_OFFSCREEN_TYPE", "MAYBE_ITERATOR_SYMBOL", "iterator", "FAUX_ITERATOR_SYMBOL", "getIteratorFn", "maybeIterable", "maybeIterator", "ReactSharedInternals", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "error", "format", "_len2", "arguments", "length", "args", "Array", "_key2", "printWarning", "level", "ReactDebugCurrentFrame", "stack", "getStackAddendum", "concat", "argsWithFormat", "map", "item", "String", "unshift", "Function", "prototype", "apply", "call", "console", "enableScopeAPI", "enableCacheElement", "enableTransitionTracing", "enableLegacyHidden", "enableDebugTracing", "REACT_MODULE_REFERENCE", "isValidElementType", "type", "$$typeof", "getModuleId", "undefined", "getWrappedName", "outerType", "innerType", "wrapperName", "displayName", "functionName", "name", "getContextName", "getComponentNameFromType", "tag", "context", "provider", "_context", "render", "outerName", "lazyComponent", "payload", "_payload", "init", "_init", "x", "assign", "Object", "<PERSON><PERSON><PERSON><PERSON>", "prevLog", "prevInfo", "prev<PERSON>arn", "prevError", "prevGroup", "prevGroupCollapsed", "prevGroupEnd", "disabledLog", "__reactDisabledLog", "disableLogs", "log", "info", "warn", "group", "groupCollapsed", "groupEnd", "props", "configurable", "enumerable", "value", "writable", "defineProperties", "reenableLogs", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix", "describeBuiltInComponentFrame", "source", "ownerFn", "Error", "match", "trim", "reentry", "componentFrameCache", "PossiblyWeakMap", "WeakMap", "Map", "describeNativeComponentFrame", "fn", "construct", "frame", "get", "control", "previousPrepareStackTrace", "prepareStackTrace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "Fake", "defineProperty", "set", "Reflect", "sample", "sampleLines", "split", "controlLines", "s", "c", "_frame", "replace", "includes", "syntheticFrame", "describeFunctionComponentFrame", "shouldConstruct", "Component", "isReactComponent", "describeUnknownElementTypeFrameInDEV", "hasOwnProperty", "loggedTypeFailures", "setCurrentlyValidatingElement", "element", "owner", "_owner", "_source", "setExtraStackFrame", "checkPropTypes", "typeSpecs", "values", "location", "componentName", "has", "bind", "typeSpecName", "error$1", "err", "ex", "message", "isArrayImpl", "isArray", "a", "typeName", "hasToStringTag", "toStringTag", "constructor", "willCoercionThrow", "testStringCoercion", "e", "checkKeyStringCoercion", "ReactCurrentOwner", "RESERVED_PROPS", "key", "ref", "__self", "__source", "specialPropKeyWarningShown", "specialPropRefWarningShown", "didWarnAboutStringRefs", "hasValidRef", "config", "getter", "getOwnPropertyDescriptor", "isReactWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warnIfStringRefCannotBeAutoConverted", "self", "stateNode", "defineKeyPropWarningGetter", "warnAboutAccessingKey", "defineRefPropWarningGetter", "warnAboutAccessingRef", "ReactElement", "_store", "freeze", "jsxDEV", "<PERSON><PERSON><PERSON>", "propName", "defaultProps", "ReactCurrentOwner$1", "ReactDebugCurrentFrame$1", "setCurrentlyValidatingElement$1", "propTypesMisspellWarningShown", "isValidElement", "object", "getDeclarationErrorAddendum", "getSourceInfoErrorAddendum", "fileName", "lineNumber", "ownerHasKeyUseWarning", "getCurrentComponentErrorInfo", "parentType", "parentName", "validateExplicitKey", "validated", "currentComponentErrorInfo", "childOwner", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "i", "child", "iteratorFn", "entries", "step", "next", "done", "validatePropTypes", "propTypes", "PropTypes", "_name", "getDefaultProps", "isReactClassApproved", "validateFragmentProps", "fragment", "keys", "jsxWithValidation", "isStaticChildren", "validType", "sourceInfo", "typeString", "children", "jsxDEV$1", "exports", "Fragment"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar React = require('react');\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV$1 =  jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV$1;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA,IAAIA,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzC,CAAC,YAAW;IACd;;IAEA,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAD,CAAnB,CAHc,CAKd;IACA;IACA;IACA;;;IACA,IAAIC,kBAAkB,GAAGC,MAAM,CAACC,GAAP,CAAW,eAAX,CAAzB;IACA,IAAIC,iBAAiB,GAAGF,MAAM,CAACC,GAAP,CAAW,cAAX,CAAxB;IACA,IAAIE,mBAAmB,GAAGH,MAAM,CAACC,GAAP,CAAW,gBAAX,CAA1B;IACA,IAAIG,sBAAsB,GAAGJ,MAAM,CAACC,GAAP,CAAW,mBAAX,CAA7B;IACA,IAAII,mBAAmB,GAAGL,MAAM,CAACC,GAAP,CAAW,gBAAX,CAA1B;IACA,IAAIK,mBAAmB,GAAGN,MAAM,CAACC,GAAP,CAAW,gBAAX,CAA1B;IACA,IAAIM,kBAAkB,GAAGP,MAAM,CAACC,GAAP,CAAW,eAAX,CAAzB;IACA,IAAIO,sBAAsB,GAAGR,MAAM,CAACC,GAAP,CAAW,mBAAX,CAA7B;IACA,IAAIQ,mBAAmB,GAAGT,MAAM,CAACC,GAAP,CAAW,gBAAX,CAA1B;IACA,IAAIS,wBAAwB,GAAGV,MAAM,CAACC,GAAP,CAAW,qBAAX,CAA/B;IACA,IAAIU,eAAe,GAAGX,MAAM,CAACC,GAAP,CAAW,YAAX,CAAtB;IACA,IAAIW,eAAe,GAAGZ,MAAM,CAACC,GAAP,CAAW,YAAX,CAAtB;IACA,IAAIY,oBAAoB,GAAGb,MAAM,CAACC,GAAP,CAAW,iBAAX,CAA3B;IACA,IAAIa,qBAAqB,GAAGd,MAAM,CAACe,QAAnC;IACA,IAAIC,oBAAoB,GAAG,YAA3B;;IACA,SAASC,aAAT,CAAuBC,aAAvB,EAAsC;MACpC,IAAIA,aAAa,KAAK,IAAlB,IAA0B,OAAOA,aAAP,KAAyB,QAAvD,EAAiE;QAC/D,OAAO,IAAP;MACD;;MAED,IAAIC,aAAa,GAAGL,qBAAqB,IAAII,aAAa,CAACJ,qBAAD,CAAtC,IAAiEI,aAAa,CAACF,oBAAD,CAAlG;;MAEA,IAAI,OAAOG,aAAP,KAAyB,UAA7B,EAAyC;QACvC,OAAOA,aAAP;MACD;;MAED,OAAO,IAAP;IACD;;IAED,IAAIC,oBAAoB,GAAGvB,KAAK,CAACwB,kDAAjC;;IAEA,SAASC,KAAT,CAAeC,MAAf,EAAuB;MACrB;QACE;UACE,KAAK,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAtB,EAA8BC,IAAI,GAAG,IAAIC,KAAJ,CAAUJ,KAAK,GAAG,CAAR,GAAYA,KAAK,GAAG,CAApB,GAAwB,CAAlC,CAArC,EAA2EK,KAAK,GAAG,CAAxF,EAA2FA,KAAK,GAAGL,KAAnG,EAA0GK,KAAK,EAA/G,EAAmH;YACjHF,IAAI,CAACE,KAAK,GAAG,CAAT,CAAJ,GAAkBJ,SAAS,CAACI,KAAD,CAA3B;UACD;;UAEDC,YAAY,CAAC,OAAD,EAAUP,MAAV,EAAkBI,IAAlB,CAAZ;QACD;MACF;IACF;;IAED,SAASG,YAAT,CAAsBC,KAAtB,EAA6BR,MAA7B,EAAqCI,IAArC,EAA2C;MACzC;MACA;MACA;QACE,IAAIK,sBAAsB,GAAGZ,oBAAoB,CAACY,sBAAlD;QACA,IAAIC,KAAK,GAAGD,sBAAsB,CAACE,gBAAvB,EAAZ;;QAEA,IAAID,KAAK,KAAK,EAAd,EAAkB;UAChBV,MAAM,IAAI,IAAV;UACAI,IAAI,GAAGA,IAAI,CAACQ,MAAL,CAAY,CAACF,KAAD,CAAZ,CAAP;QACD,CAPH,CAOI;;;QAGF,IAAIG,cAAc,GAAGT,IAAI,CAACU,GAAL,CAAS,UAAUC,IAAV,EAAgB;UAC5C,OAAOC,MAAM,CAACD,IAAD,CAAb;QACD,CAFoB,CAArB,CAVF,CAYM;;QAEJF,cAAc,CAACI,OAAf,CAAuB,cAAcjB,MAArC,EAdF,CAcgD;QAC9C;QACA;;QAEAkB,QAAQ,CAACC,SAAT,CAAmBC,KAAnB,CAAyBC,IAAzB,CAA8BC,OAAO,CAACd,KAAD,CAArC,EAA8Cc,OAA9C,EAAuDT,cAAvD;MACD;IACF,CA3Ea,CA6Ed;;;IAEA,IAAIU,cAAc,GAAG,KAArB,CA/Ec,CA+Ec;;IAC5B,IAAIC,kBAAkB,GAAG,KAAzB;IACA,IAAIC,uBAAuB,GAAG,KAA9B,CAjFc,CAiFuB;;IAErC,IAAIC,kBAAkB,GAAG,KAAzB,CAnFc,CAmFkB;IAChC;IACA;;IAEA,IAAIC,kBAAkB,GAAG,KAAzB,CAvFc,CAuFkB;;IAEhC,IAAIC,sBAAJ;IAEA;MACEA,sBAAsB,GAAGnD,MAAM,CAACC,GAAP,CAAW,wBAAX,CAAzB;IACD;;IAED,SAASmD,kBAAT,CAA4BC,IAA5B,EAAkC;MAChC,IAAI,OAAOA,IAAP,KAAgB,QAAhB,IAA4B,OAAOA,IAAP,KAAgB,UAAhD,EAA4D;QAC1D,OAAO,IAAP;MACD,CAH+B,CAG9B;;;MAGF,IAAIA,IAAI,KAAKlD,mBAAT,IAAgCkD,IAAI,KAAKhD,mBAAzC,IAAgE6C,kBAAhE,IAAuFG,IAAI,KAAKjD,sBAAhG,IAA0HiD,IAAI,KAAK5C,mBAAnI,IAA0J4C,IAAI,KAAK3C,wBAAnK,IAA+LuC,kBAA/L,IAAsNI,IAAI,KAAKxC,oBAA/N,IAAuPiC,cAAvP,IAA0QC,kBAA1Q,IAAiSC,uBAArS,EAA+T;QAC7T,OAAO,IAAP;MACD;;MAED,IAAI,OAAOK,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,KAAK,IAAzC,EAA+C;QAC7C,IAAIA,IAAI,CAACC,QAAL,KAAkB1C,eAAlB,IAAqCyC,IAAI,CAACC,QAAL,KAAkB3C,eAAvD,IAA0E0C,IAAI,CAACC,QAAL,KAAkBhD,mBAA5F,IAAmH+C,IAAI,CAACC,QAAL,KAAkB/C,kBAArI,IAA2J8C,IAAI,CAACC,QAAL,KAAkB9C,sBAA7K,IAAuM;QAC3M;QACA;QACA;QACA6C,IAAI,CAACC,QAAL,KAAkBH,sBAJd,IAIwCE,IAAI,CAACE,WAAL,KAAqBC,SAJjE,EAI4E;UAC1E,OAAO,IAAP;QACD;MACF;;MAED,OAAO,KAAP;IACD;;IAED,SAASC,cAAT,CAAwBC,SAAxB,EAAmCC,SAAnC,EAA8CC,WAA9C,EAA2D;MACzD,IAAIC,WAAW,GAAGH,SAAS,CAACG,WAA5B;;MAEA,IAAIA,WAAJ,EAAiB;QACf,OAAOA,WAAP;MACD;;MAED,IAAIC,YAAY,GAAGH,SAAS,CAACE,WAAV,IAAyBF,SAAS,CAACI,IAAnC,IAA2C,EAA9D;MACA,OAAOD,YAAY,KAAK,EAAjB,GAAsBF,WAAW,GAAG,GAAd,GAAoBE,YAApB,GAAmC,GAAzD,GAA+DF,WAAtE;IACD,CA/Ha,CA+HZ;;;IAGF,SAASI,cAAT,CAAwBX,IAAxB,EAA8B;MAC5B,OAAOA,IAAI,CAACQ,WAAL,IAAoB,SAA3B;IACD,CApIa,CAoIZ;;;IAGF,SAASI,wBAAT,CAAkCZ,IAAlC,EAAwC;MACtC,IAAIA,IAAI,IAAI,IAAZ,EAAkB;QAChB;QACA,OAAO,IAAP;MACD;;MAED;QACE,IAAI,OAAOA,IAAI,CAACa,GAAZ,KAAoB,QAAxB,EAAkC;UAChC5C,KAAK,CAAC,kEAAkE,sDAAnE,CAAL;QACD;MACF;;MAED,IAAI,OAAO+B,IAAP,KAAgB,UAApB,EAAgC;QAC9B,OAAOA,IAAI,CAACQ,WAAL,IAAoBR,IAAI,CAACU,IAAzB,IAAiC,IAAxC;MACD;;MAED,IAAI,OAAOV,IAAP,KAAgB,QAApB,EAA8B;QAC5B,OAAOA,IAAP;MACD;;MAED,QAAQA,IAAR;QACE,KAAKlD,mBAAL;UACE,OAAO,UAAP;;QAEF,KAAKD,iBAAL;UACE,OAAO,QAAP;;QAEF,KAAKG,mBAAL;UACE,OAAO,UAAP;;QAEF,KAAKD,sBAAL;UACE,OAAO,YAAP;;QAEF,KAAKK,mBAAL;UACE,OAAO,UAAP;;QAEF,KAAKC,wBAAL;UACE,OAAO,cAAP;MAjBJ;;MAqBA,IAAI,OAAO2C,IAAP,KAAgB,QAApB,EAA8B;QAC5B,QAAQA,IAAI,CAACC,QAAb;UACE,KAAK/C,kBAAL;YACE,IAAI4D,OAAO,GAAGd,IAAd;YACA,OAAOW,cAAc,CAACG,OAAD,CAAd,GAA0B,WAAjC;;UAEF,KAAK7D,mBAAL;YACE,IAAI8D,QAAQ,GAAGf,IAAf;YACA,OAAOW,cAAc,CAACI,QAAQ,CAACC,QAAV,CAAd,GAAoC,WAA3C;;UAEF,KAAK7D,sBAAL;YACE,OAAOiD,cAAc,CAACJ,IAAD,EAAOA,IAAI,CAACiB,MAAZ,EAAoB,YAApB,CAArB;;UAEF,KAAK3D,eAAL;YACE,IAAI4D,SAAS,GAAGlB,IAAI,CAACQ,WAAL,IAAoB,IAApC;;YAEA,IAAIU,SAAS,KAAK,IAAlB,EAAwB;cACtB,OAAOA,SAAP;YACD;;YAED,OAAON,wBAAwB,CAACZ,IAAI,CAACA,IAAN,CAAxB,IAAuC,MAA9C;;UAEF,KAAKzC,eAAL;YACE;cACE,IAAI4D,aAAa,GAAGnB,IAApB;cACA,IAAIoB,OAAO,GAAGD,aAAa,CAACE,QAA5B;cACA,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAzB;;cAEA,IAAI;gBACF,OAAOX,wBAAwB,CAACU,IAAI,CAACF,OAAD,CAAL,CAA/B;cACD,CAFD,CAEE,OAAOI,CAAP,EAAU;gBACV,OAAO,IAAP;cACD;YACF;UAEH;QAlCF;MAoCD;;MAED,OAAO,IAAP;IACD;;IAED,IAAIC,MAAM,GAAGC,MAAM,CAACD,MAApB,CA1Nc,CA4Nd;IACA;IACA;IACA;;IACA,IAAIE,aAAa,GAAG,CAApB;IACA,IAAIC,OAAJ;IACA,IAAIC,QAAJ;IACA,IAAIC,QAAJ;IACA,IAAIC,SAAJ;IACA,IAAIC,SAAJ;IACA,IAAIC,kBAAJ;IACA,IAAIC,YAAJ;;IAEA,SAASC,WAAT,GAAuB,CAAE;;IAEzBA,WAAW,CAACC,kBAAZ,GAAiC,IAAjC;;IACA,SAASC,WAAT,GAAuB;MACrB;QACE,IAAIV,aAAa,KAAK,CAAtB,EAAyB;UACvB;UACAC,OAAO,GAAGpC,OAAO,CAAC8C,GAAlB;UACAT,QAAQ,GAAGrC,OAAO,CAAC+C,IAAnB;UACAT,QAAQ,GAAGtC,OAAO,CAACgD,IAAnB;UACAT,SAAS,GAAGvC,OAAO,CAACvB,KAApB;UACA+D,SAAS,GAAGxC,OAAO,CAACiD,KAApB;UACAR,kBAAkB,GAAGzC,OAAO,CAACkD,cAA7B;UACAR,YAAY,GAAG1C,OAAO,CAACmD,QAAvB,CARuB,CAQU;;UAEjC,IAAIC,KAAK,GAAG;YACVC,YAAY,EAAE,IADJ;YAEVC,UAAU,EAAE,IAFF;YAGVC,KAAK,EAAEZ,WAHG;YAIVa,QAAQ,EAAE;UAJA,CAAZ,CAVuB,CAepB;;UAEHtB,MAAM,CAACuB,gBAAP,CAAwBzD,OAAxB,EAAiC;YAC/B+C,IAAI,EAAEK,KADyB;YAE/BN,GAAG,EAAEM,KAF0B;YAG/BJ,IAAI,EAAEI,KAHyB;YAI/B3E,KAAK,EAAE2E,KAJwB;YAK/BH,KAAK,EAAEG,KALwB;YAM/BF,cAAc,EAAEE,KANe;YAO/BD,QAAQ,EAAEC;UAPqB,CAAjC;UASA;QACD;;QAEDjB,aAAa;MACd;IACF;;IACD,SAASuB,YAAT,GAAwB;MACtB;QACEvB,aAAa;;QAEb,IAAIA,aAAa,KAAK,CAAtB,EAAyB;UACvB;UACA,IAAIiB,KAAK,GAAG;YACVC,YAAY,EAAE,IADJ;YAEVC,UAAU,EAAE,IAFF;YAGVE,QAAQ,EAAE;UAHA,CAAZ,CAFuB,CAMpB;;UAEHtB,MAAM,CAACuB,gBAAP,CAAwBzD,OAAxB,EAAiC;YAC/B8C,GAAG,EAAEb,MAAM,CAAC,EAAD,EAAKmB,KAAL,EAAY;cACrBG,KAAK,EAAEnB;YADc,CAAZ,CADoB;YAI/BW,IAAI,EAAEd,MAAM,CAAC,EAAD,EAAKmB,KAAL,EAAY;cACtBG,KAAK,EAAElB;YADe,CAAZ,CAJmB;YAO/BW,IAAI,EAAEf,MAAM,CAAC,EAAD,EAAKmB,KAAL,EAAY;cACtBG,KAAK,EAAEjB;YADe,CAAZ,CAPmB;YAU/B7D,KAAK,EAAEwD,MAAM,CAAC,EAAD,EAAKmB,KAAL,EAAY;cACvBG,KAAK,EAAEhB;YADgB,CAAZ,CAVkB;YAa/BU,KAAK,EAAEhB,MAAM,CAAC,EAAD,EAAKmB,KAAL,EAAY;cACvBG,KAAK,EAAEf;YADgB,CAAZ,CAbkB;YAgB/BU,cAAc,EAAEjB,MAAM,CAAC,EAAD,EAAKmB,KAAL,EAAY;cAChCG,KAAK,EAAEd;YADyB,CAAZ,CAhBS;YAmB/BU,QAAQ,EAAElB,MAAM,CAAC,EAAD,EAAKmB,KAAL,EAAY;cAC1BG,KAAK,EAAEb;YADmB,CAAZ;UAnBe,CAAjC;UAuBA;QACD;;QAED,IAAIP,aAAa,GAAG,CAApB,EAAuB;UACrB1D,KAAK,CAAC,oCAAoC,+CAArC,CAAL;QACD;MACF;IACF;;IAED,IAAIkF,sBAAsB,GAAGpF,oBAAoB,CAACoF,sBAAlD;IACA,IAAIC,MAAJ;;IACA,SAASC,6BAAT,CAAuC3C,IAAvC,EAA6C4C,MAA7C,EAAqDC,OAArD,EAA8D;MAC5D;QACE,IAAIH,MAAM,KAAKjD,SAAf,EAA0B;UACxB;UACA,IAAI;YACF,MAAMqD,KAAK,EAAX;UACD,CAFD,CAEE,OAAOhC,CAAP,EAAU;YACV,IAAIiC,KAAK,GAAGjC,CAAC,CAAC5C,KAAF,CAAQ8E,IAAR,GAAeD,KAAf,CAAqB,cAArB,CAAZ;YACAL,MAAM,GAAGK,KAAK,IAAIA,KAAK,CAAC,CAAD,CAAd,IAAqB,EAA9B;UACD;QACF,CATH,CASI;;;QAGF,OAAO,OAAOL,MAAP,GAAgB1C,IAAvB;MACD;IACF;;IACD,IAAIiD,OAAO,GAAG,KAAd;IACA,IAAIC,mBAAJ;IAEA;MACE,IAAIC,eAAe,GAAG,OAAOC,OAAP,KAAmB,UAAnB,GAAgCA,OAAhC,GAA0CC,GAAhE;MACAH,mBAAmB,GAAG,IAAIC,eAAJ,EAAtB;IACD;;IAED,SAASG,4BAAT,CAAsCC,EAAtC,EAA0CC,SAA1C,EAAqD;MACnD;MACA,IAAK,CAACD,EAAD,IAAON,OAAZ,EAAqB;QACnB,OAAO,EAAP;MACD;;MAED;QACE,IAAIQ,KAAK,GAAGP,mBAAmB,CAACQ,GAApB,CAAwBH,EAAxB,CAAZ;;QAEA,IAAIE,KAAK,KAAKhE,SAAd,EAAyB;UACvB,OAAOgE,KAAP;QACD;MACF;MAED,IAAIE,OAAJ;MACAV,OAAO,GAAG,IAAV;MACA,IAAIW,yBAAyB,GAAGd,KAAK,CAACe,iBAAtC,CAhBmD,CAgBM;;MAEzDf,KAAK,CAACe,iBAAN,GAA0BpE,SAA1B;MACA,IAAIqE,kBAAJ;MAEA;QACEA,kBAAkB,GAAGrB,sBAAsB,CAACsB,OAA5C,CADF,CACuD;QACrD;;QAEAtB,sBAAsB,CAACsB,OAAvB,GAAiC,IAAjC;QACApC,WAAW;MACZ;;MAED,IAAI;QACF;QACA,IAAI6B,SAAJ,EAAe;UACb;UACA,IAAIQ,IAAI,GAAG,YAAY;YACrB,MAAMlB,KAAK,EAAX;UACD,CAFD,CAFa,CAIV;;;UAGH9B,MAAM,CAACiD,cAAP,CAAsBD,IAAI,CAACrF,SAA3B,EAAsC,OAAtC,EAA+C;YAC7CuF,GAAG,EAAE,YAAY;cACf;cACA;cACA,MAAMpB,KAAK,EAAX;YACD;UAL4C,CAA/C;;UAQA,IAAI,OAAOqB,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,CAACX,SAA3C,EAAsD;YACpD;YACA;YACA,IAAI;cACFW,OAAO,CAACX,SAAR,CAAkBQ,IAAlB,EAAwB,EAAxB;YACD,CAFD,CAEE,OAAOlD,CAAP,EAAU;cACV6C,OAAO,GAAG7C,CAAV;YACD;;YAEDqD,OAAO,CAACX,SAAR,CAAkBD,EAAlB,EAAsB,EAAtB,EAA0BS,IAA1B;UACD,CAVD,MAUO;YACL,IAAI;cACFA,IAAI,CAACnF,IAAL;YACD,CAFD,CAEE,OAAOiC,CAAP,EAAU;cACV6C,OAAO,GAAG7C,CAAV;YACD;;YAEDyC,EAAE,CAAC1E,IAAH,CAAQmF,IAAI,CAACrF,SAAb;UACD;QACF,CAlCD,MAkCO;UACL,IAAI;YACF,MAAMmE,KAAK,EAAX;UACD,CAFD,CAEE,OAAOhC,CAAP,EAAU;YACV6C,OAAO,GAAG7C,CAAV;UACD;;UAEDyC,EAAE;QACH;MACF,CA7CD,CA6CE,OAAOa,MAAP,EAAe;QACf;QACA,IAAIA,MAAM,IAAIT,OAAV,IAAqB,OAAOS,MAAM,CAAClG,KAAd,KAAwB,QAAjD,EAA2D;UACzD;UACA;UACA,IAAImG,WAAW,GAAGD,MAAM,CAAClG,KAAP,CAAaoG,KAAb,CAAmB,IAAnB,CAAlB;UACA,IAAIC,YAAY,GAAGZ,OAAO,CAACzF,KAAR,CAAcoG,KAAd,CAAoB,IAApB,CAAnB;UACA,IAAIE,CAAC,GAAGH,WAAW,CAAC1G,MAAZ,GAAqB,CAA7B;UACA,IAAI8G,CAAC,GAAGF,YAAY,CAAC5G,MAAb,GAAsB,CAA9B;;UAEA,OAAO6G,CAAC,IAAI,CAAL,IAAUC,CAAC,IAAI,CAAf,IAAoBJ,WAAW,CAACG,CAAD,CAAX,KAAmBD,YAAY,CAACE,CAAD,CAA1D,EAA+D;YAC7D;YACA;YACA;YACA;YACA;YACA;YACAA,CAAC;UACF;;UAED,OAAOD,CAAC,IAAI,CAAL,IAAUC,CAAC,IAAI,CAAtB,EAAyBD,CAAC,IAAIC,CAAC,EAA/B,EAAmC;YACjC;YACA;YACA,IAAIJ,WAAW,CAACG,CAAD,CAAX,KAAmBD,YAAY,CAACE,CAAD,CAAnC,EAAwC;cACtC;cACA;cACA;cACA;cACA;cACA,IAAID,CAAC,KAAK,CAAN,IAAWC,CAAC,KAAK,CAArB,EAAwB;gBACtB,GAAG;kBACDD,CAAC;kBACDC,CAAC,GAFA,CAEI;kBACL;;kBAEA,IAAIA,CAAC,GAAG,CAAJ,IAASJ,WAAW,CAACG,CAAD,CAAX,KAAmBD,YAAY,CAACE,CAAD,CAA5C,EAAiD;oBAC/C;oBACA,IAAIC,MAAM,GAAG,OAAOL,WAAW,CAACG,CAAD,CAAX,CAAeG,OAAf,CAAuB,UAAvB,EAAmC,MAAnC,CAApB,CAF+C,CAEiB;oBAChE;oBACA;;;oBAGA,IAAIpB,EAAE,CAACzD,WAAH,IAAkB4E,MAAM,CAACE,QAAP,CAAgB,aAAhB,CAAtB,EAAsD;sBACpDF,MAAM,GAAGA,MAAM,CAACC,OAAP,CAAe,aAAf,EAA8BpB,EAAE,CAACzD,WAAjC,CAAT;oBACD;;oBAED;sBACE,IAAI,OAAOyD,EAAP,KAAc,UAAlB,EAA8B;wBAC5BL,mBAAmB,CAACgB,GAApB,CAAwBX,EAAxB,EAA4BmB,MAA5B;sBACD;oBACF,CAf8C,CAe7C;;oBAGF,OAAOA,MAAP;kBACD;gBACF,CAzBD,QAyBSF,CAAC,IAAI,CAAL,IAAUC,CAAC,IAAI,CAzBxB;cA0BD;;cAED;YACD;UACF;QACF;MACF,CA3GD,SA2GU;QACRxB,OAAO,GAAG,KAAV;QAEA;UACER,sBAAsB,CAACsB,OAAvB,GAAiCD,kBAAjC;UACAtB,YAAY;QACb;QAEDM,KAAK,CAACe,iBAAN,GAA0BD,yBAA1B;MACD,CAjJkD,CAiJjD;;;MAGF,IAAI5D,IAAI,GAAGuD,EAAE,GAAGA,EAAE,CAACzD,WAAH,IAAkByD,EAAE,CAACvD,IAAxB,GAA+B,EAA5C;MACA,IAAI6E,cAAc,GAAG7E,IAAI,GAAG2C,6BAA6B,CAAC3C,IAAD,CAAhC,GAAyC,EAAlE;MAEA;QACE,IAAI,OAAOuD,EAAP,KAAc,UAAlB,EAA8B;UAC5BL,mBAAmB,CAACgB,GAApB,CAAwBX,EAAxB,EAA4BsB,cAA5B;QACD;MACF;MAED,OAAOA,cAAP;IACD;;IACD,SAASC,8BAAT,CAAwCvB,EAAxC,EAA4CX,MAA5C,EAAoDC,OAApD,EAA6D;MAC3D;QACE,OAAOS,4BAA4B,CAACC,EAAD,EAAK,KAAL,CAAnC;MACD;IACF;;IAED,SAASwB,eAAT,CAAyBC,SAAzB,EAAoC;MAClC,IAAIrG,SAAS,GAAGqG,SAAS,CAACrG,SAA1B;MACA,OAAO,CAAC,EAAEA,SAAS,IAAIA,SAAS,CAACsG,gBAAzB,CAAR;IACD;;IAED,SAASC,oCAAT,CAA8C5F,IAA9C,EAAoDsD,MAApD,EAA4DC,OAA5D,EAAqE;MAEnE,IAAIvD,IAAI,IAAI,IAAZ,EAAkB;QAChB,OAAO,EAAP;MACD;;MAED,IAAI,OAAOA,IAAP,KAAgB,UAApB,EAAgC;QAC9B;UACE,OAAOgE,4BAA4B,CAAChE,IAAD,EAAOyF,eAAe,CAACzF,IAAD,CAAtB,CAAnC;QACD;MACF;;MAED,IAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;QAC5B,OAAOqD,6BAA6B,CAACrD,IAAD,CAApC;MACD;;MAED,QAAQA,IAAR;QACE,KAAK5C,mBAAL;UACE,OAAOiG,6BAA6B,CAAC,UAAD,CAApC;;QAEF,KAAKhG,wBAAL;UACE,OAAOgG,6BAA6B,CAAC,cAAD,CAApC;MALJ;;MAQA,IAAI,OAAOrD,IAAP,KAAgB,QAApB,EAA8B;QAC5B,QAAQA,IAAI,CAACC,QAAb;UACE,KAAK9C,sBAAL;YACE,OAAOqI,8BAA8B,CAACxF,IAAI,CAACiB,MAAN,CAArC;;UAEF,KAAK3D,eAAL;YACE;YACA,OAAOsI,oCAAoC,CAAC5F,IAAI,CAACA,IAAN,EAAYsD,MAAZ,EAAoBC,OAApB,CAA3C;;UAEF,KAAKhG,eAAL;YACE;cACE,IAAI4D,aAAa,GAAGnB,IAApB;cACA,IAAIoB,OAAO,GAAGD,aAAa,CAACE,QAA5B;cACA,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAzB;;cAEA,IAAI;gBACF;gBACA,OAAOqE,oCAAoC,CAACtE,IAAI,CAACF,OAAD,CAAL,EAAgBkC,MAAhB,EAAwBC,OAAxB,CAA3C;cACD,CAHD,CAGE,OAAO/B,CAAP,EAAU,CAAE;YACf;QAlBL;MAoBD;;MAED,OAAO,EAAP;IACD;;IAED,IAAIqE,cAAc,GAAGnE,MAAM,CAACrC,SAAP,CAAiBwG,cAAtC;IAEA,IAAIC,kBAAkB,GAAG,EAAzB;IACA,IAAInH,sBAAsB,GAAGZ,oBAAoB,CAACY,sBAAlD;;IAEA,SAASoH,6BAAT,CAAuCC,OAAvC,EAAgD;MAC9C;QACE,IAAIA,OAAJ,EAAa;UACX,IAAIC,KAAK,GAAGD,OAAO,CAACE,MAApB;UACA,IAAItH,KAAK,GAAGgH,oCAAoC,CAACI,OAAO,CAAChG,IAAT,EAAegG,OAAO,CAACG,OAAvB,EAAgCF,KAAK,GAAGA,KAAK,CAACjG,IAAT,GAAgB,IAArD,CAAhD;UACArB,sBAAsB,CAACyH,kBAAvB,CAA0CxH,KAA1C;QACD,CAJD,MAIO;UACLD,sBAAsB,CAACyH,kBAAvB,CAA0C,IAA1C;QACD;MACF;IACF;;IAED,SAASC,cAAT,CAAwBC,SAAxB,EAAmCC,MAAnC,EAA2CC,QAA3C,EAAqDC,aAArD,EAAoET,OAApE,EAA6E;MAC3E;QACE;QACA,IAAIU,GAAG,GAAGtH,QAAQ,CAACG,IAAT,CAAcoH,IAAd,CAAmBd,cAAnB,CAAV;;QAEA,KAAK,IAAIe,YAAT,IAAyBN,SAAzB,EAAoC;UAClC,IAAII,GAAG,CAACJ,SAAD,EAAYM,YAAZ,CAAP,EAAkC;YAChC,IAAIC,OAAO,GAAG,KAAK,CAAnB,CADgC,CACV;YACtB;YACA;;YAEA,IAAI;cACF;cACA;cACA,IAAI,OAAOP,SAAS,CAACM,YAAD,CAAhB,KAAmC,UAAvC,EAAmD;gBACjD;gBACA,IAAIE,GAAG,GAAGtD,KAAK,CAAC,CAACiD,aAAa,IAAI,aAAlB,IAAmC,IAAnC,GAA0CD,QAA1C,GAAqD,SAArD,GAAiEI,YAAjE,GAAgF,gBAAhF,GAAmG,8EAAnG,GAAoL,OAAON,SAAS,CAACM,YAAD,CAApM,GAAqN,IAArN,GAA4N,+FAA7N,CAAf;gBACAE,GAAG,CAACpG,IAAJ,GAAW,qBAAX;gBACA,MAAMoG,GAAN;cACD;;cAEDD,OAAO,GAAGP,SAAS,CAACM,YAAD,CAAT,CAAwBL,MAAxB,EAAgCK,YAAhC,EAA8CH,aAA9C,EAA6DD,QAA7D,EAAuE,IAAvE,EAA6E,8CAA7E,CAAV;YACD,CAXD,CAWE,OAAOO,EAAP,EAAW;cACXF,OAAO,GAAGE,EAAV;YACD;;YAED,IAAIF,OAAO,IAAI,EAAEA,OAAO,YAAYrD,KAArB,CAAf,EAA4C;cAC1CuC,6BAA6B,CAACC,OAAD,CAA7B;cAEA/H,KAAK,CAAC,iCAAiC,qCAAjC,GAAyE,+DAAzE,GAA2I,iEAA3I,GAA+M,gEAA/M,GAAkR,iCAAnR,EAAsTwI,aAAa,IAAI,aAAvU,EAAsVD,QAAtV,EAAgWI,YAAhW,EAA8W,OAAOC,OAArX,CAAL;cAEAd,6BAA6B,CAAC,IAAD,CAA7B;YACD;;YAED,IAAIc,OAAO,YAAYrD,KAAnB,IAA4B,EAAEqD,OAAO,CAACG,OAAR,IAAmBlB,kBAArB,CAAhC,EAA0E;cACxE;cACA;cACAA,kBAAkB,CAACe,OAAO,CAACG,OAAT,CAAlB,GAAsC,IAAtC;cACAjB,6BAA6B,CAACC,OAAD,CAA7B;cAEA/H,KAAK,CAAC,oBAAD,EAAuBuI,QAAvB,EAAiCK,OAAO,CAACG,OAAzC,CAAL;cAEAjB,6BAA6B,CAAC,IAAD,CAA7B;YACD;UACF;QACF;MACF;IACF;;IAED,IAAIkB,WAAW,GAAG1I,KAAK,CAAC2I,OAAxB,CAlnBc,CAknBmB;;IAEjC,SAASA,OAAT,CAAiBC,CAAjB,EAAoB;MAClB,OAAOF,WAAW,CAACE,CAAD,CAAlB;IACD;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;;;IACA,SAASC,QAAT,CAAkBrE,KAAlB,EAAyB;MACvB;QACE;QACA,IAAIsE,cAAc,GAAG,OAAO1K,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAAC2K,WAA5D;QACA,IAAItH,IAAI,GAAGqH,cAAc,IAAItE,KAAK,CAACpG,MAAM,CAAC2K,WAAR,CAAvB,IAA+CvE,KAAK,CAACwE,WAAN,CAAkB7G,IAAjE,IAAyE,QAApF;QACA,OAAOV,IAAP;MACD;IACF,CAzoBa,CAyoBZ;;;IAGF,SAASwH,iBAAT,CAA2BzE,KAA3B,EAAkC;MAChC;QACE,IAAI;UACF0E,kBAAkB,CAAC1E,KAAD,CAAlB;UACA,OAAO,KAAP;QACD,CAHD,CAGE,OAAO2E,CAAP,EAAU;UACV,OAAO,IAAP;QACD;MACF;IACF;;IAED,SAASD,kBAAT,CAA4B1E,KAA5B,EAAmC;MACjC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,KAAKA,KAAZ;IACD;;IACD,SAAS4E,sBAAT,CAAgC5E,KAAhC,EAAuC;MACrC;QACE,IAAIyE,iBAAiB,CAACzE,KAAD,CAArB,EAA8B;UAC5B9E,KAAK,CAAC,gDAAgD,sEAAjD,EAAyHmJ,QAAQ,CAACrE,KAAD,CAAjI,CAAL;UAEA,OAAO0E,kBAAkB,CAAC1E,KAAD,CAAzB,CAH4B,CAGM;QACnC;MACF;IACF;;IAED,IAAI6E,iBAAiB,GAAG7J,oBAAoB,CAAC6J,iBAA7C;IACA,IAAIC,cAAc,GAAG;MACnBC,GAAG,EAAE,IADc;MAEnBC,GAAG,EAAE,IAFc;MAGnBC,MAAM,EAAE,IAHW;MAInBC,QAAQ,EAAE;IAJS,CAArB;IAMA,IAAIC,0BAAJ;IACA,IAAIC,0BAAJ;IACA,IAAIC,sBAAJ;IAEA;MACEA,sBAAsB,GAAG,EAAzB;IACD;;IAED,SAASC,WAAT,CAAqBC,MAArB,EAA6B;MAC3B;QACE,IAAIzC,cAAc,CAACtG,IAAf,CAAoB+I,MAApB,EAA4B,KAA5B,CAAJ,EAAwC;UACtC,IAAIC,MAAM,GAAG7G,MAAM,CAAC8G,wBAAP,CAAgCF,MAAhC,EAAwC,KAAxC,EAA+ClE,GAA5D;;UAEA,IAAImE,MAAM,IAAIA,MAAM,CAACE,cAArB,EAAqC;YACnC,OAAO,KAAP;UACD;QACF;MACF;MAED,OAAOH,MAAM,CAACP,GAAP,KAAe5H,SAAtB;IACD;;IAED,SAASuI,WAAT,CAAqBJ,MAArB,EAA6B;MAC3B;QACE,IAAIzC,cAAc,CAACtG,IAAf,CAAoB+I,MAApB,EAA4B,KAA5B,CAAJ,EAAwC;UACtC,IAAIC,MAAM,GAAG7G,MAAM,CAAC8G,wBAAP,CAAgCF,MAAhC,EAAwC,KAAxC,EAA+ClE,GAA5D;;UAEA,IAAImE,MAAM,IAAIA,MAAM,CAACE,cAArB,EAAqC;YACnC,OAAO,KAAP;UACD;QACF;MACF;MAED,OAAOH,MAAM,CAACR,GAAP,KAAe3H,SAAtB;IACD;;IAED,SAASwI,oCAAT,CAA8CL,MAA9C,EAAsDM,IAAtD,EAA4D;MAC1D;QACE,IAAI,OAAON,MAAM,CAACP,GAAd,KAAsB,QAAtB,IAAkCH,iBAAiB,CAACnD,OAApD,IAA+DmE,IAA/D,IAAuEhB,iBAAiB,CAACnD,OAAlB,CAA0BoE,SAA1B,KAAwCD,IAAnH,EAAyH;UACvH,IAAInC,aAAa,GAAG7F,wBAAwB,CAACgH,iBAAiB,CAACnD,OAAlB,CAA0BzE,IAA3B,CAA5C;;UAEA,IAAI,CAACoI,sBAAsB,CAAC3B,aAAD,CAA3B,EAA4C;YAC1CxI,KAAK,CAAC,kDAAkD,qEAAlD,GAA0H,oEAA1H,GAAiM,iFAAjM,GAAqR,2CAArR,GAAmU,iDAApU,EAAuX2C,wBAAwB,CAACgH,iBAAiB,CAACnD,OAAlB,CAA0BzE,IAA3B,CAA/Y,EAAibsI,MAAM,CAACP,GAAxb,CAAL;YAEAK,sBAAsB,CAAC3B,aAAD,CAAtB,GAAwC,IAAxC;UACD;QACF;MACF;IACF;;IAED,SAASqC,0BAAT,CAAoClG,KAApC,EAA2CpC,WAA3C,EAAwD;MACtD;QACE,IAAIuI,qBAAqB,GAAG,YAAY;UACtC,IAAI,CAACb,0BAAL,EAAiC;YAC/BA,0BAA0B,GAAG,IAA7B;YAEAjK,KAAK,CAAC,8DAA8D,gEAA9D,GAAiI,sEAAjI,GAA0M,gDAA3M,EAA6PuC,WAA7P,CAAL;UACD;QACF,CAND;;QAQAuI,qBAAqB,CAACN,cAAtB,GAAuC,IAAvC;QACA/G,MAAM,CAACiD,cAAP,CAAsB/B,KAAtB,EAA6B,KAA7B,EAAoC;UAClCwB,GAAG,EAAE2E,qBAD6B;UAElClG,YAAY,EAAE;QAFoB,CAApC;MAID;IACF;;IAED,SAASmG,0BAAT,CAAoCpG,KAApC,EAA2CpC,WAA3C,EAAwD;MACtD;QACE,IAAIyI,qBAAqB,GAAG,YAAY;UACtC,IAAI,CAACd,0BAAL,EAAiC;YAC/BA,0BAA0B,GAAG,IAA7B;YAEAlK,KAAK,CAAC,8DAA8D,gEAA9D,GAAiI,sEAAjI,GAA0M,gDAA3M,EAA6PuC,WAA7P,CAAL;UACD;QACF,CAND;;QAQAyI,qBAAqB,CAACR,cAAtB,GAAuC,IAAvC;QACA/G,MAAM,CAACiD,cAAP,CAAsB/B,KAAtB,EAA6B,KAA7B,EAAoC;UAClCwB,GAAG,EAAE6E,qBAD6B;UAElCpG,YAAY,EAAE;QAFoB,CAApC;MAID;IACF;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,IAAIqG,YAAY,GAAG,UAAUlJ,IAAV,EAAgB8H,GAAhB,EAAqBC,GAArB,EAA0Ba,IAA1B,EAAgCtF,MAAhC,EAAwC2C,KAAxC,EAA+CrD,KAA/C,EAAsD;MACvE,IAAIoD,OAAO,GAAG;QACZ;QACA/F,QAAQ,EAAEvD,kBAFE;QAGZ;QACAsD,IAAI,EAAEA,IAJM;QAKZ8H,GAAG,EAAEA,GALO;QAMZC,GAAG,EAAEA,GANO;QAOZnF,KAAK,EAAEA,KAPK;QAQZ;QACAsD,MAAM,EAAED;MATI,CAAd;MAYA;QACE;QACA;QACA;QACA;QACAD,OAAO,CAACmD,MAAR,GAAiB,EAAjB,CALF,CAKuB;QACrB;QACA;QACA;;QAEAzH,MAAM,CAACiD,cAAP,CAAsBqB,OAAO,CAACmD,MAA9B,EAAsC,WAAtC,EAAmD;UACjDtG,YAAY,EAAE,KADmC;UAEjDC,UAAU,EAAE,KAFqC;UAGjDE,QAAQ,EAAE,IAHuC;UAIjDD,KAAK,EAAE;QAJ0C,CAAnD,EAVF,CAeM;;QAEJrB,MAAM,CAACiD,cAAP,CAAsBqB,OAAtB,EAA+B,OAA/B,EAAwC;UACtCnD,YAAY,EAAE,KADwB;UAEtCC,UAAU,EAAE,KAF0B;UAGtCE,QAAQ,EAAE,KAH4B;UAItCD,KAAK,EAAE6F;QAJ+B,CAAxC,EAjBF,CAsBM;QACJ;;QAEAlH,MAAM,CAACiD,cAAP,CAAsBqB,OAAtB,EAA+B,SAA/B,EAA0C;UACxCnD,YAAY,EAAE,KAD0B;UAExCC,UAAU,EAAE,KAF4B;UAGxCE,QAAQ,EAAE,KAH8B;UAIxCD,KAAK,EAAEO;QAJiC,CAA1C;;QAOA,IAAI5B,MAAM,CAAC0H,MAAX,EAAmB;UACjB1H,MAAM,CAAC0H,MAAP,CAAcpD,OAAO,CAACpD,KAAtB;UACAlB,MAAM,CAAC0H,MAAP,CAAcpD,OAAd;QACD;MACF;MAED,OAAOA,OAAP;IACD,CApDD;IAqDA;AACA;AACA;AACA;AACA;AACA;;;IAEA,SAASqD,MAAT,CAAgBrJ,IAAhB,EAAsBsI,MAAtB,EAA8BgB,QAA9B,EAAwChG,MAAxC,EAAgDsF,IAAhD,EAAsD;MACpD;QACE,IAAIW,QAAJ,CADF,CACgB;;QAEd,IAAI3G,KAAK,GAAG,EAAZ;QACA,IAAIkF,GAAG,GAAG,IAAV;QACA,IAAIC,GAAG,GAAG,IAAV,CALF,CAKkB;QAChB;QACA;QACA;QACA;QACA;;QAEA,IAAIuB,QAAQ,KAAKnJ,SAAjB,EAA4B;UAC1B;YACEwH,sBAAsB,CAAC2B,QAAD,CAAtB;UACD;UAEDxB,GAAG,GAAG,KAAKwB,QAAX;QACD;;QAED,IAAIZ,WAAW,CAACJ,MAAD,CAAf,EAAyB;UACvB;YACEX,sBAAsB,CAACW,MAAM,CAACR,GAAR,CAAtB;UACD;UAEDA,GAAG,GAAG,KAAKQ,MAAM,CAACR,GAAlB;QACD;;QAED,IAAIO,WAAW,CAACC,MAAD,CAAf,EAAyB;UACvBP,GAAG,GAAGO,MAAM,CAACP,GAAb;UACAY,oCAAoC,CAACL,MAAD,EAASM,IAAT,CAApC;QACD,CA/BH,CA+BI;;;QAGF,KAAKW,QAAL,IAAiBjB,MAAjB,EAAyB;UACvB,IAAIzC,cAAc,CAACtG,IAAf,CAAoB+I,MAApB,EAA4BiB,QAA5B,KAAyC,CAAC1B,cAAc,CAAChC,cAAf,CAA8B0D,QAA9B,CAA9C,EAAuF;YACrF3G,KAAK,CAAC2G,QAAD,CAAL,GAAkBjB,MAAM,CAACiB,QAAD,CAAxB;UACD;QACF,CAtCH,CAsCI;;;QAGF,IAAIvJ,IAAI,IAAIA,IAAI,CAACwJ,YAAjB,EAA+B;UAC7B,IAAIA,YAAY,GAAGxJ,IAAI,CAACwJ,YAAxB;;UAEA,KAAKD,QAAL,IAAiBC,YAAjB,EAA+B;YAC7B,IAAI5G,KAAK,CAAC2G,QAAD,CAAL,KAAoBpJ,SAAxB,EAAmC;cACjCyC,KAAK,CAAC2G,QAAD,CAAL,GAAkBC,YAAY,CAACD,QAAD,CAA9B;YACD;UACF;QACF;;QAED,IAAIzB,GAAG,IAAIC,GAAX,EAAgB;UACd,IAAIvH,WAAW,GAAG,OAAOR,IAAP,KAAgB,UAAhB,GAA6BA,IAAI,CAACQ,WAAL,IAAoBR,IAAI,CAACU,IAAzB,IAAiC,SAA9D,GAA0EV,IAA5F;;UAEA,IAAI8H,GAAJ,EAAS;YACPgB,0BAA0B,CAAClG,KAAD,EAAQpC,WAAR,CAA1B;UACD;;UAED,IAAIuH,GAAJ,EAAS;YACPiB,0BAA0B,CAACpG,KAAD,EAAQpC,WAAR,CAA1B;UACD;QACF;;QAED,OAAO0I,YAAY,CAAClJ,IAAD,EAAO8H,GAAP,EAAYC,GAAZ,EAAiBa,IAAjB,EAAuBtF,MAAvB,EAA+BsE,iBAAiB,CAACnD,OAAjD,EAA0D7B,KAA1D,CAAnB;MACD;IACF;;IAED,IAAI6G,mBAAmB,GAAG1L,oBAAoB,CAAC6J,iBAA/C;IACA,IAAI8B,wBAAwB,GAAG3L,oBAAoB,CAACY,sBAApD;;IAEA,SAASgL,+BAAT,CAAyC3D,OAAzC,EAAkD;MAChD;QACE,IAAIA,OAAJ,EAAa;UACX,IAAIC,KAAK,GAAGD,OAAO,CAACE,MAApB;UACA,IAAItH,KAAK,GAAGgH,oCAAoC,CAACI,OAAO,CAAChG,IAAT,EAAegG,OAAO,CAACG,OAAvB,EAAgCF,KAAK,GAAGA,KAAK,CAACjG,IAAT,GAAgB,IAArD,CAAhD;UACA0J,wBAAwB,CAACtD,kBAAzB,CAA4CxH,KAA5C;QACD,CAJD,MAIO;UACL8K,wBAAwB,CAACtD,kBAAzB,CAA4C,IAA5C;QACD;MACF;IACF;;IAED,IAAIwD,6BAAJ;IAEA;MACEA,6BAA6B,GAAG,KAAhC;IACD;IACD;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASC,cAAT,CAAwBC,MAAxB,EAAgC;MAC9B;QACE,OAAO,OAAOA,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,KAAK,IAAzC,IAAiDA,MAAM,CAAC7J,QAAP,KAAoBvD,kBAA5E;MACD;IACF;;IAED,SAASqN,2BAAT,GAAuC;MACrC;QACE,IAAIN,mBAAmB,CAAChF,OAAxB,EAAiC;UAC/B,IAAI/D,IAAI,GAAGE,wBAAwB,CAAC6I,mBAAmB,CAAChF,OAApB,CAA4BzE,IAA7B,CAAnC;;UAEA,IAAIU,IAAJ,EAAU;YACR,OAAO,qCAAqCA,IAArC,GAA4C,IAAnD;UACD;QACF;;QAED,OAAO,EAAP;MACD;IACF;;IAED,SAASsJ,0BAAT,CAAoC1G,MAApC,EAA4C;MAC1C;QACE,IAAIA,MAAM,KAAKnD,SAAf,EAA0B;UACxB,IAAI8J,QAAQ,GAAG3G,MAAM,CAAC2G,QAAP,CAAgB5E,OAAhB,CAAwB,WAAxB,EAAqC,EAArC,CAAf;UACA,IAAI6E,UAAU,GAAG5G,MAAM,CAAC4G,UAAxB;UACA,OAAO,4BAA4BD,QAA5B,GAAuC,GAAvC,GAA6CC,UAA7C,GAA0D,GAAjE;QACD;;QAED,OAAO,EAAP;MACD;IACF;IACD;AACA;AACA;AACA;AACA;;;IAGA,IAAIC,qBAAqB,GAAG,EAA5B;;IAEA,SAASC,4BAAT,CAAsCC,UAAtC,EAAkD;MAChD;QACE,IAAI9H,IAAI,GAAGwH,2BAA2B,EAAtC;;QAEA,IAAI,CAACxH,IAAL,EAAW;UACT,IAAI+H,UAAU,GAAG,OAAOD,UAAP,KAAsB,QAAtB,GAAiCA,UAAjC,GAA8CA,UAAU,CAAC7J,WAAX,IAA0B6J,UAAU,CAAC3J,IAApG;;UAEA,IAAI4J,UAAJ,EAAgB;YACd/H,IAAI,GAAG,gDAAgD+H,UAAhD,GAA6D,IAApE;UACD;QACF;;QAED,OAAO/H,IAAP;MACD;IACF;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASgI,mBAAT,CAA6BvE,OAA7B,EAAsCqE,UAAtC,EAAkD;MAChD;QACE,IAAI,CAACrE,OAAO,CAACmD,MAAT,IAAmBnD,OAAO,CAACmD,MAAR,CAAeqB,SAAlC,IAA+CxE,OAAO,CAAC8B,GAAR,IAAe,IAAlE,EAAwE;UACtE;QACD;;QAED9B,OAAO,CAACmD,MAAR,CAAeqB,SAAf,GAA2B,IAA3B;QACA,IAAIC,yBAAyB,GAAGL,4BAA4B,CAACC,UAAD,CAA5D;;QAEA,IAAIF,qBAAqB,CAACM,yBAAD,CAAzB,EAAsD;UACpD;QACD;;QAEDN,qBAAqB,CAACM,yBAAD,CAArB,GAAmD,IAAnD,CAZF,CAY2D;QACzD;QACA;;QAEA,IAAIC,UAAU,GAAG,EAAjB;;QAEA,IAAI1E,OAAO,IAAIA,OAAO,CAACE,MAAnB,IAA6BF,OAAO,CAACE,MAAR,KAAmBuD,mBAAmB,CAAChF,OAAxE,EAAiF;UAC/E;UACAiG,UAAU,GAAG,iCAAiC9J,wBAAwB,CAACoF,OAAO,CAACE,MAAR,CAAelG,IAAhB,CAAzD,GAAiF,GAA9F;QACD;;QAED2J,+BAA+B,CAAC3D,OAAD,CAA/B;QAEA/H,KAAK,CAAC,0DAA0D,sEAA3D,EAAmIwM,yBAAnI,EAA8JC,UAA9J,CAAL;QAEAf,+BAA+B,CAAC,IAAD,CAA/B;MACD;IACF;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASgB,iBAAT,CAA2BC,IAA3B,EAAiCP,UAAjC,EAA6C;MAC3C;QACE,IAAI,OAAOO,IAAP,KAAgB,QAApB,EAA8B;UAC5B;QACD;;QAED,IAAI1D,OAAO,CAAC0D,IAAD,CAAX,EAAmB;UACjB,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,IAAI,CAACvM,MAAzB,EAAiCwM,CAAC,EAAlC,EAAsC;YACpC,IAAIC,KAAK,GAAGF,IAAI,CAACC,CAAD,CAAhB;;YAEA,IAAIhB,cAAc,CAACiB,KAAD,CAAlB,EAA2B;cACzBP,mBAAmB,CAACO,KAAD,EAAQT,UAAR,CAAnB;YACD;UACF;QACF,CARD,MAQO,IAAIR,cAAc,CAACe,IAAD,CAAlB,EAA0B;UAC/B;UACA,IAAIA,IAAI,CAACzB,MAAT,EAAiB;YACfyB,IAAI,CAACzB,MAAL,CAAYqB,SAAZ,GAAwB,IAAxB;UACD;QACF,CALM,MAKA,IAAII,IAAJ,EAAU;UACf,IAAIG,UAAU,GAAGnN,aAAa,CAACgN,IAAD,CAA9B;;UAEA,IAAI,OAAOG,UAAP,KAAsB,UAA1B,EAAsC;YACpC;YACA;YACA,IAAIA,UAAU,KAAKH,IAAI,CAACI,OAAxB,EAAiC;cAC/B,IAAItN,QAAQ,GAAGqN,UAAU,CAACxL,IAAX,CAAgBqL,IAAhB,CAAf;cACA,IAAIK,IAAJ;;cAEA,OAAO,CAAC,CAACA,IAAI,GAAGvN,QAAQ,CAACwN,IAAT,EAAR,EAAyBC,IAAjC,EAAuC;gBACrC,IAAItB,cAAc,CAACoB,IAAI,CAAClI,KAAN,CAAlB,EAAgC;kBAC9BwH,mBAAmB,CAACU,IAAI,CAAClI,KAAN,EAAasH,UAAb,CAAnB;gBACD;cACF;YACF;UACF;QACF;MACF;IACF;IACD;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASe,iBAAT,CAA2BpF,OAA3B,EAAoC;MAClC;QACE,IAAIhG,IAAI,GAAGgG,OAAO,CAAChG,IAAnB;;QAEA,IAAIA,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAKG,SAA1B,IAAuC,OAAOH,IAAP,KAAgB,QAA3D,EAAqE;UACnE;QACD;;QAED,IAAIqL,SAAJ;;QAEA,IAAI,OAAOrL,IAAP,KAAgB,UAApB,EAAgC;UAC9BqL,SAAS,GAAGrL,IAAI,CAACqL,SAAjB;QACD,CAFD,MAEO,IAAI,OAAOrL,IAAP,KAAgB,QAAhB,KAA6BA,IAAI,CAACC,QAAL,KAAkB9C,sBAAlB,IAA4C;QACpF;QACA6C,IAAI,CAACC,QAAL,KAAkB3C,eAFP,CAAJ,EAE6B;UAClC+N,SAAS,GAAGrL,IAAI,CAACqL,SAAjB;QACD,CAJM,MAIA;UACL;QACD;;QAED,IAAIA,SAAJ,EAAe;UACb;UACA,IAAI3K,IAAI,GAAGE,wBAAwB,CAACZ,IAAD,CAAnC;UACAqG,cAAc,CAACgF,SAAD,EAAYrF,OAAO,CAACpD,KAApB,EAA2B,MAA3B,EAAmClC,IAAnC,EAAyCsF,OAAzC,CAAd;QACD,CAJD,MAIO,IAAIhG,IAAI,CAACsL,SAAL,KAAmBnL,SAAnB,IAAgC,CAACyJ,6BAArC,EAAoE;UACzEA,6BAA6B,GAAG,IAAhC,CADyE,CACnC;;UAEtC,IAAI2B,KAAK,GAAG3K,wBAAwB,CAACZ,IAAD,CAApC;;UAEA/B,KAAK,CAAC,qGAAD,EAAwGsN,KAAK,IAAI,SAAjH,CAAL;QACD;;QAED,IAAI,OAAOvL,IAAI,CAACwL,eAAZ,KAAgC,UAAhC,IAA8C,CAACxL,IAAI,CAACwL,eAAL,CAAqBC,oBAAxE,EAA8F;UAC5FxN,KAAK,CAAC,+DAA+D,kEAAhE,CAAL;QACD;MACF;IACF;IACD;AACA;AACA;AACA;;;IAGA,SAASyN,qBAAT,CAA+BC,QAA/B,EAAyC;MACvC;QACE,IAAIC,IAAI,GAAGlK,MAAM,CAACkK,IAAP,CAAYD,QAAQ,CAAC/I,KAArB,CAAX;;QAEA,KAAK,IAAIiI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGe,IAAI,CAACvN,MAAzB,EAAiCwM,CAAC,EAAlC,EAAsC;UACpC,IAAI/C,GAAG,GAAG8D,IAAI,CAACf,CAAD,CAAd;;UAEA,IAAI/C,GAAG,KAAK,UAAR,IAAsBA,GAAG,KAAK,KAAlC,EAAyC;YACvC6B,+BAA+B,CAACgC,QAAD,CAA/B;YAEA1N,KAAK,CAAC,qDAAqD,0DAAtD,EAAkH6J,GAAlH,CAAL;YAEA6B,+BAA+B,CAAC,IAAD,CAA/B;YACA;UACD;QACF;;QAED,IAAIgC,QAAQ,CAAC5D,GAAT,KAAiB,IAArB,EAA2B;UACzB4B,+BAA+B,CAACgC,QAAD,CAA/B;UAEA1N,KAAK,CAAC,uDAAD,CAAL;UAEA0L,+BAA+B,CAAC,IAAD,CAA/B;QACD;MACF;IACF;;IAED,SAASkC,iBAAT,CAA2B7L,IAA3B,EAAiC4C,KAAjC,EAAwCkF,GAAxC,EAA6CgE,gBAA7C,EAA+DxI,MAA/D,EAAuEsF,IAAvE,EAA6E;MAC3E;QACE,IAAImD,SAAS,GAAGhM,kBAAkB,CAACC,IAAD,CAAlC,CADF,CAC4C;QAC1C;;QAEA,IAAI,CAAC+L,SAAL,EAAgB;UACd,IAAIxJ,IAAI,GAAG,EAAX;;UAEA,IAAIvC,IAAI,KAAKG,SAAT,IAAsB,OAAOH,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,KAAK,IAArC,IAA6C0B,MAAM,CAACkK,IAAP,CAAY5L,IAAZ,EAAkB3B,MAAlB,KAA6B,CAApG,EAAuG;YACrGkE,IAAI,IAAI,+DAA+D,wEAAvE;UACD;;UAED,IAAIyJ,UAAU,GAAGhC,0BAA0B,CAAC1G,MAAD,CAA3C;;UAEA,IAAI0I,UAAJ,EAAgB;YACdzJ,IAAI,IAAIyJ,UAAR;UACD,CAFD,MAEO;YACLzJ,IAAI,IAAIwH,2BAA2B,EAAnC;UACD;;UAED,IAAIkC,UAAJ;;UAEA,IAAIjM,IAAI,KAAK,IAAb,EAAmB;YACjBiM,UAAU,GAAG,MAAb;UACD,CAFD,MAEO,IAAI/E,OAAO,CAAClH,IAAD,CAAX,EAAmB;YACxBiM,UAAU,GAAG,OAAb;UACD,CAFM,MAEA,IAAIjM,IAAI,KAAKG,SAAT,IAAsBH,IAAI,CAACC,QAAL,KAAkBvD,kBAA5C,EAAgE;YACrEuP,UAAU,GAAG,OAAOrL,wBAAwB,CAACZ,IAAI,CAACA,IAAN,CAAxB,IAAuC,SAA9C,IAA2D,KAAxE;YACAuC,IAAI,GAAG,oEAAP;UACD,CAHM,MAGA;YACL0J,UAAU,GAAG,OAAOjM,IAApB;UACD;;UAED/B,KAAK,CAAC,0DAA0D,0DAA1D,GAAuH,4BAAxH,EAAsJgO,UAAtJ,EAAkK1J,IAAlK,CAAL;QACD;;QAED,IAAIyD,OAAO,GAAGqD,MAAM,CAACrJ,IAAD,EAAO4C,KAAP,EAAckF,GAAd,EAAmBxE,MAAnB,EAA2BsF,IAA3B,CAApB,CAnCF,CAmCwD;QACtD;;QAEA,IAAI5C,OAAO,IAAI,IAAf,EAAqB;UACnB,OAAOA,OAAP;QACD,CAxCH,CAwCI;QACF;QACA;QACA;QACA;;;QAGA,IAAI+F,SAAJ,EAAe;UACb,IAAIG,QAAQ,GAAGtJ,KAAK,CAACsJ,QAArB;;UAEA,IAAIA,QAAQ,KAAK/L,SAAjB,EAA4B;YAC1B,IAAI2L,gBAAJ,EAAsB;cACpB,IAAI5E,OAAO,CAACgF,QAAD,CAAX,EAAuB;gBACrB,KAAK,IAAIrB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqB,QAAQ,CAAC7N,MAA7B,EAAqCwM,CAAC,EAAtC,EAA0C;kBACxCF,iBAAiB,CAACuB,QAAQ,CAACrB,CAAD,CAAT,EAAc7K,IAAd,CAAjB;gBACD;;gBAED,IAAI0B,MAAM,CAAC0H,MAAX,EAAmB;kBACjB1H,MAAM,CAAC0H,MAAP,CAAc8C,QAAd;gBACD;cACF,CARD,MAQO;gBACLjO,KAAK,CAAC,2DAA2D,gEAA3D,GAA8H,kCAA/H,CAAL;cACD;YACF,CAZD,MAYO;cACL0M,iBAAiB,CAACuB,QAAD,EAAWlM,IAAX,CAAjB;YACD;UACF;QACF;;QAED,IAAIA,IAAI,KAAKlD,mBAAb,EAAkC;UAChC4O,qBAAqB,CAAC1F,OAAD,CAArB;QACD,CAFD,MAEO;UACLoF,iBAAiB,CAACpF,OAAD,CAAjB;QACD;;QAED,OAAOA,OAAP;MACD;IACF,CA3vCa,CA2vCZ;;;IAEF,IAAImG,QAAQ,GAAIN,iBAAhB;IAEAO,OAAO,CAACC,QAAR,GAAmBvP,mBAAnB;IACAsP,OAAO,CAAC/C,MAAR,GAAiB8C,QAAjB;EACG,CAjwCD;AAkwCD"}, "metadata": {}, "sourceType": "script"}