{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\nvar hasWarnAboutTypo = !1;\n\nfunction getPartialVisibilityGutter(responsive, partialVisible, serverSideDeviceType, clientSideDeviceType) {\n  var gutter = 0,\n      deviceType = clientSideDeviceType || serverSideDeviceType;\n  return partialVisible && deviceType && (!hasWarnAboutTypo && \"production\" !== process.env.NODE_ENV && responsive[deviceType].paritialVisibilityGutter && (hasWarnAboutTypo = !0, console.warn(\"You appear to be using paritialVisibilityGutter instead of partialVisibilityGutter which will be moved to partialVisibilityGutter in the future completely\")), gutter = responsive[deviceType].partialVisibilityGutter || responsive[deviceType].paritialVisibilityGutter), gutter;\n}\n\nfunction getWidthFromDeviceType(deviceType, responsive) {\n  var itemWidth;\n  responsive[deviceType] && (itemWidth = (100 / responsive[deviceType].items).toFixed(1));\n  return itemWidth;\n}\n\nfunction getItemClientSideWidth(props, slidesToShow, containerWidth) {\n  return Math.round(containerWidth / (slidesToShow + (props.centerMode ? 1 : 0)));\n}\n\nexports.getPartialVisibilityGutter = getPartialVisibilityGutter, exports.getWidthFromDeviceType = getWidthFromDeviceType, exports.getItemClientSideWidth = getItemClientSideWidth;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "hasWarnAboutTypo", "getPartialVisibilityGutter", "responsive", "partialVisible", "serverSideDeviceType", "clientSideDeviceType", "gutter", "deviceType", "process", "env", "NODE_ENV", "paritialVisibilityGutter", "console", "warn", "partialVisibilityGutter", "getWidthFromDeviceType", "itemWidth", "items", "toFixed", "getItemClientSideWidth", "props", "slidesToShow", "containerWidth", "Math", "round", "centerMode"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/elementWidth.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var hasWarnAboutTypo=!1;function getPartialVisibilityGutter(responsive,partialVisible,serverSideDeviceType,clientSideDeviceType){var gutter=0,deviceType=clientSideDeviceType||serverSideDeviceType;return partialVisible&&deviceType&&(!hasWarnAboutTypo&&\"production\"!==process.env.NODE_ENV&&responsive[deviceType].paritialVisibilityGutter&&(hasWarnAboutTypo=!0,console.warn(\"You appear to be using paritialVisibilityGutter instead of partialVisibilityGutter which will be moved to partialVisibilityGutter in the future completely\")),gutter=responsive[deviceType].partialVisibilityGutter||responsive[deviceType].paritialVisibilityGutter),gutter}function getWidthFromDeviceType(deviceType,responsive){var itemWidth;responsive[deviceType]&&(itemWidth=(100/responsive[deviceType].items).toFixed(1));return itemWidth}function getItemClientSideWidth(props,slidesToShow,containerWidth){return Math.round(containerWidth/(slidesToShow+(props.centerMode?1:0)))}exports.getPartialVisibilityGutter=getPartialVisibilityGutter,exports.getWidthFromDeviceType=getWidthFromDeviceType,exports.getItemClientSideWidth=getItemClientSideWidth;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;AAAuD,IAAIC,gBAAgB,GAAC,CAAC,CAAtB;;AAAwB,SAASC,0BAAT,CAAoCC,UAApC,EAA+CC,cAA/C,EAA8DC,oBAA9D,EAAmFC,oBAAnF,EAAwG;EAAC,IAAIC,MAAM,GAAC,CAAX;EAAA,IAAaC,UAAU,GAACF,oBAAoB,IAAED,oBAA9C;EAAmE,OAAOD,cAAc,IAAEI,UAAhB,KAA6B,CAACP,gBAAD,IAAmB,iBAAeQ,OAAO,CAACC,GAAR,CAAYC,QAA9C,IAAwDR,UAAU,CAACK,UAAD,CAAV,CAAuBI,wBAA/E,KAA0GX,gBAAgB,GAAC,CAAC,CAAlB,EAAoBY,OAAO,CAACC,IAAR,CAAa,4JAAb,CAA9H,GAA0SP,MAAM,GAACJ,UAAU,CAACK,UAAD,CAAV,CAAuBO,uBAAvB,IAAgDZ,UAAU,CAACK,UAAD,CAAV,CAAuBI,wBAArZ,GAA+aL,MAAtb;AAA6b;;AAAA,SAASS,sBAAT,CAAgCR,UAAhC,EAA2CL,UAA3C,EAAsD;EAAC,IAAIc,SAAJ;EAAcd,UAAU,CAACK,UAAD,CAAV,KAAyBS,SAAS,GAAC,CAAC,MAAId,UAAU,CAACK,UAAD,CAAV,CAAuBU,KAA5B,EAAmCC,OAAnC,CAA2C,CAA3C,CAAnC;EAAkF,OAAOF,SAAP;AAAiB;;AAAA,SAASG,sBAAT,CAAgCC,KAAhC,EAAsCC,YAAtC,EAAmDC,cAAnD,EAAkE;EAAC,OAAOC,IAAI,CAACC,KAAL,CAAWF,cAAc,IAAED,YAAY,IAAED,KAAK,CAACK,UAAN,GAAiB,CAAjB,GAAmB,CAArB,CAAd,CAAzB,CAAP;AAAwE;;AAAA3B,OAAO,CAACG,0BAAR,GAAmCA,0BAAnC,EAA8DH,OAAO,CAACiB,sBAAR,GAA+BA,sBAA7F,EAAoHjB,OAAO,CAACqB,sBAAR,GAA+BA,sBAAnJ"}, "metadata": {}, "sourceType": "script"}