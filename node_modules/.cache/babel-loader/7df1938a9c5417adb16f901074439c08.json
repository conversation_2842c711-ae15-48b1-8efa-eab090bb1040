{"ast": null, "code": "var DESCRIPTORS = require('../internals/descriptors');\n\nvar fails = require('../internals/fails'); // V8 ~ Chrome 36-\n// https://bugs.chromium.org/p/v8/issues/detail?id=3334\n\n\nmodule.exports = DESCRIPTORS && fails(function () {\n  // eslint-disable-next-line es-x/no-object-defineproperty -- required for testing\n  return Object.defineProperty(function () {\n    /* empty */\n  }, 'prototype', {\n    value: 42,\n    writable: false\n  }).prototype != 42;\n});", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "fails", "module", "exports", "Object", "defineProperty", "value", "writable", "prototype"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/v8-prototype-define-bug.js"], "sourcesContent": ["var DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\n\n// V8 ~ Chrome 36-\n// https://bugs.chromium.org/p/v8/issues/detail?id=3334\nmodule.exports = DESCRIPTORS && fails(function () {\n  // eslint-disable-next-line es-x/no-object-defineproperty -- required for testing\n  return Object.defineProperty(function () { /* empty */ }, 'prototype', {\n    value: 42,\n    writable: false\n  }).prototype != 42;\n});\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAAD,CAAzB;;AACA,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAD,CAAnB,C,CAEA;AACA;;;AACAE,MAAM,CAACC,OAAP,GAAiBJ,WAAW,IAAIE,KAAK,CAAC,YAAY;EAChD;EACA,OAAOG,MAAM,CAACC,cAAP,CAAsB,YAAY;IAAE;EAAa,CAAjD,EAAmD,WAAnD,EAAgE;IACrEC,KAAK,EAAE,EAD8D;IAErEC,QAAQ,EAAE;EAF2D,CAAhE,EAGJC,SAHI,IAGS,EAHhB;AAID,CANoC,CAArC"}, "metadata": {}, "sourceType": "script"}