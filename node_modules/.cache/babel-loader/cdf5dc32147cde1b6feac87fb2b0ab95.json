{"ast": null, "code": "import styled from \"styled-components\";\nexport const Section = styled.section`\n  display: ${_ref => {\n  let {\n    flex\n  } = _ref;\n  return flex ? \" flex\" : \"block\";\n}};\n  align-items: flex-start;\n`;", "map": {"version": 3, "names": ["styled", "Section", "section", "flex"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Section = styled.section`\n  display: ${({ flex }) => (flex ? \" flex\" : \"block\")};\n  align-items: flex-start;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,OAAO,GAAGD,MAAM,CAACE,OAAQ;AACtC,aAAa;EAAA,IAAC;IAAEC;EAAF,CAAD;EAAA,OAAeA,IAAI,GAAG,OAAH,GAAa,OAAhC;AAAA,CAAyC;AACtD;AACA,CAHO"}, "metadata": {}, "sourceType": "module"}