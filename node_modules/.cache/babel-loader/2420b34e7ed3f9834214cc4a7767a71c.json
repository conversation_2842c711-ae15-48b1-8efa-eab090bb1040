{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is'); // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n\n\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "ReactIs", "require", "throwOnDirectAccess", "module", "exports", "isElement"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/prop-types/index.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIA,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzC,IAAIC,OAAO,GAAGC,OAAO,CAAC,UAAD,CAArB,CADyC,CAGzC;EACA;;;EACA,IAAIC,mBAAmB,GAAG,IAA1B;EACAC,MAAM,CAACC,OAAP,GAAiBH,OAAO,CAAC,2BAAD,CAAP,CAAqCD,OAAO,CAACK,SAA7C,EAAwDH,mBAAxD,CAAjB;AACD,CAPD,MAOO;EACL;EACA;EACAC,MAAM,CAACC,OAAP,GAAiBH,OAAO,CAAC,4BAAD,CAAP,EAAjB;AACD"}, "metadata": {}, "sourceType": "script"}