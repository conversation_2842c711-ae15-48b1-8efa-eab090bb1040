{"ast": null, "code": "var fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es-x/no-function-prototype-bind -- safe\n  var test = function () {\n    /* empty */\n  }.bind(); // eslint-disable-next-line no-prototype-builtins -- safe\n\n\n  return typeof test != 'function' || test.hasOwnProperty('prototype');\n});", "map": {"version": 3, "names": ["fails", "require", "module", "exports", "test", "bind", "hasOwnProperty"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/function-bind-native.js"], "sourcesContent": ["var fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es-x/no-function-prototype-bind -- safe\n  var test = (function () { /* empty */ }).bind();\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return typeof test != 'function' || test.hasOwnProperty('prototype');\n});\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAD,CAAnB;;AAEAC,MAAM,CAACC,OAAP,GAAiB,CAACH,KAAK,CAAC,YAAY;EAClC;EACA,IAAII,IAAI,GAAI,YAAY;IAAE;EAAa,CAA5B,CAA8BC,IAA9B,EAAX,CAFkC,CAGlC;;;EACA,OAAO,OAAOD,IAAP,IAAe,UAAf,IAA6BA,IAAI,CAACE,cAAL,CAAoB,WAApB,CAApC;AACD,CALsB,CAAvB"}, "metadata": {}, "sourceType": "script"}