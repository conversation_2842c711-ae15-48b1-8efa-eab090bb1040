{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/index.jsx\";\nimport React from \"react\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ClientItem = _ref => {\n  let {\n    data\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: /*#__PURE__*/_jsxDEV(Styles.ImageHolder, {\n      src: data.img\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n\n_c = ClientItem;\nexport default ClientItem;\n\nvar _c;\n\n$RefreshReg$(_c, \"ClientItem\");", "map": {"version": 3, "names": ["React", "Styles", "ClientItem", "data", "img"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst ClientItem = ({ data }) => {\n  return (\n    <Styles.Container>\n      <Styles.ImageHolder src={data.img} />\n    </Styles.Container>\n  );\n};\n\nexport default ClientItem;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,UAAU,GAAG,QAAc;EAAA,IAAb;IAAEC;EAAF,CAAa;EAC/B,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,uBACE,QAAC,MAAD,CAAQ,WAAR;MAAoB,GAAG,EAAEA,IAAI,CAACC;IAA9B;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAKD,CAND;;KAAMF,U;AAQN,eAAeA,UAAf"}, "metadata": {}, "sourceType": "module"}