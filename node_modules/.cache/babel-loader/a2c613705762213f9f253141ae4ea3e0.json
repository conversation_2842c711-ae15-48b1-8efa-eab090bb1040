{"ast": null, "code": "var isObject = require('../internals/is-object');\n\nvar $String = String;\nvar $TypeError = TypeError; // `Assert: Type(argument) is Object`\n\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw $TypeError($String(argument) + ' is not an object');\n};", "map": {"version": 3, "names": ["isObject", "require", "$String", "String", "$TypeError", "TypeError", "module", "exports", "argument"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/an-object.js"], "sourcesContent": ["var isObject = require('../internals/is-object');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw $TypeError($String(argument) + ' is not an object');\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,wBAAD,CAAtB;;AAEA,IAAIC,OAAO,GAAGC,MAAd;AACA,IAAIC,UAAU,GAAGC,SAAjB,C,CAEA;;AACAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,QAAV,EAAoB;EACnC,IAAIR,QAAQ,CAACQ,QAAD,CAAZ,EAAwB,OAAOA,QAAP;EACxB,MAAMJ,UAAU,CAACF,OAAO,CAACM,QAAD,CAAP,GAAoB,mBAArB,CAAhB;AACD,CAHD"}, "metadata": {}, "sourceType": "script"}