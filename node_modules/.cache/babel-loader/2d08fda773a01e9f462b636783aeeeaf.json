{"ast": null, "code": "import styled from 'styled-components';\nexport const Container = styled.div`\n    \n`;\nexport const TitleHolder = styled.p`\n  \n`;\nexport const ListHolder = styled.ul`\n    list-style-type: disc;\n`;\nexport const ListItem = styled.li`\n    padding: 20px 0;\n`;\nexport const Button = styled.button`\n    background-color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.main.yellow;\n}};\n    padding: 10px 20px;\n    border-radius: 5px;\n    border: none;\n\n    margin: 50px 0 20px;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "TitleHolder", "p", "ListHolder", "ul", "ListItem", "li", "<PERSON><PERSON>", "button", "theme", "colors", "main", "yellow"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Container = styled.div`\n    \n`;\n\nexport const TitleHolder = styled.p`\n  \n`;\n\nexport const ListHolder = styled.ul`\n    list-style-type: disc;\n`;\n\nexport const ListItem = styled.li`\n    padding: 20px 0;\n`;\n\nexport const Button = styled.button`\n    background-color: ${({ theme: { colors } }) => colors.main.yellow};\n    padding: 10px 20px;\n    border-radius: 5px;\n    border: none;\n\n    margin: 50px 0 20px;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA,CAFO;AAIP,OAAO,MAAMC,WAAW,GAAGH,MAAM,CAACI,CAAE;AACpC;AACA,CAFO;AAIP,OAAO,MAAMC,UAAU,GAAGL,MAAM,CAACM,EAAG;AACpC;AACA,CAFO;AAIP,OAAO,MAAMC,QAAQ,GAAGP,MAAM,CAACQ,EAAG;AAClC;AACA,CAFO;AAIP,OAAO,MAAMC,MAAM,GAAGT,MAAM,CAACU,MAAO;AACpC,wBAAwB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,MAAvC;AAAA,CAA8C;AACtE;AACA;AACA;AACA;AACA;AACA,CAPO"}, "metadata": {}, "sourceType": "module"}