{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport { BrowserRouter } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render( /*#__PURE__*/_jsxDEV(BrowserRouter, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "root", "createRoot", "document", "getElementById", "render"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\n\nimport { BrowserRouter } from 'react-router-dom';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <BrowserRouter>\n    <App />\n  </BrowserRouter>\n);\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,QAAP,MAAqB,kBAArB;AACA,OAAO,aAAP;AACA,OAAOC,GAAP,MAAgB,OAAhB;AAEA,SAASC,aAAT,QAA8B,kBAA9B;;AAEA,MAAMC,IAAI,GAAGH,QAAQ,CAACI,UAAT,CAAoBC,QAAQ,CAACC,cAAT,CAAwB,MAAxB,CAApB,CAAb;AACAH,IAAI,CAACI,MAAL,eACE,QAAC,aAAD;EAAA,uBACE,QAAC,GAAD;IAAA;IAAA;IAAA;EAAA;AADF;EAAA;EAAA;EAAA;AAAA,QADF"}, "metadata": {}, "sourceType": "module"}