{"ast": null, "code": "import styled from 'styled-components';\nexport const Container = styled.div`\n    text-align: center;\n    padding: 20px 0 30px;\n\n    font-size: ${_ref => {\n  let {\n    theme: {\n      font\n    }\n  } = _ref;\n  return font.big;\n}};\n    font-weight: bold;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "theme", "font", "big"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Container = styled.div`\n    text-align: center;\n    padding: 20px 0 30px;\n\n    font-size: ${({ theme: { font } }) => font.big};\n    font-weight: bold;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA,iBAAiB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAAyBA,IAAI,CAACC,GAA9B;AAAA,CAAkC;AACnD;AACA,CANO"}, "metadata": {}, "sourceType": "module"}