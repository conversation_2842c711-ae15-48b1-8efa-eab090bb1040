{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  margin: 50px 0;\n`;\nexport const BackButtonWrapper = styled.div`\n  display: flex;\n  align-items: center;\n`;\nexport const IconHolder = styled.img`\n  width: 25px;\n  height: 25px;\n\n  object-fit: contain;\n\n  margin-right: 10px;\n\n  /* transform: translateX(-150%); */\n`;\nexport const TextHolder = styled.span`\n  font-size: ${_ref => {\n  let {\n    heading,\n    big,\n    small,\n    theme: {\n      font\n    }\n  } = _ref;\n  return heading ? font.title : small ? font.small : big ? font.big : font.main;\n}};\n  font-weight: ${_ref2 => {\n  let {\n    bold\n  } = _ref2;\n  return bold ? 600 : 400;\n}};\n  display: block;\n`;\nexport const TitleHolder = styled.div`\n  margin: 20px 0 40px;\n`;\nexport const ImageContainer = styled.div`\n  /* margin: 50px 0 70px; */\n\n  border-radius: 20px;\n  /* height: 600px; */\n\n  display: block;\n  margin-bottom: 50px;\n\n  /* .image-gallery-content .image-gallery-slide .image-gallery-image {\n    max-height: 100%;\n  } */\n`;\nexport const ImageHolder = styled.img`\n  width: 100%;\n  height: 100%;\n\n  border-radius: 20px;\n\n  object-fit: cover;\n`;\nexport const DetailsWrapper = styled.div`\n  margin: 20px 0 100px;\n`;\nexport const DetailsItemHolder = styled.div`\n  margin: 20px 0;\n`;\nexport const ServiceListWrapper = styled.div`\n  display: flex;\n  align-items: flex-start;\n\n  flex-wrap: wrap;\n  gap: 20px;\n\n  margin: 10px 0;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "BackButtonWrapper", "IconHolder", "img", "TextHolder", "span", "heading", "big", "small", "theme", "font", "title", "main", "bold", "TitleHolder", "ImageContainer", "ImageHolder", "DetailsWrapper", "DetailsItemHolder", "ServiceListWrapper"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  margin: 50px 0;\n`;\n\nexport const BackButtonWrapper = styled.div`\n  display: flex;\n  align-items: center;\n`;\n\nexport const IconHolder = styled.img`\n  width: 25px;\n  height: 25px;\n\n  object-fit: contain;\n\n  margin-right: 10px;\n\n  /* transform: translateX(-150%); */\n`;\n\nexport const TextHolder = styled.span`\n  font-size: ${({ heading, big, small, theme: { font } }) =>\n    heading ? font.title : small ? font.small : big ? font.big : font.main};\n  font-weight: ${({ bold }) => (bold ? 600 : 400)};\n  display: block;\n`;\n\nexport const TitleHolder = styled.div`\n  margin: 20px 0 40px;\n`;\n\nexport const ImageContainer = styled.div`\n  /* margin: 50px 0 70px; */\n\n  border-radius: 20px;\n  /* height: 600px; */\n\n  display: block;\n  margin-bottom: 50px;\n\n  /* .image-gallery-content .image-gallery-slide .image-gallery-image {\n    max-height: 100%;\n  } */\n`;\n\nexport const ImageHolder = styled.img`\n  width: 100%;\n  height: 100%;\n\n  border-radius: 20px;\n\n  object-fit: cover;\n`;\n\nexport const DetailsWrapper = styled.div`\n  margin: 20px 0 100px;\n`;\n\nexport const DetailsItemHolder = styled.div`\n  margin: 20px 0;\n`;\n\nexport const ServiceListWrapper = styled.div`\n  display: flex;\n  align-items: flex-start;\n\n  flex-wrap: wrap;\n  gap: 20px;\n\n  margin: 10px 0;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA,CAFO;AAIP,OAAO,MAAMC,iBAAiB,GAAGH,MAAM,CAACE,GAAI;AAC5C;AACA;AACA,CAHO;AAKP,OAAO,MAAME,UAAU,GAAGJ,MAAM,CAACK,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CATO;AAWP,OAAO,MAAMC,UAAU,GAAGN,MAAM,CAACO,IAAK;AACtC,eAAe;EAAA,IAAC;IAAEC,OAAF;IAAWC,GAAX;IAAgBC,KAAhB;IAAuBC,KAAK,EAAE;MAAEC;IAAF;EAA9B,CAAD;EAAA,OACXJ,OAAO,GAAGI,IAAI,CAACC,KAAR,GAAgBH,KAAK,GAAGE,IAAI,CAACF,KAAR,GAAgBD,GAAG,GAAGG,IAAI,CAACH,GAAR,GAAcG,IAAI,CAACE,IADvD;AAAA,CAC4D;AAC3E,iBAAiB;EAAA,IAAC;IAAEC;EAAF,CAAD;EAAA,OAAeA,IAAI,GAAG,GAAH,GAAS,GAA5B;AAAA,CAAiC;AAClD;AACA,CALO;AAOP,OAAO,MAAMC,WAAW,GAAGhB,MAAM,CAACE,GAAI;AACtC;AACA,CAFO;AAIP,OAAO,MAAMe,cAAc,GAAGjB,MAAM,CAACE,GAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAZO;AAcP,OAAO,MAAMgB,WAAW,GAAGlB,MAAM,CAACK,GAAI;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,CAPO;AASP,OAAO,MAAMc,cAAc,GAAGnB,MAAM,CAACE,GAAI;AACzC;AACA,CAFO;AAIP,OAAO,MAAMkB,iBAAiB,GAAGpB,MAAM,CAACE,GAAI;AAC5C;AACA,CAFO;AAIP,OAAO,MAAMmB,kBAAkB,GAAGrB,MAAM,CAACE,GAAI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CARO"}, "metadata": {}, "sourceType": "module"}