{"ast": null, "code": "var _s = $RefreshSig$();\n\nimport { useState, useEffect, useCallback } from \"react\"; // import { screenConstant } from \"./mediaQuery\";\n\nconst PageDimensions = () => {\n  _s();\n\n  const hasWindow = window && typeof window !== \"undefined\";\n  const getDimensions = useCallback(() => {\n    const width = hasWindow ? window.screen.width : null;\n    const height = hasWindow ? window.screen.height : null;\n    return {\n      width,\n      height\n    };\n  }, [hasWindow]);\n  const [dimensions, setDimensions] = useState(getDimensions());\n  useEffect(() => {\n    if (hasWindow) {\n      const handleResize = () => {\n        setDimensions(getDimensions());\n      };\n\n      window.addEventListener(\"resize\", handleResize);\n      return () => window.removeEventListener(\"resize\", handleResize);\n    }\n  }, [hasWindow, getDimensions]);\n  const {\n    width,\n    height\n  } = dimensions;\n  return {\n    width,\n    height\n  };\n};\n\n_s(PageDimensions, \"fgPnm2aN8s37TWjGUxaOUImvMRM=\");\n\n_c = PageDimensions;\nexport default PageDimensions;\n\nvar _c;\n\n$RefreshReg$(_c, \"PageDimensions\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "PageDimensions", "hasW<PERSON>ow", "window", "getDimensions", "width", "screen", "height", "dimensions", "setDimensions", "handleResize", "addEventListener", "removeEventListener"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/pageDimensions.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from \"react\";\n// import { screenConstant } from \"./mediaQuery\";\n\nconst PageDimensions = () => {\n  const hasWindow = window && typeof window !== \"undefined\";\n\n  const getDimensions = useCallback(() => {\n    const width = hasWindow ? window.screen.width : null;\n    const height = hasWindow ? window.screen.height : null;\n    return {\n      width,\n      height,\n    };\n  }, [hasWindow]);\n\n  const [dimensions, setDimensions] = useState(getDimensions());\n\n  useEffect(() => {\n    if (hasWindow) {\n      const handleResize = () => {\n        setDimensions(getDimensions());\n      };\n\n      window.addEventListener(\"resize\", handleResize);\n      return () => window.removeEventListener(\"resize\", handleResize);\n    }\n  }, [hasWindow, getDimensions]);\n\n  const { width, height } = dimensions;\n\n  return { width, height };\n};\n\nexport default PageDimensions;\n"], "mappings": ";;AAAA,SAASA,QAAT,EAAmBC,SAAnB,EAA8BC,WAA9B,QAAiD,OAAjD,C,CACA;;AAEA,MAAMC,cAAc,GAAG,MAAM;EAAA;;EAC3B,MAAMC,SAAS,GAAGC,MAAM,IAAI,OAAOA,MAAP,KAAkB,WAA9C;EAEA,MAAMC,aAAa,GAAGJ,WAAW,CAAC,MAAM;IACtC,MAAMK,KAAK,GAAGH,SAAS,GAAGC,MAAM,CAACG,MAAP,CAAcD,KAAjB,GAAyB,IAAhD;IACA,MAAME,MAAM,GAAGL,SAAS,GAAGC,MAAM,CAACG,MAAP,CAAcC,MAAjB,GAA0B,IAAlD;IACA,OAAO;MACLF,KADK;MAELE;IAFK,CAAP;EAID,CAPgC,EAO9B,CAACL,SAAD,CAP8B,CAAjC;EASA,MAAM,CAACM,UAAD,EAAaC,aAAb,IAA8BX,QAAQ,CAACM,aAAa,EAAd,CAA5C;EAEAL,SAAS,CAAC,MAAM;IACd,IAAIG,SAAJ,EAAe;MACb,MAAMQ,YAAY,GAAG,MAAM;QACzBD,aAAa,CAACL,aAAa,EAAd,CAAb;MACD,CAFD;;MAIAD,MAAM,CAACQ,gBAAP,CAAwB,QAAxB,EAAkCD,YAAlC;MACA,OAAO,MAAMP,MAAM,CAACS,mBAAP,CAA2B,QAA3B,EAAqCF,YAArC,CAAb;IACD;EACF,CATQ,EASN,CAACR,SAAD,EAAYE,aAAZ,CATM,CAAT;EAWA,MAAM;IAAEC,KAAF;IAASE;EAAT,IAAoBC,UAA1B;EAEA,OAAO;IAAEH,KAAF;IAASE;EAAT,CAAP;AACD,CA5BD;;GAAMN,c;;KAAAA,c;AA8BN,eAAeA,cAAf"}, "metadata": {}, "sourceType": "module"}