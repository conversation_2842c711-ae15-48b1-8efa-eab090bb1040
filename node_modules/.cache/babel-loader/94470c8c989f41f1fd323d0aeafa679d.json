{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/index.jsx\";\nimport React from \"react\";\nimport SeactionHeading from \"../../global/seaction-title\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ParaGraph = _ref => {\n  let {\n    title,\n    text\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    className: \"px-md-5\",\n    children: [title ? /*#__PURE__*/_jsxDEV(SeactionHeading, {\n      title: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 16\n    }, this) : null, /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n\n_c = ParaGraph;\nexport default ParaGraph;\n\nvar _c;\n\n$RefreshReg$(_c, \"ParaGraph\");", "map": {"version": 3, "names": ["React", "SeactionHeading", "Styles", "ParaGraph", "title", "text"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport SeactionHeading from \"../../global/seaction-title\";\n\nimport * as Styles from \"./styles\";\n\nconst ParaGraph = ({ title, text }) => {\n  return (\n    <Styles.Container className=\"px-md-5\">\n      {title ? <SeactionHeading title={title} /> : null}\n      <Styles.TextHolder>{text}</Styles.TextHolder>\n    </Styles.Container>\n  );\n};\n\nexport default ParaGraph;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,eAAP,MAA4B,6BAA5B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,SAAS,GAAG,QAAqB;EAAA,IAApB;IAAEC,KAAF;IAASC;EAAT,CAAoB;EACrC,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAkB,SAAS,EAAC,SAA5B;IAAA,WACGD,KAAK,gBAAG,QAAC,eAAD;MAAiB,KAAK,EAAEA;IAAxB;MAAA;MAAA;MAAA;IAAA,QAAH,GAAuC,IAD/C,eAEE,QAAC,MAAD,CAAQ,UAAR;MAAA,UAAoBC;IAApB;MAAA;MAAA;MAAA;IAAA,QAFF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAMD,CAPD;;KAAMF,S;AASN,eAAeA,SAAf"}, "metadata": {}, "sourceType": "module"}