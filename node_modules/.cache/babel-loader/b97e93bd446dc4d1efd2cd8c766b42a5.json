{"ast": null, "code": "var DESCRIPTORS = require('../internals/descriptors');\n\nvar definePropertyModule = require('../internals/object-define-property');\n\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "definePropertyModule", "createPropertyDescriptor", "module", "exports", "object", "key", "value", "f"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/create-non-enumerable-property.js"], "sourcesContent": ["var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAAD,CAAzB;;AACA,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,qCAAD,CAAlC;;AACA,IAAIE,wBAAwB,GAAGF,OAAO,CAAC,yCAAD,CAAtC;;AAEAG,MAAM,CAACC,OAAP,GAAiBL,WAAW,GAAG,UAAUM,MAAV,EAAkBC,GAAlB,EAAuBC,KAAvB,EAA8B;EAC3D,OAAON,oBAAoB,CAACO,CAArB,CAAuBH,MAAvB,EAA+BC,GAA/B,EAAoCJ,wBAAwB,CAAC,CAAD,EAAIK,KAAJ,CAA5D,CAAP;AACD,CAF2B,GAExB,UAAUF,MAAV,EAAkBC,GAAlB,EAAuBC,KAAvB,EAA8B;EAChCF,MAAM,CAACC,GAAD,CAAN,GAAcC,KAAd;EACA,OAAOF,MAAP;AACD,CALD"}, "metadata": {}, "sourceType": "script"}