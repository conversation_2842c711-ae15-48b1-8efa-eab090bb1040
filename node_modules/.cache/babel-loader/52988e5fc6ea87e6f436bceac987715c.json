{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/index.jsx\";\nimport React from 'react';\nimport ProjectCard from '../card';\nimport { ProjectData } from '../projectData';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ProjectList = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    className: \"flex-wrap flex-md-nowrap\",\n    children: ProjectData.slice(0, 4).map((item, key) => {\n      return /*#__PURE__*/_jsxDEV(ProjectCard, {\n        item: item\n      }, key, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 9\n  }, this);\n};\n\n_c = ProjectList;\nexport default ProjectList;\n\nvar _c;\n\n$RefreshReg$(_c, \"ProjectList\");", "map": {"version": 3, "names": ["React", "ProjectCard", "ProjectData", "Styles", "ProjectList", "slice", "map", "item", "key"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport ProjectCard from '../card'\n\nimport { ProjectData } from '../projectData'\n\nimport * as Styles from './styles'\n\nconst ProjectList = () => {\n    return (\n        <Styles.Container className=\"flex-wrap flex-md-nowrap\">\n            {\n                ProjectData.slice(0, 4).map((item, key) => {\n                    return (\n                        <ProjectCard\n                            key={key}\n                            item={item}\n                        />\n                    )\n                })\n            }\n        </Styles.Container>\n    )\n}\n\nexport default ProjectList"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,WAAP,MAAwB,SAAxB;AAEA,SAASC,WAAT,QAA4B,gBAA5B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,WAAW,GAAG,MAAM;EACtB,oBACI,QAAC,MAAD,CAAQ,SAAR;IAAkB,SAAS,EAAC,0BAA5B;IAAA,UAEQF,WAAW,CAACG,KAAZ,CAAkB,CAAlB,EAAqB,CAArB,EAAwBC,GAAxB,CAA4B,CAACC,IAAD,EAAOC,GAAP,KAAe;MACvC,oBACI,QAAC,WAAD;QAEI,IAAI,EAAED;MAFV,GACSC,GADT;QAAA;QAAA;QAAA;MAAA,QADJ;IAMH,CAPD;EAFR;IAAA;IAAA;IAAA;EAAA,QADJ;AAcH,CAfD;;KAAMJ,W;AAiBN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}