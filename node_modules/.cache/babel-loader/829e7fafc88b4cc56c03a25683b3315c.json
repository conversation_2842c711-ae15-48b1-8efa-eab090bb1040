{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/index.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst TandC = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    className: \"px-md-5\",\n    children: [/*#__PURE__*/_jsxDEV(Styles.TitleHolder, {\n      children: \"last updated : April 28,2024\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Styles.ListHolder, {\n      children: /*#__PURE__*/_jsxDEV(Styles.ListItem, {\n        children: \"**CapaStrength Terms and Conditions** Welcome to CapaStrength! These Terms and Conditions (\\\"Terms\\\") govern your use of the CapaStrength website (the \\\"Website\\\"), provided to you by CapaStrength (\\\"we\\\", \\\"us\\\", or \\\"our\\\"). By accessing or using the Website, you agree to be bound by these Terms. Please read them carefully before using the Website. 2. **Use of the Website:** You agree to use the Website only for lawful purposes and in accordance with these Terms. You are solely responsible for all content you post, upload, or otherwise transmit through the Website. 3. **Intellectual Property:** The Website and its original content, features, and functionality are owned by CapaStrength and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws. 4. **User Accounts:** In order to access certain features of the Website, you may be required to create an account. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer, and you agree to accept responsibility for all activities that occur under your account or password. 5. **Privacy:** Your use of the Website is also subject to our Privacy Policy. Please review our Privacy Policy, which governs the use of personal information on the Website. 6. **Links to Third-Party Websites:** The Website may contain links to third-party websites or services that are not owned or controlled by CapaStrength. CapaStrength has no control over, and assumes no responsibility for, the content, privacy policies, or practices of any third-party websites or services. You further acknowledge and agree that CapaStrength shall not be responsible or liable, directly or indirectly, for any damage or loss caused or alleged to be caused by or in connection with the use of or reliance on any such content, goods, or services available on or through any such websites or services. 7. **Disclaimer of Warranties:** The Website is provided \\\"as is\\\" and \\\"as available\\\" without warranties of any kind, either express or implied, including, but not limited to, implied warranties of merchantability, fitness for a particular purpose, and non-infringement. 8. **Limitation of Liability:** In no event shall CapaStrength, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from (i) your access to or use of or inability to access or use the Website; (ii) any conduct or content of any third party on the Website; (iii) any content obtained from the Website; and (iv) unauthorized access, use, or alteration of your transmissions or content, whether based on warranty, contract, tort (including negligence), or any other legal theory, whether or not we have been informed of the possibility of such damage, and even if a remedy set forth herein is found to have failed of its essential purpose. 9. **Indemnification:** You agree to indemnify, defend, and hold harmless CapaStrength and its affiliates, officers, directors, employees, agents, licensors, and suppliers from and against any claims, liabilities, damages, judgments, awards, losses, costs, expenses, or fees (including reasonable attorneys' fees) arising out of or relating to your violation of these Terms or your use of the Website. 10. **Governing Law:** These Terms shall be governed and construed in accordance with the laws of India, without regard to its conflict of law provisions. 11. **Entire Agreement:** These Terms constitute the entire agreement between you and CapaStrength regarding the use of the Website, superseding any prior agreements between you and CapaStrength regarding the Website. If you have any questions about these Terms, please contact us at [<EMAIL>]. Last updated: [28th April 2024]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Styles.Button, {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        children: \"Go to Main Page\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n};\n\n_c = TandC;\nexport default TandC;\n\nvar _c;\n\n$RefreshReg$(_c, \"TandC\");", "map": {"version": 3, "names": ["React", "Link", "Styles", "TandC"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport { Link } from 'react-router-dom';\n\nimport * as Styles from './styles'\n\nconst TandC = () => {\n    return (\n        <Styles.Container className=\"px-md-5\">\n            <Styles.TitleHolder>\n                last updated : April 28,2024\n            </Styles.TitleHolder>\n            <Styles.ListHolder>\n                <Styles.ListItem>\n                **CapaStrength Terms and Conditions**\n\nWelcome to CapaStrength! These Terms and Conditions (\"Terms\") govern your use of the CapaStrength website (the \"Website\"), provided to you by CapaStrength (\"we\", \"us\", or \"our\"). By accessing or using the Website, you agree to be bound by these Terms. Please read them carefully before using the Website.\n\n2. **Use of the Website:** You agree to use the Website only for lawful purposes and in accordance with these Terms. You are solely responsible for all content you post, upload, or otherwise transmit through the Website.\n\n3. **Intellectual Property:** The Website and its original content, features, and functionality are owned by CapaStrength and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.\n\n4. **User Accounts:** In order to access certain features of the Website, you may be required to create an account. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer, and you agree to accept responsibility for all activities that occur under your account or password.\n\n5. **Privacy:** Your use of the Website is also subject to our Privacy Policy. Please review our Privacy Policy, which governs the use of personal information on the Website.\n\n6. **Links to Third-Party Websites:** The Website may contain links to third-party websites or services that are not owned or controlled by CapaStrength. CapaStrength has no control over, and assumes no responsibility for, the content, privacy policies, or practices of any third-party websites or services. You further acknowledge and agree that CapaStrength shall not be responsible or liable, directly or indirectly, for any damage or loss caused or alleged to be caused by or in connection with the use of or reliance on any such content, goods, or services available on or through any such websites or services.\n\n7. **Disclaimer of Warranties:** The Website is provided \"as is\" and \"as available\" without warranties of any kind, either express or implied, including, but not limited to, implied warranties of merchantability, fitness for a particular purpose, and non-infringement.\n\n8. **Limitation of Liability:** In no event shall CapaStrength, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from (i) your access to or use of or inability to access or use the Website; (ii) any conduct or content of any third party on the Website; (iii) any content obtained from the Website; and (iv) unauthorized access, use, or alteration of your transmissions or content, whether based on warranty, contract, tort (including negligence), or any other legal theory, whether or not we have been informed of the possibility of such damage, and even if a remedy set forth herein is found to have failed of its essential purpose.\n\n9. **Indemnification:** You agree to indemnify, defend, and hold harmless CapaStrength and its affiliates, officers, directors, employees, agents, licensors, and suppliers from and against any claims, liabilities, damages, judgments, awards, losses, costs, expenses, or fees (including reasonable attorneys' fees) arising out of or relating to your violation of these Terms or your use of the Website.\n\n10. **Governing Law:** These Terms shall be governed and construed in accordance with the laws of India, without regard to its conflict of law provisions.\n\n11. **Entire Agreement:** These Terms constitute the entire agreement between you and CapaStrength regarding the use of the Website, superseding any prior agreements between you and CapaStrength regarding the Website.\n\nIf you have any questions about these Terms, please contact us at [<EMAIL>]. \n\nLast updated: [28th April 2024]\n\n</Styles.ListItem>\n            </Styles.ListHolder>\n            <Styles.Button>\n                <Link to=\"/\">\n                    Go to Main Page\n                </Link>\n            </Styles.Button>\n        </Styles.Container>\n    )\n}\n\nexport default TandC"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,IAAT,QAAqB,kBAArB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,KAAK,GAAG,MAAM;EAChB,oBACI,QAAC,MAAD,CAAQ,SAAR;IAAkB,SAAS,EAAC,SAA5B;IAAA,wBACI,QAAC,MAAD,CAAQ,WAAR;MAAA;IAAA;MAAA;MAAA;MAAA;IAAA,QADJ,eAII,QAAC,MAAD,CAAQ,UAAR;MAAA,uBACI,QAAC,MAAD,CAAQ,QAAR;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QAJJ,eAoCI,QAAC,MAAD,CAAQ,MAAR;MAAA,uBACI,QAAC,IAAD;QAAM,EAAE,EAAC,GAAT;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QApCJ;EAAA;IAAA;IAAA;IAAA;EAAA,QADJ;AA4CH,CA7CD;;KAAMA,K;AA+CN,eAAeA,KAAf"}, "metadata": {}, "sourceType": "module"}