{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/index.jsx\",\n    _s = $RefreshSig$();\n\nimport { useState, useEffect, useRef } from \"react\"; // import { sendMail } from \"../../../api\";\n\nimport emailjs from \"@emailjs/browser\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ContactForm = () => {\n  _s();\n\n  let errors = useRef([]);\n  const form = useRef();\n  const [success, setSuccess] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [inputValue, setInputValue] = useState({\n    name: \"\",\n    company: \"\",\n    email: \"\",\n    mobile: \"\"\n  });\n  const {\n    name,\n    company,\n    email,\n    mobile\n  } = inputValue;\n  const [inputErrors, setInputErrors] = useState(null);\n  const errChange = errors.current;\n  useEffect(() => {\n    setInputErrors(errors.current);\n  }, [errChange]);\n\n  const handleChange = e => {\n    const value = e.target.value;\n    const name = e.target.name;\n    setInputValue({ ...inputValue,\n      [name]: value\n    });\n    setInputErrors({ ...inputErrors,\n      [name]: \"\"\n    });\n  };\n\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    console.log({\n      inputValue\n    });\n    console.log({\n      form: form.current\n    });\n    emailjs.sendForm(\"service_ifbucgn\", \"template_gryehv4\", form.current, \"ye7UvAtvh7-vkICwy\").then(result => {\n      console.log(result);\n      setLoading(false);\n    }, error => {\n      console.log(error.text);\n      setLoading(false);\n    }); // const res = await sendMail(inputValue);\n    // if (res.success) {\n    //   setSuccess(res.data);\n    //   setInputValue({ name: \"\", company: \"\", email: \"\", mobile: \"\" });\n    //   setLoading(false);\n    //   setTimeout(() => {\n    //     setSuccess(null);\n    //   }, 6000);\n    // } else if (!res.success) {\n    //   let i = 0;\n    //   while (i < res.data.length) {\n    //     errors.current[res.data[i].param] = res.data[i].msg;\n    //     setInputErrors({\n    //       ...inputErrors,\n    //       [res.data[i].param]: res.data[i].msg,\n    //     });\n    //     i++;\n    //   }\n    //   setLoading(false);\n    // }\n  };\n\n  return /*#__PURE__*/_jsxDEV(Styles.Contaier, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        color: \"Black\",\n        font: \"-moz-initial\",\n        textAlign: \"center\"\n      },\n      children: \"Who are already onboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 1\n    }, this), /*#__PURE__*/_jsxDEV(Styles.FormHolder, {\n      ref: form,\n      onSubmit: e => handleSubmit(e),\n      children: [/*#__PURE__*/_jsxDEV(Styles.RowHolder, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.LabelHolder, {\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.InputHolder, {\n          name: \"name\",\n          type: \"text\",\n          value: name,\n          onChange: e => handleChange(e),\n          error: inputErrors === null || inputErrors === void 0 ? void 0 : inputErrors.name,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), inputErrors !== null && inputErrors !== void 0 && inputErrors.name ? /*#__PURE__*/_jsxDEV(Styles.ShowError, {\n          children: inputErrors === null || inputErrors === void 0 ? void 0 : inputErrors.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.RowHolder, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.LabelHolder, {\n          children: \"Company\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.InputHolder, {\n          name: \"company\",\n          type: \"text\",\n          value: company,\n          onChange: e => handleChange(e),\n          error: inputErrors === null || inputErrors === void 0 ? void 0 : inputErrors.company,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), inputErrors !== null && inputErrors !== void 0 && inputErrors.company ? /*#__PURE__*/_jsxDEV(Styles.ShowError, {\n          children: inputErrors === null || inputErrors === void 0 ? void 0 : inputErrors.company\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.RowHolder, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.LabelHolder, {\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.InputHolder, {\n          name: \"email\",\n          type: \"email\",\n          value: email,\n          onChange: e => handleChange(e),\n          error: inputErrors === null || inputErrors === void 0 ? void 0 : inputErrors.email,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), inputErrors !== null && inputErrors !== void 0 && inputErrors.email ? /*#__PURE__*/_jsxDEV(Styles.ShowError, {\n          children: inputErrors === null || inputErrors === void 0 ? void 0 : inputErrors.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.RowHolder, {\n        children: [/*#__PURE__*/_jsxDEV(Styles.LabelHolder, {\n          children: \"Contact Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.InputHolder, {\n          name: \"mobile\",\n          type: \"tel\",\n          value: mobile,\n          onChange: e => handleChange(e),\n          error: inputErrors === null || inputErrors === void 0 ? void 0 : inputErrors.mobile,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), inputErrors !== null && inputErrors !== void 0 && inputErrors.mobile ? /*#__PURE__*/_jsxDEV(Styles.ShowError, {\n          children: inputErrors === null || inputErrors === void 0 ? void 0 : inputErrors.mobile\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.Button, {\n        success: success === null || success === void 0 ? void 0 : success.delivered,\n        type: \"submit\",\n        disabled: success === null || success === void 0 ? void 0 : success.delivered,\n        children: loading && !(success !== null && success !== void 0 && success.delivered) ? \"Sending....\" : !loading && success !== null && success !== void 0 && success.delivered ? \"Sent Successfully\" : \"Send Message\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.ShowSuccess, {\n      children: success === null || success === void 0 ? void 0 : success.msg\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n\n_s(ContactForm, \"EwvuKFV9sGZyKPqVZ2VXgwFkTwg=\");\n\n_c = ContactForm;\nexport default ContactForm;\n\nvar _c;\n\n$RefreshReg$(_c, \"ContactForm\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "emailjs", "Styles", "ContactForm", "errors", "form", "success", "setSuccess", "loading", "setLoading", "inputValue", "setInputValue", "name", "company", "email", "mobile", "inputErrors", "setInputErrors", "err<PERSON><PERSON><PERSON>", "current", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "console", "log", "sendForm", "then", "result", "error", "text", "color", "font", "textAlign", "delivered", "msg"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/index.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from \"react\";\n\n// import { sendMail } from \"../../../api\";\nimport emailjs from \"@emailjs/browser\";\n\nimport * as Styles from \"./styles\";\n\nconst ContactForm = () => {\n  let errors = useRef([]);\n  const form = useRef();\n\n  const [success, setSuccess] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [inputValue, setInputValue] = useState({\n\n    name: \"\",\n    company: \"\",\n    email: \"\",\n    mobile: \"\",\n  });\n\n  const { name, company, email, mobile } = inputValue;\n\n  const [inputErrors, setInputErrors] = useState(null);\n\n  const errChange = errors.current;\n\n  useEffect(() => {\n    setInputErrors(errors.current);\n  }, [errChange]);\n\n  const handleChange = (e) => {\n    const value = e.target.value;\n    const name = e.target.name;\n\n    setInputValue({ ...inputValue, [name]: value });\n    setInputErrors({ ...inputErrors, [name]: \"\" });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    console.log({ inputValue });\n    console.log({ form: form.current });\n    emailjs\n      .sendForm(\n        \"service_ifbucgn\",\n        \"template_gryehv4\",\n        form.current,\n        \"ye7UvAtvh7-vkICwy\"\n      )\n      .then(\n        (result) => {\n          console.log(result);\n          setLoading(false);\n        },\n        (error) => {\n          console.log(error.text);\n          setLoading(false);\n        }\n      );\n    // const res = await sendMail(inputValue);\n    // if (res.success) {\n    //   setSuccess(res.data);\n    //   setInputValue({ name: \"\", company: \"\", email: \"\", mobile: \"\" });\n    //   setLoading(false);\n\n    //   setTimeout(() => {\n    //     setSuccess(null);\n    //   }, 6000);\n    // } else if (!res.success) {\n    //   let i = 0;\n    //   while (i < res.data.length) {\n    //     errors.current[res.data[i].param] = res.data[i].msg;\n    //     setInputErrors({\n    //       ...inputErrors,\n    //       [res.data[i].param]: res.data[i].msg,\n    //     });\n    //     i++;\n    //   }\n    //   setLoading(false);\n    // }\n  };\n\n  return (\n    <Styles.Contaier>\n\n<h2 style={{ color: \"Black\",font: \"-moz-initial\", textAlign: \"center\" }}>Who are already onboard</h2>\n      <Styles.FormHolder ref={form} onSubmit={(e) => handleSubmit(e)}>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Name</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"name\"\n            type=\"text\"\n            value={name}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.name}\n            required\n          />\n          {inputErrors?.name ? (\n            <Styles.ShowError>{inputErrors?.name}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Company</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"company\"\n            type=\"text\"\n            value={company}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.company}\n            required\n          />\n          {inputErrors?.company ? (\n            <Styles.ShowError>{inputErrors?.company}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Email</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"email\"\n            type=\"email\"\n            value={email}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.email}\n            required\n          />\n          {inputErrors?.email ? (\n            <Styles.ShowError>{inputErrors?.email}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Contact Details</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"mobile\"\n            type=\"tel\"\n            value={mobile}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.mobile}\n            required\n          />\n          {inputErrors?.mobile ? (\n            <Styles.ShowError>{inputErrors?.mobile}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.Button\n          success={success?.delivered}\n          type=\"submit\"\n          disabled={success?.delivered}\n        >\n          {loading && !success?.delivered\n            ? \"Sending....\"\n            : !loading && success?.delivered\n            ? \"Sent Successfully\"\n            : \"Send Message\"}\n        </Styles.Button>\n      </Styles.FormHolder>\n      <Styles.ShowSuccess>{success?.msg}</Styles.ShowSuccess>\n    </Styles.Contaier>\n  );\n};\n\nexport default ContactForm;\n"], "mappings": ";;;AAAA,SAASA,QAAT,EAAmBC,SAAnB,EAA8BC,MAA9B,QAA4C,OAA5C,C,CAEA;;AACA,OAAOC,OAAP,MAAoB,kBAApB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,WAAW,GAAG,MAAM;EAAA;;EACxB,IAAIC,MAAM,GAAGJ,MAAM,CAAC,EAAD,CAAnB;EACA,MAAMK,IAAI,GAAGL,MAAM,EAAnB;EAEA,MAAM,CAACM,OAAD,EAAUC,UAAV,IAAwBT,QAAQ,CAAC,IAAD,CAAtC;EACA,MAAM,CAACU,OAAD,EAAUC,UAAV,IAAwBX,QAAQ,CAAC,KAAD,CAAtC;EACA,MAAM,CAACY,UAAD,EAAaC,aAAb,IAA8Bb,QAAQ,CAAC;IAE3Cc,IAAI,EAAE,EAFqC;IAG3CC,OAAO,EAAE,EAHkC;IAI3CC,KAAK,EAAE,EAJoC;IAK3CC,MAAM,EAAE;EALmC,CAAD,CAA5C;EAQA,MAAM;IAAEH,IAAF;IAAQC,OAAR;IAAiBC,KAAjB;IAAwBC;EAAxB,IAAmCL,UAAzC;EAEA,MAAM,CAACM,WAAD,EAAcC,cAAd,IAAgCnB,QAAQ,CAAC,IAAD,CAA9C;EAEA,MAAMoB,SAAS,GAAGd,MAAM,CAACe,OAAzB;EAEApB,SAAS,CAAC,MAAM;IACdkB,cAAc,CAACb,MAAM,CAACe,OAAR,CAAd;EACD,CAFQ,EAEN,CAACD,SAAD,CAFM,CAAT;;EAIA,MAAME,YAAY,GAAIC,CAAD,IAAO;IAC1B,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAF,CAASD,KAAvB;IACA,MAAMV,IAAI,GAAGS,CAAC,CAACE,MAAF,CAASX,IAAtB;IAEAD,aAAa,CAAC,EAAE,GAAGD,UAAL;MAAiB,CAACE,IAAD,GAAQU;IAAzB,CAAD,CAAb;IACAL,cAAc,CAAC,EAAE,GAAGD,WAAL;MAAkB,CAACJ,IAAD,GAAQ;IAA1B,CAAD,CAAd;EACD,CAND;;EAQA,MAAMY,YAAY,GAAG,MAAOH,CAAP,IAAa;IAChCA,CAAC,CAACI,cAAF;IACAhB,UAAU,CAAC,IAAD,CAAV;IACAiB,OAAO,CAACC,GAAR,CAAY;MAAEjB;IAAF,CAAZ;IACAgB,OAAO,CAACC,GAAR,CAAY;MAAEtB,IAAI,EAAEA,IAAI,CAACc;IAAb,CAAZ;IACAlB,OAAO,CACJ2B,QADH,CAEI,iBAFJ,EAGI,kBAHJ,EAIIvB,IAAI,CAACc,OAJT,EAKI,mBALJ,EAOGU,IAPH,CAQKC,MAAD,IAAY;MACVJ,OAAO,CAACC,GAAR,CAAYG,MAAZ;MACArB,UAAU,CAAC,KAAD,CAAV;IACD,CAXL,EAYKsB,KAAD,IAAW;MACTL,OAAO,CAACC,GAAR,CAAYI,KAAK,CAACC,IAAlB;MACAvB,UAAU,CAAC,KAAD,CAAV;IACD,CAfL,EALgC,CAsBhC;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD,CA3CD;;EA6CA,oBACE,QAAC,MAAD,CAAQ,QAAR;IAAA,wBAEJ;MAAI,KAAK,EAAE;QAAEwB,KAAK,EAAE,OAAT;QAAiBC,IAAI,EAAE,cAAvB;QAAuCC,SAAS,EAAE;MAAlD,CAAX;MAAA;IAAA;MAAA;MAAA;MAAA;IAAA,QAFI,eAGE,QAAC,MAAD,CAAQ,UAAR;MAAmB,GAAG,EAAE9B,IAAxB;MAA8B,QAAQ,EAAGgB,CAAD,IAAOG,YAAY,CAACH,CAAD,CAA3D;MAAA,wBACE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,MAAD,CAAQ,WAAR;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,WAAR;UACE,IAAI,EAAC,MADP;UAEE,IAAI,EAAC,MAFP;UAGE,KAAK,EAAET,IAHT;UAIE,QAAQ,EAAGS,CAAD,IAAOD,YAAY,CAACC,CAAD,CAJ/B;UAKE,KAAK,EAAEL,WAAF,aAAEA,WAAF,uBAAEA,WAAW,CAAEJ,IALtB;UAME,QAAQ;QANV;UAAA;UAAA;UAAA;QAAA,QAFF,EAUGI,WAAW,SAAX,IAAAA,WAAW,WAAX,IAAAA,WAAW,CAAEJ,IAAb,gBACC,QAAC,MAAD,CAAQ,SAAR;UAAA,UAAmBI,WAAnB,aAAmBA,WAAnB,uBAAmBA,WAAW,CAAEJ;QAAhC;UAAA;UAAA;UAAA;QAAA,QADD,GAEG,IAZN;MAAA;QAAA;QAAA;QAAA;MAAA,QADF,eAeE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,MAAD,CAAQ,WAAR;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,WAAR;UACE,IAAI,EAAC,SADP;UAEE,IAAI,EAAC,MAFP;UAGE,KAAK,EAAEC,OAHT;UAIE,QAAQ,EAAGQ,CAAD,IAAOD,YAAY,CAACC,CAAD,CAJ/B;UAKE,KAAK,EAAEL,WAAF,aAAEA,WAAF,uBAAEA,WAAW,CAAEH,OALtB;UAME,QAAQ;QANV;UAAA;UAAA;UAAA;QAAA,QAFF,EAUGG,WAAW,SAAX,IAAAA,WAAW,WAAX,IAAAA,WAAW,CAAEH,OAAb,gBACC,QAAC,MAAD,CAAQ,SAAR;UAAA,UAAmBG,WAAnB,aAAmBA,WAAnB,uBAAmBA,WAAW,CAAEH;QAAhC;UAAA;UAAA;UAAA;QAAA,QADD,GAEG,IAZN;MAAA;QAAA;QAAA;QAAA;MAAA,QAfF,eA6BE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,MAAD,CAAQ,WAAR;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,WAAR;UACE,IAAI,EAAC,OADP;UAEE,IAAI,EAAC,OAFP;UAGE,KAAK,EAAEC,KAHT;UAIE,QAAQ,EAAGO,CAAD,IAAOD,YAAY,CAACC,CAAD,CAJ/B;UAKE,KAAK,EAAEL,WAAF,aAAEA,WAAF,uBAAEA,WAAW,CAAEF,KALtB;UAME,QAAQ;QANV;UAAA;UAAA;UAAA;QAAA,QAFF,EAUGE,WAAW,SAAX,IAAAA,WAAW,WAAX,IAAAA,WAAW,CAAEF,KAAb,gBACC,QAAC,MAAD,CAAQ,SAAR;UAAA,UAAmBE,WAAnB,aAAmBA,WAAnB,uBAAmBA,WAAW,CAAEF;QAAhC;UAAA;UAAA;UAAA;QAAA,QADD,GAEG,IAZN;MAAA;QAAA;QAAA;QAAA;MAAA,QA7BF,eA2CE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,MAAD,CAAQ,WAAR;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,WAAR;UACE,IAAI,EAAC,QADP;UAEE,IAAI,EAAC,KAFP;UAGE,KAAK,EAAEC,MAHT;UAIE,QAAQ,EAAGM,CAAD,IAAOD,YAAY,CAACC,CAAD,CAJ/B;UAKE,KAAK,EAAEL,WAAF,aAAEA,WAAF,uBAAEA,WAAW,CAAED,MALtB;UAME,QAAQ;QANV;UAAA;UAAA;UAAA;QAAA,QAFF,EAUGC,WAAW,SAAX,IAAAA,WAAW,WAAX,IAAAA,WAAW,CAAED,MAAb,gBACC,QAAC,MAAD,CAAQ,SAAR;UAAA,UAAmBC,WAAnB,aAAmBA,WAAnB,uBAAmBA,WAAW,CAAED;QAAhC;UAAA;UAAA;UAAA;QAAA,QADD,GAEG,IAZN;MAAA;QAAA;QAAA;QAAA;MAAA,QA3CF,eAyDE,QAAC,MAAD,CAAQ,MAAR;QACE,OAAO,EAAET,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAE8B,SADpB;QAEE,IAAI,EAAC,QAFP;QAGE,QAAQ,EAAE9B,OAAF,aAAEA,OAAF,uBAAEA,OAAO,CAAE8B,SAHrB;QAAA,UAKG5B,OAAO,IAAI,EAACF,OAAD,aAACA,OAAD,eAACA,OAAO,CAAE8B,SAAV,CAAX,GACG,aADH,GAEG,CAAC5B,OAAD,IAAYF,OAAZ,aAAYA,OAAZ,eAAYA,OAAO,CAAE8B,SAArB,GACA,mBADA,GAEA;MATN;QAAA;QAAA;QAAA;MAAA,QAzDF;IAAA;MAAA;MAAA;MAAA;IAAA,QAHF,eAwEE,QAAC,MAAD,CAAQ,WAAR;MAAA,UAAqB9B,OAArB,aAAqBA,OAArB,uBAAqBA,OAAO,CAAE+B;IAA9B;MAAA;MAAA;MAAA;IAAA,QAxEF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AA4ED,CAzJD;;GAAMlC,W;;KAAAA,W;AA2JN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}