{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar clones_1 = require(\"./clones\"),\n    common_1 = require(\"./common\");\n\nfunction getLookupTableForNextSlides(numberOfDotsToShow, state, props, childrenArr) {\n  var table = {},\n      slidesToSlide = common_1.getSlidesToSlide(state, props);\n  return Array(numberOfDotsToShow).fill(0).forEach(function (_, i) {\n    var nextSlide = clones_1.getOriginalCounterPart(i, state, childrenArr);\n    if (0 === i) table[0] = nextSlide;else {\n      var now = table[i - 1] + slidesToSlide;\n      table[i] = now;\n    }\n  }), table;\n}\n\nexports.getLookupTableForNextSlides = getLookupTableForNextSlides;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "clones_1", "require", "common_1", "getLookupTableForNextSlides", "numberOfDotsToShow", "state", "props", "childrenArr", "table", "slidesToSlide", "getSlidesToSlide", "Array", "fill", "for<PERSON>ach", "_", "i", "nextSlide", "getOriginalCounterPart", "now"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/dots.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var clones_1=require(\"./clones\"),common_1=require(\"./common\");function getLookupTableForNextSlides(numberOfDotsToShow,state,props,childrenArr){var table={},slidesToSlide=common_1.getSlidesToSlide(state,props);return Array(numberOfDotsToShow).fill(0).forEach(function(_,i){var nextSlide=clones_1.getOriginalCounterPart(i,state,childrenArr);if(0===i)table[0]=nextSlide;else{var now=table[i-1]+slidesToSlide;table[i]=now}}),table}exports.getLookupTableForNextSlides=getLookupTableForNextSlides;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,QAAQ,GAACC,OAAO,CAAC,UAAD,CAApB;AAAA,IAAiCC,QAAQ,GAACD,OAAO,CAAC,UAAD,CAAjD;;AAA8D,SAASE,2BAAT,CAAqCC,kBAArC,EAAwDC,KAAxD,EAA8DC,KAA9D,EAAoEC,WAApE,EAAgF;EAAC,IAAIC,KAAK,GAAC,EAAV;EAAA,IAAaC,aAAa,GAACP,QAAQ,CAACQ,gBAAT,CAA0BL,KAA1B,EAAgCC,KAAhC,CAA3B;EAAkE,OAAOK,KAAK,CAACP,kBAAD,CAAL,CAA0BQ,IAA1B,CAA+B,CAA/B,EAAkCC,OAAlC,CAA0C,UAASC,CAAT,EAAWC,CAAX,EAAa;IAAC,IAAIC,SAAS,GAAChB,QAAQ,CAACiB,sBAAT,CAAgCF,CAAhC,EAAkCV,KAAlC,EAAwCE,WAAxC,CAAd;IAAmE,IAAG,MAAIQ,CAAP,EAASP,KAAK,CAAC,CAAD,CAAL,GAASQ,SAAT,CAAT,KAAgC;MAAC,IAAIE,GAAG,GAACV,KAAK,CAACO,CAAC,GAAC,CAAH,CAAL,GAAWN,aAAnB;MAAiCD,KAAK,CAACO,CAAD,CAAL,GAASG,GAAT;IAAa;EAAC,CAA3M,GAA6MV,KAApN;AAA0N;;AAAAV,OAAO,CAACK,2BAAR,GAAoCA,2BAApC"}, "metadata": {}, "sourceType": "script"}