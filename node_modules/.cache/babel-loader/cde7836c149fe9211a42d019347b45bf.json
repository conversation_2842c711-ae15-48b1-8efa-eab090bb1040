{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/index.jsx\",\n    _s = $RefreshSig$();\n\nimport React, { useState } from \"react\";\nimport Seaction from \"../../components/global/seaction\";\nimport CardList from \"../../components/about/card/list\";\nimport SeactionHeading from \"../../components/global/seaction-title\";\nimport ParaGraph from \"../../components/about/paragraph\";\nimport { solutionProcidersData, servicesData } from \"./data\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst About = () => {\n  _s();\n\n  const [isHovered, setIsHovered] = useState(false);\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: [/*#__PURE__*/_jsxDEV(Seaction, {\n      children: /*#__PURE__*/_jsxDEV(CardList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.Wrapper, {\n      children: [/*#__PURE__*/_jsxDEV(Styles.AboutCard, {\n        children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n          title: \"Vision\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.ParagraphWrapper, {\n          className: \"paragraph-holder\",\n          children: /*#__PURE__*/_jsxDEV(ParaGraph, {\n            text: \"To be a leader in providing quality civil engineering solutions by integrating state of the art  techniques by using eficient human resources by specialized training and quality material. To be a leader in providing quality civil engineering solutions by integrating state of the art  techniques by using eficient human resources by specialized training and quality material.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.AboutCard, {\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        hovered: isHovered,\n        children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n          title: \"About Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.ParagraphWrapper, {\n          className: \"paragraph-holder\",\n          children: /*#__PURE__*/_jsxDEV(ParaGraph, {\n            text: \"CapaStrength is one of India's leading providers of Retrotting/Structural Strengthening  services for structures. The company delivers engineering solutions with a focus on  technical quality and eficiency while ensuring on-time completion of projects. We undertake a variety of projects for a wide range of cliental from small residential  societies to large commercial projects. It includes projects that come under diverse  Categories such as high-rise Residential Buildings, Commercial Buildings, Cement  Chemical Plants, Bridges, Power Plants, Jetties, Five Star Hotels, Temples, Heritage  structures etc. We have been focused on Rehabilitation of damaged structures i.e.  retroctting and structural strengthening works. Our unique and yexible project  management systems ensure that a positive outcome is achieved regardless of size or  nature of the project.  Our philosophy of 'No Compromise' has come a long way implanting our strong  commitment to highest standards of excellence and ethics. It motivates innovation  and people development, which in turn lead to superior quality and services. It has  also resulted in rehabilitating projects at par with top standards and maximum customer satisfaction.Our strong growth has been as a result of consistently delivering quality works both on  time, on budget and high level of safety. Over the years, we have developed and implemented anintegrated management  system that ensures our work, health and safety, quality and environmental  obligations which are not only met but also continue to be monitored and improved  upon.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.AboutCard, {\n        children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n          title: \"Founder Message\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.ParagraphWrapper, {\n          className: \"paragraph-holder\",\n          children: /*#__PURE__*/_jsxDEV(ParaGraph, {\n            text: \"After providing service more than 10 years as \\u201CRESCON\\u201D now we reformed as a   \\u201CCAPASTRENGTH\\u201D. To undertake Retrofitting, Structural Strengthening of civil structures. Since then, numerous projects have been successfully undertaken and completed. We shall contribute towards creating sustainable solutions to Better, Safer and Healthier life for people. . . We constantly focus on the development of all our employees through training to make them capable enough to deliver their best while converti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.AboutCard, {\n        children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n          title: \"Managemnet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.ParagraphWrapper, {\n          className: \"paragraph-holder\",\n          children: /*#__PURE__*/_jsxDEV(ParaGraph, {\n            text: \"We have in-depth knowledge of Structural Design, FRP Design, Civil construction, which includes Retrofitting, Structural Strengthening, Environmental coatings and rehabilitation of old/damaged structures waterproofing. This has allowed to lead Capastrength from the front. Capastrength has undertaken several successful projects for eminent entities such as Johnson and Johnson, Bayer, provinces Land Hotels, MRF, Embassy Siyaram, 24x7 Services. Capastrength continues to grow and flourish under gui\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.AboutCard, {\n        children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n          title: \"Service Provider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.ParagraphWrapper, {\n          className: \"paragraph-holder\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"0 30px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: solutionProcidersData === null || solutionProcidersData === void 0 ? void 0 : solutionProcidersData.map((item, index) => {\n                return /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: item\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 26\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.AboutCard, {\n        children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n          title: \"Our Service\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.ParagraphWrapper, {\n          className: \"paragraph-holder\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"0 30px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: servicesData === null || servicesData === void 0 ? void 0 : servicesData.map((item, index) => {\n                return /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: item\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 26\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n\n_s(About, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n\n_c = About;\nexport default About;\n\nvar _c;\n\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useState", "Seaction", "CardList", "SeactionHeading", "ParaGraph", "solutionProcidersData", "servicesData", "Styles", "About", "isHovered", "setIsHovered", "padding", "map", "item", "index"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/index.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\n\nimport Seaction from \"../../components/global/seaction\";\nimport CardList from \"../../components/about/card/list\";\n\nimport SeactionHeading from \"../../components/global/seaction-title\";\n\nimport ParaGraph from \"../../components/about/paragraph\";\nimport { solutionProcidersData, servicesData } from \"./data\";\n\nimport * as Styles from \"./styles\";\n\nconst About = () => {\n  const [isHovered, setIsHovered] = useState(false);\n  \n\n  return (\n    <Styles.Container>\n      <Seaction>\n        <CardList />\n      </Seaction>\n      <Styles.Wrapper>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Vision\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <ParaGraph\n              text=\"To be a leader in providing quality civil engineering solutions by integrating state of the art \n                    techniques by using eficient human resources by specialized training and quality material. To be a leader in providing quality civil engineering solutions by integrating state of the art \n                    techniques by using eficient human resources by specialized training and quality material.\"\n            />\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          hovered={isHovered}\n        >\n          <SeactionHeading title=\"About Us\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <ParaGraph\n              text=\"CapaStrength is one of India's leading providers of Retrotting/Structural Strengthening \n                    services for structures. The company delivers engineering solutions with a focus on \n                    technical quality and eficiency while ensuring on-time completion of projects.\n                    We undertake a variety of projects for a wide range of cliental from small residential \n                    societies to large commercial projects. It includes projects that come under diverse \n                    Categories such as high-rise Residential Buildings, Commercial Buildings, Cement \n                    Chemical Plants, Bridges, Power Plants, Jetties, Five Star Hotels, Temples, Heritage \n                    structures etc. We have been focused on Rehabilitation of damaged structures i.e. \n                    retroctting and structural strengthening works. Our unique and yexible project \n                    management systems ensure that a positive outcome is achieved regardless of size or \n                    nature of the project. \n                    Our philosophy of 'No Compromise' has come a long way implanting our strong \n                    commitment to highest standards of excellence and ethics. It motivates innovation \n                    and people development, which in turn lead to superior quality and services. It has \n                    also resulted in rehabilitating projects at par with top standards and maximum customer satisfaction.Our strong growth has been as a result of consistently delivering quality works both on \n                    time, on budget and high level of safety. Over the years, we have developed and implemented anintegrated management \n                    system that ensures our work, health and safety, quality and environmental \n                    obligations which are not only met but also continue to be monitored and improved \n                    upon.\"\n            />\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Founder Message\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <ParaGraph\n              text=\"After providing service more than 10 years as “RESCON” now we reformed as a   “CAPASTRENGTH”. To undertake Retrofitting, Structural Strengthening of civil structures. Since then, numerous projects have been successfully undertaken and completed.\n                We shall contribute towards creating sustainable solutions to Better, Safer and Healthier life for people. . .\n                We constantly focus on the development of all our employees through training to make them capable enough to deliver their best while converti\"\n            />\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Managemnet\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <ParaGraph\n              text=\"We have in-depth knowledge of Structural Design, FRP Design, Civil construction, which includes Retrofitting, Structural Strengthening, Environmental coatings and rehabilitation of old/damaged structures waterproofing. This has allowed to lead Capastrength from the front.\n                Capastrength has undertaken several successful projects for eminent entities such as Johnson and Johnson, Bayer, provinces Land Hotels, MRF, Embassy Siyaram, 24x7 Services. Capastrength continues to grow and flourish under gui\"\n            />\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Service Provider\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <div style={{ padding: \"0 30px\" }}>\n              <ul>\n                {solutionProcidersData?.map((item, index) => {\n                  return <li key={index}>{item}</li>;\n                })}\n              </ul>\n            </div>\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n        <Styles.AboutCard>\n          <SeactionHeading title=\"Our Service\" />\n          <Styles.ParagraphWrapper className=\"paragraph-holder\">\n            <div style={{ padding: \"0 30px\" }}>\n              <ul>\n                {servicesData?.map((item, index) => {\n                  return <li key={index}>{item}</li>;\n                })}\n              </ul>\n            </div>\n          </Styles.ParagraphWrapper>\n        </Styles.AboutCard>\n      </Styles.Wrapper>\n    </Styles.Container>\n  );\n};\n\nexport default About;\n"], "mappings": ";;;AAAA,OAAOA,KAAP,IAAgBC,QAAhB,QAAgC,OAAhC;AAEA,OAAOC,QAAP,MAAqB,kCAArB;AACA,OAAOC,QAAP,MAAqB,kCAArB;AAEA,OAAOC,eAAP,MAA4B,wCAA5B;AAEA,OAAOC,SAAP,MAAsB,kCAAtB;AACA,SAASC,qBAAT,EAAgCC,YAAhC,QAAoD,QAApD;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,KAAK,GAAG,MAAM;EAAA;;EAClB,MAAM,CAACC,SAAD,EAAYC,YAAZ,IAA4BV,QAAQ,CAAC,KAAD,CAA1C;EAGA,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,wBACE,QAAC,QAAD;MAAA,uBACE,QAAC,QAAD;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QADF,eAIE,QAAC,MAAD,CAAQ,OAAR;MAAA,wBACE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,eAAD;UAAiB,KAAK,EAAC;QAAvB;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,gBAAR;UAAyB,SAAS,EAAC,kBAAnC;UAAA,uBACE,QAAC,SAAD;YACE,IAAI,EAAC;UADP;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAFF;MAAA;QAAA;QAAA;QAAA;MAAA,QADF,eAWE,QAAC,MAAD,CAAQ,SAAR;QACE,YAAY,EAAE,MAAMU,YAAY,CAAC,IAAD,CADlC;QAEE,YAAY,EAAE,MAAMA,YAAY,CAAC,KAAD,CAFlC;QAGE,OAAO,EAAED,SAHX;QAAA,wBAKE,QAAC,eAAD;UAAiB,KAAK,EAAC;QAAvB;UAAA;UAAA;UAAA;QAAA,QALF,eAME,QAAC,MAAD,CAAQ,gBAAR;UAAyB,SAAS,EAAC,kBAAnC;UAAA,uBACE,QAAC,SAAD;YACE,IAAI,EAAC;UADP;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QANF;MAAA;QAAA;QAAA;QAAA;MAAA,QAXF,eAyCE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,eAAD;UAAiB,KAAK,EAAC;QAAvB;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,gBAAR;UAAyB,SAAS,EAAC,kBAAnC;UAAA,uBACE,QAAC,SAAD;YACE,IAAI,EAAC;UADP;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAFF;MAAA;QAAA;QAAA;QAAA;MAAA,QAzCF,eAmDE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,eAAD;UAAiB,KAAK,EAAC;QAAvB;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,gBAAR;UAAyB,SAAS,EAAC,kBAAnC;UAAA,uBACE,QAAC,SAAD;YACE,IAAI,EAAC;UADP;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAFF;MAAA;QAAA;QAAA;QAAA;MAAA,QAnDF,eA4DE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,eAAD;UAAiB,KAAK,EAAC;QAAvB;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,gBAAR;UAAyB,SAAS,EAAC,kBAAnC;UAAA,uBACE;YAAK,KAAK,EAAE;cAAEE,OAAO,EAAE;YAAX,CAAZ;YAAA,uBACE;cAAA,UACGN,qBADH,aACGA,qBADH,uBACGA,qBAAqB,CAAEO,GAAvB,CAA2B,CAACC,IAAD,EAAOC,KAAP,KAAiB;gBAC3C,oBAAO;kBAAA,UAAiBD;gBAAjB,GAASC,KAAT;kBAAA;kBAAA;kBAAA;gBAAA,QAAP;cACD,CAFA;YADH;cAAA;cAAA;cAAA;YAAA;UADF;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAFF;MAAA;QAAA;QAAA;QAAA;MAAA,QA5DF,eAwEE,QAAC,MAAD,CAAQ,SAAR;QAAA,wBACE,QAAC,eAAD;UAAiB,KAAK,EAAC;QAAvB;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,gBAAR;UAAyB,SAAS,EAAC,kBAAnC;UAAA,uBACE;YAAK,KAAK,EAAE;cAAEH,OAAO,EAAE;YAAX,CAAZ;YAAA,uBACE;cAAA,UACGL,YADH,aACGA,YADH,uBACGA,YAAY,CAAEM,GAAd,CAAkB,CAACC,IAAD,EAAOC,KAAP,KAAiB;gBAClC,oBAAO;kBAAA,UAAiBD;gBAAjB,GAASC,KAAT;kBAAA;kBAAA;kBAAA;gBAAA,QAAP;cACD,CAFA;YADH;cAAA;cAAA;cAAA;YAAA;UADF;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAFF;MAAA;QAAA;QAAA;QAAA;MAAA,QAxEF;IAAA;MAAA;MAAA;MAAA;IAAA,QAJF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AA4FD,CAhGD;;GAAMN,K;;KAAAA,K;AAkGN,eAAeA,KAAf"}, "metadata": {}, "sourceType": "module"}