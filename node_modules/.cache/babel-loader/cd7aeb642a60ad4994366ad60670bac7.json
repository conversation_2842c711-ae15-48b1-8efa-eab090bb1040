{"ast": null, "code": "import styled from 'styled-components';\nexport const Container = styled.div`\n    display: flex;\n    align-items: center;\n\n    gap: 20px;\n`;\nexport const ImageHolder = styled.img`\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n`;\nexport const TitleWrapper = styled.div`\n    margin-bottom: 30px;\n`;\nexport const TitleHolder = styled.div`\n    font-size: 25px;\n    font-weight: bold;\n`;\nexport const TextHolder = styled.p`\n    font-size: ${_ref => {\n  let {\n    theme: {\n      font\n    }\n  } = _ref;\n  return font.main;\n}};\n`;\nexport const Button = styled.button`\n    background-color: ${_ref2 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref2;\n  return colors.main.yellow;\n}};\n    padding: 5px 10px;\n    border-radius: 5px;\n    border: none;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "ImageHolder", "img", "TitleWrapper", "TitleHolder", "TextHolder", "p", "theme", "font", "main", "<PERSON><PERSON>", "button", "colors", "yellow"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Container = styled.div`\n    display: flex;\n    align-items: center;\n\n    gap: 20px;\n`;\n\nexport const ImageHolder = styled.img`\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n`;\n\nexport const TitleWrapper = styled.div`\n    margin-bottom: 30px;\n`;\n\nexport const TitleHolder = styled.div`\n    font-size: 25px;\n    font-weight: bold;\n`;\n\nexport const TextHolder = styled.p`\n    font-size: ${({ theme: { font } }) => font.main};\n`;\n\nexport const Button = styled.button`\n    background-color: ${({ theme: { colors } }) => colors.main.yellow};\n    padding: 5px 10px;\n    border-radius: 5px;\n    border: none;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA,CALO;AAOP,OAAO,MAAMC,WAAW,GAAGH,MAAM,CAACI,GAAI;AACtC;AACA;AACA;AACA,CAJO;AAMP,OAAO,MAAMC,YAAY,GAAGL,MAAM,CAACE,GAAI;AACvC;AACA,CAFO;AAIP,OAAO,MAAMI,WAAW,GAAGN,MAAM,CAACE,GAAI;AACtC;AACA;AACA,CAHO;AAKP,OAAO,MAAMK,UAAU,GAAGP,MAAM,CAACQ,CAAE;AACnC,iBAAiB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAAyBA,IAAI,CAACC,IAA9B;AAAA,CAAmC;AACpD,CAFO;AAIP,OAAO,MAAMC,MAAM,GAAGZ,MAAM,CAACa,MAAO;AACpC,wBAAwB;EAAA,IAAC;IAAEJ,KAAK,EAAE;MAAEK;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACH,IAAP,CAAYI,MAAvC;AAAA,CAA8C;AACtE;AACA;AACA;AACA,CALO"}, "metadata": {}, "sourceType": "module"}