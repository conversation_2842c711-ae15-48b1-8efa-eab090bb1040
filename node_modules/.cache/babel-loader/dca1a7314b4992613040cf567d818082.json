{"ast": null, "code": "\"use strict\";\n\nfunction getOriginalCounterPart(index, _a, childrenArr) {\n  var slidesToShow = _a.slidesToShow,\n    currentSlide = _a.currentSlide;\n  return childrenArr.length > 2 * slidesToShow ? index + 2 * slidesToShow : currentSlide >= childrenArr.length ? childrenArr.length + index : index;\n}\nfunction getOriginalIndexLookupTableByClones(slidesToShow, childrenArr) {\n  if (childrenArr.length > 2 * slidesToShow) {\n    for (var table = {}, firstBeginningOfClones = childrenArr.length - 2 * slidesToShow, firstEndOfClones = childrenArr.length - firstBeginningOfClones, firstCount = firstBeginningOfClones, i = 0; i < firstEndOfClones; i++) table[i] = firstCount, firstCount++;\n    var secondBeginningOfClones = childrenArr.length + firstEndOfClones,\n      secondEndOfClones = secondBeginningOfClones + childrenArr.slice(0, 2 * slidesToShow).length,\n      secondCount = 0;\n    for (i = secondBeginningOfClones; i <= secondEndOfClones; i++) table[i] = secondCount, secondCount++;\n    var originalEnd = secondBeginningOfClones,\n      originalCounter = 0;\n    for (i = firstEndOfClones; i < originalEnd; i++) table[i] = originalCounter, originalCounter++;\n    return table;\n  }\n  table = {};\n  var totalSlides = 3 * childrenArr.length,\n    count = 0;\n  for (i = 0; i < totalSlides; i++) table[i] = count, ++count === childrenArr.length && (count = 0);\n  return table;\n}\nfunction getClones(slidesToShow, childrenArr) {\n  return childrenArr.length < slidesToShow ? childrenArr : childrenArr.length > 2 * slidesToShow ? childrenArr.slice(childrenArr.length - 2 * slidesToShow, childrenArr.length).concat(childrenArr, childrenArr.slice(0, 2 * slidesToShow)) : childrenArr.concat(childrenArr, childrenArr);\n}\nfunction getInitialSlideInInfiniteMode(slidesToShow, childrenArr) {\n  return childrenArr.length > 2 * slidesToShow ? 2 * slidesToShow : childrenArr.length;\n}\nfunction checkClonesPosition(_a, childrenArr, props) {\n  var isReachingTheEnd,\n    currentSlide = _a.currentSlide,\n    slidesToShow = _a.slidesToShow,\n    itemWidth = _a.itemWidth,\n    totalItems = _a.totalItems,\n    nextSlide = 0,\n    nextPosition = 0,\n    isReachingTheStart = 0 === currentSlide,\n    originalFirstSlide = childrenArr.length - (childrenArr.length - 2 * slidesToShow);\n  return childrenArr.length < slidesToShow ? (nextPosition = nextSlide = 0, isReachingTheStart = isReachingTheEnd = !1) : childrenArr.length > 2 * slidesToShow ? ((isReachingTheEnd = currentSlide >= originalFirstSlide + childrenArr.length) && (nextPosition = -itemWidth * (nextSlide = currentSlide - childrenArr.length)), isReachingTheStart && (nextPosition = -itemWidth * (nextSlide = originalFirstSlide + (childrenArr.length - 2 * slidesToShow)))) : ((isReachingTheEnd = currentSlide >= 2 * childrenArr.length) && (nextPosition = -itemWidth * (nextSlide = currentSlide - childrenArr.length)), isReachingTheStart && (nextPosition = props.showDots ? -itemWidth * (nextSlide = childrenArr.length) : -itemWidth * (nextSlide = totalItems / 3))), {\n    isReachingTheEnd: isReachingTheEnd,\n    isReachingTheStart: isReachingTheStart,\n    nextSlide: nextSlide,\n    nextPosition: nextPosition\n  };\n}\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n}), exports.getOriginalCounterPart = getOriginalCounterPart, exports.getOriginalIndexLookupTableByClones = getOriginalIndexLookupTableByClones, exports.getClones = getClones, exports.getInitialSlideInInfiniteMode = getInitialSlideInInfiniteMode, exports.checkClonesPosition = checkClonesPosition;", "map": {"version": 3, "names": ["getOriginalCounterPart", "index", "_a", "childrenArr", "slidesToShow", "currentSlide", "length", "getOriginalIndexLookupTableByClones", "table", "firstBeginningOfClones", "firstEndOfClones", "firstCount", "i", "secondBeginningOfClones", "secondEndOfClones", "slice", "secondCount", "originalEnd", "originalCounter", "totalSlides", "count", "getClones", "concat", "getInitialSlideInInfiniteMode", "checkClonesPosition", "props", "isReachingTheEnd", "itemWidth", "totalItems", "nextSlide", "nextPosition", "isReachingTheStart", "originalFirstSlide", "showDots", "Object", "defineProperty", "exports", "value"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/clones.js"], "sourcesContent": ["\"use strict\";function getOriginalCounterPart(index,_a,childrenArr){var slidesToShow=_a.slidesToShow,currentSlide=_a.currentSlide;return childrenArr.length>2*slidesToShow?index+2*slidesToShow:currentSlide>=childrenArr.length?childrenArr.length+index:index}function getOriginalIndexLookupTableByClones(slidesToShow,childrenArr){if(childrenArr.length>2*slidesToShow){for(var table={},firstBeginningOfClones=childrenArr.length-2*slidesToShow,firstEndOfClones=childrenArr.length-firstBeginningOfClones,firstCount=firstBeginningOfClones,i=0;i<firstEndOfClones;i++)table[i]=firstCount,firstCount++;var secondBeginningOfClones=childrenArr.length+firstEndOfClones,secondEndOfClones=secondBeginningOfClones+childrenArr.slice(0,2*slidesToShow).length,secondCount=0;for(i=secondBeginningOfClones;i<=secondEndOfClones;i++)table[i]=secondCount,secondCount++;var originalEnd=secondBeginningOfClones,originalCounter=0;for(i=firstEndOfClones;i<originalEnd;i++)table[i]=originalCounter,originalCounter++;return table}table={};var totalSlides=3*childrenArr.length,count=0;for(i=0;i<totalSlides;i++)table[i]=count,++count===childrenArr.length&&(count=0);return table}function getClones(slidesToShow,childrenArr){return childrenArr.length<slidesToShow?childrenArr:childrenArr.length>2*slidesToShow?childrenArr.slice(childrenArr.length-2*slidesToShow,childrenArr.length).concat(childrenArr,childrenArr.slice(0,2*slidesToShow)):childrenArr.concat(childrenArr,childrenArr)}function getInitialSlideInInfiniteMode(slidesToShow,childrenArr){return childrenArr.length>2*slidesToShow?2*slidesToShow:childrenArr.length}function checkClonesPosition(_a,childrenArr,props){var isReachingTheEnd,currentSlide=_a.currentSlide,slidesToShow=_a.slidesToShow,itemWidth=_a.itemWidth,totalItems=_a.totalItems,nextSlide=0,nextPosition=0,isReachingTheStart=0===currentSlide,originalFirstSlide=childrenArr.length-(childrenArr.length-2*slidesToShow);return childrenArr.length<slidesToShow?(nextPosition=nextSlide=0,isReachingTheStart=isReachingTheEnd=!1):childrenArr.length>2*slidesToShow?((isReachingTheEnd=currentSlide>=originalFirstSlide+childrenArr.length)&&(nextPosition=-itemWidth*(nextSlide=currentSlide-childrenArr.length)),isReachingTheStart&&(nextPosition=-itemWidth*(nextSlide=originalFirstSlide+(childrenArr.length-2*slidesToShow)))):((isReachingTheEnd=currentSlide>=2*childrenArr.length)&&(nextPosition=-itemWidth*(nextSlide=currentSlide-childrenArr.length)),isReachingTheStart&&(nextPosition=props.showDots?-itemWidth*(nextSlide=childrenArr.length):-itemWidth*(nextSlide=totalItems/3))),{isReachingTheEnd:isReachingTheEnd,isReachingTheStart:isReachingTheStart,nextSlide:nextSlide,nextPosition:nextPosition}}Object.defineProperty(exports,\"__esModule\",{value:!0}),exports.getOriginalCounterPart=getOriginalCounterPart,exports.getOriginalIndexLookupTableByClones=getOriginalIndexLookupTableByClones,exports.getClones=getClones,exports.getInitialSlideInInfiniteMode=getInitialSlideInInfiniteMode,exports.checkClonesPosition=checkClonesPosition;"], "mappings": "AAAA,YAAY;;AAAC,SAASA,sBAAsBA,CAACC,KAAK,EAACC,EAAE,EAACC,WAAW,EAAC;EAAC,IAAIC,YAAY,GAACF,EAAE,CAACE,YAAY;IAACC,YAAY,GAACH,EAAE,CAACG,YAAY;EAAC,OAAOF,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,GAA<PERSON>,KAAK,GAAC,CAAC,GAACG,YAAY,GAA<PERSON>,YAAY,IAAEF,WAAW,CAACG,MAAM,GAACH,WAAW,CAACG,MAAM,GAACL,KAAK,GAACA,KAAK;AAAA;AAAC,SAASM,mCAAmCA,CAACH,YAAY,EAACD,WAAW,EAAC;EAAC,IAAGA,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,EAAC;IAAC,KAAI,IAAII,KAAK,GAAC,CAAC,CAAC,EAACC,sBAAsB,GAACN,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,EAACM,gBAAgB,GAACP,WAAW,CAACG,MAAM,GAACG,sBAAsB,EAACE,UAAU,GAACF,sBAAsB,EAACG,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,gBAAgB,EAACE,CAAC,EAAE,EAACJ,KAAK,CAACI,CAAC,CAAC,GAACD,UAAU,EAACA,UAAU,EAAE;IAAC,IAAIE,uBAAuB,GAACV,WAAW,CAACG,MAAM,GAACI,gBAAgB;MAACI,iBAAiB,GAACD,uBAAuB,GAACV,WAAW,CAACY,KAAK,CAAC,CAAC,EAAC,CAAC,GAACX,YAAY,CAAC,CAACE,MAAM;MAACU,WAAW,GAAC,CAAC;IAAC,KAAIJ,CAAC,GAACC,uBAAuB,EAACD,CAAC,IAAEE,iBAAiB,EAACF,CAAC,EAAE,EAACJ,KAAK,CAACI,CAAC,CAAC,GAACI,WAAW,EAACA,WAAW,EAAE;IAAC,IAAIC,WAAW,GAACJ,uBAAuB;MAACK,eAAe,GAAC,CAAC;IAAC,KAAIN,CAAC,GAACF,gBAAgB,EAACE,CAAC,GAACK,WAAW,EAACL,CAAC,EAAE,EAACJ,KAAK,CAACI,CAAC,CAAC,GAACM,eAAe,EAACA,eAAe,EAAE;IAAC,OAAOV,KAAK;EAAA;EAACA,KAAK,GAAC,CAAC,CAAC;EAAC,IAAIW,WAAW,GAAC,CAAC,GAAChB,WAAW,CAACG,MAAM;IAACc,KAAK,GAAC,CAAC;EAAC,KAAIR,CAAC,GAAC,CAAC,EAACA,CAAC,GAACO,WAAW,EAACP,CAAC,EAAE,EAACJ,KAAK,CAACI,CAAC,CAAC,GAACQ,KAAK,EAAC,EAAEA,KAAK,KAAGjB,WAAW,CAACG,MAAM,KAAGc,KAAK,GAAC,CAAC,CAAC;EAAC,OAAOZ,KAAK;AAAA;AAAC,SAASa,SAASA,CAACjB,YAAY,EAACD,WAAW,EAAC;EAAC,OAAOA,WAAW,CAACG,MAAM,GAACF,YAAY,GAACD,WAAW,GAACA,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,GAACD,WAAW,CAACY,KAAK,CAACZ,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,EAACD,WAAW,CAACG,MAAM,CAAC,CAACgB,MAAM,CAACnB,WAAW,EAACA,WAAW,CAACY,KAAK,CAAC,CAAC,EAAC,CAAC,GAACX,YAAY,CAAC,CAAC,GAACD,WAAW,CAACmB,MAAM,CAACnB,WAAW,EAACA,WAAW,CAAC;AAAA;AAAC,SAASoB,6BAA6BA,CAACnB,YAAY,EAACD,WAAW,EAAC;EAAC,OAAOA,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,GAAC,CAAC,GAACA,YAAY,GAACD,WAAW,CAACG,MAAM;AAAA;AAAC,SAASkB,mBAAmBA,CAACtB,EAAE,EAACC,WAAW,EAACsB,KAAK,EAAC;EAAC,IAAIC,gBAAgB;IAACrB,YAAY,GAACH,EAAE,CAACG,YAAY;IAACD,YAAY,GAACF,EAAE,CAACE,YAAY;IAACuB,SAAS,GAACzB,EAAE,CAACyB,SAAS;IAACC,UAAU,GAAC1B,EAAE,CAAC0B,UAAU;IAACC,SAAS,GAAC,CAAC;IAACC,YAAY,GAAC,CAAC;IAACC,kBAAkB,GAAC,CAAC,KAAG1B,YAAY;IAAC2B,kBAAkB,GAAC7B,WAAW,CAACG,MAAM,IAAEH,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,CAAC;EAAC,OAAOD,WAAW,CAACG,MAAM,GAACF,YAAY,IAAE0B,YAAY,GAACD,SAAS,GAAC,CAAC,EAACE,kBAAkB,GAACL,gBAAgB,GAAC,CAAC,CAAC,IAAEvB,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,IAAE,CAACsB,gBAAgB,GAACrB,YAAY,IAAE2B,kBAAkB,GAAC7B,WAAW,CAACG,MAAM,MAAIwB,YAAY,GAAC,CAACH,SAAS,IAAEE,SAAS,GAACxB,YAAY,GAACF,WAAW,CAACG,MAAM,CAAC,CAAC,EAACyB,kBAAkB,KAAGD,YAAY,GAAC,CAACH,SAAS,IAAEE,SAAS,GAACG,kBAAkB,IAAE7B,WAAW,CAACG,MAAM,GAAC,CAAC,GAACF,YAAY,CAAC,CAAC,CAAC,KAAG,CAACsB,gBAAgB,GAACrB,YAAY,IAAE,CAAC,GAACF,WAAW,CAACG,MAAM,MAAIwB,YAAY,GAAC,CAACH,SAAS,IAAEE,SAAS,GAACxB,YAAY,GAACF,WAAW,CAACG,MAAM,CAAC,CAAC,EAACyB,kBAAkB,KAAGD,YAAY,GAACL,KAAK,CAACQ,QAAQ,GAAC,CAACN,SAAS,IAAEE,SAAS,GAAC1B,WAAW,CAACG,MAAM,CAAC,GAAC,CAACqB,SAAS,IAAEE,SAAS,GAACD,UAAU,GAAC,CAAC,CAAC,CAAC,CAAC,EAAC;IAACF,gBAAgB,EAACA,gBAAgB;IAACK,kBAAkB,EAACA,kBAAkB;IAACF,SAAS,EAACA,SAAS;IAACC,YAAY,EAACA;EAAY,CAAC;AAAA;AAACI,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC,CAAC;AAAC,CAAC,CAAC,EAACD,OAAO,CAACpC,sBAAsB,GAACA,sBAAsB,EAACoC,OAAO,CAAC7B,mCAAmC,GAACA,mCAAmC,EAAC6B,OAAO,CAACf,SAAS,GAACA,SAAS,EAACe,OAAO,CAACb,6BAA6B,GAACA,6BAA6B,EAACa,OAAO,CAACZ,mBAAmB,GAACA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}