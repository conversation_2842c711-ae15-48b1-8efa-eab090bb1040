{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\nvar throttle = function (func, limit, setIsInThrottle) {\n  var inThrottle;\n  return function () {\n    var args = arguments;\n    inThrottle || (func.apply(this, args), inThrottle = !0, \"function\" == typeof setIsInThrottle && setIsInThrottle(!0), setTimeout(function () {\n      inThrottle = !1, \"function\" == typeof setIsInThrottle && setIsInThrottle(!1);\n    }, limit));\n  };\n};\nexports.default = throttle;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "throttle", "func", "limit", "setIsInThrottle", "inThrottle", "args", "arguments", "apply", "setTimeout", "default"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/throttle.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var throttle=function(func,limit,setIsInThrottle){var inThrottle;return function(){var args=arguments;inThrottle||(func.apply(this,args),inThrottle=!0,\"function\"==typeof setIsInThrottle&&setIsInThrottle(!0),setTimeout(function(){inThrottle=!1,\"function\"==typeof setIsInThrottle&&setIsInThrottle(!1)},limit))}};exports.default=throttle;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC,CAAC;AAAC,CAAC,CAAC;AAAC,IAAIC,QAAQ,GAAC,SAAAA,CAASC,IAAI,EAACC,KAAK,EAACC,eAAe,EAAC;EAAC,IAAIC,UAAU;EAAC,OAAO,YAAU;IAAC,IAAIC,IAAI,GAACC,SAAS;IAACF,UAAU,KAAGH,IAAI,CAACM,KAAK,CAAC,IAAI,EAACF,IAAI,CAAC,EAACD,UAAU,GAAC,CAAC,CAAC,EAAC,UAAU,IAAE,OAAOD,eAAe,IAAEA,eAAe,CAAC,CAAC,CAAC,CAAC,EAACK,UAAU,CAAC,YAAU;MAACJ,UAAU,GAAC,CAAC,CAAC,EAAC,UAAU,IAAE,OAAOD,eAAe,IAAEA,eAAe,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,EAACD,KAAK,CAAC,CAAC;EAAA,CAAC;AAAA,CAAC;AAACJ,OAAO,CAACW,OAAO,GAACT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}