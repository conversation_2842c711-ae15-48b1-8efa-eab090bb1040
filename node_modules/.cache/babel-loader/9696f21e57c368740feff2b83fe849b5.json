{"ast": null, "code": "var path = require('../internals/path');\n\nvar global = require('../internals/global');\n\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (variable) {\n  return isCallable(variable) ? variable : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(global[namespace]) : path[namespace] && path[namespace][method] || global[namespace] && global[namespace][method];\n};", "map": {"version": 3, "names": ["path", "require", "global", "isCallable", "aFunction", "variable", "undefined", "module", "exports", "namespace", "method", "arguments", "length"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/get-built-in.js"], "sourcesContent": ["var path = require('../internals/path');\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (variable) {\n  return isCallable(variable) ? variable : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(global[namespace])\n    : path[namespace] && path[namespace][method] || global[namespace] && global[namespace][method];\n};\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,mBAAD,CAAlB;;AACA,IAAIC,MAAM,GAAGD,OAAO,CAAC,qBAAD,CAApB;;AACA,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAAD,CAAxB;;AAEA,IAAIG,SAAS,GAAG,UAAUC,QAAV,EAAoB;EAClC,OAAOF,UAAU,CAACE,QAAD,CAAV,GAAuBA,QAAvB,GAAkCC,SAAzC;AACD,CAFD;;AAIAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,SAAV,EAAqBC,MAArB,EAA6B;EAC5C,OAAOC,SAAS,CAACC,MAAV,GAAmB,CAAnB,GAAuBR,SAAS,CAACJ,IAAI,CAACS,SAAD,CAAL,CAAT,IAA8BL,SAAS,CAACF,MAAM,CAACO,SAAD,CAAP,CAA9D,GACHT,IAAI,CAACS,SAAD,CAAJ,IAAmBT,IAAI,CAACS,SAAD,CAAJ,CAAgBC,MAAhB,CAAnB,IAA8CR,MAAM,CAACO,SAAD,CAAN,IAAqBP,MAAM,CAACO,SAAD,CAAN,CAAkBC,MAAlB,CADvE;AAED,CAHD"}, "metadata": {}, "sourceType": "script"}