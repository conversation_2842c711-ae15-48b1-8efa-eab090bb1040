{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/contact-us/index.jsx\";\nimport React from 'react';\nimport ContactUs from '../../components/contact-us';\nimport Seaction from '../../components/global/seaction';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ContactPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: /*#__PURE__*/_jsxDEV(Seaction, {\n      children: /*#__PURE__*/_jsxDEV(ContactUs, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n\n_c = ContactPage;\nexport default ContactPage;\n\nvar _c;\n\n$RefreshReg$(_c, \"ContactPage\");", "map": {"version": 3, "names": ["React", "ContactUs", "Seaction", "ContactPage"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/contact-us/index.jsx"], "sourcesContent": ["import React from 'react'\nimport ContactUs from '../../components/contact-us';\n\nimport Seaction from '../../components/global/seaction'\n\nconst ContactPage = () => {\n    return (\n        <div className=\"container\">\n            <Seaction>\n                <ContactUs />\n            </Seaction>\n        </div>\n    )\n}\n\nexport default ContactPage"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,SAAP,MAAsB,6BAAtB;AAEA,OAAOC,QAAP,MAAqB,kCAArB;;;AAEA,MAAMC,WAAW,GAAG,MAAM;EACtB,oBACI;IAAK,SAAS,EAAC,WAAf;IAAA,uBACI,QAAC,QAAD;MAAA,uBACI,QAAC,SAAD;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA;EADJ;IAAA;IAAA;IAAA;EAAA,QADJ;AAOH,CARD;;KAAMA,W;AAUN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}