{"ast": null, "code": "var EventEmitter = require(\"events\");\n\nmodule.exports = new EventEmitter();", "map": {"version": 3, "names": ["EventEmitter", "require", "module", "exports"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/webpack/hot/emitter.js"], "sourcesContent": ["var EventEmitter = require(\"events\");\nmodule.exports = new EventEmitter();\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAD,CAA1B;;AACAC,MAAM,CAACC,OAAP,GAAiB,IAAIH,YAAJ,EAAjB"}, "metadata": {}, "sourceType": "script"}