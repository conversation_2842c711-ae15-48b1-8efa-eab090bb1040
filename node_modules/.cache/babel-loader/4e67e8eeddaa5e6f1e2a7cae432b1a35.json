{"ast": null, "code": "import styled from 'styled-components';\nexport const Container = styled.div`\n    position: fixed;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n\n    background-color: #fff;\n\n    height: 100vh;\n    width: 100vw;\n\n    z-index: 9999;\n`;\nexport const ImageContainer = styled.div`\n    height: 100vh;\n    width: 100vw;\n\n    display: flex;\n    align-items: center;\n    justify-content: center;\n`;\nexport const ImageHolder = styled.img`\n    width: 200px;\n    height: 200px;\n    object-fit: contain;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "ImageContainer", "ImageHolder", "img"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Container = styled.div`\n    position: fixed;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n\n    background-color: #fff;\n\n    height: 100vh;\n    width: 100vw;\n\n    z-index: 9999;\n`;\n\n\nexport const ImageContainer = styled.div`\n    height: 100vh;\n    width: 100vw;\n\n    display: flex;\n    align-items: center;\n    justify-content: center;\n`;\n\nexport const ImageHolder = styled.img`\n    width: 200px;\n    height: 200px;\n    object-fit: contain;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAbO;AAgBP,OAAO,MAAMC,cAAc,GAAGH,MAAM,CAACE,GAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,CAPO;AASP,OAAO,MAAME,WAAW,GAAGJ,MAAM,CAACK,GAAI;AACtC;AACA;AACA;AACA,CAJO"}, "metadata": {}, "sourceType": "module"}