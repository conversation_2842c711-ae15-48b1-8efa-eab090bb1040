{"ast": null, "code": "import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\n\nconst findHTMLForm = form => {\n  let currentForm;\n\n  if (typeof form === 'string') {\n    currentForm = document.querySelector(form);\n  } else {\n    currentForm = form;\n  }\n\n  if (!currentForm || currentForm.nodeName !== 'FORM') {\n    throw 'The 3rd parameter is expected to be the HTML form element or the style selector of form';\n  }\n\n  return currentForm;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {string} publicKey - the EmailJS public key\n * @returns {Promise<EmailJSResponseStatus>}\n */\n\n\nexport const sendForm = (serviceID, templateID, form, publicKey) => {\n  const uID = publicKey || store._userID;\n  const currentForm = findHTMLForm(form);\n  validateParams(uID, serviceID, templateID);\n  const formData = new FormData(currentForm);\n  formData.append('lib_version', '3.9.0');\n  formData.append('service_id', serviceID);\n  formData.append('template_id', templateID);\n  formData.append('user_id', uID);\n  return sendPost('/api/v1.0/email/send-form', formData);\n};", "map": {"version": 3, "names": ["store", "validateParams", "sendPost", "findHTMLForm", "form", "currentForm", "document", "querySelector", "nodeName", "sendForm", "serviceID", "templateID", "public<PERSON>ey", "uID", "_userID", "formData", "FormData", "append"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@emailjs/browser/es/methods/sendForm/sendForm.js"], "sourcesContent": ["import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\nconst findHTMLForm = (form) => {\n    let currentForm;\n    if (typeof form === 'string') {\n        currentForm = document.querySelector(form);\n    }\n    else {\n        currentForm = form;\n    }\n    if (!currentForm || currentForm.nodeName !== 'FORM') {\n        throw 'The 3rd parameter is expected to be the HTML form element or the style selector of form';\n    }\n    return currentForm;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {string} publicKey - the EmailJS public key\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const sendForm = (serviceID, templateID, form, publicKey) => {\n    const uID = publicKey || store._userID;\n    const currentForm = findHTMLForm(form);\n    validateParams(uID, serviceID, templateID);\n    const formData = new FormData(currentForm);\n    formData.append('lib_version', '3.9.0');\n    formData.append('service_id', serviceID);\n    formData.append('template_id', templateID);\n    formData.append('user_id', uID);\n    return sendPost('/api/v1.0/email/send-form', formData);\n};\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,mBAAtB;AACA,SAASC,cAAT,QAA+B,4BAA/B;AACA,SAASC,QAAT,QAAyB,oBAAzB;;AACA,MAAMC,YAAY,GAAIC,IAAD,IAAU;EAC3B,IAAIC,WAAJ;;EACA,IAAI,OAAOD,IAAP,KAAgB,QAApB,EAA8B;IAC1BC,WAAW,GAAGC,QAAQ,CAACC,aAAT,CAAuBH,IAAvB,CAAd;EACH,CAFD,MAGK;IACDC,WAAW,GAAGD,IAAd;EACH;;EACD,IAAI,CAACC,WAAD,IAAgBA,WAAW,CAACG,QAAZ,KAAyB,MAA7C,EAAqD;IACjD,MAAM,yFAAN;EACH;;EACD,OAAOH,WAAP;AACH,CAZD;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,OAAO,MAAMI,QAAQ,GAAG,CAACC,SAAD,EAAYC,UAAZ,EAAwBP,IAAxB,EAA8BQ,SAA9B,KAA4C;EAChE,MAAMC,GAAG,GAAGD,SAAS,IAAIZ,KAAK,CAACc,OAA/B;EACA,MAAMT,WAAW,GAAGF,YAAY,CAACC,IAAD,CAAhC;EACAH,cAAc,CAACY,GAAD,EAAMH,SAAN,EAAiBC,UAAjB,CAAd;EACA,MAAMI,QAAQ,GAAG,IAAIC,QAAJ,CAAaX,WAAb,CAAjB;EACAU,QAAQ,CAACE,MAAT,CAAgB,aAAhB,EAA+B,OAA/B;EACAF,QAAQ,CAACE,MAAT,CAAgB,YAAhB,EAA8BP,SAA9B;EACAK,QAAQ,CAACE,MAAT,CAAgB,aAAhB,EAA+BN,UAA/B;EACAI,QAAQ,CAACE,MAAT,CAAgB,SAAhB,EAA2BJ,GAA3B;EACA,OAAOX,QAAQ,CAAC,2BAAD,EAA8Ba,QAA9B,CAAf;AACH,CAVM"}, "metadata": {}, "sourceType": "module"}