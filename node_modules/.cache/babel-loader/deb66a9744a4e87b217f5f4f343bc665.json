{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/index.jsx\";\nimport React from 'react';\nimport { AssetsList } from '../../components/elements/assetsList';\nimport ContactForm from './form';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst ContactUs = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Styles.Contaier, {\n      className: \"flex-wrap flex-md-nowrap\",\n      children: [/*#__PURE__*/_jsxDEV(Styles.ImageWrapper, {\n        className: \"col col-12 col-md p-5 \",\n        children: /*#__PURE__*/_jsxDEV(Styles.ImageHolder, {\n          src: AssetsList.client\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Styles.FormWrapper, {\n        className: \"col col-12 col-md px-5 py-1 py-md-5\",\n        children: /*#__PURE__*/_jsxDEV(ContactForm, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Styles.AddressWrapper, {\n      className: \"col-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: /*#__PURE__*/_jsxDEV(\"b\", {\n          children: \"Office Location\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Office No. 201, 2nd Floor, Tarabaug CHS, Prashant Nagar, Near Naupada Police Station, Thane - 400602\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n\n_c = ContactUs;\nexport default ContactUs;\n\nvar _c;\n\n$RefreshReg$(_c, \"ContactUs\");", "map": {"version": 3, "names": ["React", "AssetsList", "ContactForm", "Styles", "ContactUs", "client"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport { AssetsList } from '../../components/elements/assetsList';\n\nimport ContactForm from './form';\n\nimport * as Styles from './styles'\n\nconst ContactUs = () => {\n    return (\n        <>\n        <Styles.Contaier className=\"flex-wrap flex-md-nowrap\">\n            \n            <Styles.ImageWrapper className=\"col col-12 col-md p-5 \">\n                <Styles.ImageHolder\n                    src={AssetsList.client}\n                />\n            </Styles.ImageWrapper>\n            <Styles.FormWrapper className=\"col col-12 col-md px-5 py-1 py-md-5\">\n                <ContactForm />\n            </Styles.FormWrapper>\n            \n        </Styles.Contaier>\n        <Styles.AddressWrapper className=\"col-12\">\n        <h1><b>Office Location</b></h1>\n        <p>Office No. 201, 2nd Floor, Tarabaug CHS, Prashant Nagar, Near Naupada Police Station, Thane - 400602</p>\n        </Styles.AddressWrapper>\n        </>\n    )\n}\n\nexport default ContactUs"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,UAAT,QAA2B,sCAA3B;AAEA,OAAOC,WAAP,MAAwB,QAAxB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;;AAEA,MAAMC,SAAS,GAAG,MAAM;EACpB,oBACI;IAAA,wBACA,QAAC,MAAD,CAAQ,QAAR;MAAiB,SAAS,EAAC,0BAA3B;MAAA,wBAEI,QAAC,MAAD,CAAQ,YAAR;QAAqB,SAAS,EAAC,wBAA/B;QAAA,uBACI,QAAC,MAAD,CAAQ,WAAR;UACI,GAAG,EAAEH,UAAU,CAACI;QADpB;UAAA;UAAA;UAAA;QAAA;MADJ;QAAA;QAAA;QAAA;MAAA,QAFJ,eAOI,QAAC,MAAD,CAAQ,WAAR;QAAoB,SAAS,EAAC,qCAA9B;QAAA,uBACI,QAAC,WAAD;UAAA;UAAA;UAAA;QAAA;MADJ;QAAA;QAAA;QAAA;MAAA,QAPJ;IAAA;MAAA;MAAA;MAAA;IAAA,QADA,eAaA,QAAC,MAAD,CAAQ,cAAR;MAAuB,SAAS,EAAC,QAAjC;MAAA,wBACA;QAAA,uBAAI;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA;MAAJ;QAAA;QAAA;QAAA;MAAA,QADA,eAEA;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAFA;IAAA;MAAA;MAAA;MAAA;IAAA,QAbA;EAAA,gBADJ;AAoBH,CArBD;;KAAMD,S;AAuBN,eAAeA,SAAf"}, "metadata": {}, "sourceType": "module"}