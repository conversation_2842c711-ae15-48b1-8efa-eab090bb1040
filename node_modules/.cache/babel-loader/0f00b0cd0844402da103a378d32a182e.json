{"ast": null, "code": "var fails = require('../internals/fails'); // Detect IE8's incomplete defineProperty implementation\n\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es-x/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, {\n    get: function () {\n      return 7;\n    }\n  })[1] != 7;\n});", "map": {"version": 3, "names": ["fails", "require", "module", "exports", "Object", "defineProperty", "get"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/descriptors.js"], "sourcesContent": ["var fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es-x/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] != 7;\n});\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAD,CAAnB,C,CAEA;;;AACAC,MAAM,CAACC,OAAP,GAAiB,CAACH,KAAK,CAAC,YAAY;EAClC;EACA,OAAOI,MAAM,CAACC,cAAP,CAAsB,EAAtB,EAA0B,CAA1B,EAA6B;IAAEC,GAAG,EAAE,YAAY;MAAE,OAAO,CAAP;IAAW;EAAhC,CAA7B,EAAiE,CAAjE,KAAuE,CAA9E;AACD,CAHsB,CAAvB"}, "metadata": {}, "sourceType": "script"}