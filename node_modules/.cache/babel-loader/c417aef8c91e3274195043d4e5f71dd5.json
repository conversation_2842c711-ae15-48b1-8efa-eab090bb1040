{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  background-color: #fff;\n  border-radius: 10px;\n\n  overflow: hidden;\n\n  flex: 1 1 auto;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);\n  cursor: pointer;\n\n  &:hover {\n    box-shadow: 0 0 13px rgba(0, 0, 0, 0.3);\n    .arrow {\n      margin-left: 12px;\n    }\n\n    .title {\n      color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.main.blue;\n}};\n    }\n\n    .viewlink {\n      color: ${_ref2 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref2;\n  return colors.main.red;\n}};\n    }\n\n    .image {\n      background-size: 110%;\n      transition: all 0.4s ease-in-out;\n    }\n  }\n\n  transition: all 0.3s ease-in-out;\n\n  position: relative;\n`;\nexport const ImgContainer = styled.div`\n  position: relative;\n  background-image: ${_ref3 => {\n  let {\n    img\n  } = _ref3;\n  return `url(${img})`;\n}};\n  background-size: 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n\n  transition: all 0.4s ease-in-out;\n\n  height: 180px;\n`;\nexport const TagWrapper = styled.div`\n  position: absolute;\n  right: 0;\n  top: 0;\n\n  background-color: ${_ref4 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref4;\n  return colors.main.blue;\n}};\n\n  color: #fff;\n  padding: 5px 25px;\n  border-bottom-left-radius: 10px;\n`;\nexport const TextHolder = styled.span`\n  font-size: ${_ref5 => {\n  let {\n    sub,\n    heading,\n    theme: {\n      font\n    }\n  } = _ref5;\n  return sub ? font.sub : heading ? font.title : font.main;\n}};\n  font-weight: ${_ref6 => {\n  let {\n    bold\n  } = _ref6;\n  return bold ? 600 : 400;\n}};\n  text-transform: ${_ref7 => {\n  let {\n    upper\n  } = _ref7;\n  return upper ? \"uppercase\" : \"none\";\n}};\n\n  position: relative;\n\n  transition: all 0.3s ease-in-out;\n`;\nexport const TextWrapper = styled.div`\n  padding: 20px;\n  margin-bottom: 30px;\n`;\nexport const SaveWrapper = styled.div`\n  background-color: #e5e5e6;\n  padding: 15px 16px;\n\n  position: absolute;\n  right: 10%;\n  top: 0;\n\n  transform: translateY(-50%);\n\n  border-radius: 10px;\n`;\nexport const IconHolder = styled.div`\n  margin-right: 8px;\n\n  svg {\n    width: 20px;\n    height: 20px;\n  }\n`;\nexport const LocationWrapper = styled.div`\n  display: flex;\n  align-items: center;\n`;\nexport const Padding = styled.div`\n  padding: 15px 0 10px;\n`;\nexport const LinkWrapper = styled.div`\n  display: flex;\n  align-items: center;\n  color: ${_ref8 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref8;\n  return colors.main.blue;\n}};\n\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n`;\nexport const ArrowHolder = styled.div`\n  margin-left: 6px;\n\n  svg {\n    width: 20px;\n    height: 20px;\n  }\n  transition: all 0.3s ease-in-out;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "theme", "colors", "main", "blue", "red", "ImgContainer", "img", "TagWrapper", "TextHolder", "span", "sub", "heading", "font", "title", "bold", "upper", "TextWrapper", "SaveWrapper", "IconHolder", "LocationWrapper", "Padding", "LinkWrapper", "ArrowHolder"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  background-color: #fff;\n  border-radius: 10px;\n\n  overflow: hidden;\n\n  flex: 1 1 auto;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);\n  cursor: pointer;\n\n  &:hover {\n    box-shadow: 0 0 13px rgba(0, 0, 0, 0.3);\n    .arrow {\n      margin-left: 12px;\n    }\n\n    .title {\n      color: ${({ theme: { colors } }) => colors.main.blue};\n    }\n\n    .viewlink {\n      color: ${({ theme: { colors } }) => colors.main.red};\n    }\n\n    .image {\n      background-size: 110%;\n      transition: all 0.4s ease-in-out;\n    }\n  }\n\n  transition: all 0.3s ease-in-out;\n\n  position: relative;\n`;\n\nexport const ImgContainer = styled.div`\n  position: relative;\n  background-image: ${({ img }) => `url(${img})`};\n  background-size: 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n\n  transition: all 0.4s ease-in-out;\n\n  height: 180px;\n`;\n\nexport const TagWrapper = styled.div`\n  position: absolute;\n  right: 0;\n  top: 0;\n\n  background-color: ${({ theme: { colors } }) => colors.main.blue};\n\n  color: #fff;\n  padding: 5px 25px;\n  border-bottom-left-radius: 10px;\n`;\n\nexport const TextHolder = styled.span`\n  font-size: ${({ sub, heading, theme: { font } }) =>\n    sub ? font.sub : heading ? font.title : font.main};\n  font-weight: ${({ bold }) => (bold ? 600 : 400)};\n  text-transform: ${({ upper }) => (upper ? \"uppercase\" : \"none\")};\n\n  position: relative;\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const TextWrapper = styled.div`\n  padding: 20px;\n  margin-bottom: 30px;\n`;\n\nexport const SaveWrapper = styled.div`\n  background-color: #e5e5e6;\n  padding: 15px 16px;\n\n  position: absolute;\n  right: 10%;\n  top: 0;\n\n  transform: translateY(-50%);\n\n  border-radius: 10px;\n`;\n\nexport const IconHolder = styled.div`\n  margin-right: 8px;\n\n  svg {\n    width: 20px;\n    height: 20px;\n  }\n`;\n\nexport const LocationWrapper = styled.div`\n  display: flex;\n  align-items: center;\n`;\n\nexport const Padding = styled.div`\n  padding: 15px 0 10px;\n`;\n\nexport const LinkWrapper = styled.div`\n  display: flex;\n  align-items: center;\n  color: ${({ theme: { colors } }) => colors.main.blue};\n\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n`;\n\nexport const ArrowHolder = styled.div`\n  margin-left: 6px;\n\n  svg {\n    width: 20px;\n    height: 20px;\n  }\n  transition: all 0.3s ease-in-out;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AAC3D;AACA;AACA;AACA,eAAe;EAAA,IAAC;IAAEH,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYE,GAAvC;AAAA,CAA2C;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAlCO;AAoCP,OAAO,MAAMC,YAAY,GAAGR,MAAM,CAACE,GAAI;AACvC;AACA,sBAAsB;EAAA,IAAC;IAAEO;EAAF,CAAD;EAAA,OAAc,OAAMA,GAAI,GAAxB;AAAA,CAA2B;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAVO;AAYP,OAAO,MAAMC,UAAU,GAAGV,MAAM,CAACE,GAAI;AACrC;AACA;AACA;AACA;AACA,sBAAsB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AAClE;AACA;AACA;AACA;AACA,CAVO;AAYP,OAAO,MAAMK,UAAU,GAAGX,MAAM,CAACY,IAAK;AACtC,eAAe;EAAA,IAAC;IAAEC,GAAF;IAAOC,OAAP;IAAgBX,KAAK,EAAE;MAAEY;IAAF;EAAvB,CAAD;EAAA,OACXF,GAAG,GAAGE,IAAI,CAACF,GAAR,GAAcC,OAAO,GAAGC,IAAI,CAACC,KAAR,GAAgBD,IAAI,CAACV,IADlC;AAAA,CACuC;AACtD,iBAAiB;EAAA,IAAC;IAAEY;EAAF,CAAD;EAAA,OAAeA,IAAI,GAAG,GAAH,GAAS,GAA5B;AAAA,CAAiC;AAClD,oBAAoB;EAAA,IAAC;IAAEC;EAAF,CAAD;EAAA,OAAgBA,KAAK,GAAG,WAAH,GAAiB,MAAtC;AAAA,CAA8C;AAClE;AACA;AACA;AACA;AACA,CATO;AAWP,OAAO,MAAMC,WAAW,GAAGnB,MAAM,CAACE,GAAI;AACtC;AACA;AACA,CAHO;AAKP,OAAO,MAAMkB,WAAW,GAAGpB,MAAM,CAACE,GAAI;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAXO;AAaP,OAAO,MAAMmB,UAAU,GAAGrB,MAAM,CAACE,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,CAPO;AASP,OAAO,MAAMoB,eAAe,GAAGtB,MAAM,CAACE,GAAI;AAC1C;AACA;AACA,CAHO;AAKP,OAAO,MAAMqB,OAAO,GAAGvB,MAAM,CAACE,GAAI;AAClC;AACA,CAFO;AAIP,OAAO,MAAMsB,WAAW,GAAGxB,MAAM,CAACE,GAAI;AACtC;AACA;AACA,WAAW;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AACvD;AACA;AACA;AACA;AACA,CARO;AAUP,OAAO,MAAMmB,WAAW,GAAGzB,MAAM,CAACE,GAAI;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CARO"}, "metadata": {}, "sourceType": "module"}