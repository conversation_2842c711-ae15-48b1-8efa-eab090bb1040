{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  /* text-align: center; */\n  flex: 1;\n\n  min-width: 50%;\n\n  height: 100%;\n\n`;\nexport const TitleHolder = styled.h2``;\nexport const TextHolder = styled.p``;", "map": {"version": 3, "names": ["styled", "Container", "div", "TitleHolder", "h2", "TextHolder", "p"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  /* text-align: center; */\n  flex: 1;\n\n  min-width: 50%;\n\n  height: 100%;\n\n`;\n\nexport const TitleHolder = styled.h2``;\n\nexport const TextHolder = styled.p``;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CARO;AAUP,OAAO,MAAMC,WAAW,GAAGH,MAAM,CAACI,EAAG,EAA9B;AAEP,OAAO,MAAMC,UAAU,GAAGL,MAAM,CAACM,CAAE,EAA5B"}, "metadata": {}, "sourceType": "module"}