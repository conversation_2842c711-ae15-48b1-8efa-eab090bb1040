{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/index.jsx\";\nimport React from \"react\";\nimport { ArrowRightCircle } from \"react-feather\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst NameCard = _ref => {\n  var _client$data;\n\n  let {\n    client\n  } = _ref;\n  console.log({\n    client\n  });\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: [/*#__PURE__*/_jsxDEV(Styles.TitleWrapper, {\n      className: \"title\",\n      children: [/*#__PURE__*/_jsxDEV(Styles.IconHolder, {\n        children: client === null || client === void 0 ? void 0 : client.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n        children: client === null || client === void 0 ? void 0 : client.heading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.ListHolder, {\n      children: client === null || client === void 0 ? void 0 : (_client$data = client.data) === null || _client$data === void 0 ? void 0 : _client$data.map((item, index) => {\n        return /*#__PURE__*/_jsxDEV(Styles.ListItme, {\n          className: \"list-item\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowRightCircle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n            children: item === null || item === void 0 ? void 0 : item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n\n_c = NameCard;\nexport default NameCard;\n\nvar _c;\n\n$RefreshReg$(_c, \"NameCard\");", "map": {"version": 3, "names": ["React", "ArrowRightCircle", "Styles", "NameCard", "client", "console", "log", "icon", "heading", "data", "map", "item", "index", "title"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { ArrowRightCircle } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst NameCard = ({ client }) => {\n  console.log({ client });\n  return (\n    <Styles.Container>\n      <Styles.TitleWrapper className=\"title\">\n        <Styles.IconHolder>{client?.icon}</Styles.IconHolder>\n        <Styles.TextHolder>{client?.heading}</Styles.TextHolder>\n      </Styles.TitleWrapper>\n      <Styles.ListHolder>\n        {client?.data?.map((item, index) => {\n          return (\n            <Styles.ListItme className=\"list-item\" key={index}>\n              <ArrowRightCircle />\n              <Styles.TextHolder>{item?.title}</Styles.TextHolder>\n            </Styles.ListItme>\n          );\n        })}\n      </Styles.ListHolder>\n    </Styles.Container>\n  );\n};\n\nexport default NameCard;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,gBAAT,QAAiC,eAAjC;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,QAAQ,GAAG,QAAgB;EAAA;;EAAA,IAAf;IAAEC;EAAF,CAAe;EAC/BC,OAAO,CAACC,GAAR,CAAY;IAAEF;EAAF,CAAZ;EACA,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,wBACE,QAAC,MAAD,CAAQ,YAAR;MAAqB,SAAS,EAAC,OAA/B;MAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;QAAA,UAAoBA,MAApB,aAAoBA,MAApB,uBAAoBA,MAAM,CAAEG;MAA5B;QAAA;QAAA;QAAA;MAAA,QADF,eAEE,QAAC,MAAD,CAAQ,UAAR;QAAA,UAAoBH,MAApB,aAAoBA,MAApB,uBAAoBA,MAAM,CAAEI;MAA5B;QAAA;QAAA;QAAA;MAAA,QAFF;IAAA;MAAA;MAAA;MAAA;IAAA,QADF,eAKE,QAAC,MAAD,CAAQ,UAAR;MAAA,UACGJ,MADH,aACGA,MADH,uCACGA,MAAM,CAAEK,IADX,iDACG,aAAcC,GAAd,CAAkB,CAACC,IAAD,EAAOC,KAAP,KAAiB;QAClC,oBACE,QAAC,MAAD,CAAQ,QAAR;UAAiB,SAAS,EAAC,WAA3B;UAAA,wBACE,QAAC,gBAAD;YAAA;YAAA;YAAA;UAAA,QADF,eAEE,QAAC,MAAD,CAAQ,UAAR;YAAA,UAAoBD,IAApB,aAAoBA,IAApB,uBAAoBA,IAAI,CAAEE;UAA1B;YAAA;YAAA;YAAA;UAAA,QAFF;QAAA,GAA4CD,KAA5C;UAAA;UAAA;UAAA;QAAA,QADF;MAMD,CAPA;IADH;MAAA;MAAA;MAAA;IAAA,QALF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAkBD,CApBD;;KAAMT,Q;AAsBN,eAAeA,QAAf"}, "metadata": {}, "sourceType": "module"}