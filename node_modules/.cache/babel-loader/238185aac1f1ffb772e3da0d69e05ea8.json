{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/index.jsx\";\nimport React from 'react';\nimport SeactionHeading from '../global/seaction-title';\nimport ProjectList from './list';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst Projects = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n      title: \"Featured Property\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ProjectList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n\n_c = Projects;\nexport default Projects;\n\nvar _c;\n\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "SeactionHeading", "ProjectList", "Projects"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport SeactionHeading from '../global/seaction-title'\nimport ProjectList from './list'\n\nconst Projects = () => {\n    return (\n        <>\n            <SeactionHeading title=\"Featured Property\" />\n            <ProjectList />\n        </>\n    )\n}\n\nexport default Projects"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,eAAP,MAA4B,0BAA5B;AACA,OAAOC,WAAP,MAAwB,QAAxB;;;;AAEA,MAAMC,QAAQ,GAAG,MAAM;EACnB,oBACI;IAAA,wBACI,QAAC,eAAD;MAAiB,KAAK,EAAC;IAAvB;MAAA;MAAA;MAAA;IAAA,QADJ,eAEI,QAAC,WAAD;MAAA;MAAA;MAAA;IAAA,QAFJ;EAAA,gBADJ;AAMH,CAPD;;KAAMA,Q;AASN,eAAeA,QAAf"}, "metadata": {}, "sourceType": "module"}