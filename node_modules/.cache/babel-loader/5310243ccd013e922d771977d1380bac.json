{"ast": null, "code": "import styled from 'styled-components';\nexport const Contaier = styled.div`\n    display: flex;\n    align-items: center;\n`;\nexport const ImageWrapper = styled.div`\n    \n`;\nexport const ImageHolder = styled.img`\n    width: 100%;\n    height: 100%;\n\n    object-fit: contain;\n`;\nexport const FormWrapper = styled.div`\n    width: 100%;\n`;\nexport const AddressWrapper = styled.div`\n    padding: 2rem 5rem;\n    background-color: #f8f9fa;\n    text-align: center;\n`;", "map": {"version": 3, "names": ["styled", "<PERSON><PERSON><PERSON>", "div", "ImageWrapper", "ImageHolder", "img", "FormWrapper", "AddressWrapper"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Contaier = styled.div`\n    display: flex;\n    align-items: center;\n`;\n\nexport const ImageWrapper = styled.div`\n    \n`;\n\nexport const ImageHolder = styled.img`\n    width: 100%;\n    height: 100%;\n\n    object-fit: contain;\n`;\n\nexport const FormWrapper = styled.div`\n    width: 100%;\n`;\n\nexport const AddressWrapper = styled.div`\n    padding: 2rem 5rem;\n    background-color: #f8f9fa;\n    text-align: center;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,QAAQ,GAAGD,MAAM,CAACE,GAAI;AACnC;AACA;AACA,CAHO;AAKP,OAAO,MAAMC,YAAY,GAAGH,MAAM,CAACE,GAAI;AACvC;AACA,CAFO;AAIP,OAAO,MAAME,WAAW,GAAGJ,MAAM,CAACK,GAAI;AACtC;AACA;AACA;AACA;AACA,CALO;AAOP,OAAO,MAAMC,WAAW,GAAGN,MAAM,CAACE,GAAI;AACtC;AACA,CAFO;AAIP,OAAO,MAAMK,cAAc,GAAGP,MAAM,CAACE,GAAI;AACzC;AACA;AACA;AACA,CAJO"}, "metadata": {}, "sourceType": "module"}