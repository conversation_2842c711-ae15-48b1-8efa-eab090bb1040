{"ast": null, "code": "import { createGlobalStyle } from \"styled-components\";\nexport const GlobalStyle = createGlobalStyle`\n\n  html,\n  body,\n  div,\n  span,\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6,\n  p,\n  a,\n  em,\n  img,\n  strong,\n  form,\n  table,\n  section {\n    margin: 0;\n    padding: 0;\n    border: 0;\n    font-size: 100%;\n    vertical-align: baseline;\n    box-sizing: border-box;\n  }\n\n  section,\n  footer,\n  header,\n  menu,\n  nav {\n    display: block;\n  }\n\n  input{\n    box-sizing: border-box;\n  }\n\n  ${''\n/* body {\nfont-size: ${({ theme: { font } }) => font.main};\nline-height: 20px;\n-webkit-text-size-adjust: none;\nvertical-align: baseline;\nfont-family: 'Roboto', sans-serif;\nfont-display: block;\n-webkit-font-smoothing: antialiased;\n-moz-osx-font-smoothing: grayscale;\noverflow-y: scroll;\nbox-sizing: border-box;\nbackground-color: ${({ theme: { color } }) => color.pageBg};\ncolor: ${({ theme: { color } }) => color.dark};\n} */\n}\n\n  ol,\n  ul {\n    list-style: none;\n  }\n\n  a, button {\n    cursor: pointer;\n  }\n\n  a:link {\n    text-decoration: none;\n  }\n\n  a{\n    font-size: inherit;\n    text-decoration: none;\n    color: inherit\n  }\n  \n  a:hover{\n    color: inherit\n  }\n\n`;", "map": {"version": 3, "names": ["createGlobalStyle", "GlobalStyle"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/global.styles.js"], "sourcesContent": ["import { createGlobalStyle } from \"styled-components\";\n\nexport const GlobalStyle = createGlobalStyle`\n\n  html,\n  body,\n  div,\n  span,\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6,\n  p,\n  a,\n  em,\n  img,\n  strong,\n  form,\n  table,\n  section {\n    margin: 0;\n    padding: 0;\n    border: 0;\n    font-size: 100%;\n    vertical-align: baseline;\n    box-sizing: border-box;\n  }\n\n  section,\n  footer,\n  header,\n  menu,\n  nav {\n    display: block;\n  }\n\n  input{\n    box-sizing: border-box;\n  }\n\n  ${'' /* body {\n    font-size: ${({ theme: { font } }) => font.main};\n    line-height: 20px;\n    -webkit-text-size-adjust: none;\n    vertical-align: baseline;\n    font-family: 'Roboto', sans-serif;\n    font-display: block;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    overflow-y: scroll;\n    box-sizing: border-box;\n    background-color: ${({ theme: { color } }) => color.pageBg};\n    color: ${({ theme: { color } }) => color.dark};\n  } */}\n\n  ol,\n  ul {\n    list-style: none;\n  }\n\n  a, button {\n    cursor: pointer;\n  }\n\n  a:link {\n    text-decoration: none;\n  }\n\n  a{\n    font-size: inherit;\n    text-decoration: none;\n    color: inherit\n  }\n  \n  a:hover{\n    color: inherit\n  }\n\n`;\n"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,mBAAlC;AAEA,OAAO,MAAMC,WAAW,GAAGD,iBAAkB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CA9EO"}, "metadata": {}, "sourceType": "module"}