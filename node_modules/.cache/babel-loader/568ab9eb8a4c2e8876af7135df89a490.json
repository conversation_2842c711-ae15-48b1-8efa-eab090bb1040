{"ast": null, "code": "\"use strict\";\n\nfunction populateSlidesOnMouseTouchMove(state, props, initialX, lastX, clientX, transformPlaceHolder) {\n  var direction,\n      nextPosition,\n      itemWidth = state.itemWidth,\n      slidesToShow = state.slidesToShow,\n      totalItems = state.totalItems,\n      currentSlide = state.currentSlide,\n      infinite = props.infinite,\n      canContinue = !1,\n      slidesHavePassedRight = Math.round((initialX - lastX) / itemWidth),\n      slidesHavePassedLeft = Math.round((lastX - initialX) / itemWidth),\n      isMovingLeft = initialX < clientX;\n\n  if (clientX < initialX && !!(slidesHavePassedRight <= slidesToShow)) {\n    direction = \"right\";\n    var translateXLimit = Math.abs(-itemWidth * (totalItems - slidesToShow)),\n        nextTranslate = transformPlaceHolder - (lastX - clientX),\n        isLastSlide = currentSlide === totalItems - slidesToShow;\n    (Math.abs(nextTranslate) <= translateXLimit || isLastSlide && infinite) && (nextPosition = nextTranslate, canContinue = !0);\n  }\n\n  isMovingLeft && slidesHavePassedLeft <= slidesToShow && (direction = \"left\", ((nextTranslate = transformPlaceHolder + (clientX - lastX)) <= 0 || 0 === currentSlide && infinite) && (canContinue = !0, nextPosition = nextTranslate));\n  return {\n    direction: direction,\n    nextPosition: nextPosition,\n    canContinue: canContinue\n  };\n}\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n}), exports.populateSlidesOnMouseTouchMove = populateSlidesOnMouseTouchMove;", "map": {"version": 3, "names": ["populateSlidesOnMouseTouchMove", "state", "props", "initialX", "lastX", "clientX", "transformPlaceHolder", "direction", "nextPosition", "itemWidth", "slidesToShow", "totalItems", "currentSlide", "infinite", "canContinue", "slidesHavePassedRight", "Math", "round", "slidesHavePassedLeft", "isMovingLeft", "translateXLimit", "abs", "nextTranslate", "isLastSlide", "Object", "defineProperty", "exports", "value"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/mouseOrTouchMove.js"], "sourcesContent": ["\"use strict\";function populateSlidesOnMouseTouchMove(state,props,initialX,lastX,clientX,transformPlaceHolder){var direction,nextPosition,itemWidth=state.itemWidth,slidesToShow=state.slidesToShow,totalItems=state.totalItems,currentSlide=state.currentSlide,infinite=props.infinite,canContinue=!1,slidesHavePassedRight=Math.round((initialX-lastX)/itemWidth),slidesHavePassedLeft=Math.round((lastX-initialX)/itemWidth),isMovingLeft=initialX<clientX;if(clientX<initialX&&!!(slidesHavePassedRight<=slidesToShow)){direction=\"right\";var translateXLimit=Math.abs(-itemWidth*(totalItems-slidesToShow)),nextTranslate=transformPlaceHolder-(lastX-clientX),isLastSlide=currentSlide===totalItems-slidesToShow;(Math.abs(nextTranslate)<=translateXLimit||isLastSlide&&infinite)&&(nextPosition=nextTranslate,canContinue=!0)}isMovingLeft&&slidesHavePassedLeft<=slidesToShow&&(direction=\"left\",((nextTranslate=transformPlaceHolder+(clientX-lastX))<=0||0===currentSlide&&infinite)&&(canContinue=!0,nextPosition=nextTranslate));return{direction:direction,nextPosition:nextPosition,canContinue:canContinue}}Object.defineProperty(exports,\"__esModule\",{value:!0}),exports.populateSlidesOnMouseTouchMove=populateSlidesOnMouseTouchMove;"], "mappings": "AAAA;;AAAa,SAASA,8BAAT,CAAwCC,KAAxC,EAA8CC,KAA9C,EAAoDC,QAApD,EAA6DC,KAA7D,EAAmEC,OAAnE,EAA2EC,oBAA3E,EAAgG;EAAC,IAAIC,SAAJ;EAAA,IAAcC,YAAd;EAAA,IAA2BC,SAAS,GAACR,KAAK,CAACQ,SAA3C;EAAA,IAAqDC,YAAY,GAACT,KAAK,CAACS,YAAxE;EAAA,IAAqFC,UAAU,GAACV,KAAK,CAACU,UAAtG;EAAA,IAAiHC,YAAY,GAACX,KAAK,CAACW,YAApI;EAAA,IAAiJC,QAAQ,GAACX,KAAK,CAACW,QAAhK;EAAA,IAAyKC,WAAW,GAAC,CAAC,CAAtL;EAAA,IAAwLC,qBAAqB,GAACC,IAAI,CAACC,KAAL,CAAW,CAACd,QAAQ,GAACC,KAAV,IAAiBK,SAA5B,CAA9M;EAAA,IAAqPS,oBAAoB,GAACF,IAAI,CAACC,KAAL,CAAW,CAACb,KAAK,GAACD,QAAP,IAAiBM,SAA5B,CAA1Q;EAAA,IAAiTU,YAAY,GAAChB,QAAQ,GAACE,OAAvU;;EAA+U,IAAGA,OAAO,GAACF,QAAR,IAAkB,CAAC,EAAEY,qBAAqB,IAAEL,YAAzB,CAAtB,EAA6D;IAACH,SAAS,GAAC,OAAV;IAAkB,IAAIa,eAAe,GAACJ,IAAI,CAACK,GAAL,CAAS,CAACZ,SAAD,IAAYE,UAAU,GAACD,YAAvB,CAAT,CAApB;IAAA,IAAmEY,aAAa,GAAChB,oBAAoB,IAAEF,KAAK,GAACC,OAAR,CAArG;IAAA,IAAsHkB,WAAW,GAACX,YAAY,KAAGD,UAAU,GAACD,YAA5J;IAAyK,CAACM,IAAI,CAACK,GAAL,CAASC,aAAT,KAAyBF,eAAzB,IAA0CG,WAAW,IAAEV,QAAxD,MAAoEL,YAAY,GAACc,aAAb,EAA2BR,WAAW,GAAC,CAAC,CAA5G;EAA+G;;EAAAK,YAAY,IAAED,oBAAoB,IAAER,YAApC,KAAmDH,SAAS,GAAC,MAAV,EAAiB,CAAC,CAACe,aAAa,GAAChB,oBAAoB,IAAED,OAAO,GAACD,KAAV,CAAnC,KAAsD,CAAtD,IAAyD,MAAIQ,YAAJ,IAAkBC,QAA5E,MAAwFC,WAAW,GAAC,CAAC,CAAb,EAAeN,YAAY,GAACc,aAApH,CAApE;EAAwM,OAAM;IAACf,SAAS,EAACA,SAAX;IAAqBC,YAAY,EAACA,YAAlC;IAA+CM,WAAW,EAACA;EAA3D,CAAN;AAA8E;;AAAAU,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C,GAAuDD,OAAO,CAAC1B,8BAAR,GAAuCA,8BAA9F"}, "metadata": {}, "sourceType": "script"}