{"ast": null, "code": "import React from\"react\";import*as Styles from\"./styles\";import{jsx as _jsx}from\"react/jsx-runtime\";const ClientItem=_ref=>{let{data}=_ref;return/*#__PURE__*/_jsx(Styles.Container,{children:/*#__PURE__*/_jsx(Styles.ImageHolder,{src:data.img})});};export default ClientItem;", "map": {"version": 3, "names": ["React", "Styles", "jsx", "_jsx", "ClientItem", "_ref", "data", "Container", "children", "ImageHolder", "src", "img"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst ClientItem = ({ data }) => {\n  return (\n    <Styles.Container>\n      <Styles.ImageHolder src={data.img} />\n    </Styles.Container>\n  );\n};\n\nexport default ClientItem;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,MAAO,GAAK,CAAAC,MAAM,KAAM,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEnC,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CAC1B,mBACEF,IAAA,CAACF,MAAM,CAACM,SAAS,EAAAC,QAAA,cACfL,IAAA,CAACF,MAAM,CAACQ,WAAW,EAACC,GAAG,CAAEJ,IAAI,CAACK,GAAI,CAAE,CAAC,CACrB,CAAC,CAEvB,CAAC,CAED,cAAe,CAAAP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}