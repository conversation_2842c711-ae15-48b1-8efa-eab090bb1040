{"ast": null, "code": "module.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "exec", "error"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/fails.js"], "sourcesContent": ["module.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n"], "mappings": "AAAAA,MAAM,CAACC,OAAP,GAAiB,UAAUC,IAAV,EAAgB;EAC/B,IAAI;IACF,OAAO,CAAC,CAACA,IAAI,EAAb;EACD,CAFD,CAEE,OAAOC,KAAP,EAAc;IACd,OAAO,IAAP;EACD;AACF,CAND"}, "metadata": {}, "sourceType": "script"}