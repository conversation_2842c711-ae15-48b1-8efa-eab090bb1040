{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/card.jsx\";\nimport React from \"react\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst AboutCard = _ref => {\n  let {\n    data\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Styles.CardContainer, {\n    children: /*#__PURE__*/_jsxDEV(Styles.Wrapper, {\n      children: [/*#__PURE__*/_jsxDEV(Styles.CardTitle, {\n        children: data.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n        children: data.words\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n\n_c = AboutCard;\nexport default AboutCard;\n\nvar _c;\n\n$RefreshReg$(_c, \"AboutCard\");", "map": {"version": 3, "names": ["React", "Styles", "AboutCard", "data", "name", "words"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/card.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst AboutCard = ({ data }) => {\n  return (\n    <Styles.CardContainer>\n      <Styles.Wrapper>\n        <Styles.CardTitle>{data.name}</Styles.CardTitle>\n        <Styles.TextHolder>{data.words}</Styles.TextHolder>\n      </Styles.Wrapper>\n    </Styles.CardContainer>\n  );\n};\n\nexport default AboutCard;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,SAAS,GAAG,QAAc;EAAA,IAAb;IAAEC;EAAF,CAAa;EAC9B,oBACE,QAAC,MAAD,CAAQ,aAAR;IAAA,uBACE,QAAC,MAAD,CAAQ,OAAR;MAAA,wBACE,QAAC,MAAD,CAAQ,SAAR;QAAA,UAAmBA,IAAI,CAACC;MAAxB;QAAA;QAAA;QAAA;MAAA,QADF,eAEE,QAAC,MAAD,CAAQ,UAAR;QAAA,UAAoBD,IAAI,CAACE;MAAzB;QAAA;QAAA;QAAA;MAAA,QAFF;IAAA;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAQD,CATD;;KAAMH,S;AAWN,eAAeA,SAAf"}, "metadata": {}, "sourceType": "module"}