{"ast": null, "code": "/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n    }\n\n    var enableSchedulerDebugging = false;\n    var enableProfiling = false;\n    var frameYieldMs = 5;\n\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      siftUp(heap, node, index);\n    }\n\n    function peek(heap) {\n      return heap.length === 0 ? null : heap[0];\n    }\n\n    function pop(heap) {\n      if (heap.length === 0) {\n        return null;\n      }\n\n      var first = heap[0];\n      var last = heap.pop();\n\n      if (last !== first) {\n        heap[0] = last;\n        siftDown(heap, last, 0);\n      }\n\n      return first;\n    }\n\n    function siftUp(heap, node, i) {\n      var index = i;\n\n      while (index > 0) {\n        var parentIndex = index - 1 >>> 1;\n        var parent = heap[parentIndex];\n\n        if (compare(parent, node) > 0) {\n          // The parent is larger. Swap positions.\n          heap[parentIndex] = node;\n          heap[index] = parent;\n          index = parentIndex;\n        } else {\n          // The parent is smaller. Exit.\n          return;\n        }\n      }\n    }\n\n    function siftDown(heap, node, i) {\n      var index = i;\n      var length = heap.length;\n      var halfLength = length >>> 1;\n\n      while (index < halfLength) {\n        var leftIndex = (index + 1) * 2 - 1;\n        var left = heap[leftIndex];\n        var rightIndex = leftIndex + 1;\n        var right = heap[rightIndex]; // If the left or right node is smaller, swap with the smaller of those.\n\n        if (compare(left, node) < 0) {\n          if (rightIndex < length && compare(right, left) < 0) {\n            heap[index] = right;\n            heap[rightIndex] = node;\n            index = rightIndex;\n          } else {\n            heap[index] = left;\n            heap[leftIndex] = node;\n            index = leftIndex;\n          }\n        } else if (rightIndex < length && compare(right, node) < 0) {\n          heap[index] = right;\n          heap[rightIndex] = node;\n          index = rightIndex;\n        } else {\n          // Neither child is smaller. Exit.\n          return;\n        }\n      }\n    }\n\n    function compare(a, b) {\n      // Compare sort index first, then task id.\n      var diff = a.sortIndex - b.sortIndex;\n      return diff !== 0 ? diff : a.id - b.id;\n    } // TODO: Use symbols?\n\n\n    var ImmediatePriority = 1;\n    var UserBlockingPriority = 2;\n    var NormalPriority = 3;\n    var LowPriority = 4;\n    var IdlePriority = 5;\n\n    function markTaskErrored(task, ms) {}\n    /* eslint-disable no-var */\n\n\n    var hasPerformanceNow = typeof performance === 'object' && typeof performance.now === 'function';\n\n    if (hasPerformanceNow) {\n      var localPerformance = performance;\n\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date;\n      var initialTime = localDate.now();\n\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    } // Max 31 bit integer. The max integer size in V8 for 32-bit systems.\n    // Math.pow(2, 30) - 1\n    // 0b111111111111111111111111111111\n\n\n    var maxSigned31BitInt = 1073741823; // Times out immediately\n\n    var IMMEDIATE_PRIORITY_TIMEOUT = -1; // Eventually times out\n\n    var USER_BLOCKING_PRIORITY_TIMEOUT = 250;\n    var NORMAL_PRIORITY_TIMEOUT = 5000;\n    var LOW_PRIORITY_TIMEOUT = 10000; // Never times out\n\n    var IDLE_PRIORITY_TIMEOUT = maxSigned31BitInt; // Tasks are stored on a min heap\n\n    var taskQueue = [];\n    var timerQueue = []; // Incrementing id counter. Used to maintain insertion order.\n\n    var taskIdCounter = 1; // Pausing the scheduler is useful for debugging.\n\n    var currentTask = null;\n    var currentPriorityLevel = NormalPriority; // This is set while performing work, to prevent re-entrance.\n\n    var isPerformingWork = false;\n    var isHostCallbackScheduled = false;\n    var isHostTimeoutScheduled = false; // Capture local references to native APIs, in case a polyfill overrides them.\n\n    var localSetTimeout = typeof setTimeout === 'function' ? setTimeout : null;\n    var localClearTimeout = typeof clearTimeout === 'function' ? clearTimeout : null;\n    var localSetImmediate = typeof setImmediate !== 'undefined' ? setImmediate : null; // IE and Node.js + jsdom\n\n    var isInputPending = typeof navigator !== 'undefined' && navigator.scheduling !== undefined && navigator.scheduling.isInputPending !== undefined ? navigator.scheduling.isInputPending.bind(navigator.scheduling) : null;\n\n    function advanceTimers(currentTime) {\n      // Check for tasks that are no longer delayed and add them to the queue.\n      var timer = peek(timerQueue);\n\n      while (timer !== null) {\n        if (timer.callback === null) {\n          // Timer was cancelled.\n          pop(timerQueue);\n        } else if (timer.startTime <= currentTime) {\n          // Timer fired. Transfer to the task queue.\n          pop(timerQueue);\n          timer.sortIndex = timer.expirationTime;\n          push(taskQueue, timer);\n        } else {\n          // Remaining timers are pending.\n          return;\n        }\n\n        timer = peek(timerQueue);\n      }\n    }\n\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = false;\n      advanceTimers(currentTime);\n\n      if (!isHostCallbackScheduled) {\n        if (peek(taskQueue) !== null) {\n          isHostCallbackScheduled = true;\n          requestHostCallback(flushWork);\n        } else {\n          var firstTimer = peek(timerQueue);\n\n          if (firstTimer !== null) {\n            requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n          }\n        }\n      }\n    }\n\n    function flushWork(hasTimeRemaining, initialTime) {\n      isHostCallbackScheduled = false;\n\n      if (isHostTimeoutScheduled) {\n        // We scheduled a timeout but it's no longer needed. Cancel it.\n        isHostTimeoutScheduled = false;\n        cancelHostTimeout();\n      }\n\n      isPerformingWork = true;\n      var previousPriorityLevel = currentPriorityLevel;\n\n      try {\n        if (enableProfiling) {\n          try {\n            return workLoop(hasTimeRemaining, initialTime);\n          } catch (error) {\n            if (currentTask !== null) {\n              var currentTime = exports.unstable_now();\n              markTaskErrored(currentTask, currentTime);\n              currentTask.isQueued = false;\n            }\n\n            throw error;\n          }\n        } else {\n          // No catch in prod code path.\n          return workLoop(hasTimeRemaining, initialTime);\n        }\n      } finally {\n        currentTask = null;\n        currentPriorityLevel = previousPriorityLevel;\n        isPerformingWork = false;\n      }\n    }\n\n    function workLoop(hasTimeRemaining, initialTime) {\n      var currentTime = initialTime;\n      advanceTimers(currentTime);\n      currentTask = peek(taskQueue);\n\n      while (currentTask !== null && !enableSchedulerDebugging) {\n        if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || shouldYieldToHost())) {\n          // This currentTask hasn't expired, and we've reached the deadline.\n          break;\n        }\n\n        var callback = currentTask.callback;\n\n        if (typeof callback === 'function') {\n          currentTask.callback = null;\n          currentPriorityLevel = currentTask.priorityLevel;\n          var didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n          var continuationCallback = callback(didUserCallbackTimeout);\n          currentTime = exports.unstable_now();\n\n          if (typeof continuationCallback === 'function') {\n            currentTask.callback = continuationCallback;\n          } else {\n            if (currentTask === peek(taskQueue)) {\n              pop(taskQueue);\n            }\n          }\n\n          advanceTimers(currentTime);\n        } else {\n          pop(taskQueue);\n        }\n\n        currentTask = peek(taskQueue);\n      } // Return whether there's additional work\n\n\n      if (currentTask !== null) {\n        return true;\n      } else {\n        var firstTimer = peek(timerQueue);\n\n        if (firstTimer !== null) {\n          requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n        }\n\n        return false;\n      }\n    }\n\n    function unstable_runWithPriority(priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case ImmediatePriority:\n        case UserBlockingPriority:\n        case NormalPriority:\n        case LowPriority:\n        case IdlePriority:\n          break;\n\n        default:\n          priorityLevel = NormalPriority;\n      }\n\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    }\n\n    function unstable_next(eventHandler) {\n      var priorityLevel;\n\n      switch (currentPriorityLevel) {\n        case ImmediatePriority:\n        case UserBlockingPriority:\n        case NormalPriority:\n          // Shift down to normal priority\n          priorityLevel = NormalPriority;\n          break;\n\n        default:\n          // Anything lower than normal priority should remain at the current level.\n          priorityLevel = currentPriorityLevel;\n          break;\n      }\n\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    }\n\n    function unstable_wrapCallback(callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        // This is a fork of runWithPriority, inlined for performance.\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    }\n\n    function unstable_scheduleCallback(priorityLevel, callback, options) {\n      var currentTime = exports.unstable_now();\n      var startTime;\n\n      if (typeof options === 'object' && options !== null) {\n        var delay = options.delay;\n\n        if (typeof delay === 'number' && delay > 0) {\n          startTime = currentTime + delay;\n        } else {\n          startTime = currentTime;\n        }\n      } else {\n        startTime = currentTime;\n      }\n\n      var timeout;\n\n      switch (priorityLevel) {\n        case ImmediatePriority:\n          timeout = IMMEDIATE_PRIORITY_TIMEOUT;\n          break;\n\n        case UserBlockingPriority:\n          timeout = USER_BLOCKING_PRIORITY_TIMEOUT;\n          break;\n\n        case IdlePriority:\n          timeout = IDLE_PRIORITY_TIMEOUT;\n          break;\n\n        case LowPriority:\n          timeout = LOW_PRIORITY_TIMEOUT;\n          break;\n\n        case NormalPriority:\n        default:\n          timeout = NORMAL_PRIORITY_TIMEOUT;\n          break;\n      }\n\n      var expirationTime = startTime + timeout;\n      var newTask = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: startTime,\n        expirationTime: expirationTime,\n        sortIndex: -1\n      };\n\n      if (startTime > currentTime) {\n        // This is a delayed task.\n        newTask.sortIndex = startTime;\n        push(timerQueue, newTask);\n\n        if (peek(taskQueue) === null && newTask === peek(timerQueue)) {\n          // All tasks are delayed, and this is the task with the earliest delay.\n          if (isHostTimeoutScheduled) {\n            // Cancel an existing timeout.\n            cancelHostTimeout();\n          } else {\n            isHostTimeoutScheduled = true;\n          } // Schedule a timeout.\n\n\n          requestHostTimeout(handleTimeout, startTime - currentTime);\n        }\n      } else {\n        newTask.sortIndex = expirationTime;\n        push(taskQueue, newTask); // wait until the next time we yield.\n\n        if (!isHostCallbackScheduled && !isPerformingWork) {\n          isHostCallbackScheduled = true;\n          requestHostCallback(flushWork);\n        }\n      }\n\n      return newTask;\n    }\n\n    function unstable_pauseExecution() {}\n\n    function unstable_continueExecution() {\n      if (!isHostCallbackScheduled && !isPerformingWork) {\n        isHostCallbackScheduled = true;\n        requestHostCallback(flushWork);\n      }\n    }\n\n    function unstable_getFirstCallbackNode() {\n      return peek(taskQueue);\n    }\n\n    function unstable_cancelCallback(task) {\n      // remove from the queue because you can't remove arbitrary nodes from an\n      // array based heap, only the first one.)\n      task.callback = null;\n    }\n\n    function unstable_getCurrentPriorityLevel() {\n      return currentPriorityLevel;\n    }\n\n    var isMessageLoopRunning = false;\n    var scheduledHostCallback = null;\n    var taskTimeoutID = -1; // Scheduler periodically yields in case there is other work on the main\n    // thread, like user events. By default, it yields multiple times per frame.\n    // It does not attempt to align with frame boundaries, since most tasks don't\n    // need to be frame aligned; for those that do, use requestAnimationFrame.\n\n    var frameInterval = frameYieldMs;\n    var startTime = -1;\n\n    function shouldYieldToHost() {\n      var timeElapsed = exports.unstable_now() - startTime;\n\n      if (timeElapsed < frameInterval) {\n        // The main thread has only been blocked for a really short amount of time;\n        // smaller than a single frame. Don't yield yet.\n        return false;\n      } // The main thread has been blocked for a non-negligible amount of time. We\n\n\n      return true;\n    }\n\n    function requestPaint() {}\n\n    function forceFrameRate(fps) {\n      if (fps < 0 || fps > 125) {\n        // Using console['error'] to evade Babel and ESLint\n        console['error']('forceFrameRate takes a positive int between 0 and 125, ' + 'forcing frame rates higher than 125 fps is not supported');\n        return;\n      }\n\n      if (fps > 0) {\n        frameInterval = Math.floor(1000 / fps);\n      } else {\n        // reset the framerate\n        frameInterval = frameYieldMs;\n      }\n    }\n\n    var performWorkUntilDeadline = function () {\n      if (scheduledHostCallback !== null) {\n        var currentTime = exports.unstable_now(); // Keep track of the start time so we can measure how long the main thread\n        // has been blocked.\n\n        startTime = currentTime;\n        var hasTimeRemaining = true; // If a scheduler task throws, exit the current browser task so the\n        // error can be observed.\n        //\n        // Intentionally not using a try-catch, since that makes some debugging\n        // techniques harder. Instead, if `scheduledHostCallback` errors, then\n        // `hasMoreWork` will remain true, and we'll continue the work loop.\n\n        var hasMoreWork = true;\n\n        try {\n          hasMoreWork = scheduledHostCallback(hasTimeRemaining, currentTime);\n        } finally {\n          if (hasMoreWork) {\n            // If there's more work, schedule the next message event at the end\n            // of the preceding one.\n            schedulePerformWorkUntilDeadline();\n          } else {\n            isMessageLoopRunning = false;\n            scheduledHostCallback = null;\n          }\n        }\n      } else {\n        isMessageLoopRunning = false;\n      } // Yielding to the browser will give it a chance to paint, so we can\n\n    };\n\n    var schedulePerformWorkUntilDeadline;\n\n    if (typeof localSetImmediate === 'function') {\n      // Node.js and old IE.\n      // There's a few reasons for why we prefer setImmediate.\n      //\n      // Unlike MessageChannel, it doesn't prevent a Node.js process from exiting.\n      // (Even though this is a DOM fork of the Scheduler, you could get here\n      // with a mix of Node.js 15+, which has a MessageChannel, and jsdom.)\n      // https://github.com/facebook/react/issues/20756\n      //\n      // But also, it runs earlier which is the semantic we want.\n      // If other browsers ever implement it, it's better to use it.\n      // Although both of these would be inferior to native scheduling.\n      schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    } else if (typeof MessageChannel !== 'undefined') {\n      // DOM and Worker environments.\n      // We prefer MessageChannel because of the 4ms setTimeout clamping.\n      var channel = new MessageChannel();\n      var port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else {\n      // We should only fallback here in non-browser environments.\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    }\n\n    function requestHostCallback(callback) {\n      scheduledHostCallback = callback;\n\n      if (!isMessageLoopRunning) {\n        isMessageLoopRunning = true;\n        schedulePerformWorkUntilDeadline();\n      }\n    }\n\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n\n    function cancelHostTimeout() {\n      localClearTimeout(taskTimeoutID);\n      taskTimeoutID = -1;\n    }\n\n    var unstable_requestPaint = requestPaint;\n    var unstable_Profiling = null;\n    exports.unstable_IdlePriority = IdlePriority;\n    exports.unstable_ImmediatePriority = ImmediatePriority;\n    exports.unstable_LowPriority = LowPriority;\n    exports.unstable_NormalPriority = NormalPriority;\n    exports.unstable_Profiling = unstable_Profiling;\n    exports.unstable_UserBlockingPriority = UserBlockingPriority;\n    exports.unstable_cancelCallback = unstable_cancelCallback;\n    exports.unstable_continueExecution = unstable_continueExecution;\n    exports.unstable_forceFrameRate = forceFrameRate;\n    exports.unstable_getCurrentPriorityLevel = unstable_getCurrentPriorityLevel;\n    exports.unstable_getFirstCallbackNode = unstable_getFirstCallbackNode;\n    exports.unstable_next = unstable_next;\n    exports.unstable_pauseExecution = unstable_pauseExecution;\n    exports.unstable_requestPaint = unstable_requestPaint;\n    exports.unstable_runWithPriority = unstable_runWithPriority;\n    exports.unstable_scheduleCallback = unstable_scheduleCallback;\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = unstable_wrapCallback;\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n    }\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "registerInternalModuleStart", "Error", "enableSchedulerDebugging", "enableProfiling", "frameYieldMs", "push", "heap", "node", "index", "length", "siftUp", "peek", "pop", "first", "last", "siftDown", "i", "parentIndex", "parent", "compare", "<PERSON><PERSON><PERSON><PERSON>", "leftIndex", "left", "rightIndex", "right", "a", "b", "diff", "sortIndex", "id", "ImmediatePriority", "UserBlockingPriority", "NormalPriority", "LowPriority", "IdlePriority", "mark<PERSON><PERSON><PERSON><PERSON>red", "task", "ms", "hasPerformanceNow", "performance", "now", "localPerformance", "exports", "unstable_now", "localDate", "Date", "initialTime", "maxSigned31BitInt", "IMMEDIATE_PRIORITY_TIMEOUT", "USER_BLOCKING_PRIORITY_TIMEOUT", "NORMAL_PRIORITY_TIMEOUT", "LOW_PRIORITY_TIMEOUT", "IDLE_PRIORITY_TIMEOUT", "taskQueue", "timerQueue", "taskIdCounter", "currentTask", "currentPriorityLevel", "isPerformingWork", "isHostCallbackScheduled", "isHostTimeoutScheduled", "localSetTimeout", "setTimeout", "localClearTimeout", "clearTimeout", "localSetImmediate", "setImmediate", "isInputPending", "navigator", "scheduling", "undefined", "bind", "advanceTimers", "currentTime", "timer", "callback", "startTime", "expirationTime", "handleTimeout", "requestHostCallback", "flushWork", "firstTimer", "requestHostTimeout", "hasTimeRemaining", "cancelHostTimeout", "previousPriorityLevel", "workLoop", "error", "isQueued", "shouldYieldToHost", "priorityLevel", "didUserCallbackTimeout", "continuationCallback", "unstable_runWithPriority", "<PERSON><PERSON><PERSON><PERSON>", "unstable_next", "unstable_wrapCallback", "parentPriorityLevel", "apply", "arguments", "unstable_scheduleCallback", "options", "delay", "timeout", "newTask", "unstable_pauseExecution", "unstable_continueExecution", "unstable_getFirstCallbackNode", "unstable_cancelCallback", "unstable_getCurrentPriorityLevel", "isMessageLoopRunning", "scheduledHostCallback", "taskTimeoutID", "frameInterval", "timeElapsed", "requestPaint", "forceFrameRate", "fps", "console", "Math", "floor", "performWorkUntilDeadline", "hasMoreWork", "schedulePerformWorkUntilDeadline", "MessageChannel", "channel", "port", "port2", "port1", "onmessage", "postMessage", "unstable_requestPaint", "unstable_Profiling", "unstable_IdlePriority", "unstable_ImmediatePriority", "unstable_LowPriority", "unstable_NormalPriority", "unstable_UserBlockingPriority", "unstable_forceFrameRate", "unstable_shouldYield", "registerInternalModuleStop"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var enableSchedulerDebugging = false;\nvar enableProfiling = false;\nvar frameYieldMs = 5;\n\nfunction push(heap, node) {\n  var index = heap.length;\n  heap.push(node);\n  siftUp(heap, node, index);\n}\nfunction peek(heap) {\n  return heap.length === 0 ? null : heap[0];\n}\nfunction pop(heap) {\n  if (heap.length === 0) {\n    return null;\n  }\n\n  var first = heap[0];\n  var last = heap.pop();\n\n  if (last !== first) {\n    heap[0] = last;\n    siftDown(heap, last, 0);\n  }\n\n  return first;\n}\n\nfunction siftUp(heap, node, i) {\n  var index = i;\n\n  while (index > 0) {\n    var parentIndex = index - 1 >>> 1;\n    var parent = heap[parentIndex];\n\n    if (compare(parent, node) > 0) {\n      // The parent is larger. Swap positions.\n      heap[parentIndex] = node;\n      heap[index] = parent;\n      index = parentIndex;\n    } else {\n      // The parent is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction siftDown(heap, node, i) {\n  var index = i;\n  var length = heap.length;\n  var halfLength = length >>> 1;\n\n  while (index < halfLength) {\n    var leftIndex = (index + 1) * 2 - 1;\n    var left = heap[leftIndex];\n    var rightIndex = leftIndex + 1;\n    var right = heap[rightIndex]; // If the left or right node is smaller, swap with the smaller of those.\n\n    if (compare(left, node) < 0) {\n      if (rightIndex < length && compare(right, left) < 0) {\n        heap[index] = right;\n        heap[rightIndex] = node;\n        index = rightIndex;\n      } else {\n        heap[index] = left;\n        heap[leftIndex] = node;\n        index = leftIndex;\n      }\n    } else if (rightIndex < length && compare(right, node) < 0) {\n      heap[index] = right;\n      heap[rightIndex] = node;\n      index = rightIndex;\n    } else {\n      // Neither child is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction compare(a, b) {\n  // Compare sort index first, then task id.\n  var diff = a.sortIndex - b.sortIndex;\n  return diff !== 0 ? diff : a.id - b.id;\n}\n\n// TODO: Use symbols?\nvar ImmediatePriority = 1;\nvar UserBlockingPriority = 2;\nvar NormalPriority = 3;\nvar LowPriority = 4;\nvar IdlePriority = 5;\n\nfunction markTaskErrored(task, ms) {\n}\n\n/* eslint-disable no-var */\n\nvar hasPerformanceNow = typeof performance === 'object' && typeof performance.now === 'function';\n\nif (hasPerformanceNow) {\n  var localPerformance = performance;\n\n  exports.unstable_now = function () {\n    return localPerformance.now();\n  };\n} else {\n  var localDate = Date;\n  var initialTime = localDate.now();\n\n  exports.unstable_now = function () {\n    return localDate.now() - initialTime;\n  };\n} // Max 31 bit integer. The max integer size in V8 for 32-bit systems.\n// Math.pow(2, 30) - 1\n// 0b111111111111111111111111111111\n\n\nvar maxSigned31BitInt = 1073741823; // Times out immediately\n\nvar IMMEDIATE_PRIORITY_TIMEOUT = -1; // Eventually times out\n\nvar USER_BLOCKING_PRIORITY_TIMEOUT = 250;\nvar NORMAL_PRIORITY_TIMEOUT = 5000;\nvar LOW_PRIORITY_TIMEOUT = 10000; // Never times out\n\nvar IDLE_PRIORITY_TIMEOUT = maxSigned31BitInt; // Tasks are stored on a min heap\n\nvar taskQueue = [];\nvar timerQueue = []; // Incrementing id counter. Used to maintain insertion order.\n\nvar taskIdCounter = 1; // Pausing the scheduler is useful for debugging.\nvar currentTask = null;\nvar currentPriorityLevel = NormalPriority; // This is set while performing work, to prevent re-entrance.\n\nvar isPerformingWork = false;\nvar isHostCallbackScheduled = false;\nvar isHostTimeoutScheduled = false; // Capture local references to native APIs, in case a polyfill overrides them.\n\nvar localSetTimeout = typeof setTimeout === 'function' ? setTimeout : null;\nvar localClearTimeout = typeof clearTimeout === 'function' ? clearTimeout : null;\nvar localSetImmediate = typeof setImmediate !== 'undefined' ? setImmediate : null; // IE and Node.js + jsdom\n\nvar isInputPending = typeof navigator !== 'undefined' && navigator.scheduling !== undefined && navigator.scheduling.isInputPending !== undefined ? navigator.scheduling.isInputPending.bind(navigator.scheduling) : null;\n\nfunction advanceTimers(currentTime) {\n  // Check for tasks that are no longer delayed and add them to the queue.\n  var timer = peek(timerQueue);\n\n  while (timer !== null) {\n    if (timer.callback === null) {\n      // Timer was cancelled.\n      pop(timerQueue);\n    } else if (timer.startTime <= currentTime) {\n      // Timer fired. Transfer to the task queue.\n      pop(timerQueue);\n      timer.sortIndex = timer.expirationTime;\n      push(taskQueue, timer);\n    } else {\n      // Remaining timers are pending.\n      return;\n    }\n\n    timer = peek(timerQueue);\n  }\n}\n\nfunction handleTimeout(currentTime) {\n  isHostTimeoutScheduled = false;\n  advanceTimers(currentTime);\n\n  if (!isHostCallbackScheduled) {\n    if (peek(taskQueue) !== null) {\n      isHostCallbackScheduled = true;\n      requestHostCallback(flushWork);\n    } else {\n      var firstTimer = peek(timerQueue);\n\n      if (firstTimer !== null) {\n        requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n      }\n    }\n  }\n}\n\nfunction flushWork(hasTimeRemaining, initialTime) {\n\n\n  isHostCallbackScheduled = false;\n\n  if (isHostTimeoutScheduled) {\n    // We scheduled a timeout but it's no longer needed. Cancel it.\n    isHostTimeoutScheduled = false;\n    cancelHostTimeout();\n  }\n\n  isPerformingWork = true;\n  var previousPriorityLevel = currentPriorityLevel;\n\n  try {\n    if (enableProfiling) {\n      try {\n        return workLoop(hasTimeRemaining, initialTime);\n      } catch (error) {\n        if (currentTask !== null) {\n          var currentTime = exports.unstable_now();\n          markTaskErrored(currentTask, currentTime);\n          currentTask.isQueued = false;\n        }\n\n        throw error;\n      }\n    } else {\n      // No catch in prod code path.\n      return workLoop(hasTimeRemaining, initialTime);\n    }\n  } finally {\n    currentTask = null;\n    currentPriorityLevel = previousPriorityLevel;\n    isPerformingWork = false;\n  }\n}\n\nfunction workLoop(hasTimeRemaining, initialTime) {\n  var currentTime = initialTime;\n  advanceTimers(currentTime);\n  currentTask = peek(taskQueue);\n\n  while (currentTask !== null && !(enableSchedulerDebugging )) {\n    if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || shouldYieldToHost())) {\n      // This currentTask hasn't expired, and we've reached the deadline.\n      break;\n    }\n\n    var callback = currentTask.callback;\n\n    if (typeof callback === 'function') {\n      currentTask.callback = null;\n      currentPriorityLevel = currentTask.priorityLevel;\n      var didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n\n      var continuationCallback = callback(didUserCallbackTimeout);\n      currentTime = exports.unstable_now();\n\n      if (typeof continuationCallback === 'function') {\n        currentTask.callback = continuationCallback;\n      } else {\n\n        if (currentTask === peek(taskQueue)) {\n          pop(taskQueue);\n        }\n      }\n\n      advanceTimers(currentTime);\n    } else {\n      pop(taskQueue);\n    }\n\n    currentTask = peek(taskQueue);\n  } // Return whether there's additional work\n\n\n  if (currentTask !== null) {\n    return true;\n  } else {\n    var firstTimer = peek(timerQueue);\n\n    if (firstTimer !== null) {\n      requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n    }\n\n    return false;\n  }\n}\n\nfunction unstable_runWithPriority(priorityLevel, eventHandler) {\n  switch (priorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n    case LowPriority:\n    case IdlePriority:\n      break;\n\n    default:\n      priorityLevel = NormalPriority;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_next(eventHandler) {\n  var priorityLevel;\n\n  switch (currentPriorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n      // Shift down to normal priority\n      priorityLevel = NormalPriority;\n      break;\n\n    default:\n      // Anything lower than normal priority should remain at the current level.\n      priorityLevel = currentPriorityLevel;\n      break;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_wrapCallback(callback) {\n  var parentPriorityLevel = currentPriorityLevel;\n  return function () {\n    // This is a fork of runWithPriority, inlined for performance.\n    var previousPriorityLevel = currentPriorityLevel;\n    currentPriorityLevel = parentPriorityLevel;\n\n    try {\n      return callback.apply(this, arguments);\n    } finally {\n      currentPriorityLevel = previousPriorityLevel;\n    }\n  };\n}\n\nfunction unstable_scheduleCallback(priorityLevel, callback, options) {\n  var currentTime = exports.unstable_now();\n  var startTime;\n\n  if (typeof options === 'object' && options !== null) {\n    var delay = options.delay;\n\n    if (typeof delay === 'number' && delay > 0) {\n      startTime = currentTime + delay;\n    } else {\n      startTime = currentTime;\n    }\n  } else {\n    startTime = currentTime;\n  }\n\n  var timeout;\n\n  switch (priorityLevel) {\n    case ImmediatePriority:\n      timeout = IMMEDIATE_PRIORITY_TIMEOUT;\n      break;\n\n    case UserBlockingPriority:\n      timeout = USER_BLOCKING_PRIORITY_TIMEOUT;\n      break;\n\n    case IdlePriority:\n      timeout = IDLE_PRIORITY_TIMEOUT;\n      break;\n\n    case LowPriority:\n      timeout = LOW_PRIORITY_TIMEOUT;\n      break;\n\n    case NormalPriority:\n    default:\n      timeout = NORMAL_PRIORITY_TIMEOUT;\n      break;\n  }\n\n  var expirationTime = startTime + timeout;\n  var newTask = {\n    id: taskIdCounter++,\n    callback: callback,\n    priorityLevel: priorityLevel,\n    startTime: startTime,\n    expirationTime: expirationTime,\n    sortIndex: -1\n  };\n\n  if (startTime > currentTime) {\n    // This is a delayed task.\n    newTask.sortIndex = startTime;\n    push(timerQueue, newTask);\n\n    if (peek(taskQueue) === null && newTask === peek(timerQueue)) {\n      // All tasks are delayed, and this is the task with the earliest delay.\n      if (isHostTimeoutScheduled) {\n        // Cancel an existing timeout.\n        cancelHostTimeout();\n      } else {\n        isHostTimeoutScheduled = true;\n      } // Schedule a timeout.\n\n\n      requestHostTimeout(handleTimeout, startTime - currentTime);\n    }\n  } else {\n    newTask.sortIndex = expirationTime;\n    push(taskQueue, newTask);\n    // wait until the next time we yield.\n\n\n    if (!isHostCallbackScheduled && !isPerformingWork) {\n      isHostCallbackScheduled = true;\n      requestHostCallback(flushWork);\n    }\n  }\n\n  return newTask;\n}\n\nfunction unstable_pauseExecution() {\n}\n\nfunction unstable_continueExecution() {\n\n  if (!isHostCallbackScheduled && !isPerformingWork) {\n    isHostCallbackScheduled = true;\n    requestHostCallback(flushWork);\n  }\n}\n\nfunction unstable_getFirstCallbackNode() {\n  return peek(taskQueue);\n}\n\nfunction unstable_cancelCallback(task) {\n  // remove from the queue because you can't remove arbitrary nodes from an\n  // array based heap, only the first one.)\n\n\n  task.callback = null;\n}\n\nfunction unstable_getCurrentPriorityLevel() {\n  return currentPriorityLevel;\n}\n\nvar isMessageLoopRunning = false;\nvar scheduledHostCallback = null;\nvar taskTimeoutID = -1; // Scheduler periodically yields in case there is other work on the main\n// thread, like user events. By default, it yields multiple times per frame.\n// It does not attempt to align with frame boundaries, since most tasks don't\n// need to be frame aligned; for those that do, use requestAnimationFrame.\n\nvar frameInterval = frameYieldMs;\nvar startTime = -1;\n\nfunction shouldYieldToHost() {\n  var timeElapsed = exports.unstable_now() - startTime;\n\n  if (timeElapsed < frameInterval) {\n    // The main thread has only been blocked for a really short amount of time;\n    // smaller than a single frame. Don't yield yet.\n    return false;\n  } // The main thread has been blocked for a non-negligible amount of time. We\n\n\n  return true;\n}\n\nfunction requestPaint() {\n\n}\n\nfunction forceFrameRate(fps) {\n  if (fps < 0 || fps > 125) {\n    // Using console['error'] to evade Babel and ESLint\n    console['error']('forceFrameRate takes a positive int between 0 and 125, ' + 'forcing frame rates higher than 125 fps is not supported');\n    return;\n  }\n\n  if (fps > 0) {\n    frameInterval = Math.floor(1000 / fps);\n  } else {\n    // reset the framerate\n    frameInterval = frameYieldMs;\n  }\n}\n\nvar performWorkUntilDeadline = function () {\n  if (scheduledHostCallback !== null) {\n    var currentTime = exports.unstable_now(); // Keep track of the start time so we can measure how long the main thread\n    // has been blocked.\n\n    startTime = currentTime;\n    var hasTimeRemaining = true; // If a scheduler task throws, exit the current browser task so the\n    // error can be observed.\n    //\n    // Intentionally not using a try-catch, since that makes some debugging\n    // techniques harder. Instead, if `scheduledHostCallback` errors, then\n    // `hasMoreWork` will remain true, and we'll continue the work loop.\n\n    var hasMoreWork = true;\n\n    try {\n      hasMoreWork = scheduledHostCallback(hasTimeRemaining, currentTime);\n    } finally {\n      if (hasMoreWork) {\n        // If there's more work, schedule the next message event at the end\n        // of the preceding one.\n        schedulePerformWorkUntilDeadline();\n      } else {\n        isMessageLoopRunning = false;\n        scheduledHostCallback = null;\n      }\n    }\n  } else {\n    isMessageLoopRunning = false;\n  } // Yielding to the browser will give it a chance to paint, so we can\n};\n\nvar schedulePerformWorkUntilDeadline;\n\nif (typeof localSetImmediate === 'function') {\n  // Node.js and old IE.\n  // There's a few reasons for why we prefer setImmediate.\n  //\n  // Unlike MessageChannel, it doesn't prevent a Node.js process from exiting.\n  // (Even though this is a DOM fork of the Scheduler, you could get here\n  // with a mix of Node.js 15+, which has a MessageChannel, and jsdom.)\n  // https://github.com/facebook/react/issues/20756\n  //\n  // But also, it runs earlier which is the semantic we want.\n  // If other browsers ever implement it, it's better to use it.\n  // Although both of these would be inferior to native scheduling.\n  schedulePerformWorkUntilDeadline = function () {\n    localSetImmediate(performWorkUntilDeadline);\n  };\n} else if (typeof MessageChannel !== 'undefined') {\n  // DOM and Worker environments.\n  // We prefer MessageChannel because of the 4ms setTimeout clamping.\n  var channel = new MessageChannel();\n  var port = channel.port2;\n  channel.port1.onmessage = performWorkUntilDeadline;\n\n  schedulePerformWorkUntilDeadline = function () {\n    port.postMessage(null);\n  };\n} else {\n  // We should only fallback here in non-browser environments.\n  schedulePerformWorkUntilDeadline = function () {\n    localSetTimeout(performWorkUntilDeadline, 0);\n  };\n}\n\nfunction requestHostCallback(callback) {\n  scheduledHostCallback = callback;\n\n  if (!isMessageLoopRunning) {\n    isMessageLoopRunning = true;\n    schedulePerformWorkUntilDeadline();\n  }\n}\n\nfunction requestHostTimeout(callback, ms) {\n  taskTimeoutID = localSetTimeout(function () {\n    callback(exports.unstable_now());\n  }, ms);\n}\n\nfunction cancelHostTimeout() {\n  localClearTimeout(taskTimeoutID);\n  taskTimeoutID = -1;\n}\n\nvar unstable_requestPaint = requestPaint;\nvar unstable_Profiling =  null;\n\nexports.unstable_IdlePriority = IdlePriority;\nexports.unstable_ImmediatePriority = ImmediatePriority;\nexports.unstable_LowPriority = LowPriority;\nexports.unstable_NormalPriority = NormalPriority;\nexports.unstable_Profiling = unstable_Profiling;\nexports.unstable_UserBlockingPriority = UserBlockingPriority;\nexports.unstable_cancelCallback = unstable_cancelCallback;\nexports.unstable_continueExecution = unstable_continueExecution;\nexports.unstable_forceFrameRate = forceFrameRate;\nexports.unstable_getCurrentPriorityLevel = unstable_getCurrentPriorityLevel;\nexports.unstable_getFirstCallbackNode = unstable_getFirstCallbackNode;\nexports.unstable_next = unstable_next;\nexports.unstable_pauseExecution = unstable_pauseExecution;\nexports.unstable_requestPaint = unstable_requestPaint;\nexports.unstable_runWithPriority = unstable_runWithPriority;\nexports.unstable_scheduleCallback = unstable_scheduleCallback;\nexports.unstable_shouldYield = shouldYieldToHost;\nexports.unstable_wrapCallback = unstable_wrapCallback;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA,IAAIA,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzC,CAAC,YAAW;IAEJ;IAEV;;IACA,IACE,OAAOC,8BAAP,KAA0C,WAA1C,IACA,OAAOA,8BAA8B,CAACC,2BAAtC,KACE,UAHJ,EAIE;MACAD,8BAA8B,CAACC,2BAA/B,CAA2D,IAAIC,KAAJ,EAA3D;IACD;;IACS,IAAIC,wBAAwB,GAAG,KAA/B;IACV,IAAIC,eAAe,GAAG,KAAtB;IACA,IAAIC,YAAY,GAAG,CAAnB;;IAEA,SAASC,IAAT,CAAcC,IAAd,EAAoBC,IAApB,EAA0B;MACxB,IAAIC,KAAK,GAAGF,IAAI,CAACG,MAAjB;MACAH,IAAI,CAACD,IAAL,CAAUE,IAAV;MACAG,MAAM,CAACJ,IAAD,EAAOC,IAAP,EAAaC,KAAb,CAAN;IACD;;IACD,SAASG,IAAT,CAAcL,IAAd,EAAoB;MAClB,OAAOA,IAAI,CAACG,MAAL,KAAgB,CAAhB,GAAoB,IAApB,GAA2BH,IAAI,CAAC,CAAD,CAAtC;IACD;;IACD,SAASM,GAAT,CAAaN,IAAb,EAAmB;MACjB,IAAIA,IAAI,CAACG,MAAL,KAAgB,CAApB,EAAuB;QACrB,OAAO,IAAP;MACD;;MAED,IAAII,KAAK,GAAGP,IAAI,CAAC,CAAD,CAAhB;MACA,IAAIQ,IAAI,GAAGR,IAAI,CAACM,GAAL,EAAX;;MAEA,IAAIE,IAAI,KAAKD,KAAb,EAAoB;QAClBP,IAAI,CAAC,CAAD,CAAJ,GAAUQ,IAAV;QACAC,QAAQ,CAACT,IAAD,EAAOQ,IAAP,EAAa,CAAb,CAAR;MACD;;MAED,OAAOD,KAAP;IACD;;IAED,SAASH,MAAT,CAAgBJ,IAAhB,EAAsBC,IAAtB,EAA4BS,CAA5B,EAA+B;MAC7B,IAAIR,KAAK,GAAGQ,CAAZ;;MAEA,OAAOR,KAAK,GAAG,CAAf,EAAkB;QAChB,IAAIS,WAAW,GAAGT,KAAK,GAAG,CAAR,KAAc,CAAhC;QACA,IAAIU,MAAM,GAAGZ,IAAI,CAACW,WAAD,CAAjB;;QAEA,IAAIE,OAAO,CAACD,MAAD,EAASX,IAAT,CAAP,GAAwB,CAA5B,EAA+B;UAC7B;UACAD,IAAI,CAACW,WAAD,CAAJ,GAAoBV,IAApB;UACAD,IAAI,CAACE,KAAD,CAAJ,GAAcU,MAAd;UACAV,KAAK,GAAGS,WAAR;QACD,CALD,MAKO;UACL;UACA;QACD;MACF;IACF;;IAED,SAASF,QAAT,CAAkBT,IAAlB,EAAwBC,IAAxB,EAA8BS,CAA9B,EAAiC;MAC/B,IAAIR,KAAK,GAAGQ,CAAZ;MACA,IAAIP,MAAM,GAAGH,IAAI,CAACG,MAAlB;MACA,IAAIW,UAAU,GAAGX,MAAM,KAAK,CAA5B;;MAEA,OAAOD,KAAK,GAAGY,UAAf,EAA2B;QACzB,IAAIC,SAAS,GAAG,CAACb,KAAK,GAAG,CAAT,IAAc,CAAd,GAAkB,CAAlC;QACA,IAAIc,IAAI,GAAGhB,IAAI,CAACe,SAAD,CAAf;QACA,IAAIE,UAAU,GAAGF,SAAS,GAAG,CAA7B;QACA,IAAIG,KAAK,GAAGlB,IAAI,CAACiB,UAAD,CAAhB,CAJyB,CAIK;;QAE9B,IAAIJ,OAAO,CAACG,IAAD,EAAOf,IAAP,CAAP,GAAsB,CAA1B,EAA6B;UAC3B,IAAIgB,UAAU,GAAGd,MAAb,IAAuBU,OAAO,CAACK,KAAD,EAAQF,IAAR,CAAP,GAAuB,CAAlD,EAAqD;YACnDhB,IAAI,CAACE,KAAD,CAAJ,GAAcgB,KAAd;YACAlB,IAAI,CAACiB,UAAD,CAAJ,GAAmBhB,IAAnB;YACAC,KAAK,GAAGe,UAAR;UACD,CAJD,MAIO;YACLjB,IAAI,CAACE,KAAD,CAAJ,GAAcc,IAAd;YACAhB,IAAI,CAACe,SAAD,CAAJ,GAAkBd,IAAlB;YACAC,KAAK,GAAGa,SAAR;UACD;QACF,CAVD,MAUO,IAAIE,UAAU,GAAGd,MAAb,IAAuBU,OAAO,CAACK,KAAD,EAAQjB,IAAR,CAAP,GAAuB,CAAlD,EAAqD;UAC1DD,IAAI,CAACE,KAAD,CAAJ,GAAcgB,KAAd;UACAlB,IAAI,CAACiB,UAAD,CAAJ,GAAmBhB,IAAnB;UACAC,KAAK,GAAGe,UAAR;QACD,CAJM,MAIA;UACL;UACA;QACD;MACF;IACF;;IAED,SAASJ,OAAT,CAAiBM,CAAjB,EAAoBC,CAApB,EAAuB;MACrB;MACA,IAAIC,IAAI,GAAGF,CAAC,CAACG,SAAF,GAAcF,CAAC,CAACE,SAA3B;MACA,OAAOD,IAAI,KAAK,CAAT,GAAaA,IAAb,GAAoBF,CAAC,CAACI,EAAF,GAAOH,CAAC,CAACG,EAApC;IACD,CA/Fa,CAiGd;;;IACA,IAAIC,iBAAiB,GAAG,CAAxB;IACA,IAAIC,oBAAoB,GAAG,CAA3B;IACA,IAAIC,cAAc,GAAG,CAArB;IACA,IAAIC,WAAW,GAAG,CAAlB;IACA,IAAIC,YAAY,GAAG,CAAnB;;IAEA,SAASC,eAAT,CAAyBC,IAAzB,EAA+BC,EAA/B,EAAmC,CAClC;IAED;;;IAEA,IAAIC,iBAAiB,GAAG,OAAOC,WAAP,KAAuB,QAAvB,IAAmC,OAAOA,WAAW,CAACC,GAAnB,KAA2B,UAAtF;;IAEA,IAAIF,iBAAJ,EAAuB;MACrB,IAAIG,gBAAgB,GAAGF,WAAvB;;MAEAG,OAAO,CAACC,YAAR,GAAuB,YAAY;QACjC,OAAOF,gBAAgB,CAACD,GAAjB,EAAP;MACD,CAFD;IAGD,CAND,MAMO;MACL,IAAII,SAAS,GAAGC,IAAhB;MACA,IAAIC,WAAW,GAAGF,SAAS,CAACJ,GAAV,EAAlB;;MAEAE,OAAO,CAACC,YAAR,GAAuB,YAAY;QACjC,OAAOC,SAAS,CAACJ,GAAV,KAAkBM,WAAzB;MACD,CAFD;IAGD,CA5Ha,CA4HZ;IACF;IACA;;;IAGA,IAAIC,iBAAiB,GAAG,UAAxB,CAjIc,CAiIsB;;IAEpC,IAAIC,0BAA0B,GAAG,CAAC,CAAlC,CAnIc,CAmIuB;;IAErC,IAAIC,8BAA8B,GAAG,GAArC;IACA,IAAIC,uBAAuB,GAAG,IAA9B;IACA,IAAIC,oBAAoB,GAAG,KAA3B,CAvIc,CAuIoB;;IAElC,IAAIC,qBAAqB,GAAGL,iBAA5B,CAzIc,CAyIiC;;IAE/C,IAAIM,SAAS,GAAG,EAAhB;IACA,IAAIC,UAAU,GAAG,EAAjB,CA5Ic,CA4IO;;IAErB,IAAIC,aAAa,GAAG,CAApB,CA9Ic,CA8IS;;IACvB,IAAIC,WAAW,GAAG,IAAlB;IACA,IAAIC,oBAAoB,GAAGzB,cAA3B,CAhJc,CAgJ6B;;IAE3C,IAAI0B,gBAAgB,GAAG,KAAvB;IACA,IAAIC,uBAAuB,GAAG,KAA9B;IACA,IAAIC,sBAAsB,GAAG,KAA7B,CApJc,CAoJsB;;IAEpC,IAAIC,eAAe,GAAG,OAAOC,UAAP,KAAsB,UAAtB,GAAmCA,UAAnC,GAAgD,IAAtE;IACA,IAAIC,iBAAiB,GAAG,OAAOC,YAAP,KAAwB,UAAxB,GAAqCA,YAArC,GAAoD,IAA5E;IACA,IAAIC,iBAAiB,GAAG,OAAOC,YAAP,KAAwB,WAAxB,GAAsCA,YAAtC,GAAqD,IAA7E,CAxJc,CAwJqE;;IAEnF,IAAIC,cAAc,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,UAAV,KAAyBC,SAA7D,IAA0EF,SAAS,CAACC,UAAV,CAAqBF,cAArB,KAAwCG,SAAlH,GAA8HF,SAAS,CAACC,UAAV,CAAqBF,cAArB,CAAoCI,IAApC,CAAyCH,SAAS,CAACC,UAAnD,CAA9H,GAA+L,IAApN;;IAEA,SAASG,aAAT,CAAuBC,WAAvB,EAAoC;MAClC;MACA,IAAIC,KAAK,GAAG/D,IAAI,CAAC2C,UAAD,CAAhB;;MAEA,OAAOoB,KAAK,KAAK,IAAjB,EAAuB;QACrB,IAAIA,KAAK,CAACC,QAAN,KAAmB,IAAvB,EAA6B;UAC3B;UACA/D,GAAG,CAAC0C,UAAD,CAAH;QACD,CAHD,MAGO,IAAIoB,KAAK,CAACE,SAAN,IAAmBH,WAAvB,EAAoC;UACzC;UACA7D,GAAG,CAAC0C,UAAD,CAAH;UACAoB,KAAK,CAAC9C,SAAN,GAAkB8C,KAAK,CAACG,cAAxB;UACAxE,IAAI,CAACgD,SAAD,EAAYqB,KAAZ,CAAJ;QACD,CALM,MAKA;UACL;UACA;QACD;;QAEDA,KAAK,GAAG/D,IAAI,CAAC2C,UAAD,CAAZ;MACD;IACF;;IAED,SAASwB,aAAT,CAAuBL,WAAvB,EAAoC;MAClCb,sBAAsB,GAAG,KAAzB;MACAY,aAAa,CAACC,WAAD,CAAb;;MAEA,IAAI,CAACd,uBAAL,EAA8B;QAC5B,IAAIhD,IAAI,CAAC0C,SAAD,CAAJ,KAAoB,IAAxB,EAA8B;UAC5BM,uBAAuB,GAAG,IAA1B;UACAoB,mBAAmB,CAACC,SAAD,CAAnB;QACD,CAHD,MAGO;UACL,IAAIC,UAAU,GAAGtE,IAAI,CAAC2C,UAAD,CAArB;;UAEA,IAAI2B,UAAU,KAAK,IAAnB,EAAyB;YACvBC,kBAAkB,CAACJ,aAAD,EAAgBG,UAAU,CAACL,SAAX,GAAuBH,WAAvC,CAAlB;UACD;QACF;MACF;IACF;;IAED,SAASO,SAAT,CAAmBG,gBAAnB,EAAqCrC,WAArC,EAAkD;MAGhDa,uBAAuB,GAAG,KAA1B;;MAEA,IAAIC,sBAAJ,EAA4B;QAC1B;QACAA,sBAAsB,GAAG,KAAzB;QACAwB,iBAAiB;MAClB;;MAED1B,gBAAgB,GAAG,IAAnB;MACA,IAAI2B,qBAAqB,GAAG5B,oBAA5B;;MAEA,IAAI;QACF,IAAItD,eAAJ,EAAqB;UACnB,IAAI;YACF,OAAOmF,QAAQ,CAACH,gBAAD,EAAmBrC,WAAnB,CAAf;UACD,CAFD,CAEE,OAAOyC,KAAP,EAAc;YACd,IAAI/B,WAAW,KAAK,IAApB,EAA0B;cACxB,IAAIiB,WAAW,GAAG/B,OAAO,CAACC,YAAR,EAAlB;cACAR,eAAe,CAACqB,WAAD,EAAciB,WAAd,CAAf;cACAjB,WAAW,CAACgC,QAAZ,GAAuB,KAAvB;YACD;;YAED,MAAMD,KAAN;UACD;QACF,CAZD,MAYO;UACL;UACA,OAAOD,QAAQ,CAACH,gBAAD,EAAmBrC,WAAnB,CAAf;QACD;MACF,CAjBD,SAiBU;QACRU,WAAW,GAAG,IAAd;QACAC,oBAAoB,GAAG4B,qBAAvB;QACA3B,gBAAgB,GAAG,KAAnB;MACD;IACF;;IAED,SAAS4B,QAAT,CAAkBH,gBAAlB,EAAoCrC,WAApC,EAAiD;MAC/C,IAAI2B,WAAW,GAAG3B,WAAlB;MACA0B,aAAa,CAACC,WAAD,CAAb;MACAjB,WAAW,GAAG7C,IAAI,CAAC0C,SAAD,CAAlB;;MAEA,OAAOG,WAAW,KAAK,IAAhB,IAAwB,CAAEtD,wBAAjC,EAA6D;QAC3D,IAAIsD,WAAW,CAACqB,cAAZ,GAA6BJ,WAA7B,KAA6C,CAACU,gBAAD,IAAqBM,iBAAiB,EAAnF,CAAJ,EAA4F;UAC1F;UACA;QACD;;QAED,IAAId,QAAQ,GAAGnB,WAAW,CAACmB,QAA3B;;QAEA,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;UAClCnB,WAAW,CAACmB,QAAZ,GAAuB,IAAvB;UACAlB,oBAAoB,GAAGD,WAAW,CAACkC,aAAnC;UACA,IAAIC,sBAAsB,GAAGnC,WAAW,CAACqB,cAAZ,IAA8BJ,WAA3D;UAEA,IAAImB,oBAAoB,GAAGjB,QAAQ,CAACgB,sBAAD,CAAnC;UACAlB,WAAW,GAAG/B,OAAO,CAACC,YAAR,EAAd;;UAEA,IAAI,OAAOiD,oBAAP,KAAgC,UAApC,EAAgD;YAC9CpC,WAAW,CAACmB,QAAZ,GAAuBiB,oBAAvB;UACD,CAFD,MAEO;YAEL,IAAIpC,WAAW,KAAK7C,IAAI,CAAC0C,SAAD,CAAxB,EAAqC;cACnCzC,GAAG,CAACyC,SAAD,CAAH;YACD;UACF;;UAEDmB,aAAa,CAACC,WAAD,CAAb;QACD,CAlBD,MAkBO;UACL7D,GAAG,CAACyC,SAAD,CAAH;QACD;;QAEDG,WAAW,GAAG7C,IAAI,CAAC0C,SAAD,CAAlB;MACD,CApC8C,CAoC7C;;;MAGF,IAAIG,WAAW,KAAK,IAApB,EAA0B;QACxB,OAAO,IAAP;MACD,CAFD,MAEO;QACL,IAAIyB,UAAU,GAAGtE,IAAI,CAAC2C,UAAD,CAArB;;QAEA,IAAI2B,UAAU,KAAK,IAAnB,EAAyB;UACvBC,kBAAkB,CAACJ,aAAD,EAAgBG,UAAU,CAACL,SAAX,GAAuBH,WAAvC,CAAlB;QACD;;QAED,OAAO,KAAP;MACD;IACF;;IAED,SAASoB,wBAAT,CAAkCH,aAAlC,EAAiDI,YAAjD,EAA+D;MAC7D,QAAQJ,aAAR;QACE,KAAK5D,iBAAL;QACA,KAAKC,oBAAL;QACA,KAAKC,cAAL;QACA,KAAKC,WAAL;QACA,KAAKC,YAAL;UACE;;QAEF;UACEwD,aAAa,GAAG1D,cAAhB;MATJ;;MAYA,IAAIqD,qBAAqB,GAAG5B,oBAA5B;MACAA,oBAAoB,GAAGiC,aAAvB;;MAEA,IAAI;QACF,OAAOI,YAAY,EAAnB;MACD,CAFD,SAEU;QACRrC,oBAAoB,GAAG4B,qBAAvB;MACD;IACF;;IAED,SAASU,aAAT,CAAuBD,YAAvB,EAAqC;MACnC,IAAIJ,aAAJ;;MAEA,QAAQjC,oBAAR;QACE,KAAK3B,iBAAL;QACA,KAAKC,oBAAL;QACA,KAAKC,cAAL;UACE;UACA0D,aAAa,GAAG1D,cAAhB;UACA;;QAEF;UACE;UACA0D,aAAa,GAAGjC,oBAAhB;UACA;MAXJ;;MAcA,IAAI4B,qBAAqB,GAAG5B,oBAA5B;MACAA,oBAAoB,GAAGiC,aAAvB;;MAEA,IAAI;QACF,OAAOI,YAAY,EAAnB;MACD,CAFD,SAEU;QACRrC,oBAAoB,GAAG4B,qBAAvB;MACD;IACF;;IAED,SAASW,qBAAT,CAA+BrB,QAA/B,EAAyC;MACvC,IAAIsB,mBAAmB,GAAGxC,oBAA1B;MACA,OAAO,YAAY;QACjB;QACA,IAAI4B,qBAAqB,GAAG5B,oBAA5B;QACAA,oBAAoB,GAAGwC,mBAAvB;;QAEA,IAAI;UACF,OAAOtB,QAAQ,CAACuB,KAAT,CAAe,IAAf,EAAqBC,SAArB,CAAP;QACD,CAFD,SAEU;UACR1C,oBAAoB,GAAG4B,qBAAvB;QACD;MACF,CAVD;IAWD;;IAED,SAASe,yBAAT,CAAmCV,aAAnC,EAAkDf,QAAlD,EAA4D0B,OAA5D,EAAqE;MACnE,IAAI5B,WAAW,GAAG/B,OAAO,CAACC,YAAR,EAAlB;MACA,IAAIiC,SAAJ;;MAEA,IAAI,OAAOyB,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,KAAK,IAA/C,EAAqD;QACnD,IAAIC,KAAK,GAAGD,OAAO,CAACC,KAApB;;QAEA,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,GAAG,CAAzC,EAA4C;UAC1C1B,SAAS,GAAGH,WAAW,GAAG6B,KAA1B;QACD,CAFD,MAEO;UACL1B,SAAS,GAAGH,WAAZ;QACD;MACF,CARD,MAQO;QACLG,SAAS,GAAGH,WAAZ;MACD;;MAED,IAAI8B,OAAJ;;MAEA,QAAQb,aAAR;QACE,KAAK5D,iBAAL;UACEyE,OAAO,GAAGvD,0BAAV;UACA;;QAEF,KAAKjB,oBAAL;UACEwE,OAAO,GAAGtD,8BAAV;UACA;;QAEF,KAAKf,YAAL;UACEqE,OAAO,GAAGnD,qBAAV;UACA;;QAEF,KAAKnB,WAAL;UACEsE,OAAO,GAAGpD,oBAAV;UACA;;QAEF,KAAKnB,cAAL;QACA;UACEuE,OAAO,GAAGrD,uBAAV;UACA;MApBJ;;MAuBA,IAAI2B,cAAc,GAAGD,SAAS,GAAG2B,OAAjC;MACA,IAAIC,OAAO,GAAG;QACZ3E,EAAE,EAAE0B,aAAa,EADL;QAEZoB,QAAQ,EAAEA,QAFE;QAGZe,aAAa,EAAEA,aAHH;QAIZd,SAAS,EAAEA,SAJC;QAKZC,cAAc,EAAEA,cALJ;QAMZjD,SAAS,EAAE,CAAC;MANA,CAAd;;MASA,IAAIgD,SAAS,GAAGH,WAAhB,EAA6B;QAC3B;QACA+B,OAAO,CAAC5E,SAAR,GAAoBgD,SAApB;QACAvE,IAAI,CAACiD,UAAD,EAAakD,OAAb,CAAJ;;QAEA,IAAI7F,IAAI,CAAC0C,SAAD,CAAJ,KAAoB,IAApB,IAA4BmD,OAAO,KAAK7F,IAAI,CAAC2C,UAAD,CAAhD,EAA8D;UAC5D;UACA,IAAIM,sBAAJ,EAA4B;YAC1B;YACAwB,iBAAiB;UAClB,CAHD,MAGO;YACLxB,sBAAsB,GAAG,IAAzB;UACD,CAP2D,CAO1D;;;UAGFsB,kBAAkB,CAACJ,aAAD,EAAgBF,SAAS,GAAGH,WAA5B,CAAlB;QACD;MACF,CAjBD,MAiBO;QACL+B,OAAO,CAAC5E,SAAR,GAAoBiD,cAApB;QACAxE,IAAI,CAACgD,SAAD,EAAYmD,OAAZ,CAAJ,CAFK,CAGL;;QAGA,IAAI,CAAC7C,uBAAD,IAA4B,CAACD,gBAAjC,EAAmD;UACjDC,uBAAuB,GAAG,IAA1B;UACAoB,mBAAmB,CAACC,SAAD,CAAnB;QACD;MACF;;MAED,OAAOwB,OAAP;IACD;;IAED,SAASC,uBAAT,GAAmC,CAClC;;IAED,SAASC,0BAAT,GAAsC;MAEpC,IAAI,CAAC/C,uBAAD,IAA4B,CAACD,gBAAjC,EAAmD;QACjDC,uBAAuB,GAAG,IAA1B;QACAoB,mBAAmB,CAACC,SAAD,CAAnB;MACD;IACF;;IAED,SAAS2B,6BAAT,GAAyC;MACvC,OAAOhG,IAAI,CAAC0C,SAAD,CAAX;IACD;;IAED,SAASuD,uBAAT,CAAiCxE,IAAjC,EAAuC;MACrC;MACA;MAGAA,IAAI,CAACuC,QAAL,GAAgB,IAAhB;IACD;;IAED,SAASkC,gCAAT,GAA4C;MAC1C,OAAOpD,oBAAP;IACD;;IAED,IAAIqD,oBAAoB,GAAG,KAA3B;IACA,IAAIC,qBAAqB,GAAG,IAA5B;IACA,IAAIC,aAAa,GAAG,CAAC,CAArB,CA/cc,CA+cU;IACxB;IACA;IACA;;IAEA,IAAIC,aAAa,GAAG7G,YAApB;IACA,IAAIwE,SAAS,GAAG,CAAC,CAAjB;;IAEA,SAASa,iBAAT,GAA6B;MAC3B,IAAIyB,WAAW,GAAGxE,OAAO,CAACC,YAAR,KAAyBiC,SAA3C;;MAEA,IAAIsC,WAAW,GAAGD,aAAlB,EAAiC;QAC/B;QACA;QACA,OAAO,KAAP;MACD,CAP0B,CAOzB;;;MAGF,OAAO,IAAP;IACD;;IAED,SAASE,YAAT,GAAwB,CAEvB;;IAED,SAASC,cAAT,CAAwBC,GAAxB,EAA6B;MAC3B,IAAIA,GAAG,GAAG,CAAN,IAAWA,GAAG,GAAG,GAArB,EAA0B;QACxB;QACAC,OAAO,CAAC,OAAD,CAAP,CAAiB,4DAA4D,0DAA7E;QACA;MACD;;MAED,IAAID,GAAG,GAAG,CAAV,EAAa;QACXJ,aAAa,GAAGM,IAAI,CAACC,KAAL,CAAW,OAAOH,GAAlB,CAAhB;MACD,CAFD,MAEO;QACL;QACAJ,aAAa,GAAG7G,YAAhB;MACD;IACF;;IAED,IAAIqH,wBAAwB,GAAG,YAAY;MACzC,IAAIV,qBAAqB,KAAK,IAA9B,EAAoC;QAClC,IAAItC,WAAW,GAAG/B,OAAO,CAACC,YAAR,EAAlB,CADkC,CACQ;QAC1C;;QAEAiC,SAAS,GAAGH,WAAZ;QACA,IAAIU,gBAAgB,GAAG,IAAvB,CALkC,CAKL;QAC7B;QACA;QACA;QACA;QACA;;QAEA,IAAIuC,WAAW,GAAG,IAAlB;;QAEA,IAAI;UACFA,WAAW,GAAGX,qBAAqB,CAAC5B,gBAAD,EAAmBV,WAAnB,CAAnC;QACD,CAFD,SAEU;UACR,IAAIiD,WAAJ,EAAiB;YACf;YACA;YACAC,gCAAgC;UACjC,CAJD,MAIO;YACLb,oBAAoB,GAAG,KAAvB;YACAC,qBAAqB,GAAG,IAAxB;UACD;QACF;MACF,CA1BD,MA0BO;QACLD,oBAAoB,GAAG,KAAvB;MACD,CA7BwC,CA6BvC;;IACH,CA9BD;;IAgCA,IAAIa,gCAAJ;;IAEA,IAAI,OAAO1D,iBAAP,KAA6B,UAAjC,EAA6C;MAC3C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA0D,gCAAgC,GAAG,YAAY;QAC7C1D,iBAAiB,CAACwD,wBAAD,CAAjB;MACD,CAFD;IAGD,CAfD,MAeO,IAAI,OAAOG,cAAP,KAA0B,WAA9B,EAA2C;MAChD;MACA;MACA,IAAIC,OAAO,GAAG,IAAID,cAAJ,EAAd;MACA,IAAIE,IAAI,GAAGD,OAAO,CAACE,KAAnB;MACAF,OAAO,CAACG,KAAR,CAAcC,SAAd,GAA0BR,wBAA1B;;MAEAE,gCAAgC,GAAG,YAAY;QAC7CG,IAAI,CAACI,WAAL,CAAiB,IAAjB;MACD,CAFD;IAGD,CAVM,MAUA;MACL;MACAP,gCAAgC,GAAG,YAAY;QAC7C9D,eAAe,CAAC4D,wBAAD,EAA2B,CAA3B,CAAf;MACD,CAFD;IAGD;;IAED,SAAS1C,mBAAT,CAA6BJ,QAA7B,EAAuC;MACrCoC,qBAAqB,GAAGpC,QAAxB;;MAEA,IAAI,CAACmC,oBAAL,EAA2B;QACzBA,oBAAoB,GAAG,IAAvB;QACAa,gCAAgC;MACjC;IACF;;IAED,SAASzC,kBAAT,CAA4BP,QAA5B,EAAsCtC,EAAtC,EAA0C;MACxC2E,aAAa,GAAGnD,eAAe,CAAC,YAAY;QAC1Cc,QAAQ,CAACjC,OAAO,CAACC,YAAR,EAAD,CAAR;MACD,CAF8B,EAE5BN,EAF4B,CAA/B;IAGD;;IAED,SAAS+C,iBAAT,GAA6B;MAC3BrB,iBAAiB,CAACiD,aAAD,CAAjB;MACAA,aAAa,GAAG,CAAC,CAAjB;IACD;;IAED,IAAImB,qBAAqB,GAAGhB,YAA5B;IACA,IAAIiB,kBAAkB,GAAI,IAA1B;IAEA1F,OAAO,CAAC2F,qBAAR,GAAgCnG,YAAhC;IACAQ,OAAO,CAAC4F,0BAAR,GAAqCxG,iBAArC;IACAY,OAAO,CAAC6F,oBAAR,GAA+BtG,WAA/B;IACAS,OAAO,CAAC8F,uBAAR,GAAkCxG,cAAlC;IACAU,OAAO,CAAC0F,kBAAR,GAA6BA,kBAA7B;IACA1F,OAAO,CAAC+F,6BAAR,GAAwC1G,oBAAxC;IACAW,OAAO,CAACkE,uBAAR,GAAkCA,uBAAlC;IACAlE,OAAO,CAACgE,0BAAR,GAAqCA,0BAArC;IACAhE,OAAO,CAACgG,uBAAR,GAAkCtB,cAAlC;IACA1E,OAAO,CAACmE,gCAAR,GAA2CA,gCAA3C;IACAnE,OAAO,CAACiE,6BAAR,GAAwCA,6BAAxC;IACAjE,OAAO,CAACqD,aAAR,GAAwBA,aAAxB;IACArD,OAAO,CAAC+D,uBAAR,GAAkCA,uBAAlC;IACA/D,OAAO,CAACyF,qBAAR,GAAgCA,qBAAhC;IACAzF,OAAO,CAACmD,wBAAR,GAAmCA,wBAAnC;IACAnD,OAAO,CAAC0D,yBAAR,GAAoCA,yBAApC;IACA1D,OAAO,CAACiG,oBAAR,GAA+BlD,iBAA/B;IACA/C,OAAO,CAACsD,qBAAR,GAAgCA,qBAAhC;IACU;;IACV,IACE,OAAOjG,8BAAP,KAA0C,WAA1C,IACA,OAAOA,8BAA8B,CAAC6I,0BAAtC,KACE,UAHJ,EAIE;MACA7I,8BAA8B,CAAC6I,0BAA/B,CAA0D,IAAI3I,KAAJ,EAA1D;IACD;EAEE,CA3mBD;AA4mBD"}, "metadata": {}, "sourceType": "script"}