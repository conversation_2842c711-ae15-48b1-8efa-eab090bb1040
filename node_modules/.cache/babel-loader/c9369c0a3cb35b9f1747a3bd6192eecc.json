{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect,useRef}from\"react\";// import { sendMail } from \"../../../api\";\nimport emailjs from\"@emailjs/browser\";import*as Styles from\"./styles\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const ContactForm=()=>{let errors=useRef([]);const form=useRef();const[success,setSuccess]=useState(null);const[loading,setLoading]=useState(false);const[inputValue,setInputValue]=useState({name:\"\",company:\"\",email:\"\",mobile:\"\"});const{name,company,email,mobile}=inputValue;const[inputErrors,setInputErrors]=useState(null);const errChange=errors.current;useEffect(()=>{setInputErrors(errors.current);},[errChange]);const handleChange=e=>{const value=e.target.value;const name=e.target.name;setInputValue(_objectSpread(_objectSpread({},inputValue),{},{[name]:value}));setInputErrors(_objectSpread(_objectSpread({},inputErrors),{},{[name]:\"\"}));};const handleSubmit=async e=>{e.preventDefault();setLoading(true);console.log({inputValue});console.log({form:form.current});emailjs.sendForm(\"service_ifbucgn\",\"template_gryehv4\",form.current,\"ye7UvAtvh7-vkICwy\").then(result=>{console.log(result);setLoading(false);},error=>{console.log(error.text);setLoading(false);});// const res = await sendMail(inputValue);\n// if (res.success) {\n//   setSuccess(res.data);\n//   setInputValue({ name: \"\", company: \"\", email: \"\", mobile: \"\" });\n//   setLoading(false);\n//   setTimeout(() => {\n//     setSuccess(null);\n//   }, 6000);\n// } else if (!res.success) {\n//   let i = 0;\n//   while (i < res.data.length) {\n//     errors.current[res.data[i].param] = res.data[i].msg;\n//     setInputErrors({\n//       ...inputErrors,\n//       [res.data[i].param]: res.data[i].msg,\n//     });\n//     i++;\n//   }\n//   setLoading(false);\n// }\n};return/*#__PURE__*/_jsxs(Styles.Contaier,{children:[/*#__PURE__*/_jsx(\"h2\",{style:{color:\"Black\",font:\"-moz-initial\",textAlign:\"center\"},children:\"Who are already onboard\"}),/*#__PURE__*/_jsxs(Styles.FormHolder,{ref:form,onSubmit:e=>handleSubmit(e),children:[/*#__PURE__*/_jsxs(Styles.RowHolder,{children:[/*#__PURE__*/_jsx(Styles.LabelHolder,{children:\"Name\"}),/*#__PURE__*/_jsx(Styles.InputHolder,{name:\"name\",type:\"text\",value:name,onChange:e=>handleChange(e),error:inputErrors===null||inputErrors===void 0?void 0:inputErrors.name,required:true}),inputErrors!==null&&inputErrors!==void 0&&inputErrors.name?/*#__PURE__*/_jsx(Styles.ShowError,{children:inputErrors===null||inputErrors===void 0?void 0:inputErrors.name}):null]}),/*#__PURE__*/_jsxs(Styles.RowHolder,{children:[/*#__PURE__*/_jsx(Styles.LabelHolder,{children:\"Company\"}),/*#__PURE__*/_jsx(Styles.InputHolder,{name:\"company\",type:\"text\",value:company,onChange:e=>handleChange(e),error:inputErrors===null||inputErrors===void 0?void 0:inputErrors.company,required:true}),inputErrors!==null&&inputErrors!==void 0&&inputErrors.company?/*#__PURE__*/_jsx(Styles.ShowError,{children:inputErrors===null||inputErrors===void 0?void 0:inputErrors.company}):null]}),/*#__PURE__*/_jsxs(Styles.RowHolder,{children:[/*#__PURE__*/_jsx(Styles.LabelHolder,{children:\"Email\"}),/*#__PURE__*/_jsx(Styles.InputHolder,{name:\"email\",type:\"email\",value:email,onChange:e=>handleChange(e),error:inputErrors===null||inputErrors===void 0?void 0:inputErrors.email,required:true}),inputErrors!==null&&inputErrors!==void 0&&inputErrors.email?/*#__PURE__*/_jsx(Styles.ShowError,{children:inputErrors===null||inputErrors===void 0?void 0:inputErrors.email}):null]}),/*#__PURE__*/_jsxs(Styles.RowHolder,{children:[/*#__PURE__*/_jsx(Styles.LabelHolder,{children:\"Contact Details\"}),/*#__PURE__*/_jsx(Styles.InputHolder,{name:\"mobile\",type:\"tel\",value:mobile,onChange:e=>handleChange(e),error:inputErrors===null||inputErrors===void 0?void 0:inputErrors.mobile,required:true}),inputErrors!==null&&inputErrors!==void 0&&inputErrors.mobile?/*#__PURE__*/_jsx(Styles.ShowError,{children:inputErrors===null||inputErrors===void 0?void 0:inputErrors.mobile}):null]}),/*#__PURE__*/_jsx(Styles.Button,{success:success===null||success===void 0?void 0:success.delivered,type:\"submit\",disabled:success===null||success===void 0?void 0:success.delivered,children:loading&&!(success!==null&&success!==void 0&&success.delivered)?\"Sending....\":!loading&&success!==null&&success!==void 0&&success.delivered?\"Sent Successfully\":\"Send Message\"})]}),/*#__PURE__*/_jsx(Styles.ShowSuccess,{children:success===null||success===void 0?void 0:success.msg})]});};export default ContactForm;", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "emailjs", "Styles", "jsx", "_jsx", "jsxs", "_jsxs", "ContactForm", "errors", "form", "success", "setSuccess", "loading", "setLoading", "inputValue", "setInputValue", "name", "company", "email", "mobile", "inputErrors", "setInputErrors", "err<PERSON><PERSON><PERSON>", "current", "handleChange", "e", "value", "target", "_objectSpread", "handleSubmit", "preventDefault", "console", "log", "sendForm", "then", "result", "error", "text", "<PERSON><PERSON><PERSON>", "children", "style", "color", "font", "textAlign", "FormHolder", "ref", "onSubmit", "RowHolder", "LabelHolder", "InputHolder", "type", "onChange", "required", "ShowError", "<PERSON><PERSON>", "delivered", "disabled", "ShowSuccess", "msg"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/index.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from \"react\";\n\n// import { sendMail } from \"../../../api\";\nimport emailjs from \"@emailjs/browser\";\n\nimport * as Styles from \"./styles\";\n\nconst ContactForm = () => {\n  let errors = useRef([]);\n  const form = useRef();\n\n  const [success, setSuccess] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [inputValue, setInputValue] = useState({\n\n    name: \"\",\n    company: \"\",\n    email: \"\",\n    mobile: \"\",\n  });\n\n  const { name, company, email, mobile } = inputValue;\n\n  const [inputErrors, setInputErrors] = useState(null);\n\n  const errChange = errors.current;\n\n  useEffect(() => {\n    setInputErrors(errors.current);\n  }, [errChange]);\n\n  const handleChange = (e) => {\n    const value = e.target.value;\n    const name = e.target.name;\n\n    setInputValue({ ...inputValue, [name]: value });\n    setInputErrors({ ...inputErrors, [name]: \"\" });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    console.log({ inputValue });\n    console.log({ form: form.current });\n    emailjs\n      .sendForm(\n        \"service_ifbucgn\",\n        \"template_gryehv4\",\n        form.current,\n        \"ye7UvAtvh7-vkICwy\"\n      )\n      .then(\n        (result) => {\n          console.log(result);\n          setLoading(false);\n        },\n        (error) => {\n          console.log(error.text);\n          setLoading(false);\n        }\n      );\n    // const res = await sendMail(inputValue);\n    // if (res.success) {\n    //   setSuccess(res.data);\n    //   setInputValue({ name: \"\", company: \"\", email: \"\", mobile: \"\" });\n    //   setLoading(false);\n\n    //   setTimeout(() => {\n    //     setSuccess(null);\n    //   }, 6000);\n    // } else if (!res.success) {\n    //   let i = 0;\n    //   while (i < res.data.length) {\n    //     errors.current[res.data[i].param] = res.data[i].msg;\n    //     setInputErrors({\n    //       ...inputErrors,\n    //       [res.data[i].param]: res.data[i].msg,\n    //     });\n    //     i++;\n    //   }\n    //   setLoading(false);\n    // }\n  };\n\n  return (\n    <Styles.Contaier>\n\n<h2 style={{ color: \"Black\",font: \"-moz-initial\", textAlign: \"center\" }}>Who are already onboard</h2>\n      <Styles.FormHolder ref={form} onSubmit={(e) => handleSubmit(e)}>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Name</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"name\"\n            type=\"text\"\n            value={name}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.name}\n            required\n          />\n          {inputErrors?.name ? (\n            <Styles.ShowError>{inputErrors?.name}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Company</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"company\"\n            type=\"text\"\n            value={company}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.company}\n            required\n          />\n          {inputErrors?.company ? (\n            <Styles.ShowError>{inputErrors?.company}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Email</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"email\"\n            type=\"email\"\n            value={email}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.email}\n            required\n          />\n          {inputErrors?.email ? (\n            <Styles.ShowError>{inputErrors?.email}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Contact Details</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"mobile\"\n            type=\"tel\"\n            value={mobile}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.mobile}\n            required\n          />\n          {inputErrors?.mobile ? (\n            <Styles.ShowError>{inputErrors?.mobile}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.Button\n          success={success?.delivered}\n          type=\"submit\"\n          disabled={success?.delivered}\n        >\n          {loading && !success?.delivered\n            ? \"Sending....\"\n            : !loading && success?.delivered\n            ? \"Sent Successfully\"\n            : \"Send Message\"}\n        </Styles.Button>\n      </Styles.FormHolder>\n      <Styles.ShowSuccess>{success?.msg}</Styles.ShowSuccess>\n    </Styles.Contaier>\n  );\n};\n\nexport default ContactForm;\n"], "mappings": "4IAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAEnD;AACA,MAAO,CAAAC,OAAO,KAAM,kBAAkB,CAEtC,MAAO,GAAK,CAAAC,MAAM,KAAM,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEnC,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAI,CAAAC,MAAM,CAAGR,MAAM,CAAC,EAAE,CAAC,CACvB,KAAM,CAAAS,IAAI,CAAGT,MAAM,CAAC,CAAC,CAErB,KAAM,CAACU,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,CAE3CkB,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EACV,CAAC,CAAC,CAEF,KAAM,CAAEH,IAAI,CAAEC,OAAO,CAAEC,KAAK,CAAEC,MAAO,CAAC,CAAGL,UAAU,CAEnD,KAAM,CAACM,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAEpD,KAAM,CAAAwB,SAAS,CAAGd,MAAM,CAACe,OAAO,CAEhCxB,SAAS,CAAC,IAAM,CACdsB,cAAc,CAACb,MAAM,CAACe,OAAO,CAAC,CAChC,CAAC,CAAE,CAACD,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAE,YAAY,CAAIC,CAAC,EAAK,CAC1B,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5B,KAAM,CAAAV,IAAI,CAAGS,CAAC,CAACE,MAAM,CAACX,IAAI,CAE1BD,aAAa,CAAAa,aAAA,CAAAA,aAAA,IAAMd,UAAU,MAAE,CAACE,IAAI,EAAGU,KAAK,EAAE,CAAC,CAC/CL,cAAc,CAAAO,aAAA,CAAAA,aAAA,IAAMR,WAAW,MAAE,CAACJ,IAAI,EAAG,EAAE,EAAE,CAAC,CAChD,CAAC,CAED,KAAM,CAAAa,YAAY,CAAG,KAAO,CAAAJ,CAAC,EAAK,CAChCA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBjB,UAAU,CAAC,IAAI,CAAC,CAChBkB,OAAO,CAACC,GAAG,CAAC,CAAElB,UAAW,CAAC,CAAC,CAC3BiB,OAAO,CAACC,GAAG,CAAC,CAAEvB,IAAI,CAAEA,IAAI,CAACc,OAAQ,CAAC,CAAC,CACnCtB,OAAO,CACJgC,QAAQ,CACP,iBAAiB,CACjB,kBAAkB,CAClBxB,IAAI,CAACc,OAAO,CACZ,mBACF,CAAC,CACAW,IAAI,CACFC,MAAM,EAAK,CACVJ,OAAO,CAACC,GAAG,CAACG,MAAM,CAAC,CACnBtB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CACAuB,KAAK,EAAK,CACTL,OAAO,CAACC,GAAG,CAACI,KAAK,CAACC,IAAI,CAAC,CACvBxB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CACH;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAC,CAED,mBACEP,KAAA,CAACJ,MAAM,CAACoC,QAAQ,EAAAC,QAAA,eAEpBnC,IAAA,OAAIoC,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAO,CAACC,IAAI,CAAE,cAAc,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAJ,QAAA,CAAC,yBAAuB,CAAI,CAAC,cAC/FjC,KAAA,CAACJ,MAAM,CAAC0C,UAAU,EAACC,GAAG,CAAEpC,IAAK,CAACqC,QAAQ,CAAGrB,CAAC,EAAKI,YAAY,CAACJ,CAAC,CAAE,CAAAc,QAAA,eAC7DjC,KAAA,CAACJ,MAAM,CAAC6C,SAAS,EAAAR,QAAA,eACfnC,IAAA,CAACF,MAAM,CAAC8C,WAAW,EAAAT,QAAA,CAAC,MAAI,CAAoB,CAAC,cAC7CnC,IAAA,CAACF,MAAM,CAAC+C,WAAW,EACjBjC,IAAI,CAAC,MAAM,CACXkC,IAAI,CAAC,MAAM,CACXxB,KAAK,CAAEV,IAAK,CACZmC,QAAQ,CAAG1B,CAAC,EAAKD,YAAY,CAACC,CAAC,CAAE,CACjCW,KAAK,CAAEhB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEJ,IAAK,CACzBoC,QAAQ,MACT,CAAC,CACDhC,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEJ,IAAI,cAChBZ,IAAA,CAACF,MAAM,CAACmD,SAAS,EAAAd,QAAA,CAAEnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEJ,IAAI,CAAmB,CAAC,CACtD,IAAI,EACQ,CAAC,cACnBV,KAAA,CAACJ,MAAM,CAAC6C,SAAS,EAAAR,QAAA,eACfnC,IAAA,CAACF,MAAM,CAAC8C,WAAW,EAAAT,QAAA,CAAC,SAAO,CAAoB,CAAC,cAChDnC,IAAA,CAACF,MAAM,CAAC+C,WAAW,EACjBjC,IAAI,CAAC,SAAS,CACdkC,IAAI,CAAC,MAAM,CACXxB,KAAK,CAAET,OAAQ,CACfkC,QAAQ,CAAG1B,CAAC,EAAKD,YAAY,CAACC,CAAC,CAAE,CACjCW,KAAK,CAAEhB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEH,OAAQ,CAC5BmC,QAAQ,MACT,CAAC,CACDhC,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEH,OAAO,cACnBb,IAAA,CAACF,MAAM,CAACmD,SAAS,EAAAd,QAAA,CAAEnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEH,OAAO,CAAmB,CAAC,CACzD,IAAI,EACQ,CAAC,cACnBX,KAAA,CAACJ,MAAM,CAAC6C,SAAS,EAAAR,QAAA,eACfnC,IAAA,CAACF,MAAM,CAAC8C,WAAW,EAAAT,QAAA,CAAC,OAAK,CAAoB,CAAC,cAC9CnC,IAAA,CAACF,MAAM,CAAC+C,WAAW,EACjBjC,IAAI,CAAC,OAAO,CACZkC,IAAI,CAAC,OAAO,CACZxB,KAAK,CAAER,KAAM,CACbiC,QAAQ,CAAG1B,CAAC,EAAKD,YAAY,CAACC,CAAC,CAAE,CACjCW,KAAK,CAAEhB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEF,KAAM,CAC1BkC,QAAQ,MACT,CAAC,CACDhC,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEF,KAAK,cACjBd,IAAA,CAACF,MAAM,CAACmD,SAAS,EAAAd,QAAA,CAAEnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEF,KAAK,CAAmB,CAAC,CACvD,IAAI,EACQ,CAAC,cACnBZ,KAAA,CAACJ,MAAM,CAAC6C,SAAS,EAAAR,QAAA,eACfnC,IAAA,CAACF,MAAM,CAAC8C,WAAW,EAAAT,QAAA,CAAC,iBAAe,CAAoB,CAAC,cACxDnC,IAAA,CAACF,MAAM,CAAC+C,WAAW,EACjBjC,IAAI,CAAC,QAAQ,CACbkC,IAAI,CAAC,KAAK,CACVxB,KAAK,CAAEP,MAAO,CACdgC,QAAQ,CAAG1B,CAAC,EAAKD,YAAY,CAACC,CAAC,CAAE,CACjCW,KAAK,CAAEhB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAED,MAAO,CAC3BiC,QAAQ,MACT,CAAC,CACDhC,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAED,MAAM,cAClBf,IAAA,CAACF,MAAM,CAACmD,SAAS,EAAAd,QAAA,CAAEnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAED,MAAM,CAAmB,CAAC,CACxD,IAAI,EACQ,CAAC,cACnBf,IAAA,CAACF,MAAM,CAACoD,MAAM,EACZ5C,OAAO,CAAEA,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE6C,SAAU,CAC5BL,IAAI,CAAC,QAAQ,CACbM,QAAQ,CAAE9C,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE6C,SAAU,CAAAhB,QAAA,CAE5B3B,OAAO,EAAI,EAACF,OAAO,SAAPA,OAAO,WAAPA,OAAO,CAAE6C,SAAS,EAC3B,aAAa,CACb,CAAC3C,OAAO,EAAIF,OAAO,SAAPA,OAAO,WAAPA,OAAO,CAAE6C,SAAS,CAC9B,mBAAmB,CACnB,cAAc,CACL,CAAC,EACC,CAAC,cACpBnD,IAAA,CAACF,MAAM,CAACuD,WAAW,EAAAlB,QAAA,CAAE7B,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEgD,GAAG,CAAqB,CAAC,EACxC,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAnD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}