{"ast": null, "code": "import styled from \"styled-components\";\nexport const Button = styled.button`\n  background-color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.main.yellow;\n}};\n  border: 1px solid ${_ref2 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref2;\n  return colors.main.yellow;\n}};\n  padding: 5px 15px;\n  border-radius: 100px;\n  color: ${_ref3 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref3;\n  return colors.dark;\n}};\n  font-weight: 600;\n  transition: all 0.3s ease-in-out;\n\n  cursor: pointer;\n\n  &:hover {\n    background-color: ${_ref4 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref4;\n  return colors.white;\n}};\n    border: 1px solid ${_ref5 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref5;\n  return colors.main.red;\n}};\n    color: ${_ref6 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref6;\n  return colors.main.red;\n}};\n  }\n`;", "map": {"version": 3, "names": ["styled", "<PERSON><PERSON>", "button", "theme", "colors", "main", "yellow", "dark", "white", "red"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Button = styled.button`\n  background-color: ${({ theme: { colors } }) => colors.main.yellow};\n  border: 1px solid ${({ theme: { colors } }) => colors.main.yellow};\n  padding: 5px 15px;\n  border-radius: 100px;\n  color: ${({ theme: { colors } }) => colors.dark};\n  font-weight: 600;\n  transition: all 0.3s ease-in-out;\n\n  cursor: pointer;\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.white};\n    border: 1px solid ${({ theme: { colors } }) => colors.main.red};\n    color: ${({ theme: { colors } }) => colors.main.red};\n  }\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,MAAM,GAAGD,MAAM,CAACE,MAAO;AACpC,sBAAsB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,MAAvC;AAAA,CAA8C;AACpE,sBAAsB;EAAA,IAAC;IAAEH,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,MAAvC;AAAA,CAA8C;AACpE;AACA;AACA,WAAW;EAAA,IAAC;IAAEH,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACG,IAAlC;AAAA,CAAuC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEJ,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACI,KAAlC;AAAA,CAAwC;AAChE,wBAAwB;EAAA,IAAC;IAAEL,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYI,GAAvC;AAAA,CAA2C;AACnE,aAAa;EAAA,IAAC;IAAEN,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYI,GAAvC;AAAA,CAA2C;AACxD;AACA,CAhBO"}, "metadata": {}, "sourceType": "module"}