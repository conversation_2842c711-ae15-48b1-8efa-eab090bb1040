{"ast": null, "code": "var requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar $Object = Object; // `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\n\nmodule.exports = function (argument) {\n  return $Object(requireObjectCoercible(argument));\n};", "map": {"version": 3, "names": ["requireObjectCoercible", "require", "$Object", "Object", "module", "exports", "argument"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/to-object.js"], "sourcesContent": ["var requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar $Object = Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return $Object(requireObjectCoercible(argument));\n};\n"], "mappings": "AAAA,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,uCAAD,CAApC;;AAEA,IAAIC,OAAO,GAAGC,MAAd,C,CAEA;AACA;;AACAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,QAAV,EAAoB;EACnC,OAAOJ,OAAO,CAACF,sBAAsB,CAACM,QAAD,CAAvB,CAAd;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}