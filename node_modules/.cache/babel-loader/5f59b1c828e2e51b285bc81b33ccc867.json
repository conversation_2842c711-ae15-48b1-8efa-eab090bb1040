{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  flex: 1;\n  text-align: center;\n\n  cursor: pointer;\n\n  &:hover {\n    .text {\n      color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.main.red;\n}};\n      font-weight: 600;\n    }\n\n    .image {\n      background-color: ${_ref2 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref2;\n  return colors.main.blue;\n}};\n      box-shadow: 0 0 20px 20px rgba(0, 0, 0, 0.1);\n    }\n  }\n`;\nexport const ImageWrapper = styled.div`\n  background-color: #fff;\n  box-shadow: 0 0 20px 20px rgba(0, 0, 0, 0.05);\n  border-radius: 100px;\n\n  width: 130px;\n  height: 130px;\n\n  margin: 0 auto;\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  transition: all 0.3s ease-in-out;\n`;\nexport const IconHolder = styled.img`\n  width: 60px;\n  height: 60px;\n`;\nexport const TextHolder = styled.p`\n  margin: 20px 0;\n  font-weight: 400;\n  transition: all 0.3s ease-in-out;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "theme", "colors", "main", "red", "blue", "ImageWrapper", "IconHolder", "img", "TextHolder", "p"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  flex: 1;\n  text-align: center;\n\n  cursor: pointer;\n\n  &:hover {\n    .text {\n      color: ${({ theme: { colors } }) => colors.main.red};\n      font-weight: 600;\n    }\n\n    .image {\n      background-color: ${({ theme: { colors } }) => colors.main.blue};\n      box-shadow: 0 0 20px 20px rgba(0, 0, 0, 0.1);\n    }\n  }\n`;\n\nexport const ImageWrapper = styled.div`\n  background-color: #fff;\n  box-shadow: 0 0 20px 20px rgba(0, 0, 0, 0.05);\n  border-radius: 100px;\n\n  width: 130px;\n  height: 130px;\n\n  margin: 0 auto;\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const IconHolder = styled.img`\n  width: 60px;\n  height: 60px;\n`;\n\nexport const TextHolder = styled.p`\n  margin: 20px 0;\n  font-weight: 400;\n  transition: all 0.3s ease-in-out;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,GAAvC;AAAA,CAA2C;AAC1D;AACA;AACA;AACA;AACA,0BAA0B;EAAA,IAAC;IAAEH,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYE,IAAvC;AAAA,CAA4C;AACtE;AACA;AACA;AACA,CAjBO;AAmBP,OAAO,MAAMC,YAAY,GAAGR,MAAM,CAACE,GAAI;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAfO;AAiBP,OAAO,MAAMO,UAAU,GAAGT,MAAM,CAACU,GAAI;AACrC;AACA;AACA,CAHO;AAKP,OAAO,MAAMC,UAAU,GAAGX,MAAM,CAACY,CAAE;AACnC;AACA;AACA;AACA,CAJO"}, "metadata": {}, "sourceType": "module"}