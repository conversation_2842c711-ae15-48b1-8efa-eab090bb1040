{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/home/<USER>\";\nimport React from \"react\";\nimport Seaction from \"../../components/global/seaction\";\nimport Projects from \"../../components/projects\";\nimport Services from \"../../components/services\";\nimport ClientsArea from \"../../components/home/<USER>\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst HomePage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(Seaction, {\n      children: /*#__PURE__*/_jsxDEV(Projects, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Seaction, {\n      children: /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Seaction, {\n      children: /*#__PURE__*/_jsxDEV(ClientsArea, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n\n_c = HomePage;\nexport default HomePage;\n\nvar _c;\n\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Seaction", "Projects", "Services", "ClientsArea", "HomePage"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/home/<USER>"], "sourcesContent": ["import React from \"react\";\n\nimport Seaction from \"../../components/global/seaction\";\n\nimport Projects from \"../../components/projects\";\nimport Services from \"../../components/services\";\nimport ClientsArea from \"../../components/home/<USER>\";\n\nconst HomePage = () => {\n  return (\n    <div className=\"container\">\n      <Seaction>\n        <Projects />\n      </Seaction>\n      <Seaction>\n        <Services />\n      </Seaction>\n      <Seaction>\n        <ClientsArea />\n      </Seaction>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,QAAP,MAAqB,kCAArB;AAEA,OAAOC,QAAP,MAAqB,2BAArB;AACA,OAAOC,QAAP,MAAqB,2BAArB;AACA,OAAOC,WAAP,MAAwB,8BAAxB;;;AAEA,MAAMC,QAAQ,GAAG,MAAM;EACrB,oBACE;IAAK,SAAS,EAAC,WAAf;IAAA,wBACE,QAAC,QAAD;MAAA,uBACE,QAAC,QAAD;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QADF,eAIE,QAAC,QAAD;MAAA,uBACE,QAAC,QAAD;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QAJF,eAOE,QAAC,QAAD;MAAA,uBACE,QAAC,WAAD;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QAPF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAaD,CAdD;;KAAMA,Q;AAgBN,eAAeA,QAAf"}, "metadata": {}, "sourceType": "module"}