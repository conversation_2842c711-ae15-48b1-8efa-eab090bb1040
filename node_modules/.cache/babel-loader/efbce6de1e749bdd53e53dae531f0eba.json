{"ast": null, "code": "var NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar bind = FunctionPrototype.bind;\nvar call = FunctionPrototype.call;\nvar uncurryThis = NATIVE_BIND && bind.bind(call, call);\nmodule.exports = NATIVE_BIND ? function (fn) {\n  return fn && uncurryThis(fn);\n} : function (fn) {\n  return fn && function () {\n    return call.apply(fn, arguments);\n  };\n};", "map": {"version": 3, "names": ["NATIVE_BIND", "require", "FunctionPrototype", "Function", "prototype", "bind", "call", "uncurryThis", "module", "exports", "fn", "apply", "arguments"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/function-uncurry-this.js"], "sourcesContent": ["var NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar bind = FunctionPrototype.bind;\nvar call = FunctionPrototype.call;\nvar uncurryThis = NATIVE_BIND && bind.bind(call, call);\n\nmodule.exports = NATIVE_BIND ? function (fn) {\n  return fn && uncurryThis(fn);\n} : function (fn) {\n  return fn && function () {\n    return call.apply(fn, arguments);\n  };\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,mCAAD,CAAzB;;AAEA,IAAIC,iBAAiB,GAAGC,QAAQ,CAACC,SAAjC;AACA,IAAIC,IAAI,GAAGH,iBAAiB,CAACG,IAA7B;AACA,IAAIC,IAAI,GAAGJ,iBAAiB,CAACI,IAA7B;AACA,IAAIC,WAAW,GAAGP,WAAW,IAAIK,IAAI,CAACA,IAAL,CAAUC,IAAV,EAAgBA,IAAhB,CAAjC;AAEAE,MAAM,CAACC,OAAP,GAAiBT,WAAW,GAAG,UAAUU,EAAV,EAAc;EAC3C,OAAOA,EAAE,IAAIH,WAAW,CAACG,EAAD,CAAxB;AACD,CAF2B,GAExB,UAAUA,EAAV,EAAc;EAChB,OAAOA,EAAE,IAAI,YAAY;IACvB,OAAOJ,IAAI,CAACK,KAAL,CAAWD,EAAX,EAAeE,SAAf,CAAP;EACD,CAFD;AAGD,CAND"}, "metadata": {}, "sourceType": "script"}