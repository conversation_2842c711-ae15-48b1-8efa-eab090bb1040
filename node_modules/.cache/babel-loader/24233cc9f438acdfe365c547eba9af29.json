{"ast": null, "code": "function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar Maximize = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\"\n  }));\n});\nMaximize.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nMaximize.displayName = 'Maximize';\nexport default Maximize;", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "keys", "React", "forwardRef", "PropTypes", "Maximize", "_ref", "ref", "_ref$color", "color", "_ref$size", "size", "rest", "createElement", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "propTypes", "string", "oneOfType", "number", "displayName"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-feather/dist/icons/maximize.js"], "sourcesContent": ["function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar Maximize = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 24 : _ref$size,\n      rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\"\n  }));\n});\nMaximize.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nMaximize.displayName = 'Maximize';\nexport default Maximize;"], "mappings": "AAAA,SAASA,QAAT,GAAoB;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,MAAV,EAAkB;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,SAAS,CAACC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAD,CAAtB;;MAA2B,KAAK,IAAII,GAAT,IAAgBD,MAAhB,EAAwB;QAAE,IAAIN,MAAM,CAACQ,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCJ,MAArC,EAA6CC,GAA7C,CAAJ,EAAuD;UAAEL,MAAM,CAACK,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAApB;QAA4B;MAAE;IAAE;;IAAC,OAAOL,MAAP;EAAgB,CAA5P;;EAA8P,OAAOH,QAAQ,CAACY,KAAT,CAAe,IAAf,EAAqBP,SAArB,CAAP;AAAyC;;AAE7T,SAASQ,wBAAT,CAAkCN,MAAlC,EAA0CO,QAA1C,EAAoD;EAAE,IAAIP,MAAM,IAAI,IAAd,EAAoB,OAAO,EAAP;;EAAW,IAAIJ,MAAM,GAAGY,6BAA6B,CAACR,MAAD,EAASO,QAAT,CAA1C;;EAA8D,IAAIN,GAAJ,EAASJ,CAAT;;EAAY,IAAIH,MAAM,CAACe,qBAAX,EAAkC;IAAE,IAAIC,gBAAgB,GAAGhB,MAAM,CAACe,qBAAP,CAA6BT,MAA7B,CAAvB;;IAA6D,KAAKH,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGa,gBAAgB,CAACX,MAAjC,EAAyCF,CAAC,EAA1C,EAA8C;MAAEI,GAAG,GAAGS,gBAAgB,CAACb,CAAD,CAAtB;MAA2B,IAAIU,QAAQ,CAACI,OAAT,CAAiBV,GAAjB,KAAyB,CAA7B,EAAgC;MAAU,IAAI,CAACP,MAAM,CAACQ,SAAP,CAAiBU,oBAAjB,CAAsCR,IAAtC,CAA2CJ,MAA3C,EAAmDC,GAAnD,CAAL,EAA8D;MAAUL,MAAM,CAACK,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAApB;IAA4B;EAAE;;EAAC,OAAOL,MAAP;AAAgB;;AAE5e,SAASY,6BAAT,CAAuCR,MAAvC,EAA+CO,QAA/C,EAAyD;EAAE,IAAIP,MAAM,IAAI,IAAd,EAAoB,OAAO,EAAP;EAAW,IAAIJ,MAAM,GAAG,EAAb;EAAiB,IAAIiB,UAAU,GAAGnB,MAAM,CAACoB,IAAP,CAAYd,MAAZ,CAAjB;EAAsC,IAAIC,GAAJ,EAASJ,CAAT;;EAAY,KAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGgB,UAAU,CAACd,MAA3B,EAAmCF,CAAC,EAApC,EAAwC;IAAEI,GAAG,GAAGY,UAAU,CAAChB,CAAD,CAAhB;IAAqB,IAAIU,QAAQ,CAACI,OAAT,CAAiBV,GAAjB,KAAyB,CAA7B,EAAgC;IAAUL,MAAM,CAACK,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAApB;EAA4B;;EAAC,OAAOL,MAAP;AAAgB;;AAEnT,OAAOmB,KAAP,IAAgBC,UAAhB,QAAkC,OAAlC;AACA,OAAOC,SAAP,MAAsB,YAAtB;AACA,IAAIC,QAAQ,GAAGF,UAAU,CAAC,UAAUG,IAAV,EAAgBC,GAAhB,EAAqB;EAC7C,IAAIC,UAAU,GAAGF,IAAI,CAACG,KAAtB;EAAA,IACIA,KAAK,GAAGD,UAAU,KAAK,KAAK,CAApB,GAAwB,cAAxB,GAAyCA,UADrD;EAAA,IAEIE,SAAS,GAAGJ,IAAI,CAACK,IAFrB;EAAA,IAGIA,IAAI,GAAGD,SAAS,KAAK,KAAK,CAAnB,GAAuB,EAAvB,GAA4BA,SAHvC;EAAA,IAIIE,IAAI,GAAGnB,wBAAwB,CAACa,IAAD,EAAO,CAAC,OAAD,EAAU,MAAV,CAAP,CAJnC;;EAMA,OAAO,aAAaJ,KAAK,CAACW,aAAN,CAAoB,KAApB,EAA2BjC,QAAQ,CAAC;IACtD2B,GAAG,EAAEA,GADiD;IAEtDO,KAAK,EAAE,4BAF+C;IAGtDC,KAAK,EAAEJ,IAH+C;IAItDK,MAAM,EAAEL,IAJ8C;IAKtDM,OAAO,EAAE,WAL6C;IAMtDC,IAAI,EAAE,MANgD;IAOtDC,MAAM,EAAEV,KAP8C;IAQtDW,WAAW,EAAE,GARyC;IAStDC,aAAa,EAAE,OATuC;IAUtDC,cAAc,EAAE;EAVsC,CAAD,EAWpDV,IAXoD,CAAnC,EAWV,aAAaV,KAAK,CAACW,aAAN,CAAoB,MAApB,EAA4B;IACjDU,CAAC,EAAE;EAD8C,CAA5B,CAXH,CAApB;AAcD,CArBwB,CAAzB;AAsBAlB,QAAQ,CAACmB,SAAT,GAAqB;EACnBf,KAAK,EAAEL,SAAS,CAACqB,MADE;EAEnBd,IAAI,EAAEP,SAAS,CAACsB,SAAV,CAAoB,CAACtB,SAAS,CAACqB,MAAX,EAAmBrB,SAAS,CAACuB,MAA7B,CAApB;AAFa,CAArB;AAIAtB,QAAQ,CAACuB,WAAT,GAAuB,UAAvB;AACA,eAAevB,QAAf"}, "metadata": {}, "sourceType": "module"}