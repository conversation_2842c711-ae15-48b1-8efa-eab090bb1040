{"ast": null, "code": "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n'use strict';\n/* eslint-disable no-unused-vars */\n\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n  if (val === null || val === undefined) {\n    throw new TypeError('Object.assign cannot be called with null or undefined');\n  }\n\n  return Object(val);\n}\n\nfunction shouldUseNative() {\n  try {\n    if (!Object.assign) {\n      return false;\n    } // Detect buggy property enumeration order in older V8 versions.\n    // https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\n\n    var test1 = new String('abc'); // eslint-disable-line no-new-wrappers\n\n    test1[5] = 'de';\n\n    if (Object.getOwnPropertyNames(test1)[0] === '5') {\n      return false;\n    } // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\n\n    var test2 = {};\n\n    for (var i = 0; i < 10; i++) {\n      test2['_' + String.fromCharCode(i)] = i;\n    }\n\n    var order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n      return test2[n];\n    });\n\n    if (order2.join('') !== '0123456789') {\n      return false;\n    } // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\n\n    var test3 = {};\n    'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n      test3[letter] = letter;\n    });\n\n    if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {\n      return false;\n    }\n\n    return true;\n  } catch (err) {\n    // We don't expect any of the above to throw, but better to be safe.\n    return false;\n  }\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n  var from;\n  var to = toObject(target);\n  var symbols;\n\n  for (var s = 1; s < arguments.length; s++) {\n    from = Object(arguments[s]);\n\n    for (var key in from) {\n      if (hasOwnProperty.call(from, key)) {\n        to[key] = from[key];\n      }\n    }\n\n    if (getOwnPropertySymbols) {\n      symbols = getOwnPropertySymbols(from);\n\n      for (var i = 0; i < symbols.length; i++) {\n        if (propIsEnumerable.call(from, symbols[i])) {\n          to[symbols[i]] = from[symbols[i]];\n        }\n      }\n    }\n  }\n\n  return to;\n};", "map": {"version": 3, "names": ["getOwnPropertySymbols", "Object", "hasOwnProperty", "prototype", "propIsEnumerable", "propertyIsEnumerable", "toObject", "val", "undefined", "TypeError", "shouldUseNative", "assign", "test1", "String", "getOwnPropertyNames", "test2", "i", "fromCharCode", "order2", "map", "n", "join", "test3", "split", "for<PERSON>ach", "letter", "keys", "err", "module", "exports", "target", "source", "from", "to", "symbols", "s", "arguments", "length", "key", "call"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/object-assign/index.js"], "sourcesContent": ["/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;;AACA,IAAIA,qBAAqB,GAAGC,MAAM,CAACD,qBAAnC;AACA,IAAIE,cAAc,GAAGD,MAAM,CAACE,SAAP,CAAiBD,cAAtC;AACA,IAAIE,gBAAgB,GAAGH,MAAM,CAACE,SAAP,CAAiBE,oBAAxC;;AAEA,SAASC,QAAT,CAAkBC,GAAlB,EAAuB;EACtB,IAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;IACtC,MAAM,IAAIC,SAAJ,CAAc,uDAAd,CAAN;EACA;;EAED,OAAOR,MAAM,CAACM,GAAD,CAAb;AACA;;AAED,SAASG,eAAT,GAA2B;EAC1B,IAAI;IACH,IAAI,CAACT,MAAM,CAACU,MAAZ,EAAoB;MACnB,OAAO,KAAP;IACA,CAHE,CAKH;IAEA;;;IACA,IAAIC,KAAK,GAAG,IAAIC,MAAJ,CAAW,KAAX,CAAZ,CARG,CAQ6B;;IAChCD,KAAK,CAAC,CAAD,CAAL,GAAW,IAAX;;IACA,IAAIX,MAAM,CAACa,mBAAP,CAA2BF,KAA3B,EAAkC,CAAlC,MAAyC,GAA7C,EAAkD;MACjD,OAAO,KAAP;IACA,CAZE,CAcH;;;IACA,IAAIG,KAAK,GAAG,EAAZ;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;MAC5BD,KAAK,CAAC,MAAMF,MAAM,CAACI,YAAP,CAAoBD,CAApB,CAAP,CAAL,GAAsCA,CAAtC;IACA;;IACD,IAAIE,MAAM,GAAGjB,MAAM,CAACa,mBAAP,CAA2BC,KAA3B,EAAkCI,GAAlC,CAAsC,UAAUC,CAAV,EAAa;MAC/D,OAAOL,KAAK,CAACK,CAAD,CAAZ;IACA,CAFY,CAAb;;IAGA,IAAIF,MAAM,CAACG,IAAP,CAAY,EAAZ,MAAoB,YAAxB,EAAsC;MACrC,OAAO,KAAP;IACA,CAxBE,CA0BH;;;IACA,IAAIC,KAAK,GAAG,EAAZ;IACA,uBAAuBC,KAAvB,CAA6B,EAA7B,EAAiCC,OAAjC,CAAyC,UAAUC,MAAV,EAAkB;MAC1DH,KAAK,CAACG,MAAD,CAAL,GAAgBA,MAAhB;IACA,CAFD;;IAGA,IAAIxB,MAAM,CAACyB,IAAP,CAAYzB,MAAM,CAACU,MAAP,CAAc,EAAd,EAAkBW,KAAlB,CAAZ,EAAsCD,IAAtC,CAA2C,EAA3C,MACF,sBADF,EAC0B;MACzB,OAAO,KAAP;IACA;;IAED,OAAO,IAAP;EACA,CArCD,CAqCE,OAAOM,GAAP,EAAY;IACb;IACA,OAAO,KAAP;EACA;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBnB,eAAe,KAAKT,MAAM,CAACU,MAAZ,GAAqB,UAAUmB,MAAV,EAAkBC,MAAlB,EAA0B;EAC9E,IAAIC,IAAJ;EACA,IAAIC,EAAE,GAAG3B,QAAQ,CAACwB,MAAD,CAAjB;EACA,IAAII,OAAJ;;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,SAAS,CAACC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C;IAC1CH,IAAI,GAAG/B,MAAM,CAACmC,SAAS,CAACD,CAAD,CAAV,CAAb;;IAEA,KAAK,IAAIG,GAAT,IAAgBN,IAAhB,EAAsB;MACrB,IAAI9B,cAAc,CAACqC,IAAf,CAAoBP,IAApB,EAA0BM,GAA1B,CAAJ,EAAoC;QACnCL,EAAE,CAACK,GAAD,CAAF,GAAUN,IAAI,CAACM,GAAD,CAAd;MACA;IACD;;IAED,IAAItC,qBAAJ,EAA2B;MAC1BkC,OAAO,GAAGlC,qBAAqB,CAACgC,IAAD,CAA/B;;MACA,KAAK,IAAIhB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkB,OAAO,CAACG,MAA5B,EAAoCrB,CAAC,EAArC,EAAyC;QACxC,IAAIZ,gBAAgB,CAACmC,IAAjB,CAAsBP,IAAtB,EAA4BE,OAAO,CAAClB,CAAD,CAAnC,CAAJ,EAA6C;UAC5CiB,EAAE,CAACC,OAAO,CAAClB,CAAD,CAAR,CAAF,GAAiBgB,IAAI,CAACE,OAAO,CAAClB,CAAD,CAAR,CAArB;QACA;MACD;IACD;EACD;;EAED,OAAOiB,EAAP;AACA,CAzBD"}, "metadata": {}, "sourceType": "script"}