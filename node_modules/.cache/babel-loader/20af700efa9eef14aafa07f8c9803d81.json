{"ast": null, "code": "'use strict';\n\nvar reactIs = require('react-is');\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\n\n\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\nTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\n\nfunction getStatics(component) {\n  // React v16.11 and below\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  } // React v16.12 and above\n\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\n\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\n\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n\n    var keys = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n\n  return targetComponent;\n}\n\nmodule.exports = hoistNonReactStatics;", "map": {"version": 3, "names": ["reactIs", "require", "REACT_STATICS", "childContextTypes", "contextType", "contextTypes", "defaultProps", "displayName", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "type", "KNOWN_STATICS", "name", "length", "prototype", "caller", "callee", "arguments", "arity", "FORWARD_REF_STATICS", "render", "MEMO_STATICS", "compare", "TYPE_STATICS", "ForwardRef", "Memo", "getStatics", "component", "isMemo", "defineProperty", "Object", "getOwnPropertyNames", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "getPrototypeOf", "objectPrototype", "hoistNonReactStatics", "targetComponent", "sourceComponent", "blacklist", "inheritedComponent", "keys", "concat", "targetStatics", "sourceStatics", "i", "key", "descriptor", "e", "module", "exports"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js"], "sourcesContent": ["'use strict';\n\nvar reactIs = require('react-is');\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\nTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\n\nfunction getStatics(component) {\n  // React v16.11 and below\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  } // React v16.12 and above\n\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\n\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n\n    var keys = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n\n  return targetComponent;\n}\n\nmodule.exports = hoistNonReactStatics;\n"], "mappings": "AAAA;;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAAC,UAAD,CAArB;AAEA;AACA;AACA;AACA;;;AACA,IAAIC,aAAa,GAAG;EAClBC,iBAAiB,EAAE,IADD;EAElBC,WAAW,EAAE,IAFK;EAGlBC,YAAY,EAAE,IAHI;EAIlBC,YAAY,EAAE,IAJI;EAKlBC,WAAW,EAAE,IALK;EAMlBC,eAAe,EAAE,IANC;EAOlBC,wBAAwB,EAAE,IAPR;EAQlBC,wBAAwB,EAAE,IARR;EASlBC,MAAM,EAAE,IATU;EAUlBC,SAAS,EAAE,IAVO;EAWlBC,IAAI,EAAE;AAXY,CAApB;AAaA,IAAIC,aAAa,GAAG;EAClBC,IAAI,EAAE,IADY;EAElBC,MAAM,EAAE,IAFU;EAGlBC,SAAS,EAAE,IAHO;EAIlBC,MAAM,EAAE,IAJU;EAKlBC,MAAM,EAAE,IALU;EAMlBC,SAAS,EAAE,IANO;EAOlBC,KAAK,EAAE;AAPW,CAApB;AASA,IAAIC,mBAAmB,GAAG;EACxB,YAAY,IADY;EAExBC,MAAM,EAAE,IAFgB;EAGxBjB,YAAY,EAAE,IAHU;EAIxBC,WAAW,EAAE,IAJW;EAKxBK,SAAS,EAAE;AALa,CAA1B;AAOA,IAAIY,YAAY,GAAG;EACjB,YAAY,IADK;EAEjBC,OAAO,EAAE,IAFQ;EAGjBnB,YAAY,EAAE,IAHG;EAIjBC,WAAW,EAAE,IAJI;EAKjBK,SAAS,EAAE,IALM;EAMjBC,IAAI,EAAE;AANW,CAAnB;AAQA,IAAIa,YAAY,GAAG,EAAnB;AACAA,YAAY,CAAC1B,OAAO,CAAC2B,UAAT,CAAZ,GAAmCL,mBAAnC;AACAI,YAAY,CAAC1B,OAAO,CAAC4B,IAAT,CAAZ,GAA6BJ,YAA7B;;AAEA,SAASK,UAAT,CAAoBC,SAApB,EAA+B;EAC7B;EACA,IAAI9B,OAAO,CAAC+B,MAAR,CAAeD,SAAf,CAAJ,EAA+B;IAC7B,OAAON,YAAP;EACD,CAJ4B,CAI3B;;;EAGF,OAAOE,YAAY,CAACI,SAAS,CAAC,UAAD,CAAV,CAAZ,IAAuC5B,aAA9C;AACD;;AAED,IAAI8B,cAAc,GAAGC,MAAM,CAACD,cAA5B;AACA,IAAIE,mBAAmB,GAAGD,MAAM,CAACC,mBAAjC;AACA,IAAIC,qBAAqB,GAAGF,MAAM,CAACE,qBAAnC;AACA,IAAIC,wBAAwB,GAAGH,MAAM,CAACG,wBAAtC;AACA,IAAIC,cAAc,GAAGJ,MAAM,CAACI,cAA5B;AACA,IAAIC,eAAe,GAAGL,MAAM,CAAChB,SAA7B;;AACA,SAASsB,oBAAT,CAA8BC,eAA9B,EAA+CC,eAA/C,EAAgEC,SAAhE,EAA2E;EACzE,IAAI,OAAOD,eAAP,KAA2B,QAA/B,EAAyC;IACvC;IACA,IAAIH,eAAJ,EAAqB;MACnB,IAAIK,kBAAkB,GAAGN,cAAc,CAACI,eAAD,CAAvC;;MAEA,IAAIE,kBAAkB,IAAIA,kBAAkB,KAAKL,eAAjD,EAAkE;QAChEC,oBAAoB,CAACC,eAAD,EAAkBG,kBAAlB,EAAsCD,SAAtC,CAApB;MACD;IACF;;IAED,IAAIE,IAAI,GAAGV,mBAAmB,CAACO,eAAD,CAA9B;;IAEA,IAAIN,qBAAJ,EAA2B;MACzBS,IAAI,GAAGA,IAAI,CAACC,MAAL,CAAYV,qBAAqB,CAACM,eAAD,CAAjC,CAAP;IACD;;IAED,IAAIK,aAAa,GAAGjB,UAAU,CAACW,eAAD,CAA9B;IACA,IAAIO,aAAa,GAAGlB,UAAU,CAACY,eAAD,CAA9B;;IAEA,KAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,IAAI,CAAC5B,MAAzB,EAAiC,EAAEgC,CAAnC,EAAsC;MACpC,IAAIC,GAAG,GAAGL,IAAI,CAACI,CAAD,CAAd;;MAEA,IAAI,CAAClC,aAAa,CAACmC,GAAD,CAAd,IAAuB,EAAEP,SAAS,IAAIA,SAAS,CAACO,GAAD,CAAxB,CAAvB,IAAyD,EAAEF,aAAa,IAAIA,aAAa,CAACE,GAAD,CAAhC,CAAzD,IAAmG,EAAEH,aAAa,IAAIA,aAAa,CAACG,GAAD,CAAhC,CAAvG,EAA+I;QAC7I,IAAIC,UAAU,GAAGd,wBAAwB,CAACK,eAAD,EAAkBQ,GAAlB,CAAzC;;QAEA,IAAI;UACF;UACAjB,cAAc,CAACQ,eAAD,EAAkBS,GAAlB,EAAuBC,UAAvB,CAAd;QACD,CAHD,CAGE,OAAOC,CAAP,EAAU,CAAE;MACf;IACF;EACF;;EAED,OAAOX,eAAP;AACD;;AAEDY,MAAM,CAACC,OAAP,GAAiBd,oBAAjB"}, "metadata": {}, "sourceType": "script"}