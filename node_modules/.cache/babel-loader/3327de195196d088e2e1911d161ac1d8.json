{"ast": null, "code": "/**\n * @license React\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n    }\n\n    var ReactVersion = '18.2.0'; // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types.\n\n    var REACT_ELEMENT_TYPE = Symbol.for('react.element');\n    var REACT_PORTAL_TYPE = Symbol.for('react.portal');\n    var REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\n    var REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\n    var REACT_PROFILER_TYPE = Symbol.for('react.profiler');\n    var REACT_PROVIDER_TYPE = Symbol.for('react.provider');\n    var REACT_CONTEXT_TYPE = Symbol.for('react.context');\n    var REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\n    var REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\n    var REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\n    var REACT_MEMO_TYPE = Symbol.for('react.memo');\n    var REACT_LAZY_TYPE = Symbol.for('react.lazy');\n    var REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\n    var MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\n    var FAUX_ITERATOR_SYMBOL = '@@iterator';\n\n    function getIteratorFn(maybeIterable) {\n      if (maybeIterable === null || typeof maybeIterable !== 'object') {\n        return null;\n      }\n\n      var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n      if (typeof maybeIterator === 'function') {\n        return maybeIterator;\n      }\n\n      return null;\n    }\n    /**\n     * Keeps track of the current dispatcher.\n     */\n\n\n    var ReactCurrentDispatcher = {\n      /**\n       * @internal\n       * @type {ReactComponent}\n       */\n      current: null\n    };\n    /**\n     * Keeps track of the current batch's configuration such as how long an update\n     * should suspend for if it needs to.\n     */\n\n    var ReactCurrentBatchConfig = {\n      transition: null\n    };\n    var ReactCurrentActQueue = {\n      current: null,\n      // Used to reproduce behavior of `batchedUpdates` in legacy mode.\n      isBatchingLegacy: false,\n      didScheduleLegacyUpdate: false\n    };\n    /**\n     * Keeps track of the current owner.\n     *\n     * The current owner is the component who should own any components that are\n     * currently being constructed.\n     */\n\n    var ReactCurrentOwner = {\n      /**\n       * @internal\n       * @type {ReactComponent}\n       */\n      current: null\n    };\n    var ReactDebugCurrentFrame = {};\n    var currentExtraStackFrame = null;\n\n    function setExtraStackFrame(stack) {\n      {\n        currentExtraStackFrame = stack;\n      }\n    }\n\n    {\n      ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n        {\n          currentExtraStackFrame = stack;\n        }\n      }; // Stack implementation injected by the current renderer.\n\n\n      ReactDebugCurrentFrame.getCurrentStack = null;\n\n      ReactDebugCurrentFrame.getStackAddendum = function () {\n        var stack = ''; // Add an extra top frame while an element is being validated\n\n        if (currentExtraStackFrame) {\n          stack += currentExtraStackFrame;\n        } // Delegate to the injected renderer-specific implementation\n\n\n        var impl = ReactDebugCurrentFrame.getCurrentStack;\n\n        if (impl) {\n          stack += impl() || '';\n        }\n\n        return stack;\n      };\n    } // -----------------------------------------------------------------------------\n\n    var enableScopeAPI = false; // Experimental Create Event Handle API.\n\n    var enableCacheElement = false;\n    var enableTransitionTracing = false; // No known bugs, but needs performance testing\n\n    var enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n    // stuff. Intended to enable React core members to more easily debug scheduling\n    // issues in DEV builds.\n\n    var enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\n    var ReactSharedInternals = {\n      ReactCurrentDispatcher: ReactCurrentDispatcher,\n      ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n      ReactCurrentOwner: ReactCurrentOwner\n    };\n    {\n      ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n      ReactSharedInternals.ReactCurrentActQueue = ReactCurrentActQueue;\n    } // by calls to these methods by a Babel plugin.\n    //\n    // In PROD (or in packages without access to React internals),\n    // they are left as they are instead.\n\n    function warn(format) {\n      {\n        {\n          for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n            args[_key - 1] = arguments[_key];\n          }\n\n          printWarning('warn', format, args);\n        }\n      }\n    }\n\n    function error(format) {\n      {\n        {\n          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n            args[_key2 - 1] = arguments[_key2];\n          }\n\n          printWarning('error', format, args);\n        }\n      }\n    }\n\n    function printWarning(level, format, args) {\n      // When changing this logic, you might want to also\n      // update consoleWithStackDev.www.js as well.\n      {\n        var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n        var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n        if (stack !== '') {\n          format += '%s';\n          args = args.concat([stack]);\n        } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n        var argsWithFormat = args.map(function (item) {\n          return String(item);\n        }); // Careful: RN currently depends on this prefix\n\n        argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n        // breaks IE9: https://github.com/facebook/react/issues/13610\n        // eslint-disable-next-line react-internal/no-production-logging\n\n        Function.prototype.apply.call(console[level], console, argsWithFormat);\n      }\n    }\n\n    var didWarnStateUpdateForUnmountedComponent = {};\n\n    function warnNoop(publicInstance, callerName) {\n      {\n        var _constructor = publicInstance.constructor;\n        var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n        var warningKey = componentName + \".\" + callerName;\n\n        if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n          return;\n        }\n\n        error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n        didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n      }\n    }\n    /**\n     * This is the abstract API for an update queue.\n     */\n\n\n    var ReactNoopUpdateQueue = {\n      /**\n       * Checks whether or not this composite component is mounted.\n       * @param {ReactClass} publicInstance The instance we want to test.\n       * @return {boolean} True if mounted, false otherwise.\n       * @protected\n       * @final\n       */\n      isMounted: function (publicInstance) {\n        return false;\n      },\n\n      /**\n       * Forces an update. This should only be invoked when it is known with\n       * certainty that we are **not** in a DOM transaction.\n       *\n       * You may want to call this when you know that some deeper aspect of the\n       * component's state has changed but `setState` was not called.\n       *\n       * This will not invoke `shouldComponentUpdate`, but it will invoke\n       * `componentWillUpdate` and `componentDidUpdate`.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} callerName name of the calling function in the public API.\n       * @internal\n       */\n      enqueueForceUpdate: function (publicInstance, callback, callerName) {\n        warnNoop(publicInstance, 'forceUpdate');\n      },\n\n      /**\n       * Replaces all of the state. Always use this or `setState` to mutate state.\n       * You should treat `this.state` as immutable.\n       *\n       * There is no guarantee that `this.state` will be immediately updated, so\n       * accessing `this.state` after calling this method may return the old value.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {object} completeState Next state.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} callerName name of the calling function in the public API.\n       * @internal\n       */\n      enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n        warnNoop(publicInstance, 'replaceState');\n      },\n\n      /**\n       * Sets a subset of the state. This only exists because _pendingState is\n       * internal. This provides a merging strategy that is not available to deep\n       * properties which is confusing. TODO: Expose pendingState or don't use it\n       * during the merge.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {object} partialState Next partial state to be merged with state.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} Name of the calling function in the public API.\n       * @internal\n       */\n      enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n        warnNoop(publicInstance, 'setState');\n      }\n    };\n    var assign = Object.assign;\n    var emptyObject = {};\n    {\n      Object.freeze(emptyObject);\n    }\n    /**\n     * Base class helpers for the updating state of a component.\n     */\n\n    function Component(props, context, updater) {\n      this.props = props;\n      this.context = context; // If a component has string refs, we will assign a different object later.\n\n      this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n      // renderer.\n\n      this.updater = updater || ReactNoopUpdateQueue;\n    }\n\n    Component.prototype.isReactComponent = {};\n    /**\n     * Sets a subset of the state. Always use this to mutate\n     * state. You should treat `this.state` as immutable.\n     *\n     * There is no guarantee that `this.state` will be immediately updated, so\n     * accessing `this.state` after calling this method may return the old value.\n     *\n     * There is no guarantee that calls to `setState` will run synchronously,\n     * as they may eventually be batched together.  You can provide an optional\n     * callback that will be executed when the call to setState is actually\n     * completed.\n     *\n     * When a function is provided to setState, it will be called at some point in\n     * the future (not synchronously). It will be called with the up to date\n     * component arguments (state, props, context). These values can be different\n     * from this.* because your function may be called after receiveProps but before\n     * shouldComponentUpdate, and this new state, props, and context will not yet be\n     * assigned to this.\n     *\n     * @param {object|function} partialState Next partial state or function to\n     *        produce next partial state to be merged with current state.\n     * @param {?function} callback Called after state is updated.\n     * @final\n     * @protected\n     */\n\n    Component.prototype.setState = function (partialState, callback) {\n      if (typeof partialState !== 'object' && typeof partialState !== 'function' && partialState != null) {\n        throw new Error('setState(...): takes an object of state variables to update or a ' + 'function which returns an object of state variables.');\n      }\n\n      this.updater.enqueueSetState(this, partialState, callback, 'setState');\n    };\n    /**\n     * Forces an update. This should only be invoked when it is known with\n     * certainty that we are **not** in a DOM transaction.\n     *\n     * You may want to call this when you know that some deeper aspect of the\n     * component's state has changed but `setState` was not called.\n     *\n     * This will not invoke `shouldComponentUpdate`, but it will invoke\n     * `componentWillUpdate` and `componentDidUpdate`.\n     *\n     * @param {?function} callback Called after update is complete.\n     * @final\n     * @protected\n     */\n\n\n    Component.prototype.forceUpdate = function (callback) {\n      this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n    };\n    /**\n     * Deprecated APIs. These APIs used to exist on classic React classes but since\n     * we would like to deprecate them, we're not going to move them over to this\n     * modern base class. Instead, we define a getter that warns if it's accessed.\n     */\n\n\n    {\n      var deprecatedAPIs = {\n        isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n        replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n      };\n\n      var defineDeprecationWarning = function (methodName, info) {\n        Object.defineProperty(Component.prototype, methodName, {\n          get: function () {\n            warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n            return undefined;\n          }\n        });\n      };\n\n      for (var fnName in deprecatedAPIs) {\n        if (deprecatedAPIs.hasOwnProperty(fnName)) {\n          defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n        }\n      }\n    }\n\n    function ComponentDummy() {}\n\n    ComponentDummy.prototype = Component.prototype;\n    /**\n     * Convenience component with default shallow equality check for sCU.\n     */\n\n    function PureComponent(props, context, updater) {\n      this.props = props;\n      this.context = context; // If a component has string refs, we will assign a different object later.\n\n      this.refs = emptyObject;\n      this.updater = updater || ReactNoopUpdateQueue;\n    }\n\n    var pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\n    pureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\n    assign(pureComponentPrototype, Component.prototype);\n    pureComponentPrototype.isPureReactComponent = true; // an immutable object with a single mutable value\n\n    function createRef() {\n      var refObject = {\n        current: null\n      };\n      {\n        Object.seal(refObject);\n      }\n      return refObject;\n    }\n\n    var isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\n    function isArray(a) {\n      return isArrayImpl(a);\n    }\n    /*\n     * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n     * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n     *\n     * The functions in this module will throw an easier-to-understand,\n     * easier-to-debug exception with a clear errors message message explaining the\n     * problem. (Instead of a confusing exception thrown inside the implementation\n     * of the `value` object).\n     */\n    // $FlowFixMe only called in DEV, so void return is not possible.\n\n\n    function typeName(value) {\n      {\n        // toStringTag is needed for namespaced types like Temporal.Instant\n        var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n        var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n        return type;\n      }\n    } // $FlowFixMe only called in DEV, so void return is not possible.\n\n\n    function willCoercionThrow(value) {\n      {\n        try {\n          testStringCoercion(value);\n          return false;\n        } catch (e) {\n          return true;\n        }\n      }\n    }\n\n    function testStringCoercion(value) {\n      // If you ended up here by following an exception call stack, here's what's\n      // happened: you supplied an object or symbol value to React (as a prop, key,\n      // DOM attribute, CSS property, string ref, etc.) and when React tried to\n      // coerce it to a string using `'' + value`, an exception was thrown.\n      //\n      // The most common types that will cause this exception are `Symbol` instances\n      // and Temporal objects like `Temporal.Instant`. But any object that has a\n      // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n      // exception. (Library authors do this to prevent users from using built-in\n      // numeric operators like `+` or comparison operators like `>=` because custom\n      // methods are needed to perform accurate arithmetic or comparison.)\n      //\n      // To fix the problem, coerce this object or symbol value to a string before\n      // passing it to React. The most reliable way is usually `String(value)`.\n      //\n      // To find which value is throwing, check the browser or debugger console.\n      // Before this exception was thrown, there should be `console.error` output\n      // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n      // problem and how that type was used: key, atrribute, input value prop, etc.\n      // In most cases, this console output also shows the component and its\n      // ancestor components where the exception happened.\n      //\n      // eslint-disable-next-line react-internal/safe-string-coercion\n      return '' + value;\n    }\n\n    function checkKeyStringCoercion(value) {\n      {\n        if (willCoercionThrow(value)) {\n          error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n          return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n        }\n      }\n    }\n\n    function getWrappedName(outerType, innerType, wrapperName) {\n      var displayName = outerType.displayName;\n\n      if (displayName) {\n        return displayName;\n      }\n\n      var functionName = innerType.displayName || innerType.name || '';\n      return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n    } // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\n    function getContextName(type) {\n      return type.displayName || 'Context';\n    } // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\n    function getComponentNameFromType(type) {\n      if (type == null) {\n        // Host root, text node or just invalid type.\n        return null;\n      }\n\n      {\n        if (typeof type.tag === 'number') {\n          error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n        }\n      }\n\n      if (typeof type === 'function') {\n        return type.displayName || type.name || null;\n      }\n\n      if (typeof type === 'string') {\n        return type;\n      }\n\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return 'Fragment';\n\n        case REACT_PORTAL_TYPE:\n          return 'Portal';\n\n        case REACT_PROFILER_TYPE:\n          return 'Profiler';\n\n        case REACT_STRICT_MODE_TYPE:\n          return 'StrictMode';\n\n        case REACT_SUSPENSE_TYPE:\n          return 'Suspense';\n\n        case REACT_SUSPENSE_LIST_TYPE:\n          return 'SuspenseList';\n      }\n\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_CONTEXT_TYPE:\n            var context = type;\n            return getContextName(context) + '.Consumer';\n\n          case REACT_PROVIDER_TYPE:\n            var provider = type;\n            return getContextName(provider._context) + '.Provider';\n\n          case REACT_FORWARD_REF_TYPE:\n            return getWrappedName(type, type.render, 'ForwardRef');\n\n          case REACT_MEMO_TYPE:\n            var outerName = type.displayName || null;\n\n            if (outerName !== null) {\n              return outerName;\n            }\n\n            return getComponentNameFromType(type.type) || 'Memo';\n\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n\n              try {\n                return getComponentNameFromType(init(payload));\n              } catch (x) {\n                return null;\n              }\n            }\n          // eslint-disable-next-line no-fallthrough\n        }\n      }\n\n      return null;\n    }\n\n    var hasOwnProperty = Object.prototype.hasOwnProperty;\n    var RESERVED_PROPS = {\n      key: true,\n      ref: true,\n      __self: true,\n      __source: true\n    };\n    var specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n    {\n      didWarnAboutStringRefs = {};\n    }\n\n    function hasValidRef(config) {\n      {\n        if (hasOwnProperty.call(config, 'ref')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.ref !== undefined;\n    }\n\n    function hasValidKey(config) {\n      {\n        if (hasOwnProperty.call(config, 'key')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.key !== undefined;\n    }\n\n    function defineKeyPropWarningGetter(props, displayName) {\n      var warnAboutAccessingKey = function () {\n        {\n          if (!specialPropKeyWarningShown) {\n            specialPropKeyWarningShown = true;\n            error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        }\n      };\n\n      warnAboutAccessingKey.isReactWarning = true;\n      Object.defineProperty(props, 'key', {\n        get: warnAboutAccessingKey,\n        configurable: true\n      });\n    }\n\n    function defineRefPropWarningGetter(props, displayName) {\n      var warnAboutAccessingRef = function () {\n        {\n          if (!specialPropRefWarningShown) {\n            specialPropRefWarningShown = true;\n            error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n\n    function warnIfStringRefCannotBeAutoConverted(config) {\n      {\n        if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n          var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n          if (!didWarnAboutStringRefs[componentName]) {\n            error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n            didWarnAboutStringRefs[componentName] = true;\n          }\n        }\n      }\n    }\n    /**\n     * Factory method to create a new React element. This no longer adheres to\n     * the class pattern, so do not use new to call it. Also, instanceof check\n     * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n     * if something is a React Element.\n     *\n     * @param {*} type\n     * @param {*} props\n     * @param {*} key\n     * @param {string|object} ref\n     * @param {*} owner\n     * @param {*} self A *temporary* helper to detect places where `this` is\n     * different from the `owner` when React.createElement is called, so that we\n     * can warn. We want to get rid of owner and replace string `ref`s with arrow\n     * functions, and as long as `this` and owner are the same, there will be no\n     * change in behavior.\n     * @param {*} source An annotation object (added by a transpiler or otherwise)\n     * indicating filename, line number, and/or other information.\n     * @internal\n     */\n\n\n    var ReactElement = function (type, key, ref, self, source, owner, props) {\n      var element = {\n        // This tag allows us to uniquely identify this as a React Element\n        $$typeof: REACT_ELEMENT_TYPE,\n        // Built-in properties that belong on the element\n        type: type,\n        key: key,\n        ref: ref,\n        props: props,\n        // Record the component responsible for creating this element.\n        _owner: owner\n      };\n      {\n        // The validation flag is currently mutative. We put it on\n        // an external backing store so that we can freeze the whole object.\n        // This can be replaced with a WeakMap once they are implemented in\n        // commonly used development environments.\n        element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n        // the validation flag non-enumerable (where possible, which should\n        // include every environment we run tests in), so the test framework\n        // ignores it.\n\n        Object.defineProperty(element._store, 'validated', {\n          configurable: false,\n          enumerable: false,\n          writable: true,\n          value: false\n        }); // self and source are DEV only properties.\n\n        Object.defineProperty(element, '_self', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: self\n        }); // Two elements created in two different places should be considered\n        // equal for testing purposes and therefore we hide it from enumeration.\n\n        Object.defineProperty(element, '_source', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: source\n        });\n\n        if (Object.freeze) {\n          Object.freeze(element.props);\n          Object.freeze(element);\n        }\n      }\n      return element;\n    };\n    /**\n     * Create and return a new ReactElement of the given type.\n     * See https://reactjs.org/docs/react-api.html#createelement\n     */\n\n\n    function createElement(type, config, children) {\n      var propName; // Reserved names are extracted\n\n      var props = {};\n      var key = null;\n      var ref = null;\n      var self = null;\n      var source = null;\n\n      if (config != null) {\n        if (hasValidRef(config)) {\n          ref = config.ref;\n          {\n            warnIfStringRefCannotBeAutoConverted(config);\n          }\n        }\n\n        if (hasValidKey(config)) {\n          {\n            checkKeyStringCoercion(config.key);\n          }\n          key = '' + config.key;\n        }\n\n        self = config.__self === undefined ? null : config.__self;\n        source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n        for (propName in config) {\n          if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n            props[propName] = config[propName];\n          }\n        }\n      } // Children can be more than one argument, and those are transferred onto\n      // the newly allocated props object.\n\n\n      var childrenLength = arguments.length - 2;\n\n      if (childrenLength === 1) {\n        props.children = children;\n      } else if (childrenLength > 1) {\n        var childArray = Array(childrenLength);\n\n        for (var i = 0; i < childrenLength; i++) {\n          childArray[i] = arguments[i + 2];\n        }\n\n        {\n          if (Object.freeze) {\n            Object.freeze(childArray);\n          }\n        }\n        props.children = childArray;\n      } // Resolve default props\n\n\n      if (type && type.defaultProps) {\n        var defaultProps = type.defaultProps;\n\n        for (propName in defaultProps) {\n          if (props[propName] === undefined) {\n            props[propName] = defaultProps[propName];\n          }\n        }\n      }\n\n      {\n        if (key || ref) {\n          var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n          if (key) {\n            defineKeyPropWarningGetter(props, displayName);\n          }\n\n          if (ref) {\n            defineRefPropWarningGetter(props, displayName);\n          }\n        }\n      }\n      return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n    }\n\n    function cloneAndReplaceKey(oldElement, newKey) {\n      var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n      return newElement;\n    }\n    /**\n     * Clone and return a new ReactElement using element as the starting point.\n     * See https://reactjs.org/docs/react-api.html#cloneelement\n     */\n\n\n    function cloneElement(element, config, children) {\n      if (element === null || element === undefined) {\n        throw new Error(\"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\");\n      }\n\n      var propName; // Original props are copied\n\n      var props = assign({}, element.props); // Reserved names are extracted\n\n      var key = element.key;\n      var ref = element.ref; // Self is preserved since the owner is preserved.\n\n      var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n      // transpiler, and the original source is probably a better indicator of the\n      // true owner.\n\n      var source = element._source; // Owner will be preserved, unless ref is overridden\n\n      var owner = element._owner;\n\n      if (config != null) {\n        if (hasValidRef(config)) {\n          // Silently steal the ref from the parent.\n          ref = config.ref;\n          owner = ReactCurrentOwner.current;\n        }\n\n        if (hasValidKey(config)) {\n          {\n            checkKeyStringCoercion(config.key);\n          }\n          key = '' + config.key;\n        } // Remaining properties override existing props\n\n\n        var defaultProps;\n\n        if (element.type && element.type.defaultProps) {\n          defaultProps = element.type.defaultProps;\n        }\n\n        for (propName in config) {\n          if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n            if (config[propName] === undefined && defaultProps !== undefined) {\n              // Resolve default props\n              props[propName] = defaultProps[propName];\n            } else {\n              props[propName] = config[propName];\n            }\n          }\n        }\n      } // Children can be more than one argument, and those are transferred onto\n      // the newly allocated props object.\n\n\n      var childrenLength = arguments.length - 2;\n\n      if (childrenLength === 1) {\n        props.children = children;\n      } else if (childrenLength > 1) {\n        var childArray = Array(childrenLength);\n\n        for (var i = 0; i < childrenLength; i++) {\n          childArray[i] = arguments[i + 2];\n        }\n\n        props.children = childArray;\n      }\n\n      return ReactElement(element.type, key, ref, self, source, owner, props);\n    }\n    /**\n     * Verifies the object is a ReactElement.\n     * See https://reactjs.org/docs/react-api.html#isvalidelement\n     * @param {?object} object\n     * @return {boolean} True if `object` is a ReactElement.\n     * @final\n     */\n\n\n    function isValidElement(object) {\n      return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n    }\n\n    var SEPARATOR = '.';\n    var SUBSEPARATOR = ':';\n    /**\n     * Escape and wrap key so it is safe to use as a reactid\n     *\n     * @param {string} key to be escaped.\n     * @return {string} the escaped key.\n     */\n\n    function escape(key) {\n      var escapeRegex = /[=:]/g;\n      var escaperLookup = {\n        '=': '=0',\n        ':': '=2'\n      };\n      var escapedString = key.replace(escapeRegex, function (match) {\n        return escaperLookup[match];\n      });\n      return '$' + escapedString;\n    }\n    /**\n     * TODO: Test that a single child and an array with one item have the same key\n     * pattern.\n     */\n\n\n    var didWarnAboutMaps = false;\n    var userProvidedKeyEscapeRegex = /\\/+/g;\n\n    function escapeUserProvidedKey(text) {\n      return text.replace(userProvidedKeyEscapeRegex, '$&/');\n    }\n    /**\n     * Generate a key string that identifies a element within a set.\n     *\n     * @param {*} element A element that could contain a manual key.\n     * @param {number} index Index that is used if a manual key is not provided.\n     * @return {string}\n     */\n\n\n    function getElementKey(element, index) {\n      // Do some typechecking here since we call this blindly. We want to ensure\n      // that we don't block potential future ES APIs.\n      if (typeof element === 'object' && element !== null && element.key != null) {\n        // Explicit key\n        {\n          checkKeyStringCoercion(element.key);\n        }\n        return escape('' + element.key);\n      } // Implicit key determined by the index in the set\n\n\n      return index.toString(36);\n    }\n\n    function mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n      var type = typeof children;\n\n      if (type === 'undefined' || type === 'boolean') {\n        // All of the above are perceived as null.\n        children = null;\n      }\n\n      var invokeCallback = false;\n\n      if (children === null) {\n        invokeCallback = true;\n      } else {\n        switch (type) {\n          case 'string':\n          case 'number':\n            invokeCallback = true;\n            break;\n\n          case 'object':\n            switch (children.$$typeof) {\n              case REACT_ELEMENT_TYPE:\n              case REACT_PORTAL_TYPE:\n                invokeCallback = true;\n            }\n\n        }\n      }\n\n      if (invokeCallback) {\n        var _child = children;\n        var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n        // so that it's consistent if the number of children grows:\n\n        var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n\n        if (isArray(mappedChild)) {\n          var escapedChildKey = '';\n\n          if (childKey != null) {\n            escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n          }\n\n          mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n            return c;\n          });\n        } else if (mappedChild != null) {\n          if (isValidElement(mappedChild)) {\n            {\n              // The `if` statement here prevents auto-disabling of the safe\n              // coercion ESLint rule, so we must manually disable it below.\n              // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n              if (mappedChild.key && (!_child || _child.key !== mappedChild.key)) {\n                checkKeyStringCoercion(mappedChild.key);\n              }\n            }\n            mappedChild = cloneAndReplaceKey(mappedChild, // Keep both the (mapped) and old keys if they differ, just as\n            // traverseAllChildren used to do for objects as children\n            escapedPrefix + ( // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n            mappedChild.key && (!_child || _child.key !== mappedChild.key) ? // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n            // eslint-disable-next-line react-internal/safe-string-coercion\n            escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n          }\n\n          array.push(mappedChild);\n        }\n\n        return 1;\n      }\n\n      var child;\n      var nextName;\n      var subtreeCount = 0; // Count of children found in the current subtree.\n\n      var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n\n      if (isArray(children)) {\n        for (var i = 0; i < children.length; i++) {\n          child = children[i];\n          nextName = nextNamePrefix + getElementKey(child, i);\n          subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n        }\n      } else {\n        var iteratorFn = getIteratorFn(children);\n\n        if (typeof iteratorFn === 'function') {\n          var iterableChildren = children;\n          {\n            // Warn about using Maps as children\n            if (iteratorFn === iterableChildren.entries) {\n              if (!didWarnAboutMaps) {\n                warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n              }\n\n              didWarnAboutMaps = true;\n            }\n          }\n          var iterator = iteratorFn.call(iterableChildren);\n          var step;\n          var ii = 0;\n\n          while (!(step = iterator.next()).done) {\n            child = step.value;\n            nextName = nextNamePrefix + getElementKey(child, ii++);\n            subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n          }\n        } else if (type === 'object') {\n          // eslint-disable-next-line react-internal/safe-string-coercion\n          var childrenString = String(children);\n          throw new Error(\"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). \" + 'If you meant to render a collection of children, use an array ' + 'instead.');\n        }\n      }\n\n      return subtreeCount;\n    }\n    /**\n     * Maps children that are typically specified as `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n     *\n     * The provided mapFunction(child, index) will be called for each\n     * leaf child.\n     *\n     * @param {?*} children Children tree container.\n     * @param {function(*, int)} func The map function.\n     * @param {*} context Context for mapFunction.\n     * @return {object} Object containing the ordered map of results.\n     */\n\n\n    function mapChildren(children, func, context) {\n      if (children == null) {\n        return children;\n      }\n\n      var result = [];\n      var count = 0;\n      mapIntoArray(children, result, '', '', function (child) {\n        return func.call(context, child, count++);\n      });\n      return result;\n    }\n    /**\n     * Count the number of children that are typically specified as\n     * `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrencount\n     *\n     * @param {?*} children Children tree container.\n     * @return {number} The number of children.\n     */\n\n\n    function countChildren(children) {\n      var n = 0;\n      mapChildren(children, function () {\n        n++; // Don't return anything\n      });\n      return n;\n    }\n    /**\n     * Iterates through children that are typically specified as `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n     *\n     * The provided forEachFunc(child, index) will be called for each\n     * leaf child.\n     *\n     * @param {?*} children Children tree container.\n     * @param {function(*, int)} forEachFunc\n     * @param {*} forEachContext Context for forEachContext.\n     */\n\n\n    function forEachChildren(children, forEachFunc, forEachContext) {\n      mapChildren(children, function () {\n        forEachFunc.apply(this, arguments); // Don't return anything.\n      }, forEachContext);\n    }\n    /**\n     * Flatten a children object (typically specified as `props.children`) and\n     * return an array with appropriately re-keyed children.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n     */\n\n\n    function toArray(children) {\n      return mapChildren(children, function (child) {\n        return child;\n      }) || [];\n    }\n    /**\n     * Returns the first child in a collection of children and verifies that there\n     * is only one child in the collection.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n     *\n     * The current implementation of this function assumes that a single child gets\n     * passed without a wrapper, but the purpose of this helper function is to\n     * abstract away the particular structure of children.\n     *\n     * @param {?object} children Child collection structure.\n     * @return {ReactElement} The first and only `ReactElement` contained in the\n     * structure.\n     */\n\n\n    function onlyChild(children) {\n      if (!isValidElement(children)) {\n        throw new Error('React.Children.only expected to receive a single React element child.');\n      }\n\n      return children;\n    }\n\n    function createContext(defaultValue) {\n      // TODO: Second argument used to be an optional `calculateChangedBits`\n      // function. Warn to reserve for future use?\n      var context = {\n        $$typeof: REACT_CONTEXT_TYPE,\n        // As a workaround to support multiple concurrent renderers, we categorize\n        // some renderers as primary and others as secondary. We only expect\n        // there to be two concurrent renderers at most: React Native (primary) and\n        // Fabric (secondary); React DOM (primary) and React ART (secondary).\n        // Secondary renderers store their context values on separate fields.\n        _currentValue: defaultValue,\n        _currentValue2: defaultValue,\n        // Used to track how many concurrent renderers this context currently\n        // supports within in a single renderer. Such as parallel server rendering.\n        _threadCount: 0,\n        // These are circular\n        Provider: null,\n        Consumer: null,\n        // Add these to use same hidden class in VM as ServerContext\n        _defaultValue: null,\n        _globalName: null\n      };\n      context.Provider = {\n        $$typeof: REACT_PROVIDER_TYPE,\n        _context: context\n      };\n      var hasWarnedAboutUsingNestedContextConsumers = false;\n      var hasWarnedAboutUsingConsumerProvider = false;\n      var hasWarnedAboutDisplayNameOnConsumer = false;\n      {\n        // A separate object, but proxies back to the original context object for\n        // backwards compatibility. It has a different $$typeof, so we can properly\n        // warn for the incorrect usage of Context as a Consumer.\n        var Consumer = {\n          $$typeof: REACT_CONTEXT_TYPE,\n          _context: context\n        }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n        Object.defineProperties(Consumer, {\n          Provider: {\n            get: function () {\n              if (!hasWarnedAboutUsingConsumerProvider) {\n                hasWarnedAboutUsingConsumerProvider = true;\n                error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n              }\n\n              return context.Provider;\n            },\n            set: function (_Provider) {\n              context.Provider = _Provider;\n            }\n          },\n          _currentValue: {\n            get: function () {\n              return context._currentValue;\n            },\n            set: function (_currentValue) {\n              context._currentValue = _currentValue;\n            }\n          },\n          _currentValue2: {\n            get: function () {\n              return context._currentValue2;\n            },\n            set: function (_currentValue2) {\n              context._currentValue2 = _currentValue2;\n            }\n          },\n          _threadCount: {\n            get: function () {\n              return context._threadCount;\n            },\n            set: function (_threadCount) {\n              context._threadCount = _threadCount;\n            }\n          },\n          Consumer: {\n            get: function () {\n              if (!hasWarnedAboutUsingNestedContextConsumers) {\n                hasWarnedAboutUsingNestedContextConsumers = true;\n                error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n              }\n\n              return context.Consumer;\n            }\n          },\n          displayName: {\n            get: function () {\n              return context.displayName;\n            },\n            set: function (displayName) {\n              if (!hasWarnedAboutDisplayNameOnConsumer) {\n                warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n                hasWarnedAboutDisplayNameOnConsumer = true;\n              }\n            }\n          }\n        }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n        context.Consumer = Consumer;\n      }\n      {\n        context._currentRenderer = null;\n        context._currentRenderer2 = null;\n      }\n      return context;\n    }\n\n    var Uninitialized = -1;\n    var Pending = 0;\n    var Resolved = 1;\n    var Rejected = 2;\n\n    function lazyInitializer(payload) {\n      if (payload._status === Uninitialized) {\n        var ctor = payload._result;\n        var thenable = ctor(); // Transition to the next state.\n        // This might throw either because it's missing or throws. If so, we treat it\n        // as still uninitialized and try again next time. Which is the same as what\n        // happens if the ctor or any wrappers processing the ctor throws. This might\n        // end up fixing it if the resolution was a concurrency bug.\n\n        thenable.then(function (moduleObject) {\n          if (payload._status === Pending || payload._status === Uninitialized) {\n            // Transition to the next state.\n            var resolved = payload;\n            resolved._status = Resolved;\n            resolved._result = moduleObject;\n          }\n        }, function (error) {\n          if (payload._status === Pending || payload._status === Uninitialized) {\n            // Transition to the next state.\n            var rejected = payload;\n            rejected._status = Rejected;\n            rejected._result = error;\n          }\n        });\n\n        if (payload._status === Uninitialized) {\n          // In case, we're still uninitialized, then we're waiting for the thenable\n          // to resolve. Set it as pending in the meantime.\n          var pending = payload;\n          pending._status = Pending;\n          pending._result = thenable;\n        }\n      }\n\n      if (payload._status === Resolved) {\n        var moduleObject = payload._result;\n        {\n          if (moduleObject === undefined) {\n            error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n            'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\\n\\n\" + 'Did you accidentally put curly braces around the import?', moduleObject);\n          }\n        }\n        {\n          if (!('default' in moduleObject)) {\n            error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n            'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n          }\n        }\n        return moduleObject.default;\n      } else {\n        throw payload._result;\n      }\n    }\n\n    function lazy(ctor) {\n      var payload = {\n        // We use these fields to store the result.\n        _status: Uninitialized,\n        _result: ctor\n      };\n      var lazyType = {\n        $$typeof: REACT_LAZY_TYPE,\n        _payload: payload,\n        _init: lazyInitializer\n      };\n      {\n        // In production, this would just set it on the object.\n        var defaultProps;\n        var propTypes; // $FlowFixMe\n\n        Object.defineProperties(lazyType, {\n          defaultProps: {\n            configurable: true,\n            get: function () {\n              return defaultProps;\n            },\n            set: function (newDefaultProps) {\n              error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n              defaultProps = newDefaultProps; // Match production behavior more closely:\n              // $FlowFixMe\n\n              Object.defineProperty(lazyType, 'defaultProps', {\n                enumerable: true\n              });\n            }\n          },\n          propTypes: {\n            configurable: true,\n            get: function () {\n              return propTypes;\n            },\n            set: function (newPropTypes) {\n              error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n              propTypes = newPropTypes; // Match production behavior more closely:\n              // $FlowFixMe\n\n              Object.defineProperty(lazyType, 'propTypes', {\n                enumerable: true\n              });\n            }\n          }\n        });\n      }\n      return lazyType;\n    }\n\n    function forwardRef(render) {\n      {\n        if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n          error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n        } else if (typeof render !== 'function') {\n          error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n        } else {\n          if (render.length !== 0 && render.length !== 2) {\n            error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n          }\n        }\n\n        if (render != null) {\n          if (render.defaultProps != null || render.propTypes != null) {\n            error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n          }\n        }\n      }\n      var elementType = {\n        $$typeof: REACT_FORWARD_REF_TYPE,\n        render: render\n      };\n      {\n        var ownName;\n        Object.defineProperty(elementType, 'displayName', {\n          enumerable: false,\n          configurable: true,\n          get: function () {\n            return ownName;\n          },\n          set: function (name) {\n            ownName = name; // The inner component shouldn't inherit this display name in most cases,\n            // because the component may be used elsewhere.\n            // But it's nice for anonymous functions to inherit the name,\n            // so that our component-stack generation logic will display their frames.\n            // An anonymous function generally suggests a pattern like:\n            //   React.forwardRef((props, ref) => {...});\n            // This kind of inner function is not used elsewhere so the side effect is okay.\n\n            if (!render.name && !render.displayName) {\n              render.displayName = name;\n            }\n          }\n        });\n      }\n      return elementType;\n    }\n\n    var REACT_MODULE_REFERENCE;\n    {\n      REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n    }\n\n    function isValidElementType(type) {\n      if (typeof type === 'string' || typeof type === 'function') {\n        return true;\n      } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n      if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden || type === REACT_OFFSCREEN_TYPE || enableScopeAPI || enableCacheElement || enableTransitionTracing) {\n        return true;\n      }\n\n      if (typeof type === 'object' && type !== null) {\n        if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n        // types supported by any Flight configuration anywhere since\n        // we don't know which Flight build this will end up being used\n        // with.\n        type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n          return true;\n        }\n      }\n\n      return false;\n    }\n\n    function memo(type, compare) {\n      {\n        if (!isValidElementType(type)) {\n          error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n        }\n      }\n      var elementType = {\n        $$typeof: REACT_MEMO_TYPE,\n        type: type,\n        compare: compare === undefined ? null : compare\n      };\n      {\n        var ownName;\n        Object.defineProperty(elementType, 'displayName', {\n          enumerable: false,\n          configurable: true,\n          get: function () {\n            return ownName;\n          },\n          set: function (name) {\n            ownName = name; // The inner component shouldn't inherit this display name in most cases,\n            // because the component may be used elsewhere.\n            // But it's nice for anonymous functions to inherit the name,\n            // so that our component-stack generation logic will display their frames.\n            // An anonymous function generally suggests a pattern like:\n            //   React.memo((props) => {...});\n            // This kind of inner function is not used elsewhere so the side effect is okay.\n\n            if (!type.name && !type.displayName) {\n              type.displayName = name;\n            }\n          }\n        });\n      }\n      return elementType;\n    }\n\n    function resolveDispatcher() {\n      var dispatcher = ReactCurrentDispatcher.current;\n      {\n        if (dispatcher === null) {\n          error('Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for' + ' one of the following reasons:\\n' + '1. You might have mismatching versions of React and the renderer (such as React DOM)\\n' + '2. You might be breaking the Rules of Hooks\\n' + '3. You might have more than one copy of React in the same app\\n' + 'See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.');\n        }\n      } // Will result in a null access error if accessed outside render phase. We\n      // intentionally don't throw our own error because this is in a hot path.\n      // Also helps ensure this is inlined.\n\n      return dispatcher;\n    }\n\n    function useContext(Context) {\n      var dispatcher = resolveDispatcher();\n      {\n        // TODO: add a more generic warning for invalid values.\n        if (Context._context !== undefined) {\n          var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n          // and nobody should be using this in existing code.\n\n          if (realContext.Consumer === Context) {\n            error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n          } else if (realContext.Provider === Context) {\n            error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n          }\n        }\n      }\n      return dispatcher.useContext(Context);\n    }\n\n    function useState(initialState) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useState(initialState);\n    }\n\n    function useReducer(reducer, initialArg, init) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useReducer(reducer, initialArg, init);\n    }\n\n    function useRef(initialValue) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useRef(initialValue);\n    }\n\n    function useEffect(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useEffect(create, deps);\n    }\n\n    function useInsertionEffect(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useInsertionEffect(create, deps);\n    }\n\n    function useLayoutEffect(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useLayoutEffect(create, deps);\n    }\n\n    function useCallback(callback, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useCallback(callback, deps);\n    }\n\n    function useMemo(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useMemo(create, deps);\n    }\n\n    function useImperativeHandle(ref, create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useImperativeHandle(ref, create, deps);\n    }\n\n    function useDebugValue(value, formatterFn) {\n      {\n        var dispatcher = resolveDispatcher();\n        return dispatcher.useDebugValue(value, formatterFn);\n      }\n    }\n\n    function useTransition() {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useTransition();\n    }\n\n    function useDeferredValue(value) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useDeferredValue(value);\n    }\n\n    function useId() {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useId();\n    }\n\n    function useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n    } // Helpers to patch console.logs to avoid logging during side-effect free\n    // replaying on render function. This currently only patches the object\n    // lazily which won't cover if the log function was extracted eagerly.\n    // We could also eagerly patch the method.\n\n\n    var disabledDepth = 0;\n    var prevLog;\n    var prevInfo;\n    var prevWarn;\n    var prevError;\n    var prevGroup;\n    var prevGroupCollapsed;\n    var prevGroupEnd;\n\n    function disabledLog() {}\n\n    disabledLog.__reactDisabledLog = true;\n\n    function disableLogs() {\n      {\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          prevLog = console.log;\n          prevInfo = console.info;\n          prevWarn = console.warn;\n          prevError = console.error;\n          prevGroup = console.group;\n          prevGroupCollapsed = console.groupCollapsed;\n          prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n          var props = {\n            configurable: true,\n            enumerable: true,\n            value: disabledLog,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            info: props,\n            log: props,\n            warn: props,\n            error: props,\n            group: props,\n            groupCollapsed: props,\n            groupEnd: props\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n\n        disabledDepth++;\n      }\n    }\n\n    function reenableLogs() {\n      {\n        disabledDepth--;\n\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          var props = {\n            configurable: true,\n            enumerable: true,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            log: assign({}, props, {\n              value: prevLog\n            }),\n            info: assign({}, props, {\n              value: prevInfo\n            }),\n            warn: assign({}, props, {\n              value: prevWarn\n            }),\n            error: assign({}, props, {\n              value: prevError\n            }),\n            group: assign({}, props, {\n              value: prevGroup\n            }),\n            groupCollapsed: assign({}, props, {\n              value: prevGroupCollapsed\n            }),\n            groupEnd: assign({}, props, {\n              value: prevGroupEnd\n            })\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n\n        if (disabledDepth < 0) {\n          error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n        }\n      }\n    }\n\n    var ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\n    var prefix;\n\n    function describeBuiltInComponentFrame(name, source, ownerFn) {\n      {\n        if (prefix === undefined) {\n          // Extract the VM specific prefix used by each line.\n          try {\n            throw Error();\n          } catch (x) {\n            var match = x.stack.trim().match(/\\n( *(at )?)/);\n            prefix = match && match[1] || '';\n          }\n        } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n        return '\\n' + prefix + name;\n      }\n    }\n\n    var reentry = false;\n    var componentFrameCache;\n    {\n      var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n      componentFrameCache = new PossiblyWeakMap();\n    }\n\n    function describeNativeComponentFrame(fn, construct) {\n      // If something asked for a stack inside a fake render, it should get ignored.\n      if (!fn || reentry) {\n        return '';\n      }\n\n      {\n        var frame = componentFrameCache.get(fn);\n\n        if (frame !== undefined) {\n          return frame;\n        }\n      }\n      var control;\n      reentry = true;\n      var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n      Error.prepareStackTrace = undefined;\n      var previousDispatcher;\n      {\n        previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n        // for warnings.\n\n        ReactCurrentDispatcher$1.current = null;\n        disableLogs();\n      }\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            }\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          }\n\n          fn();\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          // This extracts the first frame from the sample that isn't also in the control.\n          // Skipping one frame that we assume is the frame that calls the two.\n          var sampleLines = sample.stack.split('\\n');\n          var controlLines = control.stack.split('\\n');\n          var s = sampleLines.length - 1;\n          var c = controlLines.length - 1;\n\n          while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n            // We expect at least one stack frame to be shared.\n            // Typically this will be the root most one. However, stack frames may be\n            // cut off due to maximum stack limits. In this case, one maybe cut off\n            // earlier than the other. We assume that the sample is longer or the same\n            // and there for cut off earlier. So we should find the root most frame in\n            // the sample somewhere in the control.\n            c--;\n          }\n\n          for (; s >= 1 && c >= 0; s--, c--) {\n            // Next we find the first one that isn't the same which should be the\n            // frame that called our sample function and the control.\n            if (sampleLines[s] !== controlLines[c]) {\n              // In V8, the first line is describing the message but other VMs don't.\n              // If we're about to return the first line, and the control is also on the same\n              // line, that's a pretty good indicator that our sample threw at same line as\n              // the control. I.e. before we entered the sample frame. So we ignore this result.\n              // This can happen if you passed a class to function component, or non-function.\n              if (s !== 1 || c !== 1) {\n                do {\n                  s--;\n                  c--; // We may still have similar intermediate frames from the construct call.\n                  // The next one that isn't the same should be our match though.\n\n                  if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                    // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                    var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                    // but we have a user-provided \"displayName\"\n                    // splice it in to make the stack more readable.\n\n\n                    if (fn.displayName && _frame.includes('<anonymous>')) {\n                      _frame = _frame.replace('<anonymous>', fn.displayName);\n                    }\n\n                    {\n                      if (typeof fn === 'function') {\n                        componentFrameCache.set(fn, _frame);\n                      }\n                    } // Return the line we found.\n\n                    return _frame;\n                  }\n                } while (s >= 1 && c >= 0);\n              }\n\n              break;\n            }\n          }\n        }\n      } finally {\n        reentry = false;\n        {\n          ReactCurrentDispatcher$1.current = previousDispatcher;\n          reenableLogs();\n        }\n        Error.prepareStackTrace = previousPrepareStackTrace;\n      } // Fallback to just using the name if we couldn't make it throw.\n\n\n      var name = fn ? fn.displayName || fn.name : '';\n      var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n      {\n        if (typeof fn === 'function') {\n          componentFrameCache.set(fn, syntheticFrame);\n        }\n      }\n      return syntheticFrame;\n    }\n\n    function describeFunctionComponentFrame(fn, source, ownerFn) {\n      {\n        return describeNativeComponentFrame(fn, false);\n      }\n    }\n\n    function shouldConstruct(Component) {\n      var prototype = Component.prototype;\n      return !!(prototype && prototype.isReactComponent);\n    }\n\n    function describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n      if (type == null) {\n        return '';\n      }\n\n      if (typeof type === 'function') {\n        {\n          return describeNativeComponentFrame(type, shouldConstruct(type));\n        }\n      }\n\n      if (typeof type === 'string') {\n        return describeBuiltInComponentFrame(type);\n      }\n\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame('Suspense');\n\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame('SuspenseList');\n      }\n\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return describeFunctionComponentFrame(type.render);\n\n          case REACT_MEMO_TYPE:\n            // Memo may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n\n              try {\n                // Lazy may contain any component type so we recursively resolve it.\n                return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n              } catch (x) {}\n            }\n        }\n      }\n\n      return '';\n    }\n\n    var loggedTypeFailures = {};\n    var ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\n    function setCurrentlyValidatingElement(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n        } else {\n          ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n        }\n      }\n    }\n\n    function checkPropTypes(typeSpecs, values, location, componentName, element) {\n      {\n        // $FlowFixMe This is okay but Flow doesn't know it.\n        var has = Function.call.bind(hasOwnProperty);\n\n        for (var typeSpecName in typeSpecs) {\n          if (has(typeSpecs, typeSpecName)) {\n            var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n            // fail the render phase where it didn't fail before. So we log it.\n            // After these have been cleaned up, we'll let them throw.\n\n            try {\n              // This is intentionally an invariant that gets caught. It's the same\n              // behavior as without this statement except with a better message.\n              if (typeof typeSpecs[typeSpecName] !== 'function') {\n                // eslint-disable-next-line react-internal/prod-error-codes\n                var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n                err.name = 'Invariant Violation';\n                throw err;\n              }\n\n              error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n            } catch (ex) {\n              error$1 = ex;\n            }\n\n            if (error$1 && !(error$1 instanceof Error)) {\n              setCurrentlyValidatingElement(element);\n              error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n              setCurrentlyValidatingElement(null);\n            }\n\n            if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n              // Only monitor this failure once because there tends to be a lot of the\n              // same error.\n              loggedTypeFailures[error$1.message] = true;\n              setCurrentlyValidatingElement(element);\n              error('Failed %s type: %s', location, error$1.message);\n              setCurrentlyValidatingElement(null);\n            }\n          }\n        }\n      }\n    }\n\n    function setCurrentlyValidatingElement$1(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          setExtraStackFrame(stack);\n        } else {\n          setExtraStackFrame(null);\n        }\n      }\n    }\n\n    var propTypesMisspellWarningShown;\n    {\n      propTypesMisspellWarningShown = false;\n    }\n\n    function getDeclarationErrorAddendum() {\n      if (ReactCurrentOwner.current) {\n        var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n        if (name) {\n          return '\\n\\nCheck the render method of `' + name + '`.';\n        }\n      }\n\n      return '';\n    }\n\n    function getSourceInfoErrorAddendum(source) {\n      if (source !== undefined) {\n        var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n        var lineNumber = source.lineNumber;\n        return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n      }\n\n      return '';\n    }\n\n    function getSourceInfoErrorAddendumForProps(elementProps) {\n      if (elementProps !== null && elementProps !== undefined) {\n        return getSourceInfoErrorAddendum(elementProps.__source);\n      }\n\n      return '';\n    }\n    /**\n     * Warn if there's no key explicitly set on dynamic arrays of children or\n     * object keys are not valid. This allows us to keep track of children between\n     * updates.\n     */\n\n\n    var ownerHasKeyUseWarning = {};\n\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = getDeclarationErrorAddendum();\n\n      if (!info) {\n        var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n        if (parentName) {\n          info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n        }\n      }\n\n      return info;\n    }\n    /**\n     * Warn if the element doesn't have an explicit key assigned to it.\n     * This element is in an array. The array could grow and shrink or be\n     * reordered. All children that haven't already been validated are required to\n     * have a \"key\" property assigned to it. Error statuses are cached so a warning\n     * will only be shown once.\n     *\n     * @internal\n     * @param {ReactElement} element Element that requires a key.\n     * @param {*} parentType element's parent's type.\n     */\n\n\n    function validateExplicitKey(element, parentType) {\n      if (!element._store || element._store.validated || element.key != null) {\n        return;\n      }\n\n      element._store.validated = true;\n      var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n      if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n        return;\n      }\n\n      ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n      // property, it may be the creator of the child that's responsible for\n      // assigning it a key.\n\n      var childOwner = '';\n\n      if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n        // Give the component that originally created this child.\n        childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n      }\n\n      {\n        setCurrentlyValidatingElement$1(element);\n        error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n        setCurrentlyValidatingElement$1(null);\n      }\n    }\n    /**\n     * Ensure that every element either is passed in a static location, in an\n     * array with an explicit keys property defined, or in an object literal\n     * with valid key property.\n     *\n     * @internal\n     * @param {ReactNode} node Statically passed child of any type.\n     * @param {*} parentType node's parent's type.\n     */\n\n\n    function validateChildKeys(node, parentType) {\n      if (typeof node !== 'object') {\n        return;\n      }\n\n      if (isArray(node)) {\n        for (var i = 0; i < node.length; i++) {\n          var child = node[i];\n\n          if (isValidElement(child)) {\n            validateExplicitKey(child, parentType);\n          }\n        }\n      } else if (isValidElement(node)) {\n        // This element was passed in a valid location.\n        if (node._store) {\n          node._store.validated = true;\n        }\n      } else if (node) {\n        var iteratorFn = getIteratorFn(node);\n\n        if (typeof iteratorFn === 'function') {\n          // Entry iterators used to provide implicit keys,\n          // but now we print a separate warning for them later.\n          if (iteratorFn !== node.entries) {\n            var iterator = iteratorFn.call(node);\n            var step;\n\n            while (!(step = iterator.next()).done) {\n              if (isValidElement(step.value)) {\n                validateExplicitKey(step.value, parentType);\n              }\n            }\n          }\n        }\n      }\n    }\n    /**\n     * Given an element, validate that its props follow the propTypes definition,\n     * provided by the type.\n     *\n     * @param {ReactElement} element\n     */\n\n\n    function validatePropTypes(element) {\n      {\n        var type = element.type;\n\n        if (type === null || type === undefined || typeof type === 'string') {\n          return;\n        }\n\n        var propTypes;\n\n        if (typeof type === 'function') {\n          propTypes = type.propTypes;\n        } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n        // Inner props are checked in the reconciler.\n        type.$$typeof === REACT_MEMO_TYPE)) {\n          propTypes = type.propTypes;\n        } else {\n          return;\n        }\n\n        if (propTypes) {\n          // Intentionally inside to avoid triggering lazy initializers:\n          var name = getComponentNameFromType(type);\n          checkPropTypes(propTypes, element.props, 'prop', name, element);\n        } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n          propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n          var _name = getComponentNameFromType(type);\n\n          error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n        }\n\n        if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n          error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n        }\n      }\n    }\n    /**\n     * Given a fragment, validate that it can only be provided with fragment props\n     * @param {ReactElement} fragment\n     */\n\n\n    function validateFragmentProps(fragment) {\n      {\n        var keys = Object.keys(fragment.props);\n\n        for (var i = 0; i < keys.length; i++) {\n          var key = keys[i];\n\n          if (key !== 'children' && key !== 'key') {\n            setCurrentlyValidatingElement$1(fragment);\n            error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n            setCurrentlyValidatingElement$1(null);\n            break;\n          }\n        }\n\n        if (fragment.ref !== null) {\n          setCurrentlyValidatingElement$1(fragment);\n          error('Invalid attribute `ref` supplied to `React.Fragment`.');\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n\n    function createElementWithValidation(type, props, children) {\n      var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n\n      if (!validType) {\n        var info = '';\n\n        if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n          info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n        }\n\n        var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n\n        if (sourceInfo) {\n          info += sourceInfo;\n        } else {\n          info += getDeclarationErrorAddendum();\n        }\n\n        var typeString;\n\n        if (type === null) {\n          typeString = 'null';\n        } else if (isArray(type)) {\n          typeString = 'array';\n        } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n          typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n          info = ' Did you accidentally export a JSX literal instead of a component?';\n        } else {\n          typeString = typeof type;\n        }\n\n        {\n          error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n        }\n      }\n\n      var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n      // TODO: Drop this when these are no longer allowed as the type argument.\n\n      if (element == null) {\n        return element;\n      } // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing errors.\n      // We don't want exception behavior to differ between dev and prod.\n      // (Rendering will throw with a helpful message and as soon as the type is\n      // fixed, the key warnings will appear.)\n\n\n      if (validType) {\n        for (var i = 2; i < arguments.length; i++) {\n          validateChildKeys(arguments[i], type);\n        }\n      }\n\n      if (type === REACT_FRAGMENT_TYPE) {\n        validateFragmentProps(element);\n      } else {\n        validatePropTypes(element);\n      }\n\n      return element;\n    }\n\n    var didWarnAboutDeprecatedCreateFactory = false;\n\n    function createFactoryWithValidation(type) {\n      var validatedFactory = createElementWithValidation.bind(null, type);\n      validatedFactory.type = type;\n      {\n        if (!didWarnAboutDeprecatedCreateFactory) {\n          didWarnAboutDeprecatedCreateFactory = true;\n          warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n        } // Legacy hook: remove it\n\n\n        Object.defineProperty(validatedFactory, 'type', {\n          enumerable: false,\n          get: function () {\n            warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n            Object.defineProperty(this, 'type', {\n              value: type\n            });\n            return type;\n          }\n        });\n      }\n      return validatedFactory;\n    }\n\n    function cloneElementWithValidation(element, props, children) {\n      var newElement = cloneElement.apply(this, arguments);\n\n      for (var i = 2; i < arguments.length; i++) {\n        validateChildKeys(arguments[i], newElement.type);\n      }\n\n      validatePropTypes(newElement);\n      return newElement;\n    }\n\n    function startTransition(scope, options) {\n      var prevTransition = ReactCurrentBatchConfig.transition;\n      ReactCurrentBatchConfig.transition = {};\n      var currentTransition = ReactCurrentBatchConfig.transition;\n      {\n        ReactCurrentBatchConfig.transition._updatedFibers = new Set();\n      }\n\n      try {\n        scope();\n      } finally {\n        ReactCurrentBatchConfig.transition = prevTransition;\n        {\n          if (prevTransition === null && currentTransition._updatedFibers) {\n            var updatedFibersCount = currentTransition._updatedFibers.size;\n\n            if (updatedFibersCount > 10) {\n              warn('Detected a large number of updates inside startTransition. ' + 'If this is due to a subscription please re-write it to use React provided hooks. ' + 'Otherwise concurrent mode guarantees are off the table.');\n            }\n\n            currentTransition._updatedFibers.clear();\n          }\n        }\n      }\n    }\n\n    var didWarnAboutMessageChannel = false;\n    var enqueueTaskImpl = null;\n\n    function enqueueTask(task) {\n      if (enqueueTaskImpl === null) {\n        try {\n          // read require off the module object to get around the bundlers.\n          // we don't want them to detect a require and bundle a Node polyfill.\n          var requireString = ('require' + Math.random()).slice(0, 7);\n          var nodeRequire = module && module[requireString]; // assuming we're in node, let's try to get node's\n          // version of setImmediate, bypassing fake timers if any.\n\n          enqueueTaskImpl = nodeRequire.call(module, 'timers').setImmediate;\n        } catch (_err) {\n          // we're in a browser\n          // we can't use regular timers because they may still be faked\n          // so we try MessageChannel+postMessage instead\n          enqueueTaskImpl = function (callback) {\n            {\n              if (didWarnAboutMessageChannel === false) {\n                didWarnAboutMessageChannel = true;\n\n                if (typeof MessageChannel === 'undefined') {\n                  error('This browser does not have a MessageChannel implementation, ' + 'so enqueuing tasks via await act(async () => ...) will fail. ' + 'Please file an issue at https://github.com/facebook/react/issues ' + 'if you encounter this warning.');\n                }\n              }\n            }\n            var channel = new MessageChannel();\n            channel.port1.onmessage = callback;\n            channel.port2.postMessage(undefined);\n          };\n        }\n      }\n\n      return enqueueTaskImpl(task);\n    }\n\n    var actScopeDepth = 0;\n    var didWarnNoAwaitAct = false;\n\n    function act(callback) {\n      {\n        // `act` calls can be nested, so we track the depth. This represents the\n        // number of `act` scopes on the stack.\n        var prevActScopeDepth = actScopeDepth;\n        actScopeDepth++;\n\n        if (ReactCurrentActQueue.current === null) {\n          // This is the outermost `act` scope. Initialize the queue. The reconciler\n          // will detect the queue and use it instead of Scheduler.\n          ReactCurrentActQueue.current = [];\n        }\n\n        var prevIsBatchingLegacy = ReactCurrentActQueue.isBatchingLegacy;\n        var result;\n\n        try {\n          // Used to reproduce behavior of `batchedUpdates` in legacy mode. Only\n          // set to `true` while the given callback is executed, not for updates\n          // triggered during an async event, because this is how the legacy\n          // implementation of `act` behaved.\n          ReactCurrentActQueue.isBatchingLegacy = true;\n          result = callback(); // Replicate behavior of original `act` implementation in legacy mode,\n          // which flushed updates immediately after the scope function exits, even\n          // if it's an async function.\n\n          if (!prevIsBatchingLegacy && ReactCurrentActQueue.didScheduleLegacyUpdate) {\n            var queue = ReactCurrentActQueue.current;\n\n            if (queue !== null) {\n              ReactCurrentActQueue.didScheduleLegacyUpdate = false;\n              flushActQueue(queue);\n            }\n          }\n        } catch (error) {\n          popActScope(prevActScopeDepth);\n          throw error;\n        } finally {\n          ReactCurrentActQueue.isBatchingLegacy = prevIsBatchingLegacy;\n        }\n\n        if (result !== null && typeof result === 'object' && typeof result.then === 'function') {\n          var thenableResult = result; // The callback is an async function (i.e. returned a promise). Wait\n          // for it to resolve before exiting the current scope.\n\n          var wasAwaited = false;\n          var thenable = {\n            then: function (resolve, reject) {\n              wasAwaited = true;\n              thenableResult.then(function (returnValue) {\n                popActScope(prevActScopeDepth);\n\n                if (actScopeDepth === 0) {\n                  // We've exited the outermost act scope. Recursively flush the\n                  // queue until there's no remaining work.\n                  recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n                } else {\n                  resolve(returnValue);\n                }\n              }, function (error) {\n                // The callback threw an error.\n                popActScope(prevActScopeDepth);\n                reject(error);\n              });\n            }\n          };\n          {\n            if (!didWarnNoAwaitAct && typeof Promise !== 'undefined') {\n              // eslint-disable-next-line no-undef\n              Promise.resolve().then(function () {}).then(function () {\n                if (!wasAwaited) {\n                  didWarnNoAwaitAct = true;\n                  error('You called act(async () => ...) without await. ' + 'This could lead to unexpected testing behaviour, ' + 'interleaving multiple act calls and mixing their ' + 'scopes. ' + 'You should - await act(async () => ...);');\n                }\n              });\n            }\n          }\n          return thenable;\n        } else {\n          var returnValue = result; // The callback is not an async function. Exit the current scope\n          // immediately, without awaiting.\n\n          popActScope(prevActScopeDepth);\n\n          if (actScopeDepth === 0) {\n            // Exiting the outermost act scope. Flush the queue.\n            var _queue = ReactCurrentActQueue.current;\n\n            if (_queue !== null) {\n              flushActQueue(_queue);\n              ReactCurrentActQueue.current = null;\n            } // Return a thenable. If the user awaits it, we'll flush again in\n            // case additional work was scheduled by a microtask.\n\n\n            var _thenable = {\n              then: function (resolve, reject) {\n                // Confirm we haven't re-entered another `act` scope, in case\n                // the user does something weird like await the thenable\n                // multiple times.\n                if (ReactCurrentActQueue.current === null) {\n                  // Recursively flush the queue until there's no remaining work.\n                  ReactCurrentActQueue.current = [];\n                  recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n                } else {\n                  resolve(returnValue);\n                }\n              }\n            };\n            return _thenable;\n          } else {\n            // Since we're inside a nested `act` scope, the returned thenable\n            // immediately resolves. The outer scope will flush the queue.\n            var _thenable2 = {\n              then: function (resolve, reject) {\n                resolve(returnValue);\n              }\n            };\n            return _thenable2;\n          }\n        }\n      }\n    }\n\n    function popActScope(prevActScopeDepth) {\n      {\n        if (prevActScopeDepth !== actScopeDepth - 1) {\n          error('You seem to have overlapping act() calls, this is not supported. ' + 'Be sure to await previous act() calls before making a new one. ');\n        }\n\n        actScopeDepth = prevActScopeDepth;\n      }\n    }\n\n    function recursivelyFlushAsyncActWork(returnValue, resolve, reject) {\n      {\n        var queue = ReactCurrentActQueue.current;\n\n        if (queue !== null) {\n          try {\n            flushActQueue(queue);\n            enqueueTask(function () {\n              if (queue.length === 0) {\n                // No additional work was scheduled. Finish.\n                ReactCurrentActQueue.current = null;\n                resolve(returnValue);\n              } else {\n                // Keep flushing work until there's none left.\n                recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n              }\n            });\n          } catch (error) {\n            reject(error);\n          }\n        } else {\n          resolve(returnValue);\n        }\n      }\n    }\n\n    var isFlushing = false;\n\n    function flushActQueue(queue) {\n      {\n        if (!isFlushing) {\n          // Prevent re-entrance.\n          isFlushing = true;\n          var i = 0;\n\n          try {\n            for (; i < queue.length; i++) {\n              var callback = queue[i];\n\n              do {\n                callback = callback(true);\n              } while (callback !== null);\n            }\n\n            queue.length = 0;\n          } catch (error) {\n            // If something throws, leave the remaining callbacks on the queue.\n            queue = queue.slice(i + 1);\n            throw error;\n          } finally {\n            isFlushing = false;\n          }\n        }\n      }\n    }\n\n    var createElement$1 = createElementWithValidation;\n    var cloneElement$1 = cloneElementWithValidation;\n    var createFactory = createFactoryWithValidation;\n    var Children = {\n      map: mapChildren,\n      forEach: forEachChildren,\n      count: countChildren,\n      toArray: toArray,\n      only: onlyChild\n    };\n    exports.Children = Children;\n    exports.Component = Component;\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.Profiler = REACT_PROFILER_TYPE;\n    exports.PureComponent = PureComponent;\n    exports.StrictMode = REACT_STRICT_MODE_TYPE;\n    exports.Suspense = REACT_SUSPENSE_TYPE;\n    exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\n    exports.cloneElement = cloneElement$1;\n    exports.createContext = createContext;\n    exports.createElement = createElement$1;\n    exports.createFactory = createFactory;\n    exports.createRef = createRef;\n    exports.forwardRef = forwardRef;\n    exports.isValidElement = isValidElement;\n    exports.lazy = lazy;\n    exports.memo = memo;\n    exports.startTransition = startTransition;\n    exports.unstable_act = act;\n    exports.useCallback = useCallback;\n    exports.useContext = useContext;\n    exports.useDebugValue = useDebugValue;\n    exports.useDeferredValue = useDeferredValue;\n    exports.useEffect = useEffect;\n    exports.useId = useId;\n    exports.useImperativeHandle = useImperativeHandle;\n    exports.useInsertionEffect = useInsertionEffect;\n    exports.useLayoutEffect = useLayoutEffect;\n    exports.useMemo = useMemo;\n    exports.useReducer = useReducer;\n    exports.useRef = useRef;\n    exports.useState = useState;\n    exports.useSyncExternalStore = useSyncExternalStore;\n    exports.useTransition = useTransition;\n    exports.version = ReactVersion;\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n    }\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "registerInternalModuleStart", "Error", "ReactVersion", "REACT_ELEMENT_TYPE", "Symbol", "for", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_OFFSCREEN_TYPE", "MAYBE_ITERATOR_SYMBOL", "iterator", "FAUX_ITERATOR_SYMBOL", "getIteratorFn", "maybeIterable", "maybeIterator", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "ReactCurrentBatchConfig", "transition", "ReactCurrentActQueue", "isBatchingLegacy", "didScheduleLegacyUpdate", "ReactCurrentOwner", "ReactDebugCurrentFrame", "currentExtraStackFrame", "setExtraStackFrame", "stack", "getCurrentStack", "getStackAddendum", "impl", "enableScopeAPI", "enableCacheElement", "enableTransitionTracing", "enableLegacyHidden", "enableDebugTracing", "ReactSharedInternals", "warn", "format", "_len", "arguments", "length", "args", "Array", "_key", "printWarning", "error", "_len2", "_key2", "level", "concat", "argsWithFormat", "map", "item", "String", "unshift", "Function", "prototype", "apply", "call", "console", "didWarnStateUpdateForUnmountedComponent", "warnNoop", "publicInstance", "callerName", "_constructor", "constructor", "componentName", "displayName", "name", "<PERSON><PERSON><PERSON>", "ReactNoopUpdateQueue", "isMounted", "enqueueForceUpdate", "callback", "enqueueReplaceState", "completeState", "enqueueSetState", "partialState", "assign", "Object", "emptyObject", "freeze", "Component", "props", "context", "updater", "refs", "isReactComponent", "setState", "forceUpdate", "deprecatedAPIs", "replaceState", "defineDeprecationWarning", "methodName", "info", "defineProperty", "get", "undefined", "fnName", "hasOwnProperty", "ComponentDummy", "PureComponent", "pureComponentPrototype", "isPureReactComponent", "createRef", "refObject", "seal", "isArrayImpl", "isArray", "a", "typeName", "value", "hasToStringTag", "toStringTag", "type", "willCoercionThrow", "testStringCoercion", "e", "checkKeyStringCoercion", "getWrappedName", "outerType", "innerType", "wrapperName", "functionName", "getContextName", "getComponentNameFromType", "tag", "$$typeof", "provider", "_context", "render", "outerName", "lazyComponent", "payload", "_payload", "init", "_init", "x", "RESERVED_PROPS", "key", "ref", "__self", "__source", "specialPropKeyWarningShown", "specialPropRefWarningShown", "didWarnAboutStringRefs", "hasValidRef", "config", "getter", "getOwnPropertyDescriptor", "isReactWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defineKeyPropWarningGetter", "warnAboutAccessingKey", "configurable", "defineRefPropWarningGetter", "warnAboutAccessingRef", "warnIfStringRefCannotBeAutoConverted", "stateNode", "ReactElement", "self", "source", "owner", "element", "_owner", "_store", "enumerable", "writable", "createElement", "children", "propName", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "i", "defaultProps", "cloneAndReplaceKey", "oldElement", "new<PERSON>ey", "newElement", "_self", "_source", "cloneElement", "isValidElement", "object", "SEPARATOR", "SUBSEPARATOR", "escape", "escapeRegex", "escaper<PERSON><PERSON><PERSON>", "escapedString", "replace", "match", "didWarnAboutMaps", "userProvidedKeyEscapeRegex", "escapeUserProvidedKey", "text", "get<PERSON><PERSON><PERSON><PERSON>", "index", "toString", "mapIntoArray", "array", "escapedPrefix", "nameSoFar", "invokeCallback", "_child", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "c", "push", "child", "nextName", "subtreeCount", "nextNamePrefix", "iteratorFn", "iterable<PERSON><PERSON><PERSON>n", "entries", "step", "ii", "next", "done", "childrenString", "keys", "join", "mapChildren", "func", "result", "count", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "n", "forEachChildren", "forEachFunc", "forEachContext", "toArray", "<PERSON><PERSON><PERSON><PERSON>", "createContext", "defaultValue", "_currentValue", "_currentValue2", "_threadCount", "Provider", "Consumer", "_defaultValue", "_globalName", "hasWarnedAboutUsingNestedContextConsumers", "hasWarnedAboutUsingConsumerProvider", "hasWarnedAboutDisplayNameOnConsumer", "defineProperties", "set", "_Provider", "_current<PERSON><PERSON><PERSON>", "_currentRenderer2", "Uninitialized", "Pending", "Resolved", "Rejected", "lazyInitializer", "_status", "ctor", "_result", "thenable", "then", "moduleObject", "resolved", "rejected", "pending", "default", "lazy", "lazyType", "propTypes", "newDefaultProps", "newPropTypes", "forwardRef", "elementType", "ownName", "REACT_MODULE_REFERENCE", "isValidElementType", "getModuleId", "memo", "compare", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatcher", "useContext", "Context", "realContext", "useState", "initialState", "useReducer", "reducer", "initialArg", "useRef", "initialValue", "useEffect", "create", "deps", "useInsertionEffect", "useLayoutEffect", "useCallback", "useMemo", "useImperativeHandle", "useDebugValue", "formatterFn", "useTransition", "useDeferredValue", "useId", "useSyncExternalStore", "subscribe", "getSnapshot", "getServerSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "prevLog", "prevInfo", "prev<PERSON>arn", "prevError", "prevGroup", "prevGroupCollapsed", "prevGroupEnd", "disabledLog", "__reactDisabledLog", "disableLogs", "log", "group", "groupCollapsed", "groupEnd", "reenableLogs", "ReactCurrentDispatcher$1", "prefix", "describeBuiltInComponentFrame", "ownerFn", "trim", "reentry", "componentFrameCache", "PossiblyWeakMap", "WeakMap", "Map", "describeNativeComponentFrame", "fn", "construct", "frame", "control", "previousPrepareStackTrace", "prepareStackTrace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Fake", "Reflect", "sample", "sampleLines", "split", "controlLines", "s", "_frame", "includes", "syntheticFrame", "describeFunctionComponentFrame", "shouldConstruct", "describeUnknownElementTypeFrameInDEV", "loggedTypeFailures", "ReactDebugCurrentFrame$1", "setCurrentlyValidatingElement", "checkPropTypes", "typeSpecs", "values", "location", "has", "bind", "typeSpecName", "error$1", "err", "ex", "message", "setCurrentlyValidatingElement$1", "propTypesMisspellWarningShown", "getDeclarationErrorAddendum", "getSourceInfoErrorAddendum", "fileName", "lineNumber", "getSourceInfoErrorAddendumForProps", "elementProps", "ownerHasKeyUseWarning", "getCurrentComponentErrorInfo", "parentType", "parentName", "validateExplicitKey", "validated", "currentComponentErrorInfo", "childOwner", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "validatePropTypes", "PropTypes", "_name", "getDefaultProps", "isReactClassApproved", "validateFragmentProps", "fragment", "createElementWithValidation", "validType", "sourceInfo", "typeString", "didWarnAboutDeprecatedCreateFactory", "createFactoryWithValidation", "validatedFactory", "cloneElementWithValidation", "startTransition", "scope", "options", "prevTransition", "currentTransition", "_updatedFibers", "Set", "updatedFibersCount", "size", "clear", "didWarnAboutMessageChannel", "enqueueTaskImpl", "enqueueTask", "task", "requireString", "Math", "random", "slice", "nodeRequire", "module", "setImmediate", "_err", "MessageChannel", "channel", "port1", "onmessage", "port2", "postMessage", "actScopeDepth", "didWarnNoAwaitAct", "act", "prevActScope<PERSON>epth", "prevIsBatchingLegacy", "queue", "flushActQueue", "popActScope", "thenableResult", "wasAwaited", "resolve", "reject", "returnValue", "recursivelyFlushAsyncActWork", "Promise", "_queue", "_thenable", "_thenable2", "isFlushing", "createElement$1", "cloneElement$1", "createFactory", "Children", "for<PERSON>ach", "only", "exports", "Fragment", "Profiler", "StrictMode", "Suspense", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "unstable_act", "version", "registerInternalModuleStop"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react/cjs/react.development.js"], "sourcesContent": ["/**\n * @license React\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var ReactVersion = '18.2.0';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\n/**\n * Keeps track of the current dispatcher.\n */\nvar ReactCurrentDispatcher = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\n/**\n * Keeps track of the current batch's configuration such as how long an update\n * should suspend for if it needs to.\n */\nvar ReactCurrentBatchConfig = {\n  transition: null\n};\n\nvar ReactCurrentActQueue = {\n  current: null,\n  // Used to reproduce behavior of `batchedUpdates` in legacy mode.\n  isBatchingLegacy: false,\n  didScheduleLegacyUpdate: false\n};\n\n/**\n * Keeps track of the current owner.\n *\n * The current owner is the component who should own any components that are\n * currently being constructed.\n */\nvar ReactCurrentOwner = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\nvar ReactDebugCurrentFrame = {};\nvar currentExtraStackFrame = null;\nfunction setExtraStackFrame(stack) {\n  {\n    currentExtraStackFrame = stack;\n  }\n}\n\n{\n  ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n    {\n      currentExtraStackFrame = stack;\n    }\n  }; // Stack implementation injected by the current renderer.\n\n\n  ReactDebugCurrentFrame.getCurrentStack = null;\n\n  ReactDebugCurrentFrame.getStackAddendum = function () {\n    var stack = ''; // Add an extra top frame while an element is being validated\n\n    if (currentExtraStackFrame) {\n      stack += currentExtraStackFrame;\n    } // Delegate to the injected renderer-specific implementation\n\n\n    var impl = ReactDebugCurrentFrame.getCurrentStack;\n\n    if (impl) {\n      stack += impl() || '';\n    }\n\n    return stack;\n  };\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar ReactSharedInternals = {\n  ReactCurrentDispatcher: ReactCurrentDispatcher,\n  ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n  ReactCurrentOwner: ReactCurrentOwner\n};\n\n{\n  ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n  ReactSharedInternals.ReactCurrentActQueue = ReactCurrentActQueue;\n}\n\n// by calls to these methods by a Babel plugin.\n//\n// In PROD (or in packages without access to React internals),\n// they are left as they are instead.\n\nfunction warn(format) {\n  {\n    {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      printWarning('warn', format, args);\n    }\n  }\n}\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\nvar didWarnStateUpdateForUnmountedComponent = {};\n\nfunction warnNoop(publicInstance, callerName) {\n  {\n    var _constructor = publicInstance.constructor;\n    var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n    var warningKey = componentName + \".\" + callerName;\n\n    if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n      return;\n    }\n\n    error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n\n    didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n  }\n}\n/**\n * This is the abstract API for an update queue.\n */\n\n\nvar ReactNoopUpdateQueue = {\n  /**\n   * Checks whether or not this composite component is mounted.\n   * @param {ReactClass} publicInstance The instance we want to test.\n   * @return {boolean} True if mounted, false otherwise.\n   * @protected\n   * @final\n   */\n  isMounted: function (publicInstance) {\n    return false;\n  },\n\n  /**\n   * Forces an update. This should only be invoked when it is known with\n   * certainty that we are **not** in a DOM transaction.\n   *\n   * You may want to call this when you know that some deeper aspect of the\n   * component's state has changed but `setState` was not called.\n   *\n   * This will not invoke `shouldComponentUpdate`, but it will invoke\n   * `componentWillUpdate` and `componentDidUpdate`.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueForceUpdate: function (publicInstance, callback, callerName) {\n    warnNoop(publicInstance, 'forceUpdate');\n  },\n\n  /**\n   * Replaces all of the state. Always use this or `setState` to mutate state.\n   * You should treat `this.state` as immutable.\n   *\n   * There is no guarantee that `this.state` will be immediately updated, so\n   * accessing `this.state` after calling this method may return the old value.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} completeState Next state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n    warnNoop(publicInstance, 'replaceState');\n  },\n\n  /**\n   * Sets a subset of the state. This only exists because _pendingState is\n   * internal. This provides a merging strategy that is not available to deep\n   * properties which is confusing. TODO: Expose pendingState or don't use it\n   * during the merge.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} partialState Next partial state to be merged with state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} Name of the calling function in the public API.\n   * @internal\n   */\n  enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n    warnNoop(publicInstance, 'setState');\n  }\n};\n\nvar assign = Object.assign;\n\nvar emptyObject = {};\n\n{\n  Object.freeze(emptyObject);\n}\n/**\n * Base class helpers for the updating state of a component.\n */\n\n\nfunction Component(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n  // renderer.\n\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nComponent.prototype.isReactComponent = {};\n/**\n * Sets a subset of the state. Always use this to mutate\n * state. You should treat `this.state` as immutable.\n *\n * There is no guarantee that `this.state` will be immediately updated, so\n * accessing `this.state` after calling this method may return the old value.\n *\n * There is no guarantee that calls to `setState` will run synchronously,\n * as they may eventually be batched together.  You can provide an optional\n * callback that will be executed when the call to setState is actually\n * completed.\n *\n * When a function is provided to setState, it will be called at some point in\n * the future (not synchronously). It will be called with the up to date\n * component arguments (state, props, context). These values can be different\n * from this.* because your function may be called after receiveProps but before\n * shouldComponentUpdate, and this new state, props, and context will not yet be\n * assigned to this.\n *\n * @param {object|function} partialState Next partial state or function to\n *        produce next partial state to be merged with current state.\n * @param {?function} callback Called after state is updated.\n * @final\n * @protected\n */\n\nComponent.prototype.setState = function (partialState, callback) {\n  if (typeof partialState !== 'object' && typeof partialState !== 'function' && partialState != null) {\n    throw new Error('setState(...): takes an object of state variables to update or a ' + 'function which returns an object of state variables.');\n  }\n\n  this.updater.enqueueSetState(this, partialState, callback, 'setState');\n};\n/**\n * Forces an update. This should only be invoked when it is known with\n * certainty that we are **not** in a DOM transaction.\n *\n * You may want to call this when you know that some deeper aspect of the\n * component's state has changed but `setState` was not called.\n *\n * This will not invoke `shouldComponentUpdate`, but it will invoke\n * `componentWillUpdate` and `componentDidUpdate`.\n *\n * @param {?function} callback Called after update is complete.\n * @final\n * @protected\n */\n\n\nComponent.prototype.forceUpdate = function (callback) {\n  this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n};\n/**\n * Deprecated APIs. These APIs used to exist on classic React classes but since\n * we would like to deprecate them, we're not going to move them over to this\n * modern base class. Instead, we define a getter that warns if it's accessed.\n */\n\n\n{\n  var deprecatedAPIs = {\n    isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n    replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n  };\n\n  var defineDeprecationWarning = function (methodName, info) {\n    Object.defineProperty(Component.prototype, methodName, {\n      get: function () {\n        warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n\n        return undefined;\n      }\n    });\n  };\n\n  for (var fnName in deprecatedAPIs) {\n    if (deprecatedAPIs.hasOwnProperty(fnName)) {\n      defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n    }\n  }\n}\n\nfunction ComponentDummy() {}\n\nComponentDummy.prototype = Component.prototype;\n/**\n * Convenience component with default shallow equality check for sCU.\n */\n\nfunction PureComponent(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nvar pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\npureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\nassign(pureComponentPrototype, Component.prototype);\npureComponentPrototype.isPureReactComponent = true;\n\n// an immutable object with a single mutable value\nfunction createRef() {\n  var refObject = {\n    current: null\n  };\n\n  {\n    Object.seal(refObject);\n  }\n\n  return refObject;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  var warnAboutAccessingKey = function () {\n    {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingKey.isReactWarning = true;\n  Object.defineProperty(props, 'key', {\n    get: warnAboutAccessingKey,\n    configurable: true\n  });\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  var warnAboutAccessingRef = function () {\n    {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingRef.isReactWarning = true;\n  Object.defineProperty(props, 'ref', {\n    get: warnAboutAccessingRef,\n    configurable: true\n  });\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * Create and return a new ReactElement of the given type.\n * See https://reactjs.org/docs/react-api.html#createelement\n */\n\nfunction createElement(type, config, children) {\n  var propName; // Reserved names are extracted\n\n  var props = {};\n  var key = null;\n  var ref = null;\n  var self = null;\n  var source = null;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      ref = config.ref;\n\n      {\n        warnIfStringRefCannotBeAutoConverted(config);\n      }\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    self = config.__self === undefined ? null : config.__self;\n    source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    {\n      if (Object.freeze) {\n        Object.freeze(childArray);\n      }\n    }\n\n    props.children = childArray;\n  } // Resolve default props\n\n\n  if (type && type.defaultProps) {\n    var defaultProps = type.defaultProps;\n\n    for (propName in defaultProps) {\n      if (props[propName] === undefined) {\n        props[propName] = defaultProps[propName];\n      }\n    }\n  }\n\n  {\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n  }\n\n  return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n}\nfunction cloneAndReplaceKey(oldElement, newKey) {\n  var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n  return newElement;\n}\n/**\n * Clone and return a new ReactElement using element as the starting point.\n * See https://reactjs.org/docs/react-api.html#cloneelement\n */\n\nfunction cloneElement(element, config, children) {\n  if (element === null || element === undefined) {\n    throw new Error(\"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\");\n  }\n\n  var propName; // Original props are copied\n\n  var props = assign({}, element.props); // Reserved names are extracted\n\n  var key = element.key;\n  var ref = element.ref; // Self is preserved since the owner is preserved.\n\n  var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n  // transpiler, and the original source is probably a better indicator of the\n  // true owner.\n\n  var source = element._source; // Owner will be preserved, unless ref is overridden\n\n  var owner = element._owner;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      // Silently steal the ref from the parent.\n      ref = config.ref;\n      owner = ReactCurrentOwner.current;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    } // Remaining properties override existing props\n\n\n    var defaultProps;\n\n    if (element.type && element.type.defaultProps) {\n      defaultProps = element.type.defaultProps;\n    }\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        if (config[propName] === undefined && defaultProps !== undefined) {\n          // Resolve default props\n          props[propName] = defaultProps[propName];\n        } else {\n          props[propName] = config[propName];\n        }\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    props.children = childArray;\n  }\n\n  return ReactElement(element.type, key, ref, self, source, owner, props);\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\n\nvar SEPARATOR = '.';\nvar SUBSEPARATOR = ':';\n/**\n * Escape and wrap key so it is safe to use as a reactid\n *\n * @param {string} key to be escaped.\n * @return {string} the escaped key.\n */\n\nfunction escape(key) {\n  var escapeRegex = /[=:]/g;\n  var escaperLookup = {\n    '=': '=0',\n    ':': '=2'\n  };\n  var escapedString = key.replace(escapeRegex, function (match) {\n    return escaperLookup[match];\n  });\n  return '$' + escapedString;\n}\n/**\n * TODO: Test that a single child and an array with one item have the same key\n * pattern.\n */\n\n\nvar didWarnAboutMaps = false;\nvar userProvidedKeyEscapeRegex = /\\/+/g;\n\nfunction escapeUserProvidedKey(text) {\n  return text.replace(userProvidedKeyEscapeRegex, '$&/');\n}\n/**\n * Generate a key string that identifies a element within a set.\n *\n * @param {*} element A element that could contain a manual key.\n * @param {number} index Index that is used if a manual key is not provided.\n * @return {string}\n */\n\n\nfunction getElementKey(element, index) {\n  // Do some typechecking here since we call this blindly. We want to ensure\n  // that we don't block potential future ES APIs.\n  if (typeof element === 'object' && element !== null && element.key != null) {\n    // Explicit key\n    {\n      checkKeyStringCoercion(element.key);\n    }\n\n    return escape('' + element.key);\n  } // Implicit key determined by the index in the set\n\n\n  return index.toString(36);\n}\n\nfunction mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n  var type = typeof children;\n\n  if (type === 'undefined' || type === 'boolean') {\n    // All of the above are perceived as null.\n    children = null;\n  }\n\n  var invokeCallback = false;\n\n  if (children === null) {\n    invokeCallback = true;\n  } else {\n    switch (type) {\n      case 'string':\n      case 'number':\n        invokeCallback = true;\n        break;\n\n      case 'object':\n        switch (children.$$typeof) {\n          case REACT_ELEMENT_TYPE:\n          case REACT_PORTAL_TYPE:\n            invokeCallback = true;\n        }\n\n    }\n  }\n\n  if (invokeCallback) {\n    var _child = children;\n    var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n    // so that it's consistent if the number of children grows:\n\n    var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n\n    if (isArray(mappedChild)) {\n      var escapedChildKey = '';\n\n      if (childKey != null) {\n        escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n      }\n\n      mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n        return c;\n      });\n    } else if (mappedChild != null) {\n      if (isValidElement(mappedChild)) {\n        {\n          // The `if` statement here prevents auto-disabling of the safe\n          // coercion ESLint rule, so we must manually disable it below.\n          // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n          if (mappedChild.key && (!_child || _child.key !== mappedChild.key)) {\n            checkKeyStringCoercion(mappedChild.key);\n          }\n        }\n\n        mappedChild = cloneAndReplaceKey(mappedChild, // Keep both the (mapped) and old keys if they differ, just as\n        // traverseAllChildren used to do for objects as children\n        escapedPrefix + ( // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n        mappedChild.key && (!_child || _child.key !== mappedChild.key) ? // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n        // eslint-disable-next-line react-internal/safe-string-coercion\n        escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n      }\n\n      array.push(mappedChild);\n    }\n\n    return 1;\n  }\n\n  var child;\n  var nextName;\n  var subtreeCount = 0; // Count of children found in the current subtree.\n\n  var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n\n  if (isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      nextName = nextNamePrefix + getElementKey(child, i);\n      subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n    }\n  } else {\n    var iteratorFn = getIteratorFn(children);\n\n    if (typeof iteratorFn === 'function') {\n      var iterableChildren = children;\n\n      {\n        // Warn about using Maps as children\n        if (iteratorFn === iterableChildren.entries) {\n          if (!didWarnAboutMaps) {\n            warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n          }\n\n          didWarnAboutMaps = true;\n        }\n      }\n\n      var iterator = iteratorFn.call(iterableChildren);\n      var step;\n      var ii = 0;\n\n      while (!(step = iterator.next()).done) {\n        child = step.value;\n        nextName = nextNamePrefix + getElementKey(child, ii++);\n        subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n      }\n    } else if (type === 'object') {\n      // eslint-disable-next-line react-internal/safe-string-coercion\n      var childrenString = String(children);\n      throw new Error(\"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). \" + 'If you meant to render a collection of children, use an array ' + 'instead.');\n    }\n  }\n\n  return subtreeCount;\n}\n\n/**\n * Maps children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n *\n * The provided mapFunction(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} func The map function.\n * @param {*} context Context for mapFunction.\n * @return {object} Object containing the ordered map of results.\n */\nfunction mapChildren(children, func, context) {\n  if (children == null) {\n    return children;\n  }\n\n  var result = [];\n  var count = 0;\n  mapIntoArray(children, result, '', '', function (child) {\n    return func.call(context, child, count++);\n  });\n  return result;\n}\n/**\n * Count the number of children that are typically specified as\n * `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrencount\n *\n * @param {?*} children Children tree container.\n * @return {number} The number of children.\n */\n\n\nfunction countChildren(children) {\n  var n = 0;\n  mapChildren(children, function () {\n    n++; // Don't return anything\n  });\n  return n;\n}\n\n/**\n * Iterates through children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n *\n * The provided forEachFunc(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} forEachFunc\n * @param {*} forEachContext Context for forEachContext.\n */\nfunction forEachChildren(children, forEachFunc, forEachContext) {\n  mapChildren(children, function () {\n    forEachFunc.apply(this, arguments); // Don't return anything.\n  }, forEachContext);\n}\n/**\n * Flatten a children object (typically specified as `props.children`) and\n * return an array with appropriately re-keyed children.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n */\n\n\nfunction toArray(children) {\n  return mapChildren(children, function (child) {\n    return child;\n  }) || [];\n}\n/**\n * Returns the first child in a collection of children and verifies that there\n * is only one child in the collection.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n *\n * The current implementation of this function assumes that a single child gets\n * passed without a wrapper, but the purpose of this helper function is to\n * abstract away the particular structure of children.\n *\n * @param {?object} children Child collection structure.\n * @return {ReactElement} The first and only `ReactElement` contained in the\n * structure.\n */\n\n\nfunction onlyChild(children) {\n  if (!isValidElement(children)) {\n    throw new Error('React.Children.only expected to receive a single React element child.');\n  }\n\n  return children;\n}\n\nfunction createContext(defaultValue) {\n  // TODO: Second argument used to be an optional `calculateChangedBits`\n  // function. Warn to reserve for future use?\n  var context = {\n    $$typeof: REACT_CONTEXT_TYPE,\n    // As a workaround to support multiple concurrent renderers, we categorize\n    // some renderers as primary and others as secondary. We only expect\n    // there to be two concurrent renderers at most: React Native (primary) and\n    // Fabric (secondary); React DOM (primary) and React ART (secondary).\n    // Secondary renderers store their context values on separate fields.\n    _currentValue: defaultValue,\n    _currentValue2: defaultValue,\n    // Used to track how many concurrent renderers this context currently\n    // supports within in a single renderer. Such as parallel server rendering.\n    _threadCount: 0,\n    // These are circular\n    Provider: null,\n    Consumer: null,\n    // Add these to use same hidden class in VM as ServerContext\n    _defaultValue: null,\n    _globalName: null\n  };\n  context.Provider = {\n    $$typeof: REACT_PROVIDER_TYPE,\n    _context: context\n  };\n  var hasWarnedAboutUsingNestedContextConsumers = false;\n  var hasWarnedAboutUsingConsumerProvider = false;\n  var hasWarnedAboutDisplayNameOnConsumer = false;\n\n  {\n    // A separate object, but proxies back to the original context object for\n    // backwards compatibility. It has a different $$typeof, so we can properly\n    // warn for the incorrect usage of Context as a Consumer.\n    var Consumer = {\n      $$typeof: REACT_CONTEXT_TYPE,\n      _context: context\n    }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n    Object.defineProperties(Consumer, {\n      Provider: {\n        get: function () {\n          if (!hasWarnedAboutUsingConsumerProvider) {\n            hasWarnedAboutUsingConsumerProvider = true;\n\n            error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n          }\n\n          return context.Provider;\n        },\n        set: function (_Provider) {\n          context.Provider = _Provider;\n        }\n      },\n      _currentValue: {\n        get: function () {\n          return context._currentValue;\n        },\n        set: function (_currentValue) {\n          context._currentValue = _currentValue;\n        }\n      },\n      _currentValue2: {\n        get: function () {\n          return context._currentValue2;\n        },\n        set: function (_currentValue2) {\n          context._currentValue2 = _currentValue2;\n        }\n      },\n      _threadCount: {\n        get: function () {\n          return context._threadCount;\n        },\n        set: function (_threadCount) {\n          context._threadCount = _threadCount;\n        }\n      },\n      Consumer: {\n        get: function () {\n          if (!hasWarnedAboutUsingNestedContextConsumers) {\n            hasWarnedAboutUsingNestedContextConsumers = true;\n\n            error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n          }\n\n          return context.Consumer;\n        }\n      },\n      displayName: {\n        get: function () {\n          return context.displayName;\n        },\n        set: function (displayName) {\n          if (!hasWarnedAboutDisplayNameOnConsumer) {\n            warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n\n            hasWarnedAboutDisplayNameOnConsumer = true;\n          }\n        }\n      }\n    }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n    context.Consumer = Consumer;\n  }\n\n  {\n    context._currentRenderer = null;\n    context._currentRenderer2 = null;\n  }\n\n  return context;\n}\n\nvar Uninitialized = -1;\nvar Pending = 0;\nvar Resolved = 1;\nvar Rejected = 2;\n\nfunction lazyInitializer(payload) {\n  if (payload._status === Uninitialized) {\n    var ctor = payload._result;\n    var thenable = ctor(); // Transition to the next state.\n    // This might throw either because it's missing or throws. If so, we treat it\n    // as still uninitialized and try again next time. Which is the same as what\n    // happens if the ctor or any wrappers processing the ctor throws. This might\n    // end up fixing it if the resolution was a concurrency bug.\n\n    thenable.then(function (moduleObject) {\n      if (payload._status === Pending || payload._status === Uninitialized) {\n        // Transition to the next state.\n        var resolved = payload;\n        resolved._status = Resolved;\n        resolved._result = moduleObject;\n      }\n    }, function (error) {\n      if (payload._status === Pending || payload._status === Uninitialized) {\n        // Transition to the next state.\n        var rejected = payload;\n        rejected._status = Rejected;\n        rejected._result = error;\n      }\n    });\n\n    if (payload._status === Uninitialized) {\n      // In case, we're still uninitialized, then we're waiting for the thenable\n      // to resolve. Set it as pending in the meantime.\n      var pending = payload;\n      pending._status = Pending;\n      pending._result = thenable;\n    }\n  }\n\n  if (payload._status === Resolved) {\n    var moduleObject = payload._result;\n\n    {\n      if (moduleObject === undefined) {\n        error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n        'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\\n\\n\" + 'Did you accidentally put curly braces around the import?', moduleObject);\n      }\n    }\n\n    {\n      if (!('default' in moduleObject)) {\n        error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n        'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n      }\n    }\n\n    return moduleObject.default;\n  } else {\n    throw payload._result;\n  }\n}\n\nfunction lazy(ctor) {\n  var payload = {\n    // We use these fields to store the result.\n    _status: Uninitialized,\n    _result: ctor\n  };\n  var lazyType = {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: payload,\n    _init: lazyInitializer\n  };\n\n  {\n    // In production, this would just set it on the object.\n    var defaultProps;\n    var propTypes; // $FlowFixMe\n\n    Object.defineProperties(lazyType, {\n      defaultProps: {\n        configurable: true,\n        get: function () {\n          return defaultProps;\n        },\n        set: function (newDefaultProps) {\n          error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          defaultProps = newDefaultProps; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'defaultProps', {\n            enumerable: true\n          });\n        }\n      },\n      propTypes: {\n        configurable: true,\n        get: function () {\n          return propTypes;\n        },\n        set: function (newPropTypes) {\n          error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          propTypes = newPropTypes; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'propTypes', {\n            enumerable: true\n          });\n        }\n      }\n    });\n  }\n\n  return lazyType;\n}\n\nfunction forwardRef(render) {\n  {\n    if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n      error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n    } else if (typeof render !== 'function') {\n      error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n    } else {\n      if (render.length !== 0 && render.length !== 2) {\n        error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n      }\n    }\n\n    if (render != null) {\n      if (render.defaultProps != null || render.propTypes != null) {\n        error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n      }\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_FORWARD_REF_TYPE,\n    render: render\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name; // The inner component shouldn't inherit this display name in most cases,\n        // because the component may be used elsewhere.\n        // But it's nice for anonymous functions to inherit the name,\n        // so that our component-stack generation logic will display their frames.\n        // An anonymous function generally suggests a pattern like:\n        //   React.forwardRef((props, ref) => {...});\n        // This kind of inner function is not used elsewhere so the side effect is okay.\n\n        if (!render.name && !render.displayName) {\n          render.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction memo(type, compare) {\n  {\n    if (!isValidElementType(type)) {\n      error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_MEMO_TYPE,\n    type: type,\n    compare: compare === undefined ? null : compare\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name; // The inner component shouldn't inherit this display name in most cases,\n        // because the component may be used elsewhere.\n        // But it's nice for anonymous functions to inherit the name,\n        // so that our component-stack generation logic will display their frames.\n        // An anonymous function generally suggests a pattern like:\n        //   React.memo((props) => {...});\n        // This kind of inner function is not used elsewhere so the side effect is okay.\n\n        if (!type.name && !type.displayName) {\n          type.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nfunction resolveDispatcher() {\n  var dispatcher = ReactCurrentDispatcher.current;\n\n  {\n    if (dispatcher === null) {\n      error('Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for' + ' one of the following reasons:\\n' + '1. You might have mismatching versions of React and the renderer (such as React DOM)\\n' + '2. You might be breaking the Rules of Hooks\\n' + '3. You might have more than one copy of React in the same app\\n' + 'See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.');\n    }\n  } // Will result in a null access error if accessed outside render phase. We\n  // intentionally don't throw our own error because this is in a hot path.\n  // Also helps ensure this is inlined.\n\n\n  return dispatcher;\n}\nfunction useContext(Context) {\n  var dispatcher = resolveDispatcher();\n\n  {\n    // TODO: add a more generic warning for invalid values.\n    if (Context._context !== undefined) {\n      var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n      // and nobody should be using this in existing code.\n\n      if (realContext.Consumer === Context) {\n        error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n      } else if (realContext.Provider === Context) {\n        error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n      }\n    }\n  }\n\n  return dispatcher.useContext(Context);\n}\nfunction useState(initialState) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useState(initialState);\n}\nfunction useReducer(reducer, initialArg, init) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useReducer(reducer, initialArg, init);\n}\nfunction useRef(initialValue) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useRef(initialValue);\n}\nfunction useEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useEffect(create, deps);\n}\nfunction useInsertionEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useInsertionEffect(create, deps);\n}\nfunction useLayoutEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useLayoutEffect(create, deps);\n}\nfunction useCallback(callback, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useCallback(callback, deps);\n}\nfunction useMemo(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useMemo(create, deps);\n}\nfunction useImperativeHandle(ref, create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useImperativeHandle(ref, create, deps);\n}\nfunction useDebugValue(value, formatterFn) {\n  {\n    var dispatcher = resolveDispatcher();\n    return dispatcher.useDebugValue(value, formatterFn);\n  }\n}\nfunction useTransition() {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useTransition();\n}\nfunction useDeferredValue(value) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useDeferredValue(value);\n}\nfunction useId() {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useId();\n}\nfunction useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher$1.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher$1.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      setExtraStackFrame(stack);\n    } else {\n      setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n\nfunction getDeclarationErrorAddendum() {\n  if (ReactCurrentOwner.current) {\n    var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n    if (name) {\n      return '\\n\\nCheck the render method of `' + name + '`.';\n    }\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  if (source !== undefined) {\n    var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n    var lineNumber = source.lineNumber;\n    return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendumForProps(elementProps) {\n  if (elementProps !== null && elementProps !== undefined) {\n    return getSourceInfoErrorAddendum(elementProps.__source);\n  }\n\n  return '';\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  var info = getDeclarationErrorAddendum();\n\n  if (!info) {\n    var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n    if (parentName) {\n      info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n    }\n  }\n\n  return info;\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  if (!element._store || element._store.validated || element.key != null) {\n    return;\n  }\n\n  element._store.validated = true;\n  var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n  if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n    return;\n  }\n\n  ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n  // property, it may be the creator of the child that's responsible for\n  // assigning it a key.\n\n  var childOwner = '';\n\n  if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n    // Give the component that originally created this child.\n    childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n  }\n\n  {\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  if (typeof node !== 'object') {\n    return;\n  }\n\n  if (isArray(node)) {\n    for (var i = 0; i < node.length; i++) {\n      var child = node[i];\n\n      if (isValidElement(child)) {\n        validateExplicitKey(child, parentType);\n      }\n    }\n  } else if (isValidElement(node)) {\n    // This element was passed in a valid location.\n    if (node._store) {\n      node._store.validated = true;\n    }\n  } else if (node) {\n    var iteratorFn = getIteratorFn(node);\n\n    if (typeof iteratorFn === 'function') {\n      // Entry iterators used to provide implicit keys,\n      // but now we print a separate warning for them later.\n      if (iteratorFn !== node.entries) {\n        var iterator = iteratorFn.call(node);\n        var step;\n\n        while (!(step = iterator.next()).done) {\n          if (isValidElement(step.value)) {\n            validateExplicitKey(step.value, parentType);\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\nfunction createElementWithValidation(type, props, children) {\n  var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n  // succeed and there will likely be errors in render.\n\n  if (!validType) {\n    var info = '';\n\n    if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n      info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n    }\n\n    var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n\n    if (sourceInfo) {\n      info += sourceInfo;\n    } else {\n      info += getDeclarationErrorAddendum();\n    }\n\n    var typeString;\n\n    if (type === null) {\n      typeString = 'null';\n    } else if (isArray(type)) {\n      typeString = 'array';\n    } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n      typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n      info = ' Did you accidentally export a JSX literal instead of a component?';\n    } else {\n      typeString = typeof type;\n    }\n\n    {\n      error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n  }\n\n  var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n  // TODO: Drop this when these are no longer allowed as the type argument.\n\n  if (element == null) {\n    return element;\n  } // Skip key warning if the type isn't valid since our key validation logic\n  // doesn't expect a non-string/function type and can throw confusing errors.\n  // We don't want exception behavior to differ between dev and prod.\n  // (Rendering will throw with a helpful message and as soon as the type is\n  // fixed, the key warnings will appear.)\n\n\n  if (validType) {\n    for (var i = 2; i < arguments.length; i++) {\n      validateChildKeys(arguments[i], type);\n    }\n  }\n\n  if (type === REACT_FRAGMENT_TYPE) {\n    validateFragmentProps(element);\n  } else {\n    validatePropTypes(element);\n  }\n\n  return element;\n}\nvar didWarnAboutDeprecatedCreateFactory = false;\nfunction createFactoryWithValidation(type) {\n  var validatedFactory = createElementWithValidation.bind(null, type);\n  validatedFactory.type = type;\n\n  {\n    if (!didWarnAboutDeprecatedCreateFactory) {\n      didWarnAboutDeprecatedCreateFactory = true;\n\n      warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n    } // Legacy hook: remove it\n\n\n    Object.defineProperty(validatedFactory, 'type', {\n      enumerable: false,\n      get: function () {\n        warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n\n        Object.defineProperty(this, 'type', {\n          value: type\n        });\n        return type;\n      }\n    });\n  }\n\n  return validatedFactory;\n}\nfunction cloneElementWithValidation(element, props, children) {\n  var newElement = cloneElement.apply(this, arguments);\n\n  for (var i = 2; i < arguments.length; i++) {\n    validateChildKeys(arguments[i], newElement.type);\n  }\n\n  validatePropTypes(newElement);\n  return newElement;\n}\n\nfunction startTransition(scope, options) {\n  var prevTransition = ReactCurrentBatchConfig.transition;\n  ReactCurrentBatchConfig.transition = {};\n  var currentTransition = ReactCurrentBatchConfig.transition;\n\n  {\n    ReactCurrentBatchConfig.transition._updatedFibers = new Set();\n  }\n\n  try {\n    scope();\n  } finally {\n    ReactCurrentBatchConfig.transition = prevTransition;\n\n    {\n      if (prevTransition === null && currentTransition._updatedFibers) {\n        var updatedFibersCount = currentTransition._updatedFibers.size;\n\n        if (updatedFibersCount > 10) {\n          warn('Detected a large number of updates inside startTransition. ' + 'If this is due to a subscription please re-write it to use React provided hooks. ' + 'Otherwise concurrent mode guarantees are off the table.');\n        }\n\n        currentTransition._updatedFibers.clear();\n      }\n    }\n  }\n}\n\nvar didWarnAboutMessageChannel = false;\nvar enqueueTaskImpl = null;\nfunction enqueueTask(task) {\n  if (enqueueTaskImpl === null) {\n    try {\n      // read require off the module object to get around the bundlers.\n      // we don't want them to detect a require and bundle a Node polyfill.\n      var requireString = ('require' + Math.random()).slice(0, 7);\n      var nodeRequire = module && module[requireString]; // assuming we're in node, let's try to get node's\n      // version of setImmediate, bypassing fake timers if any.\n\n      enqueueTaskImpl = nodeRequire.call(module, 'timers').setImmediate;\n    } catch (_err) {\n      // we're in a browser\n      // we can't use regular timers because they may still be faked\n      // so we try MessageChannel+postMessage instead\n      enqueueTaskImpl = function (callback) {\n        {\n          if (didWarnAboutMessageChannel === false) {\n            didWarnAboutMessageChannel = true;\n\n            if (typeof MessageChannel === 'undefined') {\n              error('This browser does not have a MessageChannel implementation, ' + 'so enqueuing tasks via await act(async () => ...) will fail. ' + 'Please file an issue at https://github.com/facebook/react/issues ' + 'if you encounter this warning.');\n            }\n          }\n        }\n\n        var channel = new MessageChannel();\n        channel.port1.onmessage = callback;\n        channel.port2.postMessage(undefined);\n      };\n    }\n  }\n\n  return enqueueTaskImpl(task);\n}\n\nvar actScopeDepth = 0;\nvar didWarnNoAwaitAct = false;\nfunction act(callback) {\n  {\n    // `act` calls can be nested, so we track the depth. This represents the\n    // number of `act` scopes on the stack.\n    var prevActScopeDepth = actScopeDepth;\n    actScopeDepth++;\n\n    if (ReactCurrentActQueue.current === null) {\n      // This is the outermost `act` scope. Initialize the queue. The reconciler\n      // will detect the queue and use it instead of Scheduler.\n      ReactCurrentActQueue.current = [];\n    }\n\n    var prevIsBatchingLegacy = ReactCurrentActQueue.isBatchingLegacy;\n    var result;\n\n    try {\n      // Used to reproduce behavior of `batchedUpdates` in legacy mode. Only\n      // set to `true` while the given callback is executed, not for updates\n      // triggered during an async event, because this is how the legacy\n      // implementation of `act` behaved.\n      ReactCurrentActQueue.isBatchingLegacy = true;\n      result = callback(); // Replicate behavior of original `act` implementation in legacy mode,\n      // which flushed updates immediately after the scope function exits, even\n      // if it's an async function.\n\n      if (!prevIsBatchingLegacy && ReactCurrentActQueue.didScheduleLegacyUpdate) {\n        var queue = ReactCurrentActQueue.current;\n\n        if (queue !== null) {\n          ReactCurrentActQueue.didScheduleLegacyUpdate = false;\n          flushActQueue(queue);\n        }\n      }\n    } catch (error) {\n      popActScope(prevActScopeDepth);\n      throw error;\n    } finally {\n      ReactCurrentActQueue.isBatchingLegacy = prevIsBatchingLegacy;\n    }\n\n    if (result !== null && typeof result === 'object' && typeof result.then === 'function') {\n      var thenableResult = result; // The callback is an async function (i.e. returned a promise). Wait\n      // for it to resolve before exiting the current scope.\n\n      var wasAwaited = false;\n      var thenable = {\n        then: function (resolve, reject) {\n          wasAwaited = true;\n          thenableResult.then(function (returnValue) {\n            popActScope(prevActScopeDepth);\n\n            if (actScopeDepth === 0) {\n              // We've exited the outermost act scope. Recursively flush the\n              // queue until there's no remaining work.\n              recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n            } else {\n              resolve(returnValue);\n            }\n          }, function (error) {\n            // The callback threw an error.\n            popActScope(prevActScopeDepth);\n            reject(error);\n          });\n        }\n      };\n\n      {\n        if (!didWarnNoAwaitAct && typeof Promise !== 'undefined') {\n          // eslint-disable-next-line no-undef\n          Promise.resolve().then(function () {}).then(function () {\n            if (!wasAwaited) {\n              didWarnNoAwaitAct = true;\n\n              error('You called act(async () => ...) without await. ' + 'This could lead to unexpected testing behaviour, ' + 'interleaving multiple act calls and mixing their ' + 'scopes. ' + 'You should - await act(async () => ...);');\n            }\n          });\n        }\n      }\n\n      return thenable;\n    } else {\n      var returnValue = result; // The callback is not an async function. Exit the current scope\n      // immediately, without awaiting.\n\n      popActScope(prevActScopeDepth);\n\n      if (actScopeDepth === 0) {\n        // Exiting the outermost act scope. Flush the queue.\n        var _queue = ReactCurrentActQueue.current;\n\n        if (_queue !== null) {\n          flushActQueue(_queue);\n          ReactCurrentActQueue.current = null;\n        } // Return a thenable. If the user awaits it, we'll flush again in\n        // case additional work was scheduled by a microtask.\n\n\n        var _thenable = {\n          then: function (resolve, reject) {\n            // Confirm we haven't re-entered another `act` scope, in case\n            // the user does something weird like await the thenable\n            // multiple times.\n            if (ReactCurrentActQueue.current === null) {\n              // Recursively flush the queue until there's no remaining work.\n              ReactCurrentActQueue.current = [];\n              recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n            } else {\n              resolve(returnValue);\n            }\n          }\n        };\n        return _thenable;\n      } else {\n        // Since we're inside a nested `act` scope, the returned thenable\n        // immediately resolves. The outer scope will flush the queue.\n        var _thenable2 = {\n          then: function (resolve, reject) {\n            resolve(returnValue);\n          }\n        };\n        return _thenable2;\n      }\n    }\n  }\n}\n\nfunction popActScope(prevActScopeDepth) {\n  {\n    if (prevActScopeDepth !== actScopeDepth - 1) {\n      error('You seem to have overlapping act() calls, this is not supported. ' + 'Be sure to await previous act() calls before making a new one. ');\n    }\n\n    actScopeDepth = prevActScopeDepth;\n  }\n}\n\nfunction recursivelyFlushAsyncActWork(returnValue, resolve, reject) {\n  {\n    var queue = ReactCurrentActQueue.current;\n\n    if (queue !== null) {\n      try {\n        flushActQueue(queue);\n        enqueueTask(function () {\n          if (queue.length === 0) {\n            // No additional work was scheduled. Finish.\n            ReactCurrentActQueue.current = null;\n            resolve(returnValue);\n          } else {\n            // Keep flushing work until there's none left.\n            recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n          }\n        });\n      } catch (error) {\n        reject(error);\n      }\n    } else {\n      resolve(returnValue);\n    }\n  }\n}\n\nvar isFlushing = false;\n\nfunction flushActQueue(queue) {\n  {\n    if (!isFlushing) {\n      // Prevent re-entrance.\n      isFlushing = true;\n      var i = 0;\n\n      try {\n        for (; i < queue.length; i++) {\n          var callback = queue[i];\n\n          do {\n            callback = callback(true);\n          } while (callback !== null);\n        }\n\n        queue.length = 0;\n      } catch (error) {\n        // If something throws, leave the remaining callbacks on the queue.\n        queue = queue.slice(i + 1);\n        throw error;\n      } finally {\n        isFlushing = false;\n      }\n    }\n  }\n}\n\nvar createElement$1 =  createElementWithValidation ;\nvar cloneElement$1 =  cloneElementWithValidation ;\nvar createFactory =  createFactoryWithValidation ;\nvar Children = {\n  map: mapChildren,\n  forEach: forEachChildren,\n  count: countChildren,\n  toArray: toArray,\n  only: onlyChild\n};\n\nexports.Children = Children;\nexports.Component = Component;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.PureComponent = PureComponent;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\nexports.cloneElement = cloneElement$1;\nexports.createContext = createContext;\nexports.createElement = createElement$1;\nexports.createFactory = createFactory;\nexports.createRef = createRef;\nexports.forwardRef = forwardRef;\nexports.isValidElement = isValidElement;\nexports.lazy = lazy;\nexports.memo = memo;\nexports.startTransition = startTransition;\nexports.unstable_act = act;\nexports.useCallback = useCallback;\nexports.useContext = useContext;\nexports.useDebugValue = useDebugValue;\nexports.useDeferredValue = useDeferredValue;\nexports.useEffect = useEffect;\nexports.useId = useId;\nexports.useImperativeHandle = useImperativeHandle;\nexports.useInsertionEffect = useInsertionEffect;\nexports.useLayoutEffect = useLayoutEffect;\nexports.useMemo = useMemo;\nexports.useReducer = useReducer;\nexports.useRef = useRef;\nexports.useState = useState;\nexports.useSyncExternalStore = useSyncExternalStore;\nexports.useTransition = useTransition;\nexports.version = ReactVersion;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA,IAAIA,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzC,CAAC,YAAW;IAEJ;IAEV;;IACA,IACE,OAAOC,8BAAP,KAA0C,WAA1C,IACA,OAAOA,8BAA8B,CAACC,2BAAtC,KACE,UAHJ,EAIE;MACAD,8BAA8B,CAACC,2BAA/B,CAA2D,IAAIC,KAAJ,EAA3D;IACD;;IACS,IAAIC,YAAY,GAAG,QAAnB,CAZI,CAcd;IACA;IACA;IACA;;IACA,IAAIC,kBAAkB,GAAGC,MAAM,CAACC,GAAP,CAAW,eAAX,CAAzB;IACA,IAAIC,iBAAiB,GAAGF,MAAM,CAACC,GAAP,CAAW,cAAX,CAAxB;IACA,IAAIE,mBAAmB,GAAGH,MAAM,CAACC,GAAP,CAAW,gBAAX,CAA1B;IACA,IAAIG,sBAAsB,GAAGJ,MAAM,CAACC,GAAP,CAAW,mBAAX,CAA7B;IACA,IAAII,mBAAmB,GAAGL,MAAM,CAACC,GAAP,CAAW,gBAAX,CAA1B;IACA,IAAIK,mBAAmB,GAAGN,MAAM,CAACC,GAAP,CAAW,gBAAX,CAA1B;IACA,IAAIM,kBAAkB,GAAGP,MAAM,CAACC,GAAP,CAAW,eAAX,CAAzB;IACA,IAAIO,sBAAsB,GAAGR,MAAM,CAACC,GAAP,CAAW,mBAAX,CAA7B;IACA,IAAIQ,mBAAmB,GAAGT,MAAM,CAACC,GAAP,CAAW,gBAAX,CAA1B;IACA,IAAIS,wBAAwB,GAAGV,MAAM,CAACC,GAAP,CAAW,qBAAX,CAA/B;IACA,IAAIU,eAAe,GAAGX,MAAM,CAACC,GAAP,CAAW,YAAX,CAAtB;IACA,IAAIW,eAAe,GAAGZ,MAAM,CAACC,GAAP,CAAW,YAAX,CAAtB;IACA,IAAIY,oBAAoB,GAAGb,MAAM,CAACC,GAAP,CAAW,iBAAX,CAA3B;IACA,IAAIa,qBAAqB,GAAGd,MAAM,CAACe,QAAnC;IACA,IAAIC,oBAAoB,GAAG,YAA3B;;IACA,SAASC,aAAT,CAAuBC,aAAvB,EAAsC;MACpC,IAAIA,aAAa,KAAK,IAAlB,IAA0B,OAAOA,aAAP,KAAyB,QAAvD,EAAiE;QAC/D,OAAO,IAAP;MACD;;MAED,IAAIC,aAAa,GAAGL,qBAAqB,IAAII,aAAa,CAACJ,qBAAD,CAAtC,IAAiEI,aAAa,CAACF,oBAAD,CAAlG;;MAEA,IAAI,OAAOG,aAAP,KAAyB,UAA7B,EAAyC;QACvC,OAAOA,aAAP;MACD;;MAED,OAAO,IAAP;IACD;IAED;AACA;AACA;;;IACA,IAAIC,sBAAsB,GAAG;MAC3B;AACF;AACA;AACA;MACEC,OAAO,EAAE;IALkB,CAA7B;IAQA;AACA;AACA;AACA;;IACA,IAAIC,uBAAuB,GAAG;MAC5BC,UAAU,EAAE;IADgB,CAA9B;IAIA,IAAIC,oBAAoB,GAAG;MACzBH,OAAO,EAAE,IADgB;MAEzB;MACAI,gBAAgB,EAAE,KAHO;MAIzBC,uBAAuB,EAAE;IAJA,CAA3B;IAOA;AACA;AACA;AACA;AACA;AACA;;IACA,IAAIC,iBAAiB,GAAG;MACtB;AACF;AACA;AACA;MACEN,OAAO,EAAE;IALa,CAAxB;IAQA,IAAIO,sBAAsB,GAAG,EAA7B;IACA,IAAIC,sBAAsB,GAAG,IAA7B;;IACA,SAASC,kBAAT,CAA4BC,KAA5B,EAAmC;MACjC;QACEF,sBAAsB,GAAGE,KAAzB;MACD;IACF;;IAED;MACEH,sBAAsB,CAACE,kBAAvB,GAA4C,UAAUC,KAAV,EAAiB;QAC3D;UACEF,sBAAsB,GAAGE,KAAzB;QACD;MACF,CAJD,CADF,CAKK;;;MAGHH,sBAAsB,CAACI,eAAvB,GAAyC,IAAzC;;MAEAJ,sBAAsB,CAACK,gBAAvB,GAA0C,YAAY;QACpD,IAAIF,KAAK,GAAG,EAAZ,CADoD,CACpC;;QAEhB,IAAIF,sBAAJ,EAA4B;UAC1BE,KAAK,IAAIF,sBAAT;QACD,CALmD,CAKlD;;;QAGF,IAAIK,IAAI,GAAGN,sBAAsB,CAACI,eAAlC;;QAEA,IAAIE,IAAJ,EAAU;UACRH,KAAK,IAAIG,IAAI,MAAM,EAAnB;QACD;;QAED,OAAOH,KAAP;MACD,CAfD;IAgBD,CAzHa,CA2Hd;;IAEA,IAAII,cAAc,GAAG,KAArB,CA7Hc,CA6Hc;;IAC5B,IAAIC,kBAAkB,GAAG,KAAzB;IACA,IAAIC,uBAAuB,GAAG,KAA9B,CA/Hc,CA+HuB;;IAErC,IAAIC,kBAAkB,GAAG,KAAzB,CAjIc,CAiIkB;IAChC;IACA;;IAEA,IAAIC,kBAAkB,GAAG,KAAzB,CArIc,CAqIkB;;IAEhC,IAAIC,oBAAoB,GAAG;MACzBpB,sBAAsB,EAAEA,sBADC;MAEzBE,uBAAuB,EAAEA,uBAFA;MAGzBK,iBAAiB,EAAEA;IAHM,CAA3B;IAMA;MACEa,oBAAoB,CAACZ,sBAArB,GAA8CA,sBAA9C;MACAY,oBAAoB,CAAChB,oBAArB,GAA4CA,oBAA5C;IACD,CAhJa,CAkJd;IACA;IACA;IACA;;IAEA,SAASiB,IAAT,CAAcC,MAAd,EAAsB;MACpB;QACE;UACE,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAArB,EAA6BC,IAAI,GAAG,IAAIC,KAAJ,CAAUJ,IAAI,GAAG,CAAP,GAAWA,IAAI,GAAG,CAAlB,GAAsB,CAAhC,CAApC,EAAwEK,IAAI,GAAG,CAApF,EAAuFA,IAAI,GAAGL,IAA9F,EAAoGK,IAAI,EAAxG,EAA4G;YAC1GF,IAAI,CAACE,IAAI,GAAG,CAAR,CAAJ,GAAiBJ,SAAS,CAACI,IAAD,CAA1B;UACD;;UAEDC,YAAY,CAAC,MAAD,EAASP,MAAT,EAAiBI,IAAjB,CAAZ;QACD;MACF;IACF;;IACD,SAASI,KAAT,CAAeR,MAAf,EAAuB;MACrB;QACE;UACE,KAAK,IAAIS,KAAK,GAAGP,SAAS,CAACC,MAAtB,EAA8BC,IAAI,GAAG,IAAIC,KAAJ,CAAUI,KAAK,GAAG,CAAR,GAAYA,KAAK,GAAG,CAApB,GAAwB,CAAlC,CAArC,EAA2EC,KAAK,GAAG,CAAxF,EAA2FA,KAAK,GAAGD,KAAnG,EAA0GC,KAAK,EAA/G,EAAmH;YACjHN,IAAI,CAACM,KAAK,GAAG,CAAT,CAAJ,GAAkBR,SAAS,CAACQ,KAAD,CAA3B;UACD;;UAEDH,YAAY,CAAC,OAAD,EAAUP,MAAV,EAAkBI,IAAlB,CAAZ;QACD;MACF;IACF;;IAED,SAASG,YAAT,CAAsBI,KAAtB,EAA6BX,MAA7B,EAAqCI,IAArC,EAA2C;MACzC;MACA;MACA;QACE,IAAIlB,sBAAsB,GAAGY,oBAAoB,CAACZ,sBAAlD;QACA,IAAIG,KAAK,GAAGH,sBAAsB,CAACK,gBAAvB,EAAZ;;QAEA,IAAIF,KAAK,KAAK,EAAd,EAAkB;UAChBW,MAAM,IAAI,IAAV;UACAI,IAAI,GAAGA,IAAI,CAACQ,MAAL,CAAY,CAACvB,KAAD,CAAZ,CAAP;QACD,CAPH,CAOI;;;QAGF,IAAIwB,cAAc,GAAGT,IAAI,CAACU,GAAL,CAAS,UAAUC,IAAV,EAAgB;UAC5C,OAAOC,MAAM,CAACD,IAAD,CAAb;QACD,CAFoB,CAArB,CAVF,CAYM;;QAEJF,cAAc,CAACI,OAAf,CAAuB,cAAcjB,MAArC,EAdF,CAcgD;QAC9C;QACA;;QAEAkB,QAAQ,CAACC,SAAT,CAAmBC,KAAnB,CAAyBC,IAAzB,CAA8BC,OAAO,CAACX,KAAD,CAArC,EAA8CW,OAA9C,EAAuDT,cAAvD;MACD;IACF;;IAED,IAAIU,uCAAuC,GAAG,EAA9C;;IAEA,SAASC,QAAT,CAAkBC,cAAlB,EAAkCC,UAAlC,EAA8C;MAC5C;QACE,IAAIC,YAAY,GAAGF,cAAc,CAACG,WAAlC;QACA,IAAIC,aAAa,GAAGF,YAAY,KAAKA,YAAY,CAACG,WAAb,IAA4BH,YAAY,CAACI,IAA9C,CAAZ,IAAmE,YAAvF;QACA,IAAIC,UAAU,GAAGH,aAAa,GAAG,GAAhB,GAAsBH,UAAvC;;QAEA,IAAIH,uCAAuC,CAACS,UAAD,CAA3C,EAAyD;UACvD;QACD;;QAEDxB,KAAK,CAAC,2DAA2D,oEAA3D,GAAkI,qEAAlI,GAA0M,4DAA3M,EAAyQkB,UAAzQ,EAAqRG,aAArR,CAAL;QAEAN,uCAAuC,CAACS,UAAD,CAAvC,GAAsD,IAAtD;MACD;IACF;IACD;AACA;AACA;;;IAGA,IAAIC,oBAAoB,GAAG;MACzB;AACF;AACA;AACA;AACA;AACA;AACA;MACEC,SAAS,EAAE,UAAUT,cAAV,EAA0B;QACnC,OAAO,KAAP;MACD,CAVwB;;MAYzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEU,kBAAkB,EAAE,UAAUV,cAAV,EAA0BW,QAA1B,EAAoCV,UAApC,EAAgD;QAClEF,QAAQ,CAACC,cAAD,EAAiB,aAAjB,CAAR;MACD,CA7BwB;;MA+BzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEY,mBAAmB,EAAE,UAAUZ,cAAV,EAA0Ba,aAA1B,EAAyCF,QAAzC,EAAmDV,UAAnD,EAA+D;QAClFF,QAAQ,CAACC,cAAD,EAAiB,cAAjB,CAAR;MACD,CA9CwB;;MAgDzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEc,eAAe,EAAE,UAAUd,cAAV,EAA0Be,YAA1B,EAAwCJ,QAAxC,EAAkDV,UAAlD,EAA8D;QAC7EF,QAAQ,CAACC,cAAD,EAAiB,UAAjB,CAAR;MACD;IA9DwB,CAA3B;IAiEA,IAAIgB,MAAM,GAAGC,MAAM,CAACD,MAApB;IAEA,IAAIE,WAAW,GAAG,EAAlB;IAEA;MACED,MAAM,CAACE,MAAP,CAAcD,WAAd;IACD;IACD;AACA;AACA;;IAGA,SAASE,SAAT,CAAmBC,KAAnB,EAA0BC,OAA1B,EAAmCC,OAAnC,EAA4C;MAC1C,KAAKF,KAAL,GAAaA,KAAb;MACA,KAAKC,OAAL,GAAeA,OAAf,CAF0C,CAElB;;MAExB,KAAKE,IAAL,GAAYN,WAAZ,CAJ0C,CAIjB;MACzB;;MAEA,KAAKK,OAAL,GAAeA,OAAO,IAAIf,oBAA1B;IACD;;IAEDY,SAAS,CAAC1B,SAAV,CAAoB+B,gBAApB,GAAuC,EAAvC;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEAL,SAAS,CAAC1B,SAAV,CAAoBgC,QAApB,GAA+B,UAAUX,YAAV,EAAwBJ,QAAxB,EAAkC;MAC/D,IAAI,OAAOI,YAAP,KAAwB,QAAxB,IAAoC,OAAOA,YAAP,KAAwB,UAA5D,IAA0EA,YAAY,IAAI,IAA9F,EAAoG;QAClG,MAAM,IAAIrF,KAAJ,CAAU,sEAAsE,sDAAhF,CAAN;MACD;;MAED,KAAK6F,OAAL,CAAaT,eAAb,CAA6B,IAA7B,EAAmCC,YAAnC,EAAiDJ,QAAjD,EAA2D,UAA3D;IACD,CAND;IAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGAS,SAAS,CAAC1B,SAAV,CAAoBiC,WAApB,GAAkC,UAAUhB,QAAV,EAAoB;MACpD,KAAKY,OAAL,CAAab,kBAAb,CAAgC,IAAhC,EAAsCC,QAAtC,EAAgD,aAAhD;IACD,CAFD;IAGA;AACA;AACA;AACA;AACA;;;IAGA;MACE,IAAIiB,cAAc,GAAG;QACnBnB,SAAS,EAAE,CAAC,WAAD,EAAc,0EAA0E,+CAAxF,CADQ;QAEnBoB,YAAY,EAAE,CAAC,cAAD,EAAiB,qDAAqD,iDAAtE;MAFK,CAArB;;MAKA,IAAIC,wBAAwB,GAAG,UAAUC,UAAV,EAAsBC,IAAtB,EAA4B;QACzDf,MAAM,CAACgB,cAAP,CAAsBb,SAAS,CAAC1B,SAAhC,EAA2CqC,UAA3C,EAAuD;UACrDG,GAAG,EAAE,YAAY;YACf5D,IAAI,CAAC,6DAAD,EAAgE0D,IAAI,CAAC,CAAD,CAApE,EAAyEA,IAAI,CAAC,CAAD,CAA7E,CAAJ;YAEA,OAAOG,SAAP;UACD;QALoD,CAAvD;MAOD,CARD;;MAUA,KAAK,IAAIC,MAAT,IAAmBR,cAAnB,EAAmC;QACjC,IAAIA,cAAc,CAACS,cAAf,CAA8BD,MAA9B,CAAJ,EAA2C;UACzCN,wBAAwB,CAACM,MAAD,EAASR,cAAc,CAACQ,MAAD,CAAvB,CAAxB;QACD;MACF;IACF;;IAED,SAASE,cAAT,GAA0B,CAAE;;IAE5BA,cAAc,CAAC5C,SAAf,GAA2B0B,SAAS,CAAC1B,SAArC;IACA;AACA;AACA;;IAEA,SAAS6C,aAAT,CAAuBlB,KAAvB,EAA8BC,OAA9B,EAAuCC,OAAvC,EAAgD;MAC9C,KAAKF,KAAL,GAAaA,KAAb;MACA,KAAKC,OAAL,GAAeA,OAAf,CAF8C,CAEtB;;MAExB,KAAKE,IAAL,GAAYN,WAAZ;MACA,KAAKK,OAAL,GAAeA,OAAO,IAAIf,oBAA1B;IACD;;IAED,IAAIgC,sBAAsB,GAAGD,aAAa,CAAC7C,SAAd,GAA0B,IAAI4C,cAAJ,EAAvD;IACAE,sBAAsB,CAACrC,WAAvB,GAAqCoC,aAArC,CAvZc,CAuZsC;;IAEpDvB,MAAM,CAACwB,sBAAD,EAAyBpB,SAAS,CAAC1B,SAAnC,CAAN;IACA8C,sBAAsB,CAACC,oBAAvB,GAA8C,IAA9C,CA1Zc,CA4Zd;;IACA,SAASC,SAAT,GAAqB;MACnB,IAAIC,SAAS,GAAG;QACdzF,OAAO,EAAE;MADK,CAAhB;MAIA;QACE+D,MAAM,CAAC2B,IAAP,CAAYD,SAAZ;MACD;MAED,OAAOA,SAAP;IACD;;IAED,IAAIE,WAAW,GAAGjE,KAAK,CAACkE,OAAxB,CAzac,CAyamB;;IAEjC,SAASA,OAAT,CAAiBC,CAAjB,EAAoB;MAClB,OAAOF,WAAW,CAACE,CAAD,CAAlB;IACD;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;;;IACA,SAASC,QAAT,CAAkBC,KAAlB,EAAyB;MACvB;QACE;QACA,IAAIC,cAAc,GAAG,OAAOrH,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACsH,WAA5D;QACA,IAAIC,IAAI,GAAGF,cAAc,IAAID,KAAK,CAACpH,MAAM,CAACsH,WAAR,CAAvB,IAA+CF,KAAK,CAAC9C,WAAN,CAAkBG,IAAjE,IAAyE,QAApF;QACA,OAAO8C,IAAP;MACD;IACF,CAhca,CAgcZ;;;IAGF,SAASC,iBAAT,CAA2BJ,KAA3B,EAAkC;MAChC;QACE,IAAI;UACFK,kBAAkB,CAACL,KAAD,CAAlB;UACA,OAAO,KAAP;QACD,CAHD,CAGE,OAAOM,CAAP,EAAU;UACV,OAAO,IAAP;QACD;MACF;IACF;;IAED,SAASD,kBAAT,CAA4BL,KAA5B,EAAmC;MACjC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,KAAKA,KAAZ;IACD;;IACD,SAASO,sBAAT,CAAgCP,KAAhC,EAAuC;MACrC;QACE,IAAII,iBAAiB,CAACJ,KAAD,CAArB,EAA8B;UAC5BlE,KAAK,CAAC,gDAAgD,sEAAjD,EAAyHiE,QAAQ,CAACC,KAAD,CAAjI,CAAL;UAEA,OAAOK,kBAAkB,CAACL,KAAD,CAAzB,CAH4B,CAGM;QACnC;MACF;IACF;;IAED,SAASQ,cAAT,CAAwBC,SAAxB,EAAmCC,SAAnC,EAA8CC,WAA9C,EAA2D;MACzD,IAAIvD,WAAW,GAAGqD,SAAS,CAACrD,WAA5B;;MAEA,IAAIA,WAAJ,EAAiB;QACf,OAAOA,WAAP;MACD;;MAED,IAAIwD,YAAY,GAAGF,SAAS,CAACtD,WAAV,IAAyBsD,SAAS,CAACrD,IAAnC,IAA2C,EAA9D;MACA,OAAOuD,YAAY,KAAK,EAAjB,GAAsBD,WAAW,GAAG,GAAd,GAAoBC,YAApB,GAAmC,GAAzD,GAA+DD,WAAtE;IACD,CA3fa,CA2fZ;;;IAGF,SAASE,cAAT,CAAwBV,IAAxB,EAA8B;MAC5B,OAAOA,IAAI,CAAC/C,WAAL,IAAoB,SAA3B;IACD,CAhgBa,CAggBZ;;;IAGF,SAAS0D,wBAAT,CAAkCX,IAAlC,EAAwC;MACtC,IAAIA,IAAI,IAAI,IAAZ,EAAkB;QAChB;QACA,OAAO,IAAP;MACD;;MAED;QACE,IAAI,OAAOA,IAAI,CAACY,GAAZ,KAAoB,QAAxB,EAAkC;UAChCjF,KAAK,CAAC,kEAAkE,sDAAnE,CAAL;QACD;MACF;;MAED,IAAI,OAAOqE,IAAP,KAAgB,UAApB,EAAgC;QAC9B,OAAOA,IAAI,CAAC/C,WAAL,IAAoB+C,IAAI,CAAC9C,IAAzB,IAAiC,IAAxC;MACD;;MAED,IAAI,OAAO8C,IAAP,KAAgB,QAApB,EAA8B;QAC5B,OAAOA,IAAP;MACD;;MAED,QAAQA,IAAR;QACE,KAAKpH,mBAAL;UACE,OAAO,UAAP;;QAEF,KAAKD,iBAAL;UACE,OAAO,QAAP;;QAEF,KAAKG,mBAAL;UACE,OAAO,UAAP;;QAEF,KAAKD,sBAAL;UACE,OAAO,YAAP;;QAEF,KAAKK,mBAAL;UACE,OAAO,UAAP;;QAEF,KAAKC,wBAAL;UACE,OAAO,cAAP;MAjBJ;;MAqBA,IAAI,OAAO6G,IAAP,KAAgB,QAApB,EAA8B;QAC5B,QAAQA,IAAI,CAACa,QAAb;UACE,KAAK7H,kBAAL;YACE,IAAIkF,OAAO,GAAG8B,IAAd;YACA,OAAOU,cAAc,CAACxC,OAAD,CAAd,GAA0B,WAAjC;;UAEF,KAAKnF,mBAAL;YACE,IAAI+H,QAAQ,GAAGd,IAAf;YACA,OAAOU,cAAc,CAACI,QAAQ,CAACC,QAAV,CAAd,GAAoC,WAA3C;;UAEF,KAAK9H,sBAAL;YACE,OAAOoH,cAAc,CAACL,IAAD,EAAOA,IAAI,CAACgB,MAAZ,EAAoB,YAApB,CAArB;;UAEF,KAAK5H,eAAL;YACE,IAAI6H,SAAS,GAAGjB,IAAI,CAAC/C,WAAL,IAAoB,IAApC;;YAEA,IAAIgE,SAAS,KAAK,IAAlB,EAAwB;cACtB,OAAOA,SAAP;YACD;;YAED,OAAON,wBAAwB,CAACX,IAAI,CAACA,IAAN,CAAxB,IAAuC,MAA9C;;UAEF,KAAK3G,eAAL;YACE;cACE,IAAI6H,aAAa,GAAGlB,IAApB;cACA,IAAImB,OAAO,GAAGD,aAAa,CAACE,QAA5B;cACA,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAzB;;cAEA,IAAI;gBACF,OAAOX,wBAAwB,CAACU,IAAI,CAACF,OAAD,CAAL,CAA/B;cACD,CAFD,CAEE,OAAOI,CAAP,EAAU;gBACV,OAAO,IAAP;cACD;YACF;UAEH;QAlCF;MAoCD;;MAED,OAAO,IAAP;IACD;;IAED,IAAItC,cAAc,GAAGpB,MAAM,CAACvB,SAAP,CAAiB2C,cAAtC;IAEA,IAAIuC,cAAc,GAAG;MACnBC,GAAG,EAAE,IADc;MAEnBC,GAAG,EAAE,IAFc;MAGnBC,MAAM,EAAE,IAHW;MAInBC,QAAQ,EAAE;IAJS,CAArB;IAMA,IAAIC,0BAAJ,EAAgCC,0BAAhC,EAA4DC,sBAA5D;IAEA;MACEA,sBAAsB,GAAG,EAAzB;IACD;;IAED,SAASC,WAAT,CAAqBC,MAArB,EAA6B;MAC3B;QACE,IAAIhD,cAAc,CAACzC,IAAf,CAAoByF,MAApB,EAA4B,KAA5B,CAAJ,EAAwC;UACtC,IAAIC,MAAM,GAAGrE,MAAM,CAACsE,wBAAP,CAAgCF,MAAhC,EAAwC,KAAxC,EAA+CnD,GAA5D;;UAEA,IAAIoD,MAAM,IAAIA,MAAM,CAACE,cAArB,EAAqC;YACnC,OAAO,KAAP;UACD;QACF;MACF;MAED,OAAOH,MAAM,CAACP,GAAP,KAAe3C,SAAtB;IACD;;IAED,SAASsD,WAAT,CAAqBJ,MAArB,EAA6B;MAC3B;QACE,IAAIhD,cAAc,CAACzC,IAAf,CAAoByF,MAApB,EAA4B,KAA5B,CAAJ,EAAwC;UACtC,IAAIC,MAAM,GAAGrE,MAAM,CAACsE,wBAAP,CAAgCF,MAAhC,EAAwC,KAAxC,EAA+CnD,GAA5D;;UAEA,IAAIoD,MAAM,IAAIA,MAAM,CAACE,cAArB,EAAqC;YACnC,OAAO,KAAP;UACD;QACF;MACF;MAED,OAAOH,MAAM,CAACR,GAAP,KAAe1C,SAAtB;IACD;;IAED,SAASuD,0BAAT,CAAoCrE,KAApC,EAA2ChB,WAA3C,EAAwD;MACtD,IAAIsF,qBAAqB,GAAG,YAAY;QACtC;UACE,IAAI,CAACV,0BAAL,EAAiC;YAC/BA,0BAA0B,GAAG,IAA7B;YAEAlG,KAAK,CAAC,8DAA8D,gEAA9D,GAAiI,sEAAjI,GAA0M,gDAA3M,EAA6PsB,WAA7P,CAAL;UACD;QACF;MACF,CARD;;MAUAsF,qBAAqB,CAACH,cAAtB,GAAuC,IAAvC;MACAvE,MAAM,CAACgB,cAAP,CAAsBZ,KAAtB,EAA6B,KAA7B,EAAoC;QAClCa,GAAG,EAAEyD,qBAD6B;QAElCC,YAAY,EAAE;MAFoB,CAApC;IAID;;IAED,SAASC,0BAAT,CAAoCxE,KAApC,EAA2ChB,WAA3C,EAAwD;MACtD,IAAIyF,qBAAqB,GAAG,YAAY;QACtC;UACE,IAAI,CAACZ,0BAAL,EAAiC;YAC/BA,0BAA0B,GAAG,IAA7B;YAEAnG,KAAK,CAAC,8DAA8D,gEAA9D,GAAiI,sEAAjI,GAA0M,gDAA3M,EAA6PsB,WAA7P,CAAL;UACD;QACF;MACF,CARD;;MAUAyF,qBAAqB,CAACN,cAAtB,GAAuC,IAAvC;MACAvE,MAAM,CAACgB,cAAP,CAAsBZ,KAAtB,EAA6B,KAA7B,EAAoC;QAClCa,GAAG,EAAE4D,qBAD6B;QAElCF,YAAY,EAAE;MAFoB,CAApC;IAID;;IAED,SAASG,oCAAT,CAA8CV,MAA9C,EAAsD;MACpD;QACE,IAAI,OAAOA,MAAM,CAACP,GAAd,KAAsB,QAAtB,IAAkCtH,iBAAiB,CAACN,OAApD,IAA+DmI,MAAM,CAACN,MAAtE,IAAgFvH,iBAAiB,CAACN,OAAlB,CAA0B8I,SAA1B,KAAwCX,MAAM,CAACN,MAAnI,EAA2I;UACzI,IAAI3E,aAAa,GAAG2D,wBAAwB,CAACvG,iBAAiB,CAACN,OAAlB,CAA0BkG,IAA3B,CAA5C;;UAEA,IAAI,CAAC+B,sBAAsB,CAAC/E,aAAD,CAA3B,EAA4C;YAC1CrB,KAAK,CAAC,kDAAkD,qEAAlD,GAA0H,oEAA1H,GAAiM,iFAAjM,GAAqR,2CAArR,GAAmU,iDAApU,EAAuXqB,aAAvX,EAAsYiF,MAAM,CAACP,GAA7Y,CAAL;YAEAK,sBAAsB,CAAC/E,aAAD,CAAtB,GAAwC,IAAxC;UACD;QACF;MACF;IACF;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,IAAI6F,YAAY,GAAG,UAAU7C,IAAV,EAAgByB,GAAhB,EAAqBC,GAArB,EAA0BoB,IAA1B,EAAgCC,MAAhC,EAAwCC,KAAxC,EAA+C/E,KAA/C,EAAsD;MACvE,IAAIgF,OAAO,GAAG;QACZ;QACApC,QAAQ,EAAErI,kBAFE;QAGZ;QACAwH,IAAI,EAAEA,IAJM;QAKZyB,GAAG,EAAEA,GALO;QAMZC,GAAG,EAAEA,GANO;QAOZzD,KAAK,EAAEA,KAPK;QAQZ;QACAiF,MAAM,EAAEF;MATI,CAAd;MAYA;QACE;QACA;QACA;QACA;QACAC,OAAO,CAACE,MAAR,GAAiB,EAAjB,CALF,CAKuB;QACrB;QACA;QACA;;QAEAtF,MAAM,CAACgB,cAAP,CAAsBoE,OAAO,CAACE,MAA9B,EAAsC,WAAtC,EAAmD;UACjDX,YAAY,EAAE,KADmC;UAEjDY,UAAU,EAAE,KAFqC;UAGjDC,QAAQ,EAAE,IAHuC;UAIjDxD,KAAK,EAAE;QAJ0C,CAAnD,EAVF,CAeM;;QAEJhC,MAAM,CAACgB,cAAP,CAAsBoE,OAAtB,EAA+B,OAA/B,EAAwC;UACtCT,YAAY,EAAE,KADwB;UAEtCY,UAAU,EAAE,KAF0B;UAGtCC,QAAQ,EAAE,KAH4B;UAItCxD,KAAK,EAAEiD;QAJ+B,CAAxC,EAjBF,CAsBM;QACJ;;QAEAjF,MAAM,CAACgB,cAAP,CAAsBoE,OAAtB,EAA+B,SAA/B,EAA0C;UACxCT,YAAY,EAAE,KAD0B;UAExCY,UAAU,EAAE,KAF4B;UAGxCC,QAAQ,EAAE,KAH8B;UAIxCxD,KAAK,EAAEkD;QAJiC,CAA1C;;QAOA,IAAIlF,MAAM,CAACE,MAAX,EAAmB;UACjBF,MAAM,CAACE,MAAP,CAAckF,OAAO,CAAChF,KAAtB;UACAJ,MAAM,CAACE,MAAP,CAAckF,OAAd;QACD;MACF;MAED,OAAOA,OAAP;IACD,CApDD;IAqDA;AACA;AACA;AACA;;;IAEA,SAASK,aAAT,CAAuBtD,IAAvB,EAA6BiC,MAA7B,EAAqCsB,QAArC,EAA+C;MAC7C,IAAIC,QAAJ,CAD6C,CAC/B;;MAEd,IAAIvF,KAAK,GAAG,EAAZ;MACA,IAAIwD,GAAG,GAAG,IAAV;MACA,IAAIC,GAAG,GAAG,IAAV;MACA,IAAIoB,IAAI,GAAG,IAAX;MACA,IAAIC,MAAM,GAAG,IAAb;;MAEA,IAAId,MAAM,IAAI,IAAd,EAAoB;QAClB,IAAID,WAAW,CAACC,MAAD,CAAf,EAAyB;UACvBP,GAAG,GAAGO,MAAM,CAACP,GAAb;UAEA;YACEiB,oCAAoC,CAACV,MAAD,CAApC;UACD;QACF;;QAED,IAAII,WAAW,CAACJ,MAAD,CAAf,EAAyB;UACvB;YACE7B,sBAAsB,CAAC6B,MAAM,CAACR,GAAR,CAAtB;UACD;UAEDA,GAAG,GAAG,KAAKQ,MAAM,CAACR,GAAlB;QACD;;QAEDqB,IAAI,GAAGb,MAAM,CAACN,MAAP,KAAkB5C,SAAlB,GAA8B,IAA9B,GAAqCkD,MAAM,CAACN,MAAnD;QACAoB,MAAM,GAAGd,MAAM,CAACL,QAAP,KAAoB7C,SAApB,GAAgC,IAAhC,GAAuCkD,MAAM,CAACL,QAAvD,CAlBkB,CAkB+C;;QAEjE,KAAK4B,QAAL,IAAiBvB,MAAjB,EAAyB;UACvB,IAAIhD,cAAc,CAACzC,IAAf,CAAoByF,MAApB,EAA4BuB,QAA5B,KAAyC,CAAChC,cAAc,CAACvC,cAAf,CAA8BuE,QAA9B,CAA9C,EAAuF;YACrFvF,KAAK,CAACuF,QAAD,CAAL,GAAkBvB,MAAM,CAACuB,QAAD,CAAxB;UACD;QACF;MACF,CAlC4C,CAkC3C;MACF;;;MAGA,IAAIC,cAAc,GAAGpI,SAAS,CAACC,MAAV,GAAmB,CAAxC;;MAEA,IAAImI,cAAc,KAAK,CAAvB,EAA0B;QACxBxF,KAAK,CAACsF,QAAN,GAAiBA,QAAjB;MACD,CAFD,MAEO,IAAIE,cAAc,GAAG,CAArB,EAAwB;QAC7B,IAAIC,UAAU,GAAGlI,KAAK,CAACiI,cAAD,CAAtB;;QAEA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,cAApB,EAAoCE,CAAC,EAArC,EAAyC;UACvCD,UAAU,CAACC,CAAD,CAAV,GAAgBtI,SAAS,CAACsI,CAAC,GAAG,CAAL,CAAzB;QACD;;QAED;UACE,IAAI9F,MAAM,CAACE,MAAX,EAAmB;YACjBF,MAAM,CAACE,MAAP,CAAc2F,UAAd;UACD;QACF;QAEDzF,KAAK,CAACsF,QAAN,GAAiBG,UAAjB;MACD,CAxD4C,CAwD3C;;;MAGF,IAAI1D,IAAI,IAAIA,IAAI,CAAC4D,YAAjB,EAA+B;QAC7B,IAAIA,YAAY,GAAG5D,IAAI,CAAC4D,YAAxB;;QAEA,KAAKJ,QAAL,IAAiBI,YAAjB,EAA+B;UAC7B,IAAI3F,KAAK,CAACuF,QAAD,CAAL,KAAoBzE,SAAxB,EAAmC;YACjCd,KAAK,CAACuF,QAAD,CAAL,GAAkBI,YAAY,CAACJ,QAAD,CAA9B;UACD;QACF;MACF;;MAED;QACE,IAAI/B,GAAG,IAAIC,GAAX,EAAgB;UACd,IAAIzE,WAAW,GAAG,OAAO+C,IAAP,KAAgB,UAAhB,GAA6BA,IAAI,CAAC/C,WAAL,IAAoB+C,IAAI,CAAC9C,IAAzB,IAAiC,SAA9D,GAA0E8C,IAA5F;;UAEA,IAAIyB,GAAJ,EAAS;YACPa,0BAA0B,CAACrE,KAAD,EAAQhB,WAAR,CAA1B;UACD;;UAED,IAAIyE,GAAJ,EAAS;YACPe,0BAA0B,CAACxE,KAAD,EAAQhB,WAAR,CAA1B;UACD;QACF;MACF;MAED,OAAO4F,YAAY,CAAC7C,IAAD,EAAOyB,GAAP,EAAYC,GAAZ,EAAiBoB,IAAjB,EAAuBC,MAAvB,EAA+B3I,iBAAiB,CAACN,OAAjD,EAA0DmE,KAA1D,CAAnB;IACD;;IACD,SAAS4F,kBAAT,CAA4BC,UAA5B,EAAwCC,MAAxC,EAAgD;MAC9C,IAAIC,UAAU,GAAGnB,YAAY,CAACiB,UAAU,CAAC9D,IAAZ,EAAkB+D,MAAlB,EAA0BD,UAAU,CAACpC,GAArC,EAA0CoC,UAAU,CAACG,KAArD,EAA4DH,UAAU,CAACI,OAAvE,EAAgFJ,UAAU,CAACZ,MAA3F,EAAmGY,UAAU,CAAC7F,KAA9G,CAA7B;MACA,OAAO+F,UAAP;IACD;IACD;AACA;AACA;AACA;;;IAEA,SAASG,YAAT,CAAsBlB,OAAtB,EAA+BhB,MAA/B,EAAuCsB,QAAvC,EAAiD;MAC/C,IAAIN,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAKlE,SAApC,EAA+C;QAC7C,MAAM,IAAIzG,KAAJ,CAAU,mFAAmF2K,OAAnF,GAA6F,GAAvG,CAAN;MACD;;MAED,IAAIO,QAAJ,CAL+C,CAKjC;;MAEd,IAAIvF,KAAK,GAAGL,MAAM,CAAC,EAAD,EAAKqF,OAAO,CAAChF,KAAb,CAAlB,CAP+C,CAOR;;MAEvC,IAAIwD,GAAG,GAAGwB,OAAO,CAACxB,GAAlB;MACA,IAAIC,GAAG,GAAGuB,OAAO,CAACvB,GAAlB,CAV+C,CAUxB;;MAEvB,IAAIoB,IAAI,GAAGG,OAAO,CAACgB,KAAnB,CAZ+C,CAYrB;MAC1B;MACA;;MAEA,IAAIlB,MAAM,GAAGE,OAAO,CAACiB,OAArB,CAhB+C,CAgBjB;;MAE9B,IAAIlB,KAAK,GAAGC,OAAO,CAACC,MAApB;;MAEA,IAAIjB,MAAM,IAAI,IAAd,EAAoB;QAClB,IAAID,WAAW,CAACC,MAAD,CAAf,EAAyB;UACvB;UACAP,GAAG,GAAGO,MAAM,CAACP,GAAb;UACAsB,KAAK,GAAG5I,iBAAiB,CAACN,OAA1B;QACD;;QAED,IAAIuI,WAAW,CAACJ,MAAD,CAAf,EAAyB;UACvB;YACE7B,sBAAsB,CAAC6B,MAAM,CAACR,GAAR,CAAtB;UACD;UAEDA,GAAG,GAAG,KAAKQ,MAAM,CAACR,GAAlB;QACD,CAbiB,CAahB;;;QAGF,IAAImC,YAAJ;;QAEA,IAAIX,OAAO,CAACjD,IAAR,IAAgBiD,OAAO,CAACjD,IAAR,CAAa4D,YAAjC,EAA+C;UAC7CA,YAAY,GAAGX,OAAO,CAACjD,IAAR,CAAa4D,YAA5B;QACD;;QAED,KAAKJ,QAAL,IAAiBvB,MAAjB,EAAyB;UACvB,IAAIhD,cAAc,CAACzC,IAAf,CAAoByF,MAApB,EAA4BuB,QAA5B,KAAyC,CAAChC,cAAc,CAACvC,cAAf,CAA8BuE,QAA9B,CAA9C,EAAuF;YACrF,IAAIvB,MAAM,CAACuB,QAAD,CAAN,KAAqBzE,SAArB,IAAkC6E,YAAY,KAAK7E,SAAvD,EAAkE;cAChE;cACAd,KAAK,CAACuF,QAAD,CAAL,GAAkBI,YAAY,CAACJ,QAAD,CAA9B;YACD,CAHD,MAGO;cACLvF,KAAK,CAACuF,QAAD,CAAL,GAAkBvB,MAAM,CAACuB,QAAD,CAAxB;YACD;UACF;QACF;MACF,CApD8C,CAoD7C;MACF;;;MAGA,IAAIC,cAAc,GAAGpI,SAAS,CAACC,MAAV,GAAmB,CAAxC;;MAEA,IAAImI,cAAc,KAAK,CAAvB,EAA0B;QACxBxF,KAAK,CAACsF,QAAN,GAAiBA,QAAjB;MACD,CAFD,MAEO,IAAIE,cAAc,GAAG,CAArB,EAAwB;QAC7B,IAAIC,UAAU,GAAGlI,KAAK,CAACiI,cAAD,CAAtB;;QAEA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,cAApB,EAAoCE,CAAC,EAArC,EAAyC;UACvCD,UAAU,CAACC,CAAD,CAAV,GAAgBtI,SAAS,CAACsI,CAAC,GAAG,CAAL,CAAzB;QACD;;QAED1F,KAAK,CAACsF,QAAN,GAAiBG,UAAjB;MACD;;MAED,OAAOb,YAAY,CAACI,OAAO,CAACjD,IAAT,EAAeyB,GAAf,EAAoBC,GAApB,EAAyBoB,IAAzB,EAA+BC,MAA/B,EAAuCC,KAAvC,EAA8C/E,KAA9C,CAAnB;IACD;IACD;AACA;AACA;AACA;AACA;AACA;AACA;;;IAEA,SAASmG,cAAT,CAAwBC,MAAxB,EAAgC;MAC9B,OAAO,OAAOA,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,KAAK,IAAzC,IAAiDA,MAAM,CAACxD,QAAP,KAAoBrI,kBAA5E;IACD;;IAED,IAAI8L,SAAS,GAAG,GAAhB;IACA,IAAIC,YAAY,GAAG,GAAnB;IACA;AACA;AACA;AACA;AACA;AACA;;IAEA,SAASC,MAAT,CAAgB/C,GAAhB,EAAqB;MACnB,IAAIgD,WAAW,GAAG,OAAlB;MACA,IAAIC,aAAa,GAAG;QAClB,KAAK,IADa;QAElB,KAAK;MAFa,CAApB;MAIA,IAAIC,aAAa,GAAGlD,GAAG,CAACmD,OAAJ,CAAYH,WAAZ,EAAyB,UAAUI,KAAV,EAAiB;QAC5D,OAAOH,aAAa,CAACG,KAAD,CAApB;MACD,CAFmB,CAApB;MAGA,OAAO,MAAMF,aAAb;IACD;IACD;AACA;AACA;AACA;;;IAGA,IAAIG,gBAAgB,GAAG,KAAvB;IACA,IAAIC,0BAA0B,GAAG,MAAjC;;IAEA,SAASC,qBAAT,CAA+BC,IAA/B,EAAqC;MACnC,OAAOA,IAAI,CAACL,OAAL,CAAaG,0BAAb,EAAyC,KAAzC,CAAP;IACD;IACD;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASG,aAAT,CAAuBjC,OAAvB,EAAgCkC,KAAhC,EAAuC;MACrC;MACA;MACA,IAAI,OAAOlC,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,KAAK,IAA3C,IAAmDA,OAAO,CAACxB,GAAR,IAAe,IAAtE,EAA4E;QAC1E;QACA;UACErB,sBAAsB,CAAC6C,OAAO,CAACxB,GAAT,CAAtB;QACD;QAED,OAAO+C,MAAM,CAAC,KAAKvB,OAAO,CAACxB,GAAd,CAAb;MACD,CAVoC,CAUnC;;;MAGF,OAAO0D,KAAK,CAACC,QAAN,CAAe,EAAf,CAAP;IACD;;IAED,SAASC,YAAT,CAAsB9B,QAAtB,EAAgC+B,KAAhC,EAAuCC,aAAvC,EAAsDC,SAAtD,EAAiEjI,QAAjE,EAA2E;MACzE,IAAIyC,IAAI,GAAG,OAAOuD,QAAlB;;MAEA,IAAIvD,IAAI,KAAK,WAAT,IAAwBA,IAAI,KAAK,SAArC,EAAgD;QAC9C;QACAuD,QAAQ,GAAG,IAAX;MACD;;MAED,IAAIkC,cAAc,GAAG,KAArB;;MAEA,IAAIlC,QAAQ,KAAK,IAAjB,EAAuB;QACrBkC,cAAc,GAAG,IAAjB;MACD,CAFD,MAEO;QACL,QAAQzF,IAAR;UACE,KAAK,QAAL;UACA,KAAK,QAAL;YACEyF,cAAc,GAAG,IAAjB;YACA;;UAEF,KAAK,QAAL;YACE,QAAQlC,QAAQ,CAAC1C,QAAjB;cACE,KAAKrI,kBAAL;cACA,KAAKG,iBAAL;gBACE8M,cAAc,GAAG,IAAjB;YAHJ;;QAPJ;MAcD;;MAED,IAAIA,cAAJ,EAAoB;QAClB,IAAIC,MAAM,GAAGnC,QAAb;QACA,IAAIoC,WAAW,GAAGpI,QAAQ,CAACmI,MAAD,CAA1B,CAFkB,CAEkB;QACpC;;QAEA,IAAIE,QAAQ,GAAGJ,SAAS,KAAK,EAAd,GAAmBlB,SAAS,GAAGY,aAAa,CAACQ,MAAD,EAAS,CAAT,CAA5C,GAA0DF,SAAzE;;QAEA,IAAI9F,OAAO,CAACiG,WAAD,CAAX,EAA0B;UACxB,IAAIE,eAAe,GAAG,EAAtB;;UAEA,IAAID,QAAQ,IAAI,IAAhB,EAAsB;YACpBC,eAAe,GAAGb,qBAAqB,CAACY,QAAD,CAArB,GAAkC,GAApD;UACD;;UAEDP,YAAY,CAACM,WAAD,EAAcL,KAAd,EAAqBO,eAArB,EAAsC,EAAtC,EAA0C,UAAUC,CAAV,EAAa;YACjE,OAAOA,CAAP;UACD,CAFW,CAAZ;QAGD,CAVD,MAUO,IAAIH,WAAW,IAAI,IAAnB,EAAyB;UAC9B,IAAIvB,cAAc,CAACuB,WAAD,CAAlB,EAAiC;YAC/B;cACE;cACA;cACA;cACA,IAAIA,WAAW,CAAClE,GAAZ,KAAoB,CAACiE,MAAD,IAAWA,MAAM,CAACjE,GAAP,KAAekE,WAAW,CAAClE,GAA1D,CAAJ,EAAoE;gBAClErB,sBAAsB,CAACuF,WAAW,CAAClE,GAAb,CAAtB;cACD;YACF;YAEDkE,WAAW,GAAG9B,kBAAkB,CAAC8B,WAAD,EAAc;YAC9C;YACAJ,aAAa,KAAK;YAClBI,WAAW,CAAClE,GAAZ,KAAoB,CAACiE,MAAD,IAAWA,MAAM,CAACjE,GAAP,KAAekE,WAAW,CAAClE,GAA1D,IAAiE;YACjE;YACAuD,qBAAqB,CAAC,KAAKW,WAAW,CAAClE,GAAlB,CAArB,GAA8C,GAF9C,GAEoD,EAHvC,CAAb,GAG0DmE,QAL1B,CAAhC;UAMD;;UAEDN,KAAK,CAACS,IAAN,CAAWJ,WAAX;QACD;;QAED,OAAO,CAAP;MACD;;MAED,IAAIK,KAAJ;MACA,IAAIC,QAAJ;MACA,IAAIC,YAAY,GAAG,CAAnB,CAzEyE,CAyEnD;;MAEtB,IAAIC,cAAc,GAAGX,SAAS,KAAK,EAAd,GAAmBlB,SAAnB,GAA+BkB,SAAS,GAAGjB,YAAhE;;MAEA,IAAI7E,OAAO,CAAC6D,QAAD,CAAX,EAAuB;QACrB,KAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,QAAQ,CAACjI,MAA7B,EAAqCqI,CAAC,EAAtC,EAA0C;UACxCqC,KAAK,GAAGzC,QAAQ,CAACI,CAAD,CAAhB;UACAsC,QAAQ,GAAGE,cAAc,GAAGjB,aAAa,CAACc,KAAD,EAAQrC,CAAR,CAAzC;UACAuC,YAAY,IAAIb,YAAY,CAACW,KAAD,EAAQV,KAAR,EAAeC,aAAf,EAA8BU,QAA9B,EAAwC1I,QAAxC,CAA5B;QACD;MACF,CAND,MAMO;QACL,IAAI6I,UAAU,GAAG1M,aAAa,CAAC6J,QAAD,CAA9B;;QAEA,IAAI,OAAO6C,UAAP,KAAsB,UAA1B,EAAsC;UACpC,IAAIC,gBAAgB,GAAG9C,QAAvB;UAEA;YACE;YACA,IAAI6C,UAAU,KAAKC,gBAAgB,CAACC,OAApC,EAA6C;cAC3C,IAAI,CAACxB,gBAAL,EAAuB;gBACrB5J,IAAI,CAAC,8CAA8C,8CAA/C,CAAJ;cACD;;cAED4J,gBAAgB,GAAG,IAAnB;YACD;UACF;UAED,IAAItL,QAAQ,GAAG4M,UAAU,CAAC5J,IAAX,CAAgB6J,gBAAhB,CAAf;UACA,IAAIE,IAAJ;UACA,IAAIC,EAAE,GAAG,CAAT;;UAEA,OAAO,CAAC,CAACD,IAAI,GAAG/M,QAAQ,CAACiN,IAAT,EAAR,EAAyBC,IAAjC,EAAuC;YACrCV,KAAK,GAAGO,IAAI,CAAC1G,KAAb;YACAoG,QAAQ,GAAGE,cAAc,GAAGjB,aAAa,CAACc,KAAD,EAAQQ,EAAE,EAAV,CAAzC;YACAN,YAAY,IAAIb,YAAY,CAACW,KAAD,EAAQV,KAAR,EAAeC,aAAf,EAA8BU,QAA9B,EAAwC1I,QAAxC,CAA5B;UACD;QACF,CAvBD,MAuBO,IAAIyC,IAAI,KAAK,QAAb,EAAuB;UAC5B;UACA,IAAI2G,cAAc,GAAGxK,MAAM,CAACoH,QAAD,CAA3B;UACA,MAAM,IAAIjL,KAAJ,CAAU,qDAAqDqO,cAAc,KAAK,iBAAnB,GAAuC,uBAAuB9I,MAAM,CAAC+I,IAAP,CAAYrD,QAAZ,EAAsBsD,IAAtB,CAA2B,IAA3B,CAAvB,GAA0D,GAAjG,GAAuGF,cAA5J,IAA8K,KAA9K,GAAsL,gEAAtL,GAAyP,UAAnQ,CAAN;QACD;MACF;;MAED,OAAOT,YAAP;IACD;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACA,SAASY,WAAT,CAAqBvD,QAArB,EAA+BwD,IAA/B,EAAqC7I,OAArC,EAA8C;MAC5C,IAAIqF,QAAQ,IAAI,IAAhB,EAAsB;QACpB,OAAOA,QAAP;MACD;;MAED,IAAIyD,MAAM,GAAG,EAAb;MACA,IAAIC,KAAK,GAAG,CAAZ;MACA5B,YAAY,CAAC9B,QAAD,EAAWyD,MAAX,EAAmB,EAAnB,EAAuB,EAAvB,EAA2B,UAAUhB,KAAV,EAAiB;QACtD,OAAOe,IAAI,CAACvK,IAAL,CAAU0B,OAAV,EAAmB8H,KAAnB,EAA0BiB,KAAK,EAA/B,CAAP;MACD,CAFW,CAAZ;MAGA,OAAOD,MAAP;IACD;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASE,aAAT,CAAuB3D,QAAvB,EAAiC;MAC/B,IAAI4D,CAAC,GAAG,CAAR;MACAL,WAAW,CAACvD,QAAD,EAAW,YAAY;QAChC4D,CAAC,GAD+B,CAC3B;MACN,CAFU,CAAX;MAGA,OAAOA,CAAP;IACD;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACA,SAASC,eAAT,CAAyB7D,QAAzB,EAAmC8D,WAAnC,EAAgDC,cAAhD,EAAgE;MAC9DR,WAAW,CAACvD,QAAD,EAAW,YAAY;QAChC8D,WAAW,CAAC9K,KAAZ,CAAkB,IAAlB,EAAwBlB,SAAxB,EADgC,CACI;MACrC,CAFU,EAERiM,cAFQ,CAAX;IAGD;IACD;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASC,OAAT,CAAiBhE,QAAjB,EAA2B;MACzB,OAAOuD,WAAW,CAACvD,QAAD,EAAW,UAAUyC,KAAV,EAAiB;QAC5C,OAAOA,KAAP;MACD,CAFiB,CAAX,IAED,EAFN;IAGD;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASwB,SAAT,CAAmBjE,QAAnB,EAA6B;MAC3B,IAAI,CAACa,cAAc,CAACb,QAAD,CAAnB,EAA+B;QAC7B,MAAM,IAAIjL,KAAJ,CAAU,uEAAV,CAAN;MACD;;MAED,OAAOiL,QAAP;IACD;;IAED,SAASkE,aAAT,CAAuBC,YAAvB,EAAqC;MACnC;MACA;MACA,IAAIxJ,OAAO,GAAG;QACZ2C,QAAQ,EAAE7H,kBADE;QAEZ;QACA;QACA;QACA;QACA;QACA2O,aAAa,EAAED,YAPH;QAQZE,cAAc,EAAEF,YARJ;QASZ;QACA;QACAG,YAAY,EAAE,CAXF;QAYZ;QACAC,QAAQ,EAAE,IAbE;QAcZC,QAAQ,EAAE,IAdE;QAeZ;QACAC,aAAa,EAAE,IAhBH;QAiBZC,WAAW,EAAE;MAjBD,CAAd;MAmBA/J,OAAO,CAAC4J,QAAR,GAAmB;QACjBjH,QAAQ,EAAE9H,mBADO;QAEjBgI,QAAQ,EAAE7C;MAFO,CAAnB;MAIA,IAAIgK,yCAAyC,GAAG,KAAhD;MACA,IAAIC,mCAAmC,GAAG,KAA1C;MACA,IAAIC,mCAAmC,GAAG,KAA1C;MAEA;QACE;QACA;QACA;QACA,IAAIL,QAAQ,GAAG;UACblH,QAAQ,EAAE7H,kBADG;UAEb+H,QAAQ,EAAE7C;QAFG,CAAf,CAJF,CAOK;;QAEHL,MAAM,CAACwK,gBAAP,CAAwBN,QAAxB,EAAkC;UAChCD,QAAQ,EAAE;YACRhJ,GAAG,EAAE,YAAY;cACf,IAAI,CAACqJ,mCAAL,EAA0C;gBACxCA,mCAAmC,GAAG,IAAtC;gBAEAxM,KAAK,CAAC,mFAAmF,4EAApF,CAAL;cACD;;cAED,OAAOuC,OAAO,CAAC4J,QAAf;YACD,CATO;YAURQ,GAAG,EAAE,UAAUC,SAAV,EAAqB;cACxBrK,OAAO,CAAC4J,QAAR,GAAmBS,SAAnB;YACD;UAZO,CADsB;UAehCZ,aAAa,EAAE;YACb7I,GAAG,EAAE,YAAY;cACf,OAAOZ,OAAO,CAACyJ,aAAf;YACD,CAHY;YAIbW,GAAG,EAAE,UAAUX,aAAV,EAAyB;cAC5BzJ,OAAO,CAACyJ,aAAR,GAAwBA,aAAxB;YACD;UANY,CAfiB;UAuBhCC,cAAc,EAAE;YACd9I,GAAG,EAAE,YAAY;cACf,OAAOZ,OAAO,CAAC0J,cAAf;YACD,CAHa;YAIdU,GAAG,EAAE,UAAUV,cAAV,EAA0B;cAC7B1J,OAAO,CAAC0J,cAAR,GAAyBA,cAAzB;YACD;UANa,CAvBgB;UA+BhCC,YAAY,EAAE;YACZ/I,GAAG,EAAE,YAAY;cACf,OAAOZ,OAAO,CAAC2J,YAAf;YACD,CAHW;YAIZS,GAAG,EAAE,UAAUT,YAAV,EAAwB;cAC3B3J,OAAO,CAAC2J,YAAR,GAAuBA,YAAvB;YACD;UANW,CA/BkB;UAuChCE,QAAQ,EAAE;YACRjJ,GAAG,EAAE,YAAY;cACf,IAAI,CAACoJ,yCAAL,EAAgD;gBAC9CA,yCAAyC,GAAG,IAA5C;gBAEAvM,KAAK,CAAC,mFAAmF,4EAApF,CAAL;cACD;;cAED,OAAOuC,OAAO,CAAC6J,QAAf;YACD;UATO,CAvCsB;UAkDhC9K,WAAW,EAAE;YACX6B,GAAG,EAAE,YAAY;cACf,OAAOZ,OAAO,CAACjB,WAAf;YACD,CAHU;YAIXqL,GAAG,EAAE,UAAUrL,WAAV,EAAuB;cAC1B,IAAI,CAACmL,mCAAL,EAA0C;gBACxClN,IAAI,CAAC,8DAA8D,4EAA/D,EAA6I+B,WAA7I,CAAJ;gBAEAmL,mCAAmC,GAAG,IAAtC;cACD;YACF;UAVU;QAlDmB,CAAlC,EATF,CAuEM;;QAEJlK,OAAO,CAAC6J,QAAR,GAAmBA,QAAnB;MACD;MAED;QACE7J,OAAO,CAACsK,gBAAR,GAA2B,IAA3B;QACAtK,OAAO,CAACuK,iBAAR,GAA4B,IAA5B;MACD;MAED,OAAOvK,OAAP;IACD;;IAED,IAAIwK,aAAa,GAAG,CAAC,CAArB;IACA,IAAIC,OAAO,GAAG,CAAd;IACA,IAAIC,QAAQ,GAAG,CAAf;IACA,IAAIC,QAAQ,GAAG,CAAf;;IAEA,SAASC,eAAT,CAAyB3H,OAAzB,EAAkC;MAChC,IAAIA,OAAO,CAAC4H,OAAR,KAAoBL,aAAxB,EAAuC;QACrC,IAAIM,IAAI,GAAG7H,OAAO,CAAC8H,OAAnB;QACA,IAAIC,QAAQ,GAAGF,IAAI,EAAnB,CAFqC,CAEd;QACvB;QACA;QACA;QACA;;QAEAE,QAAQ,CAACC,IAAT,CAAc,UAAUC,YAAV,EAAwB;UACpC,IAAIjI,OAAO,CAAC4H,OAAR,KAAoBJ,OAApB,IAA+BxH,OAAO,CAAC4H,OAAR,KAAoBL,aAAvD,EAAsE;YACpE;YACA,IAAIW,QAAQ,GAAGlI,OAAf;YACAkI,QAAQ,CAACN,OAAT,GAAmBH,QAAnB;YACAS,QAAQ,CAACJ,OAAT,GAAmBG,YAAnB;UACD;QACF,CAPD,EAOG,UAAUzN,KAAV,EAAiB;UAClB,IAAIwF,OAAO,CAAC4H,OAAR,KAAoBJ,OAApB,IAA+BxH,OAAO,CAAC4H,OAAR,KAAoBL,aAAvD,EAAsE;YACpE;YACA,IAAIY,QAAQ,GAAGnI,OAAf;YACAmI,QAAQ,CAACP,OAAT,GAAmBF,QAAnB;YACAS,QAAQ,CAACL,OAAT,GAAmBtN,KAAnB;UACD;QACF,CAdD;;QAgBA,IAAIwF,OAAO,CAAC4H,OAAR,KAAoBL,aAAxB,EAAuC;UACrC;UACA;UACA,IAAIa,OAAO,GAAGpI,OAAd;UACAoI,OAAO,CAACR,OAAR,GAAkBJ,OAAlB;UACAY,OAAO,CAACN,OAAR,GAAkBC,QAAlB;QACD;MACF;;MAED,IAAI/H,OAAO,CAAC4H,OAAR,KAAoBH,QAAxB,EAAkC;QAChC,IAAIQ,YAAY,GAAGjI,OAAO,CAAC8H,OAA3B;QAEA;UACE,IAAIG,YAAY,KAAKrK,SAArB,EAAgC;YAC9BpD,KAAK,CAAC,+CAA+C,cAA/C,GAAgE,0DAAhE,GAA6H;YACnI,oCADM,GACiC,2BADjC,GAC+D,0DADhE,EAC4HyN,YAD5H,CAAL;UAED;QACF;QAED;UACE,IAAI,EAAE,aAAaA,YAAf,CAAJ,EAAkC;YAChCzN,KAAK,CAAC,+CAA+C,cAA/C,GAAgE,0DAAhE,GAA6H;YACnI,oCADM,GACiC,uBADlC,EAC2DyN,YAD3D,CAAL;UAED;QACF;QAED,OAAOA,YAAY,CAACI,OAApB;MACD,CAlBD,MAkBO;QACL,MAAMrI,OAAO,CAAC8H,OAAd;MACD;IACF;;IAED,SAASQ,IAAT,CAAcT,IAAd,EAAoB;MAClB,IAAI7H,OAAO,GAAG;QACZ;QACA4H,OAAO,EAAEL,aAFG;QAGZO,OAAO,EAAED;MAHG,CAAd;MAKA,IAAIU,QAAQ,GAAG;QACb7I,QAAQ,EAAExH,eADG;QAEb+H,QAAQ,EAAED,OAFG;QAGbG,KAAK,EAAEwH;MAHM,CAAf;MAMA;QACE;QACA,IAAIlF,YAAJ;QACA,IAAI+F,SAAJ,CAHF,CAGiB;;QAEf9L,MAAM,CAACwK,gBAAP,CAAwBqB,QAAxB,EAAkC;UAChC9F,YAAY,EAAE;YACZpB,YAAY,EAAE,IADF;YAEZ1D,GAAG,EAAE,YAAY;cACf,OAAO8E,YAAP;YACD,CAJW;YAKZ0E,GAAG,EAAE,UAAUsB,eAAV,EAA2B;cAC9BjO,KAAK,CAAC,sEAAsE,mEAAtE,GAA4I,uDAA7I,CAAL;cAEAiI,YAAY,GAAGgG,eAAf,CAH8B,CAGE;cAChC;;cAEA/L,MAAM,CAACgB,cAAP,CAAsB6K,QAAtB,EAAgC,cAAhC,EAAgD;gBAC9CtG,UAAU,EAAE;cADkC,CAAhD;YAGD;UAdW,CADkB;UAiBhCuG,SAAS,EAAE;YACTnH,YAAY,EAAE,IADL;YAET1D,GAAG,EAAE,YAAY;cACf,OAAO6K,SAAP;YACD,CAJQ;YAKTrB,GAAG,EAAE,UAAUuB,YAAV,EAAwB;cAC3BlO,KAAK,CAAC,mEAAmE,mEAAnE,GAAyI,uDAA1I,CAAL;cAEAgO,SAAS,GAAGE,YAAZ,CAH2B,CAGD;cAC1B;;cAEAhM,MAAM,CAACgB,cAAP,CAAsB6K,QAAtB,EAAgC,WAAhC,EAA6C;gBAC3CtG,UAAU,EAAE;cAD+B,CAA7C;YAGD;UAdQ;QAjBqB,CAAlC;MAkCD;MAED,OAAOsG,QAAP;IACD;;IAED,SAASI,UAAT,CAAoB9I,MAApB,EAA4B;MAC1B;QACE,IAAIA,MAAM,IAAI,IAAV,IAAkBA,MAAM,CAACH,QAAP,KAAoBzH,eAA1C,EAA2D;UACzDuC,KAAK,CAAC,iEAAiE,mDAAjE,GAAuH,wBAAxH,CAAL;QACD,CAFD,MAEO,IAAI,OAAOqF,MAAP,KAAkB,UAAtB,EAAkC;UACvCrF,KAAK,CAAC,yDAAD,EAA4DqF,MAAM,KAAK,IAAX,GAAkB,MAAlB,GAA2B,OAAOA,MAA9F,CAAL;QACD,CAFM,MAEA;UACL,IAAIA,MAAM,CAAC1F,MAAP,KAAkB,CAAlB,IAAuB0F,MAAM,CAAC1F,MAAP,KAAkB,CAA7C,EAAgD;YAC9CK,KAAK,CAAC,8EAAD,EAAiFqF,MAAM,CAAC1F,MAAP,KAAkB,CAAlB,GAAsB,0CAAtB,GAAmE,6CAApJ,CAAL;UACD;QACF;;QAED,IAAI0F,MAAM,IAAI,IAAd,EAAoB;UAClB,IAAIA,MAAM,CAAC4C,YAAP,IAAuB,IAAvB,IAA+B5C,MAAM,CAAC2I,SAAP,IAAoB,IAAvD,EAA6D;YAC3DhO,KAAK,CAAC,2EAA2E,8CAA5E,CAAL;UACD;QACF;MACF;MAED,IAAIoO,WAAW,GAAG;QAChBlJ,QAAQ,EAAE5H,sBADM;QAEhB+H,MAAM,EAAEA;MAFQ,CAAlB;MAKA;QACE,IAAIgJ,OAAJ;QACAnM,MAAM,CAACgB,cAAP,CAAsBkL,WAAtB,EAAmC,aAAnC,EAAkD;UAChD3G,UAAU,EAAE,KADoC;UAEhDZ,YAAY,EAAE,IAFkC;UAGhD1D,GAAG,EAAE,YAAY;YACf,OAAOkL,OAAP;UACD,CAL+C;UAMhD1B,GAAG,EAAE,UAAUpL,IAAV,EAAgB;YACnB8M,OAAO,GAAG9M,IAAV,CADmB,CACH;YAChB;YACA;YACA;YACA;YACA;YACA;;YAEA,IAAI,CAAC8D,MAAM,CAAC9D,IAAR,IAAgB,CAAC8D,MAAM,CAAC/D,WAA5B,EAAyC;cACvC+D,MAAM,CAAC/D,WAAP,GAAqBC,IAArB;YACD;UACF;QAlB+C,CAAlD;MAoBD;MAED,OAAO6M,WAAP;IACD;;IAED,IAAIE,sBAAJ;IAEA;MACEA,sBAAsB,GAAGxR,MAAM,CAACC,GAAP,CAAW,wBAAX,CAAzB;IACD;;IAED,SAASwR,kBAAT,CAA4BlK,IAA5B,EAAkC;MAChC,IAAI,OAAOA,IAAP,KAAgB,QAAhB,IAA4B,OAAOA,IAAP,KAAgB,UAAhD,EAA4D;QAC1D,OAAO,IAAP;MACD,CAH+B,CAG9B;;;MAGF,IAAIA,IAAI,KAAKpH,mBAAT,IAAgCoH,IAAI,KAAKlH,mBAAzC,IAAgEkC,kBAAhE,IAAuFgF,IAAI,KAAKnH,sBAAhG,IAA0HmH,IAAI,KAAK9G,mBAAnI,IAA0J8G,IAAI,KAAK7G,wBAAnK,IAA+L4B,kBAA/L,IAAsNiF,IAAI,KAAK1G,oBAA/N,IAAuPsB,cAAvP,IAA0QC,kBAA1Q,IAAiSC,uBAArS,EAA+T;QAC7T,OAAO,IAAP;MACD;;MAED,IAAI,OAAOkF,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,KAAK,IAAzC,EAA+C;QAC7C,IAAIA,IAAI,CAACa,QAAL,KAAkBxH,eAAlB,IAAqC2G,IAAI,CAACa,QAAL,KAAkBzH,eAAvD,IAA0E4G,IAAI,CAACa,QAAL,KAAkB9H,mBAA5F,IAAmHiH,IAAI,CAACa,QAAL,KAAkB7H,kBAArI,IAA2JgH,IAAI,CAACa,QAAL,KAAkB5H,sBAA7K,IAAuM;QAC3M;QACA;QACA;QACA+G,IAAI,CAACa,QAAL,KAAkBoJ,sBAJd,IAIwCjK,IAAI,CAACmK,WAAL,KAAqBpL,SAJjE,EAI4E;UAC1E,OAAO,IAAP;QACD;MACF;;MAED,OAAO,KAAP;IACD;;IAED,SAASqL,IAAT,CAAcpK,IAAd,EAAoBqK,OAApB,EAA6B;MAC3B;QACE,IAAI,CAACH,kBAAkB,CAAClK,IAAD,CAAvB,EAA+B;UAC7BrE,KAAK,CAAC,2DAA2D,cAA5D,EAA4EqE,IAAI,KAAK,IAAT,GAAgB,MAAhB,GAAyB,OAAOA,IAA5G,CAAL;QACD;MACF;MAED,IAAI+J,WAAW,GAAG;QAChBlJ,QAAQ,EAAEzH,eADM;QAEhB4G,IAAI,EAAEA,IAFU;QAGhBqK,OAAO,EAAEA,OAAO,KAAKtL,SAAZ,GAAwB,IAAxB,GAA+BsL;MAHxB,CAAlB;MAMA;QACE,IAAIL,OAAJ;QACAnM,MAAM,CAACgB,cAAP,CAAsBkL,WAAtB,EAAmC,aAAnC,EAAkD;UAChD3G,UAAU,EAAE,KADoC;UAEhDZ,YAAY,EAAE,IAFkC;UAGhD1D,GAAG,EAAE,YAAY;YACf,OAAOkL,OAAP;UACD,CAL+C;UAMhD1B,GAAG,EAAE,UAAUpL,IAAV,EAAgB;YACnB8M,OAAO,GAAG9M,IAAV,CADmB,CACH;YAChB;YACA;YACA;YACA;YACA;YACA;;YAEA,IAAI,CAAC8C,IAAI,CAAC9C,IAAN,IAAc,CAAC8C,IAAI,CAAC/C,WAAxB,EAAqC;cACnC+C,IAAI,CAAC/C,WAAL,GAAmBC,IAAnB;YACD;UACF;QAlB+C,CAAlD;MAoBD;MAED,OAAO6M,WAAP;IACD;;IAED,SAASO,iBAAT,GAA6B;MAC3B,IAAIC,UAAU,GAAG1Q,sBAAsB,CAACC,OAAxC;MAEA;QACE,IAAIyQ,UAAU,KAAK,IAAnB,EAAyB;UACvB5O,KAAK,CAAC,kHAAkH,kCAAlH,GAAuJ,wFAAvJ,GAAkP,+CAAlP,GAAoS,iEAApS,GAAwW,kGAAzW,CAAL;QACD;MACF,CAP0B,CAOzB;MACF;MACA;;MAGA,OAAO4O,UAAP;IACD;;IACD,SAASC,UAAT,CAAoBC,OAApB,EAA6B;MAC3B,IAAIF,UAAU,GAAGD,iBAAiB,EAAlC;MAEA;QACE;QACA,IAAIG,OAAO,CAAC1J,QAAR,KAAqBhC,SAAzB,EAAoC;UAClC,IAAI2L,WAAW,GAAGD,OAAO,CAAC1J,QAA1B,CADkC,CACE;UACpC;;UAEA,IAAI2J,WAAW,CAAC3C,QAAZ,KAAyB0C,OAA7B,EAAsC;YACpC9O,KAAK,CAAC,wFAAwF,sFAAzF,CAAL;UACD,CAFD,MAEO,IAAI+O,WAAW,CAAC5C,QAAZ,KAAyB2C,OAA7B,EAAsC;YAC3C9O,KAAK,CAAC,4DAA4D,mDAA7D,CAAL;UACD;QACF;MACF;MAED,OAAO4O,UAAU,CAACC,UAAX,CAAsBC,OAAtB,CAAP;IACD;;IACD,SAASE,QAAT,CAAkBC,YAAlB,EAAgC;MAC9B,IAAIL,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACI,QAAX,CAAoBC,YAApB,CAAP;IACD;;IACD,SAASC,UAAT,CAAoBC,OAApB,EAA6BC,UAA7B,EAAyC1J,IAAzC,EAA+C;MAC7C,IAAIkJ,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACM,UAAX,CAAsBC,OAAtB,EAA+BC,UAA/B,EAA2C1J,IAA3C,CAAP;IACD;;IACD,SAAS2J,MAAT,CAAgBC,YAAhB,EAA8B;MAC5B,IAAIV,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACS,MAAX,CAAkBC,YAAlB,CAAP;IACD;;IACD,SAASC,SAAT,CAAmBC,MAAnB,EAA2BC,IAA3B,EAAiC;MAC/B,IAAIb,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACW,SAAX,CAAqBC,MAArB,EAA6BC,IAA7B,CAAP;IACD;;IACD,SAASC,kBAAT,CAA4BF,MAA5B,EAAoCC,IAApC,EAA0C;MACxC,IAAIb,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACc,kBAAX,CAA8BF,MAA9B,EAAsCC,IAAtC,CAAP;IACD;;IACD,SAASE,eAAT,CAAyBH,MAAzB,EAAiCC,IAAjC,EAAuC;MACrC,IAAIb,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACe,eAAX,CAA2BH,MAA3B,EAAmCC,IAAnC,CAAP;IACD;;IACD,SAASG,WAAT,CAAqBhO,QAArB,EAA+B6N,IAA/B,EAAqC;MACnC,IAAIb,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACgB,WAAX,CAAuBhO,QAAvB,EAAiC6N,IAAjC,CAAP;IACD;;IACD,SAASI,OAAT,CAAiBL,MAAjB,EAAyBC,IAAzB,EAA+B;MAC7B,IAAIb,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACiB,OAAX,CAAmBL,MAAnB,EAA2BC,IAA3B,CAAP;IACD;;IACD,SAASK,mBAAT,CAA6B/J,GAA7B,EAAkCyJ,MAAlC,EAA0CC,IAA1C,EAAgD;MAC9C,IAAIb,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACkB,mBAAX,CAA+B/J,GAA/B,EAAoCyJ,MAApC,EAA4CC,IAA5C,CAAP;IACD;;IACD,SAASM,aAAT,CAAuB7L,KAAvB,EAA8B8L,WAA9B,EAA2C;MACzC;QACE,IAAIpB,UAAU,GAAGD,iBAAiB,EAAlC;QACA,OAAOC,UAAU,CAACmB,aAAX,CAAyB7L,KAAzB,EAAgC8L,WAAhC,CAAP;MACD;IACF;;IACD,SAASC,aAAT,GAAyB;MACvB,IAAIrB,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACqB,aAAX,EAAP;IACD;;IACD,SAASC,gBAAT,CAA0BhM,KAA1B,EAAiC;MAC/B,IAAI0K,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACsB,gBAAX,CAA4BhM,KAA5B,CAAP;IACD;;IACD,SAASiM,KAAT,GAAiB;MACf,IAAIvB,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACuB,KAAX,EAAP;IACD;;IACD,SAASC,oBAAT,CAA8BC,SAA9B,EAAyCC,WAAzC,EAAsDC,iBAAtD,EAAyE;MACvE,IAAI3B,UAAU,GAAGD,iBAAiB,EAAlC;MACA,OAAOC,UAAU,CAACwB,oBAAX,CAAgCC,SAAhC,EAA2CC,WAA3C,EAAwDC,iBAAxD,CAAP;IACD,CA/nDa,CAioDd;IACA;IACA;IACA;;;IACA,IAAIC,aAAa,GAAG,CAApB;IACA,IAAIC,OAAJ;IACA,IAAIC,QAAJ;IACA,IAAIC,QAAJ;IACA,IAAIC,SAAJ;IACA,IAAIC,SAAJ;IACA,IAAIC,kBAAJ;IACA,IAAIC,YAAJ;;IAEA,SAASC,WAAT,GAAuB,CAAE;;IAEzBA,WAAW,CAACC,kBAAZ,GAAiC,IAAjC;;IACA,SAASC,WAAT,GAAuB;MACrB;QACE,IAAIV,aAAa,KAAK,CAAtB,EAAyB;UACvB;UACAC,OAAO,GAAG3P,OAAO,CAACqQ,GAAlB;UACAT,QAAQ,GAAG5P,OAAO,CAACmC,IAAnB;UACA0N,QAAQ,GAAG7P,OAAO,CAACvB,IAAnB;UACAqR,SAAS,GAAG9P,OAAO,CAACd,KAApB;UACA6Q,SAAS,GAAG/P,OAAO,CAACsQ,KAApB;UACAN,kBAAkB,GAAGhQ,OAAO,CAACuQ,cAA7B;UACAN,YAAY,GAAGjQ,OAAO,CAACwQ,QAAvB,CARuB,CAQU;;UAEjC,IAAIhP,KAAK,GAAG;YACVuE,YAAY,EAAE,IADJ;YAEVY,UAAU,EAAE,IAFF;YAGVvD,KAAK,EAAE8M,WAHG;YAIVtJ,QAAQ,EAAE;UAJA,CAAZ,CAVuB,CAepB;;UAEHxF,MAAM,CAACwK,gBAAP,CAAwB5L,OAAxB,EAAiC;YAC/BmC,IAAI,EAAEX,KADyB;YAE/B6O,GAAG,EAAE7O,KAF0B;YAG/B/C,IAAI,EAAE+C,KAHyB;YAI/BtC,KAAK,EAAEsC,KAJwB;YAK/B8O,KAAK,EAAE9O,KALwB;YAM/B+O,cAAc,EAAE/O,KANe;YAO/BgP,QAAQ,EAAEhP;UAPqB,CAAjC;UASA;QACD;;QAEDkO,aAAa;MACd;IACF;;IACD,SAASe,YAAT,GAAwB;MACtB;QACEf,aAAa;;QAEb,IAAIA,aAAa,KAAK,CAAtB,EAAyB;UACvB;UACA,IAAIlO,KAAK,GAAG;YACVuE,YAAY,EAAE,IADJ;YAEVY,UAAU,EAAE,IAFF;YAGVC,QAAQ,EAAE;UAHA,CAAZ,CAFuB,CAMpB;;UAEHxF,MAAM,CAACwK,gBAAP,CAAwB5L,OAAxB,EAAiC;YAC/BqQ,GAAG,EAAElP,MAAM,CAAC,EAAD,EAAKK,KAAL,EAAY;cACrB4B,KAAK,EAAEuM;YADc,CAAZ,CADoB;YAI/BxN,IAAI,EAAEhB,MAAM,CAAC,EAAD,EAAKK,KAAL,EAAY;cACtB4B,KAAK,EAAEwM;YADe,CAAZ,CAJmB;YAO/BnR,IAAI,EAAE0C,MAAM,CAAC,EAAD,EAAKK,KAAL,EAAY;cACtB4B,KAAK,EAAEyM;YADe,CAAZ,CAPmB;YAU/B3Q,KAAK,EAAEiC,MAAM,CAAC,EAAD,EAAKK,KAAL,EAAY;cACvB4B,KAAK,EAAE0M;YADgB,CAAZ,CAVkB;YAa/BQ,KAAK,EAAEnP,MAAM,CAAC,EAAD,EAAKK,KAAL,EAAY;cACvB4B,KAAK,EAAE2M;YADgB,CAAZ,CAbkB;YAgB/BQ,cAAc,EAAEpP,MAAM,CAAC,EAAD,EAAKK,KAAL,EAAY;cAChC4B,KAAK,EAAE4M;YADyB,CAAZ,CAhBS;YAmB/BQ,QAAQ,EAAErP,MAAM,CAAC,EAAD,EAAKK,KAAL,EAAY;cAC1B4B,KAAK,EAAE6M;YADmB,CAAZ;UAnBe,CAAjC;UAuBA;QACD;;QAED,IAAIP,aAAa,GAAG,CAApB,EAAuB;UACrBxQ,KAAK,CAAC,oCAAoC,+CAArC,CAAL;QACD;MACF;IACF;;IAED,IAAIwR,wBAAwB,GAAGlS,oBAAoB,CAACpB,sBAApD;IACA,IAAIuT,MAAJ;;IACA,SAASC,6BAAT,CAAuCnQ,IAAvC,EAA6C6F,MAA7C,EAAqDuK,OAArD,EAA8D;MAC5D;QACE,IAAIF,MAAM,KAAKrO,SAAf,EAA0B;UACxB;UACA,IAAI;YACF,MAAMzG,KAAK,EAAX;UACD,CAFD,CAEE,OAAOiJ,CAAP,EAAU;YACV,IAAIsD,KAAK,GAAGtD,CAAC,CAAC/G,KAAF,CAAQ+S,IAAR,GAAe1I,KAAf,CAAqB,cAArB,CAAZ;YACAuI,MAAM,GAAGvI,KAAK,IAAIA,KAAK,CAAC,CAAD,CAAd,IAAqB,EAA9B;UACD;QACF,CATH,CASI;;;QAGF,OAAO,OAAOuI,MAAP,GAAgBlQ,IAAvB;MACD;IACF;;IACD,IAAIsQ,OAAO,GAAG,KAAd;IACA,IAAIC,mBAAJ;IAEA;MACE,IAAIC,eAAe,GAAG,OAAOC,OAAP,KAAmB,UAAnB,GAAgCA,OAAhC,GAA0CC,GAAhE;MACAH,mBAAmB,GAAG,IAAIC,eAAJ,EAAtB;IACD;;IAED,SAASG,4BAAT,CAAsCC,EAAtC,EAA0CC,SAA1C,EAAqD;MACnD;MACA,IAAK,CAACD,EAAD,IAAON,OAAZ,EAAqB;QACnB,OAAO,EAAP;MACD;;MAED;QACE,IAAIQ,KAAK,GAAGP,mBAAmB,CAAC3O,GAApB,CAAwBgP,EAAxB,CAAZ;;QAEA,IAAIE,KAAK,KAAKjP,SAAd,EAAyB;UACvB,OAAOiP,KAAP;QACD;MACF;MAED,IAAIC,OAAJ;MACAT,OAAO,GAAG,IAAV;MACA,IAAIU,yBAAyB,GAAG5V,KAAK,CAAC6V,iBAAtC,CAhBmD,CAgBM;;MAEzD7V,KAAK,CAAC6V,iBAAN,GAA0BpP,SAA1B;MACA,IAAIqP,kBAAJ;MAEA;QACEA,kBAAkB,GAAGjB,wBAAwB,CAACrT,OAA9C,CADF,CACyD;QACvD;;QAEAqT,wBAAwB,CAACrT,OAAzB,GAAmC,IAAnC;QACA+S,WAAW;MACZ;;MAED,IAAI;QACF;QACA,IAAIkB,SAAJ,EAAe;UACb;UACA,IAAIM,IAAI,GAAG,YAAY;YACrB,MAAM/V,KAAK,EAAX;UACD,CAFD,CAFa,CAIV;;;UAGHuF,MAAM,CAACgB,cAAP,CAAsBwP,IAAI,CAAC/R,SAA3B,EAAsC,OAAtC,EAA+C;YAC7CgM,GAAG,EAAE,YAAY;cACf;cACA;cACA,MAAMhQ,KAAK,EAAX;YACD;UAL4C,CAA/C;;UAQA,IAAI,OAAOgW,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,CAACP,SAA3C,EAAsD;YACpD;YACA;YACA,IAAI;cACFO,OAAO,CAACP,SAAR,CAAkBM,IAAlB,EAAwB,EAAxB;YACD,CAFD,CAEE,OAAO9M,CAAP,EAAU;cACV0M,OAAO,GAAG1M,CAAV;YACD;;YAED+M,OAAO,CAACP,SAAR,CAAkBD,EAAlB,EAAsB,EAAtB,EAA0BO,IAA1B;UACD,CAVD,MAUO;YACL,IAAI;cACFA,IAAI,CAAC7R,IAAL;YACD,CAFD,CAEE,OAAO+E,CAAP,EAAU;cACV0M,OAAO,GAAG1M,CAAV;YACD;;YAEDuM,EAAE,CAACtR,IAAH,CAAQ6R,IAAI,CAAC/R,SAAb;UACD;QACF,CAlCD,MAkCO;UACL,IAAI;YACF,MAAMhE,KAAK,EAAX;UACD,CAFD,CAEE,OAAOiJ,CAAP,EAAU;YACV0M,OAAO,GAAG1M,CAAV;UACD;;UAEDuM,EAAE;QACH;MACF,CA7CD,CA6CE,OAAOS,MAAP,EAAe;QACf;QACA,IAAIA,MAAM,IAAIN,OAAV,IAAqB,OAAOM,MAAM,CAAC/T,KAAd,KAAwB,QAAjD,EAA2D;UACzD;UACA;UACA,IAAIgU,WAAW,GAAGD,MAAM,CAAC/T,KAAP,CAAaiU,KAAb,CAAmB,IAAnB,CAAlB;UACA,IAAIC,YAAY,GAAGT,OAAO,CAACzT,KAAR,CAAciU,KAAd,CAAoB,IAApB,CAAnB;UACA,IAAIE,CAAC,GAAGH,WAAW,CAAClT,MAAZ,GAAqB,CAA7B;UACA,IAAIwK,CAAC,GAAG4I,YAAY,CAACpT,MAAb,GAAsB,CAA9B;;UAEA,OAAOqT,CAAC,IAAI,CAAL,IAAU7I,CAAC,IAAI,CAAf,IAAoB0I,WAAW,CAACG,CAAD,CAAX,KAAmBD,YAAY,CAAC5I,CAAD,CAA1D,EAA+D;YAC7D;YACA;YACA;YACA;YACA;YACA;YACAA,CAAC;UACF;;UAED,OAAO6I,CAAC,IAAI,CAAL,IAAU7I,CAAC,IAAI,CAAtB,EAAyB6I,CAAC,IAAI7I,CAAC,EAA/B,EAAmC;YACjC;YACA;YACA,IAAI0I,WAAW,CAACG,CAAD,CAAX,KAAmBD,YAAY,CAAC5I,CAAD,CAAnC,EAAwC;cACtC;cACA;cACA;cACA;cACA;cACA,IAAI6I,CAAC,KAAK,CAAN,IAAW7I,CAAC,KAAK,CAArB,EAAwB;gBACtB,GAAG;kBACD6I,CAAC;kBACD7I,CAAC,GAFA,CAEI;kBACL;;kBAEA,IAAIA,CAAC,GAAG,CAAJ,IAAS0I,WAAW,CAACG,CAAD,CAAX,KAAmBD,YAAY,CAAC5I,CAAD,CAA5C,EAAiD;oBAC/C;oBACA,IAAI8I,MAAM,GAAG,OAAOJ,WAAW,CAACG,CAAD,CAAX,CAAe/J,OAAf,CAAuB,UAAvB,EAAmC,MAAnC,CAApB,CAF+C,CAEiB;oBAChE;oBACA;;;oBAGA,IAAIkJ,EAAE,CAAC7Q,WAAH,IAAkB2R,MAAM,CAACC,QAAP,CAAgB,aAAhB,CAAtB,EAAsD;sBACpDD,MAAM,GAAGA,MAAM,CAAChK,OAAP,CAAe,aAAf,EAA8BkJ,EAAE,CAAC7Q,WAAjC,CAAT;oBACD;;oBAED;sBACE,IAAI,OAAO6Q,EAAP,KAAc,UAAlB,EAA8B;wBAC5BL,mBAAmB,CAACnF,GAApB,CAAwBwF,EAAxB,EAA4Bc,MAA5B;sBACD;oBACF,CAf8C,CAe7C;;oBAGF,OAAOA,MAAP;kBACD;gBACF,CAzBD,QAyBSD,CAAC,IAAI,CAAL,IAAU7I,CAAC,IAAI,CAzBxB;cA0BD;;cAED;YACD;UACF;QACF;MACF,CA3GD,SA2GU;QACR0H,OAAO,GAAG,KAAV;QAEA;UACEL,wBAAwB,CAACrT,OAAzB,GAAmCsU,kBAAnC;UACAlB,YAAY;QACb;QAED5U,KAAK,CAAC6V,iBAAN,GAA0BD,yBAA1B;MACD,CAjJkD,CAiJjD;;;MAGF,IAAIhR,IAAI,GAAG4Q,EAAE,GAAGA,EAAE,CAAC7Q,WAAH,IAAkB6Q,EAAE,CAAC5Q,IAAxB,GAA+B,EAA5C;MACA,IAAI4R,cAAc,GAAG5R,IAAI,GAAGmQ,6BAA6B,CAACnQ,IAAD,CAAhC,GAAyC,EAAlE;MAEA;QACE,IAAI,OAAO4Q,EAAP,KAAc,UAAlB,EAA8B;UAC5BL,mBAAmB,CAACnF,GAApB,CAAwBwF,EAAxB,EAA4BgB,cAA5B;QACD;MACF;MAED,OAAOA,cAAP;IACD;;IACD,SAASC,8BAAT,CAAwCjB,EAAxC,EAA4C/K,MAA5C,EAAoDuK,OAApD,EAA6D;MAC3D;QACE,OAAOO,4BAA4B,CAACC,EAAD,EAAK,KAAL,CAAnC;MACD;IACF;;IAED,SAASkB,eAAT,CAAyBhR,SAAzB,EAAoC;MAClC,IAAI1B,SAAS,GAAG0B,SAAS,CAAC1B,SAA1B;MACA,OAAO,CAAC,EAAEA,SAAS,IAAIA,SAAS,CAAC+B,gBAAzB,CAAR;IACD;;IAED,SAAS4Q,oCAAT,CAA8CjP,IAA9C,EAAoD+C,MAApD,EAA4DuK,OAA5D,EAAqE;MAEnE,IAAItN,IAAI,IAAI,IAAZ,EAAkB;QAChB,OAAO,EAAP;MACD;;MAED,IAAI,OAAOA,IAAP,KAAgB,UAApB,EAAgC;QAC9B;UACE,OAAO6N,4BAA4B,CAAC7N,IAAD,EAAOgP,eAAe,CAAChP,IAAD,CAAtB,CAAnC;QACD;MACF;;MAED,IAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;QAC5B,OAAOqN,6BAA6B,CAACrN,IAAD,CAApC;MACD;;MAED,QAAQA,IAAR;QACE,KAAK9G,mBAAL;UACE,OAAOmU,6BAA6B,CAAC,UAAD,CAApC;;QAEF,KAAKlU,wBAAL;UACE,OAAOkU,6BAA6B,CAAC,cAAD,CAApC;MALJ;;MAQA,IAAI,OAAOrN,IAAP,KAAgB,QAApB,EAA8B;QAC5B,QAAQA,IAAI,CAACa,QAAb;UACE,KAAK5H,sBAAL;YACE,OAAO8V,8BAA8B,CAAC/O,IAAI,CAACgB,MAAN,CAArC;;UAEF,KAAK5H,eAAL;YACE;YACA,OAAO6V,oCAAoC,CAACjP,IAAI,CAACA,IAAN,EAAY+C,MAAZ,EAAoBuK,OAApB,CAA3C;;UAEF,KAAKjU,eAAL;YACE;cACE,IAAI6H,aAAa,GAAGlB,IAApB;cACA,IAAImB,OAAO,GAAGD,aAAa,CAACE,QAA5B;cACA,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAzB;;cAEA,IAAI;gBACF;gBACA,OAAO2N,oCAAoC,CAAC5N,IAAI,CAACF,OAAD,CAAL,EAAgB4B,MAAhB,EAAwBuK,OAAxB,CAA3C;cACD,CAHD,CAGE,OAAO/L,CAAP,EAAU,CAAE;YACf;QAlBL;MAoBD;;MAED,OAAO,EAAP;IACD;;IAED,IAAI2N,kBAAkB,GAAG,EAAzB;IACA,IAAIC,wBAAwB,GAAGlU,oBAAoB,CAACZ,sBAApD;;IAEA,SAAS+U,6BAAT,CAAuCnM,OAAvC,EAAgD;MAC9C;QACE,IAAIA,OAAJ,EAAa;UACX,IAAID,KAAK,GAAGC,OAAO,CAACC,MAApB;UACA,IAAI1I,KAAK,GAAGyU,oCAAoC,CAAChM,OAAO,CAACjD,IAAT,EAAeiD,OAAO,CAACiB,OAAvB,EAAgClB,KAAK,GAAGA,KAAK,CAAChD,IAAT,GAAgB,IAArD,CAAhD;UACAmP,wBAAwB,CAAC5U,kBAAzB,CAA4CC,KAA5C;QACD,CAJD,MAIO;UACL2U,wBAAwB,CAAC5U,kBAAzB,CAA4C,IAA5C;QACD;MACF;IACF;;IAED,SAAS8U,cAAT,CAAwBC,SAAxB,EAAmCC,MAAnC,EAA2CC,QAA3C,EAAqDxS,aAArD,EAAoEiG,OAApE,EAA6E;MAC3E;QACE;QACA,IAAIwM,GAAG,GAAGpT,QAAQ,CAACG,IAAT,CAAckT,IAAd,CAAmBzQ,cAAnB,CAAV;;QAEA,KAAK,IAAI0Q,YAAT,IAAyBL,SAAzB,EAAoC;UAClC,IAAIG,GAAG,CAACH,SAAD,EAAYK,YAAZ,CAAP,EAAkC;YAChC,IAAIC,OAAO,GAAG,KAAK,CAAnB,CADgC,CACV;YACtB;YACA;;YAEA,IAAI;cACF;cACA;cACA,IAAI,OAAON,SAAS,CAACK,YAAD,CAAhB,KAAmC,UAAvC,EAAmD;gBACjD;gBACA,IAAIE,GAAG,GAAGvX,KAAK,CAAC,CAAC0E,aAAa,IAAI,aAAlB,IAAmC,IAAnC,GAA0CwS,QAA1C,GAAqD,SAArD,GAAiEG,YAAjE,GAAgF,gBAAhF,GAAmG,8EAAnG,GAAoL,OAAOL,SAAS,CAACK,YAAD,CAApM,GAAqN,IAArN,GAA4N,+FAA7N,CAAf;gBACAE,GAAG,CAAC3S,IAAJ,GAAW,qBAAX;gBACA,MAAM2S,GAAN;cACD;;cAEDD,OAAO,GAAGN,SAAS,CAACK,YAAD,CAAT,CAAwBJ,MAAxB,EAAgCI,YAAhC,EAA8C3S,aAA9C,EAA6DwS,QAA7D,EAAuE,IAAvE,EAA6E,8CAA7E,CAAV;YACD,CAXD,CAWE,OAAOM,EAAP,EAAW;cACXF,OAAO,GAAGE,EAAV;YACD;;YAED,IAAIF,OAAO,IAAI,EAAEA,OAAO,YAAYtX,KAArB,CAAf,EAA4C;cAC1C8W,6BAA6B,CAACnM,OAAD,CAA7B;cAEAtH,KAAK,CAAC,iCAAiC,qCAAjC,GAAyE,+DAAzE,GAA2I,iEAA3I,GAA+M,gEAA/M,GAAkR,iCAAnR,EAAsTqB,aAAa,IAAI,aAAvU,EAAsVwS,QAAtV,EAAgWG,YAAhW,EAA8W,OAAOC,OAArX,CAAL;cAEAR,6BAA6B,CAAC,IAAD,CAA7B;YACD;;YAED,IAAIQ,OAAO,YAAYtX,KAAnB,IAA4B,EAAEsX,OAAO,CAACG,OAAR,IAAmBb,kBAArB,CAAhC,EAA0E;cACxE;cACA;cACAA,kBAAkB,CAACU,OAAO,CAACG,OAAT,CAAlB,GAAsC,IAAtC;cACAX,6BAA6B,CAACnM,OAAD,CAA7B;cAEAtH,KAAK,CAAC,oBAAD,EAAuB6T,QAAvB,EAAiCI,OAAO,CAACG,OAAzC,CAAL;cAEAX,6BAA6B,CAAC,IAAD,CAA7B;YACD;UACF;QACF;MACF;IACF;;IAED,SAASY,+BAAT,CAAyC/M,OAAzC,EAAkD;MAChD;QACE,IAAIA,OAAJ,EAAa;UACX,IAAID,KAAK,GAAGC,OAAO,CAACC,MAApB;UACA,IAAI1I,KAAK,GAAGyU,oCAAoC,CAAChM,OAAO,CAACjD,IAAT,EAAeiD,OAAO,CAACiB,OAAvB,EAAgClB,KAAK,GAAGA,KAAK,CAAChD,IAAT,GAAgB,IAArD,CAAhD;UACAzF,kBAAkB,CAACC,KAAD,CAAlB;QACD,CAJD,MAIO;UACLD,kBAAkB,CAAC,IAAD,CAAlB;QACD;MACF;IACF;;IAED,IAAI0V,6BAAJ;IAEA;MACEA,6BAA6B,GAAG,KAAhC;IACD;;IAED,SAASC,2BAAT,GAAuC;MACrC,IAAI9V,iBAAiB,CAACN,OAAtB,EAA+B;QAC7B,IAAIoD,IAAI,GAAGyD,wBAAwB,CAACvG,iBAAiB,CAACN,OAAlB,CAA0BkG,IAA3B,CAAnC;;QAEA,IAAI9C,IAAJ,EAAU;UACR,OAAO,qCAAqCA,IAArC,GAA4C,IAAnD;QACD;MACF;;MAED,OAAO,EAAP;IACD;;IAED,SAASiT,0BAAT,CAAoCpN,MAApC,EAA4C;MAC1C,IAAIA,MAAM,KAAKhE,SAAf,EAA0B;QACxB,IAAIqR,QAAQ,GAAGrN,MAAM,CAACqN,QAAP,CAAgBxL,OAAhB,CAAwB,WAAxB,EAAqC,EAArC,CAAf;QACA,IAAIyL,UAAU,GAAGtN,MAAM,CAACsN,UAAxB;QACA,OAAO,4BAA4BD,QAA5B,GAAuC,GAAvC,GAA6CC,UAA7C,GAA0D,GAAjE;MACD;;MAED,OAAO,EAAP;IACD;;IAED,SAASC,kCAAT,CAA4CC,YAA5C,EAA0D;MACxD,IAAIA,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAKxR,SAA9C,EAAyD;QACvD,OAAOoR,0BAA0B,CAACI,YAAY,CAAC3O,QAAd,CAAjC;MACD;;MAED,OAAO,EAAP;IACD;IACD;AACA;AACA;AACA;AACA;;;IAGA,IAAI4O,qBAAqB,GAAG,EAA5B;;IAEA,SAASC,4BAAT,CAAsCC,UAAtC,EAAkD;MAChD,IAAI9R,IAAI,GAAGsR,2BAA2B,EAAtC;;MAEA,IAAI,CAACtR,IAAL,EAAW;QACT,IAAI+R,UAAU,GAAG,OAAOD,UAAP,KAAsB,QAAtB,GAAiCA,UAAjC,GAA8CA,UAAU,CAACzT,WAAX,IAA0ByT,UAAU,CAACxT,IAApG;;QAEA,IAAIyT,UAAJ,EAAgB;UACd/R,IAAI,GAAG,gDAAgD+R,UAAhD,GAA6D,IAApE;QACD;MACF;;MAED,OAAO/R,IAAP;IACD;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASgS,mBAAT,CAA6B3N,OAA7B,EAAsCyN,UAAtC,EAAkD;MAChD,IAAI,CAACzN,OAAO,CAACE,MAAT,IAAmBF,OAAO,CAACE,MAAR,CAAe0N,SAAlC,IAA+C5N,OAAO,CAACxB,GAAR,IAAe,IAAlE,EAAwE;QACtE;MACD;;MAEDwB,OAAO,CAACE,MAAR,CAAe0N,SAAf,GAA2B,IAA3B;MACA,IAAIC,yBAAyB,GAAGL,4BAA4B,CAACC,UAAD,CAA5D;;MAEA,IAAIF,qBAAqB,CAACM,yBAAD,CAAzB,EAAsD;QACpD;MACD;;MAEDN,qBAAqB,CAACM,yBAAD,CAArB,GAAmD,IAAnD,CAZgD,CAYS;MACzD;MACA;;MAEA,IAAIC,UAAU,GAAG,EAAjB;;MAEA,IAAI9N,OAAO,IAAIA,OAAO,CAACC,MAAnB,IAA6BD,OAAO,CAACC,MAAR,KAAmB9I,iBAAiB,CAACN,OAAtE,EAA+E;QAC7E;QACAiX,UAAU,GAAG,iCAAiCpQ,wBAAwB,CAACsC,OAAO,CAACC,MAAR,CAAelD,IAAhB,CAAzD,GAAiF,GAA9F;MACD;;MAED;QACEgQ,+BAA+B,CAAC/M,OAAD,CAA/B;QAEAtH,KAAK,CAAC,0DAA0D,sEAA3D,EAAmImV,yBAAnI,EAA8JC,UAA9J,CAAL;QAEAf,+BAA+B,CAAC,IAAD,CAA/B;MACD;IACF;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASgB,iBAAT,CAA2BC,IAA3B,EAAiCP,UAAjC,EAA6C;MAC3C,IAAI,OAAOO,IAAP,KAAgB,QAApB,EAA8B;QAC5B;MACD;;MAED,IAAIvR,OAAO,CAACuR,IAAD,CAAX,EAAmB;QACjB,KAAK,IAAItN,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsN,IAAI,CAAC3V,MAAzB,EAAiCqI,CAAC,EAAlC,EAAsC;UACpC,IAAIqC,KAAK,GAAGiL,IAAI,CAACtN,CAAD,CAAhB;;UAEA,IAAIS,cAAc,CAAC4B,KAAD,CAAlB,EAA2B;YACzB4K,mBAAmB,CAAC5K,KAAD,EAAQ0K,UAAR,CAAnB;UACD;QACF;MACF,CARD,MAQO,IAAItM,cAAc,CAAC6M,IAAD,CAAlB,EAA0B;QAC/B;QACA,IAAIA,IAAI,CAAC9N,MAAT,EAAiB;UACf8N,IAAI,CAAC9N,MAAL,CAAY0N,SAAZ,GAAwB,IAAxB;QACD;MACF,CALM,MAKA,IAAII,IAAJ,EAAU;QACf,IAAI7K,UAAU,GAAG1M,aAAa,CAACuX,IAAD,CAA9B;;QAEA,IAAI,OAAO7K,UAAP,KAAsB,UAA1B,EAAsC;UACpC;UACA;UACA,IAAIA,UAAU,KAAK6K,IAAI,CAAC3K,OAAxB,EAAiC;YAC/B,IAAI9M,QAAQ,GAAG4M,UAAU,CAAC5J,IAAX,CAAgByU,IAAhB,CAAf;YACA,IAAI1K,IAAJ;;YAEA,OAAO,CAAC,CAACA,IAAI,GAAG/M,QAAQ,CAACiN,IAAT,EAAR,EAAyBC,IAAjC,EAAuC;cACrC,IAAItC,cAAc,CAACmC,IAAI,CAAC1G,KAAN,CAAlB,EAAgC;gBAC9B+Q,mBAAmB,CAACrK,IAAI,CAAC1G,KAAN,EAAa6Q,UAAb,CAAnB;cACD;YACF;UACF;QACF;MACF;IACF;IACD;AACA;AACA;AACA;AACA;AACA;;;IAGA,SAASQ,iBAAT,CAA2BjO,OAA3B,EAAoC;MAClC;QACE,IAAIjD,IAAI,GAAGiD,OAAO,CAACjD,IAAnB;;QAEA,IAAIA,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAKjB,SAA1B,IAAuC,OAAOiB,IAAP,KAAgB,QAA3D,EAAqE;UACnE;QACD;;QAED,IAAI2J,SAAJ;;QAEA,IAAI,OAAO3J,IAAP,KAAgB,UAApB,EAAgC;UAC9B2J,SAAS,GAAG3J,IAAI,CAAC2J,SAAjB;QACD,CAFD,MAEO,IAAI,OAAO3J,IAAP,KAAgB,QAAhB,KAA6BA,IAAI,CAACa,QAAL,KAAkB5H,sBAAlB,IAA4C;QACpF;QACA+G,IAAI,CAACa,QAAL,KAAkBzH,eAFP,CAAJ,EAE6B;UAClCuQ,SAAS,GAAG3J,IAAI,CAAC2J,SAAjB;QACD,CAJM,MAIA;UACL;QACD;;QAED,IAAIA,SAAJ,EAAe;UACb;UACA,IAAIzM,IAAI,GAAGyD,wBAAwB,CAACX,IAAD,CAAnC;UACAqP,cAAc,CAAC1F,SAAD,EAAY1G,OAAO,CAAChF,KAApB,EAA2B,MAA3B,EAAmCf,IAAnC,EAAyC+F,OAAzC,CAAd;QACD,CAJD,MAIO,IAAIjD,IAAI,CAACmR,SAAL,KAAmBpS,SAAnB,IAAgC,CAACkR,6BAArC,EAAoE;UACzEA,6BAA6B,GAAG,IAAhC,CADyE,CACnC;;UAEtC,IAAImB,KAAK,GAAGzQ,wBAAwB,CAACX,IAAD,CAApC;;UAEArE,KAAK,CAAC,qGAAD,EAAwGyV,KAAK,IAAI,SAAjH,CAAL;QACD;;QAED,IAAI,OAAOpR,IAAI,CAACqR,eAAZ,KAAgC,UAAhC,IAA8C,CAACrR,IAAI,CAACqR,eAAL,CAAqBC,oBAAxE,EAA8F;UAC5F3V,KAAK,CAAC,+DAA+D,kEAAhE,CAAL;QACD;MACF;IACF;IACD;AACA;AACA;AACA;;;IAGA,SAAS4V,qBAAT,CAA+BC,QAA/B,EAAyC;MACvC;QACE,IAAI5K,IAAI,GAAG/I,MAAM,CAAC+I,IAAP,CAAY4K,QAAQ,CAACvT,KAArB,CAAX;;QAEA,KAAK,IAAI0F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiD,IAAI,CAACtL,MAAzB,EAAiCqI,CAAC,EAAlC,EAAsC;UACpC,IAAIlC,GAAG,GAAGmF,IAAI,CAACjD,CAAD,CAAd;;UAEA,IAAIlC,GAAG,KAAK,UAAR,IAAsBA,GAAG,KAAK,KAAlC,EAAyC;YACvCuO,+BAA+B,CAACwB,QAAD,CAA/B;YAEA7V,KAAK,CAAC,qDAAqD,0DAAtD,EAAkH8F,GAAlH,CAAL;YAEAuO,+BAA+B,CAAC,IAAD,CAA/B;YACA;UACD;QACF;;QAED,IAAIwB,QAAQ,CAAC9P,GAAT,KAAiB,IAArB,EAA2B;UACzBsO,+BAA+B,CAACwB,QAAD,CAA/B;UAEA7V,KAAK,CAAC,uDAAD,CAAL;UAEAqU,+BAA+B,CAAC,IAAD,CAA/B;QACD;MACF;IACF;;IACD,SAASyB,2BAAT,CAAqCzR,IAArC,EAA2C/B,KAA3C,EAAkDsF,QAAlD,EAA4D;MAC1D,IAAImO,SAAS,GAAGxH,kBAAkB,CAAClK,IAAD,CAAlC,CAD0D,CAChB;MAC1C;;MAEA,IAAI,CAAC0R,SAAL,EAAgB;QACd,IAAI9S,IAAI,GAAG,EAAX;;QAEA,IAAIoB,IAAI,KAAKjB,SAAT,IAAsB,OAAOiB,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,KAAK,IAArC,IAA6CnC,MAAM,CAAC+I,IAAP,CAAY5G,IAAZ,EAAkB1E,MAAlB,KAA6B,CAApG,EAAuG;UACrGsD,IAAI,IAAI,+DAA+D,wEAAvE;QACD;;QAED,IAAI+S,UAAU,GAAGrB,kCAAkC,CAACrS,KAAD,CAAnD;;QAEA,IAAI0T,UAAJ,EAAgB;UACd/S,IAAI,IAAI+S,UAAR;QACD,CAFD,MAEO;UACL/S,IAAI,IAAIsR,2BAA2B,EAAnC;QACD;;QAED,IAAI0B,UAAJ;;QAEA,IAAI5R,IAAI,KAAK,IAAb,EAAmB;UACjB4R,UAAU,GAAG,MAAb;QACD,CAFD,MAEO,IAAIlS,OAAO,CAACM,IAAD,CAAX,EAAmB;UACxB4R,UAAU,GAAG,OAAb;QACD,CAFM,MAEA,IAAI5R,IAAI,KAAKjB,SAAT,IAAsBiB,IAAI,CAACa,QAAL,KAAkBrI,kBAA5C,EAAgE;UACrEoZ,UAAU,GAAG,OAAOjR,wBAAwB,CAACX,IAAI,CAACA,IAAN,CAAxB,IAAuC,SAA9C,IAA2D,KAAxE;UACApB,IAAI,GAAG,oEAAP;QACD,CAHM,MAGA;UACLgT,UAAU,GAAG,OAAO5R,IAApB;QACD;;QAED;UACErE,KAAK,CAAC,oEAAoE,0DAApE,GAAiI,4BAAlI,EAAgKiW,UAAhK,EAA4KhT,IAA5K,CAAL;QACD;MACF;;MAED,IAAIqE,OAAO,GAAGK,aAAa,CAAC/G,KAAd,CAAoB,IAApB,EAA0BlB,SAA1B,CAAd,CArC0D,CAqCN;MACpD;;MAEA,IAAI4H,OAAO,IAAI,IAAf,EAAqB;QACnB,OAAOA,OAAP;MACD,CA1CyD,CA0CxD;MACF;MACA;MACA;MACA;;;MAGA,IAAIyO,SAAJ,EAAe;QACb,KAAK,IAAI/N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtI,SAAS,CAACC,MAA9B,EAAsCqI,CAAC,EAAvC,EAA2C;UACzCqN,iBAAiB,CAAC3V,SAAS,CAACsI,CAAD,CAAV,EAAe3D,IAAf,CAAjB;QACD;MACF;;MAED,IAAIA,IAAI,KAAKpH,mBAAb,EAAkC;QAChC2Y,qBAAqB,CAACtO,OAAD,CAArB;MACD,CAFD,MAEO;QACLiO,iBAAiB,CAACjO,OAAD,CAAjB;MACD;;MAED,OAAOA,OAAP;IACD;;IACD,IAAI4O,mCAAmC,GAAG,KAA1C;;IACA,SAASC,2BAAT,CAAqC9R,IAArC,EAA2C;MACzC,IAAI+R,gBAAgB,GAAGN,2BAA2B,CAAC/B,IAA5B,CAAiC,IAAjC,EAAuC1P,IAAvC,CAAvB;MACA+R,gBAAgB,CAAC/R,IAAjB,GAAwBA,IAAxB;MAEA;QACE,IAAI,CAAC6R,mCAAL,EAA0C;UACxCA,mCAAmC,GAAG,IAAtC;UAEA3W,IAAI,CAAC,gEAAgE,6CAAhE,GAAgH,gDAAjH,CAAJ;QACD,CALH,CAKI;;;QAGF2C,MAAM,CAACgB,cAAP,CAAsBkT,gBAAtB,EAAwC,MAAxC,EAAgD;UAC9C3O,UAAU,EAAE,KADkC;UAE9CtE,GAAG,EAAE,YAAY;YACf5D,IAAI,CAAC,2DAA2D,qCAA5D,CAAJ;YAEA2C,MAAM,CAACgB,cAAP,CAAsB,IAAtB,EAA4B,MAA5B,EAAoC;cAClCgB,KAAK,EAAEG;YAD2B,CAApC;YAGA,OAAOA,IAAP;UACD;QAT6C,CAAhD;MAWD;MAED,OAAO+R,gBAAP;IACD;;IACD,SAASC,0BAAT,CAAoC/O,OAApC,EAA6ChF,KAA7C,EAAoDsF,QAApD,EAA8D;MAC5D,IAAIS,UAAU,GAAGG,YAAY,CAAC5H,KAAb,CAAmB,IAAnB,EAAyBlB,SAAzB,CAAjB;;MAEA,KAAK,IAAIsI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtI,SAAS,CAACC,MAA9B,EAAsCqI,CAAC,EAAvC,EAA2C;QACzCqN,iBAAiB,CAAC3V,SAAS,CAACsI,CAAD,CAAV,EAAeK,UAAU,CAAChE,IAA1B,CAAjB;MACD;;MAEDkR,iBAAiB,CAAClN,UAAD,CAAjB;MACA,OAAOA,UAAP;IACD;;IAED,SAASiO,eAAT,CAAyBC,KAAzB,EAAgCC,OAAhC,EAAyC;MACvC,IAAIC,cAAc,GAAGrY,uBAAuB,CAACC,UAA7C;MACAD,uBAAuB,CAACC,UAAxB,GAAqC,EAArC;MACA,IAAIqY,iBAAiB,GAAGtY,uBAAuB,CAACC,UAAhD;MAEA;QACED,uBAAuB,CAACC,UAAxB,CAAmCsY,cAAnC,GAAoD,IAAIC,GAAJ,EAApD;MACD;;MAED,IAAI;QACFL,KAAK;MACN,CAFD,SAEU;QACRnY,uBAAuB,CAACC,UAAxB,GAAqCoY,cAArC;QAEA;UACE,IAAIA,cAAc,KAAK,IAAnB,IAA2BC,iBAAiB,CAACC,cAAjD,EAAiE;YAC/D,IAAIE,kBAAkB,GAAGH,iBAAiB,CAACC,cAAlB,CAAiCG,IAA1D;;YAEA,IAAID,kBAAkB,GAAG,EAAzB,EAA6B;cAC3BtX,IAAI,CAAC,gEAAgE,mFAAhE,GAAsJ,yDAAvJ,CAAJ;YACD;;YAEDmX,iBAAiB,CAACC,cAAlB,CAAiCI,KAAjC;UACD;QACF;MACF;IACF;;IAED,IAAIC,0BAA0B,GAAG,KAAjC;IACA,IAAIC,eAAe,GAAG,IAAtB;;IACA,SAASC,WAAT,CAAqBC,IAArB,EAA2B;MACzB,IAAIF,eAAe,KAAK,IAAxB,EAA8B;QAC5B,IAAI;UACF;UACA;UACA,IAAIG,aAAa,GAAG,CAAC,YAAYC,IAAI,CAACC,MAAL,EAAb,EAA4BC,KAA5B,CAAkC,CAAlC,EAAqC,CAArC,CAApB;UACA,IAAIC,WAAW,GAAGC,MAAM,IAAIA,MAAM,CAACL,aAAD,CAAlC,CAJE,CAIiD;UACnD;;UAEAH,eAAe,GAAGO,WAAW,CAAC3W,IAAZ,CAAiB4W,MAAjB,EAAyB,QAAzB,EAAmCC,YAArD;QACD,CARD,CAQE,OAAOC,IAAP,EAAa;UACb;UACA;UACA;UACAV,eAAe,GAAG,UAAUrV,QAAV,EAAoB;YACpC;cACE,IAAIoV,0BAA0B,KAAK,KAAnC,EAA0C;gBACxCA,0BAA0B,GAAG,IAA7B;;gBAEA,IAAI,OAAOY,cAAP,KAA0B,WAA9B,EAA2C;kBACzC5X,KAAK,CAAC,iEAAiE,+DAAjE,GAAmI,mEAAnI,GAAyM,gCAA1M,CAAL;gBACD;cACF;YACF;YAED,IAAI6X,OAAO,GAAG,IAAID,cAAJ,EAAd;YACAC,OAAO,CAACC,KAAR,CAAcC,SAAd,GAA0BnW,QAA1B;YACAiW,OAAO,CAACG,KAAR,CAAcC,WAAd,CAA0B7U,SAA1B;UACD,CAdD;QAeD;MACF;;MAED,OAAO6T,eAAe,CAACE,IAAD,CAAtB;IACD;;IAED,IAAIe,aAAa,GAAG,CAApB;IACA,IAAIC,iBAAiB,GAAG,KAAxB;;IACA,SAASC,GAAT,CAAaxW,QAAb,EAAuB;MACrB;QACE;QACA;QACA,IAAIyW,iBAAiB,GAAGH,aAAxB;QACAA,aAAa;;QAEb,IAAI5Z,oBAAoB,CAACH,OAArB,KAAiC,IAArC,EAA2C;UACzC;UACA;UACAG,oBAAoB,CAACH,OAArB,GAA+B,EAA/B;QACD;;QAED,IAAIma,oBAAoB,GAAGha,oBAAoB,CAACC,gBAAhD;QACA,IAAI8M,MAAJ;;QAEA,IAAI;UACF;UACA;UACA;UACA;UACA/M,oBAAoB,CAACC,gBAArB,GAAwC,IAAxC;UACA8M,MAAM,GAAGzJ,QAAQ,EAAjB,CANE,CAMmB;UACrB;UACA;;UAEA,IAAI,CAAC0W,oBAAD,IAAyBha,oBAAoB,CAACE,uBAAlD,EAA2E;YACzE,IAAI+Z,KAAK,GAAGja,oBAAoB,CAACH,OAAjC;;YAEA,IAAIoa,KAAK,KAAK,IAAd,EAAoB;cAClBja,oBAAoB,CAACE,uBAArB,GAA+C,KAA/C;cACAga,aAAa,CAACD,KAAD,CAAb;YACD;UACF;QACF,CAlBD,CAkBE,OAAOvY,KAAP,EAAc;UACdyY,WAAW,CAACJ,iBAAD,CAAX;UACA,MAAMrY,KAAN;QACD,CArBD,SAqBU;UACR1B,oBAAoB,CAACC,gBAArB,GAAwC+Z,oBAAxC;QACD;;QAED,IAAIjN,MAAM,KAAK,IAAX,IAAmB,OAAOA,MAAP,KAAkB,QAArC,IAAiD,OAAOA,MAAM,CAACmC,IAAd,KAAuB,UAA5E,EAAwF;UACtF,IAAIkL,cAAc,GAAGrN,MAArB,CADsF,CACzD;UAC7B;;UAEA,IAAIsN,UAAU,GAAG,KAAjB;UACA,IAAIpL,QAAQ,GAAG;YACbC,IAAI,EAAE,UAAUoL,OAAV,EAAmBC,MAAnB,EAA2B;cAC/BF,UAAU,GAAG,IAAb;cACAD,cAAc,CAAClL,IAAf,CAAoB,UAAUsL,WAAV,EAAuB;gBACzCL,WAAW,CAACJ,iBAAD,CAAX;;gBAEA,IAAIH,aAAa,KAAK,CAAtB,EAAyB;kBACvB;kBACA;kBACAa,4BAA4B,CAACD,WAAD,EAAcF,OAAd,EAAuBC,MAAvB,CAA5B;gBACD,CAJD,MAIO;kBACLD,OAAO,CAACE,WAAD,CAAP;gBACD;cACF,CAVD,EAUG,UAAU9Y,KAAV,EAAiB;gBAClB;gBACAyY,WAAW,CAACJ,iBAAD,CAAX;gBACAQ,MAAM,CAAC7Y,KAAD,CAAN;cACD,CAdD;YAeD;UAlBY,CAAf;UAqBA;YACE,IAAI,CAACmY,iBAAD,IAAsB,OAAOa,OAAP,KAAmB,WAA7C,EAA0D;cACxD;cACAA,OAAO,CAACJ,OAAR,GAAkBpL,IAAlB,CAAuB,YAAY,CAAE,CAArC,EAAuCA,IAAvC,CAA4C,YAAY;gBACtD,IAAI,CAACmL,UAAL,EAAiB;kBACfR,iBAAiB,GAAG,IAApB;kBAEAnY,KAAK,CAAC,oDAAoD,mDAApD,GAA0G,mDAA1G,GAAgK,UAAhK,GAA6K,0CAA9K,CAAL;gBACD;cACF,CAND;YAOD;UACF;UAED,OAAOuN,QAAP;QACD,CAxCD,MAwCO;UACL,IAAIuL,WAAW,GAAGzN,MAAlB,CADK,CACqB;UAC1B;;UAEAoN,WAAW,CAACJ,iBAAD,CAAX;;UAEA,IAAIH,aAAa,KAAK,CAAtB,EAAyB;YACvB;YACA,IAAIe,MAAM,GAAG3a,oBAAoB,CAACH,OAAlC;;YAEA,IAAI8a,MAAM,KAAK,IAAf,EAAqB;cACnBT,aAAa,CAACS,MAAD,CAAb;cACA3a,oBAAoB,CAACH,OAArB,GAA+B,IAA/B;YACD,CAPsB,CAOrB;YACF;;;YAGA,IAAI+a,SAAS,GAAG;cACd1L,IAAI,EAAE,UAAUoL,OAAV,EAAmBC,MAAnB,EAA2B;gBAC/B;gBACA;gBACA;gBACA,IAAIva,oBAAoB,CAACH,OAArB,KAAiC,IAArC,EAA2C;kBACzC;kBACAG,oBAAoB,CAACH,OAArB,GAA+B,EAA/B;kBACA4a,4BAA4B,CAACD,WAAD,EAAcF,OAAd,EAAuBC,MAAvB,CAA5B;gBACD,CAJD,MAIO;kBACLD,OAAO,CAACE,WAAD,CAAP;gBACD;cACF;YAZa,CAAhB;YAcA,OAAOI,SAAP;UACD,CA1BD,MA0BO;YACL;YACA;YACA,IAAIC,UAAU,GAAG;cACf3L,IAAI,EAAE,UAAUoL,OAAV,EAAmBC,MAAnB,EAA2B;gBAC/BD,OAAO,CAACE,WAAD,CAAP;cACD;YAHc,CAAjB;YAKA,OAAOK,UAAP;UACD;QACF;MACF;IACF;;IAED,SAASV,WAAT,CAAqBJ,iBAArB,EAAwC;MACtC;QACE,IAAIA,iBAAiB,KAAKH,aAAa,GAAG,CAA1C,EAA6C;UAC3ClY,KAAK,CAAC,sEAAsE,iEAAvE,CAAL;QACD;;QAEDkY,aAAa,GAAGG,iBAAhB;MACD;IACF;;IAED,SAASU,4BAAT,CAAsCD,WAAtC,EAAmDF,OAAnD,EAA4DC,MAA5D,EAAoE;MAClE;QACE,IAAIN,KAAK,GAAGja,oBAAoB,CAACH,OAAjC;;QAEA,IAAIoa,KAAK,KAAK,IAAd,EAAoB;UAClB,IAAI;YACFC,aAAa,CAACD,KAAD,CAAb;YACArB,WAAW,CAAC,YAAY;cACtB,IAAIqB,KAAK,CAAC5Y,MAAN,KAAiB,CAArB,EAAwB;gBACtB;gBACArB,oBAAoB,CAACH,OAArB,GAA+B,IAA/B;gBACAya,OAAO,CAACE,WAAD,CAAP;cACD,CAJD,MAIO;gBACL;gBACAC,4BAA4B,CAACD,WAAD,EAAcF,OAAd,EAAuBC,MAAvB,CAA5B;cACD;YACF,CATU,CAAX;UAUD,CAZD,CAYE,OAAO7Y,KAAP,EAAc;YACd6Y,MAAM,CAAC7Y,KAAD,CAAN;UACD;QACF,CAhBD,MAgBO;UACL4Y,OAAO,CAACE,WAAD,CAAP;QACD;MACF;IACF;;IAED,IAAIM,UAAU,GAAG,KAAjB;;IAEA,SAASZ,aAAT,CAAuBD,KAAvB,EAA8B;MAC5B;QACE,IAAI,CAACa,UAAL,EAAiB;UACf;UACAA,UAAU,GAAG,IAAb;UACA,IAAIpR,CAAC,GAAG,CAAR;;UAEA,IAAI;YACF,OAAOA,CAAC,GAAGuQ,KAAK,CAAC5Y,MAAjB,EAAyBqI,CAAC,EAA1B,EAA8B;cAC5B,IAAIpG,QAAQ,GAAG2W,KAAK,CAACvQ,CAAD,CAApB;;cAEA,GAAG;gBACDpG,QAAQ,GAAGA,QAAQ,CAAC,IAAD,CAAnB;cACD,CAFD,QAESA,QAAQ,KAAK,IAFtB;YAGD;;YAED2W,KAAK,CAAC5Y,MAAN,GAAe,CAAf;UACD,CAVD,CAUE,OAAOK,KAAP,EAAc;YACd;YACAuY,KAAK,GAAGA,KAAK,CAAChB,KAAN,CAAYvP,CAAC,GAAG,CAAhB,CAAR;YACA,MAAMhI,KAAN;UACD,CAdD,SAcU;YACRoZ,UAAU,GAAG,KAAb;UACD;QACF;MACF;IACF;;IAED,IAAIC,eAAe,GAAIvD,2BAAvB;IACA,IAAIwD,cAAc,GAAIjD,0BAAtB;IACA,IAAIkD,aAAa,GAAIpD,2BAArB;IACA,IAAIqD,QAAQ,GAAG;MACblZ,GAAG,EAAE6K,WADQ;MAEbsO,OAAO,EAAEhO,eAFI;MAGbH,KAAK,EAAEC,aAHM;MAIbK,OAAO,EAAEA,OAJI;MAKb8N,IAAI,EAAE7N;IALO,CAAf;IAQA8N,OAAO,CAACH,QAAR,GAAmBA,QAAnB;IACAG,OAAO,CAACtX,SAAR,GAAoBA,SAApB;IACAsX,OAAO,CAACC,QAAR,GAAmB3c,mBAAnB;IACA0c,OAAO,CAACE,QAAR,GAAmB1c,mBAAnB;IACAwc,OAAO,CAACnW,aAAR,GAAwBA,aAAxB;IACAmW,OAAO,CAACG,UAAR,GAAqB5c,sBAArB;IACAyc,OAAO,CAACI,QAAR,GAAmBxc,mBAAnB;IACAoc,OAAO,CAACK,kDAAR,GAA6D1a,oBAA7D;IACAqa,OAAO,CAACnR,YAAR,GAAuB8Q,cAAvB;IACAK,OAAO,CAAC7N,aAAR,GAAwBA,aAAxB;IACA6N,OAAO,CAAChS,aAAR,GAAwB0R,eAAxB;IACAM,OAAO,CAACJ,aAAR,GAAwBA,aAAxB;IACAI,OAAO,CAAChW,SAAR,GAAoBA,SAApB;IACAgW,OAAO,CAACxL,UAAR,GAAqBA,UAArB;IACAwL,OAAO,CAAClR,cAAR,GAAyBA,cAAzB;IACAkR,OAAO,CAAC7L,IAAR,GAAeA,IAAf;IACA6L,OAAO,CAAClL,IAAR,GAAeA,IAAf;IACAkL,OAAO,CAACrD,eAAR,GAA0BA,eAA1B;IACAqD,OAAO,CAACM,YAAR,GAAuB7B,GAAvB;IACAuB,OAAO,CAAC/J,WAAR,GAAsBA,WAAtB;IACA+J,OAAO,CAAC9K,UAAR,GAAqBA,UAArB;IACA8K,OAAO,CAAC5J,aAAR,GAAwBA,aAAxB;IACA4J,OAAO,CAACzJ,gBAAR,GAA2BA,gBAA3B;IACAyJ,OAAO,CAACpK,SAAR,GAAoBA,SAApB;IACAoK,OAAO,CAACxJ,KAAR,GAAgBA,KAAhB;IACAwJ,OAAO,CAAC7J,mBAAR,GAA8BA,mBAA9B;IACA6J,OAAO,CAACjK,kBAAR,GAA6BA,kBAA7B;IACAiK,OAAO,CAAChK,eAAR,GAA0BA,eAA1B;IACAgK,OAAO,CAAC9J,OAAR,GAAkBA,OAAlB;IACA8J,OAAO,CAACzK,UAAR,GAAqBA,UAArB;IACAyK,OAAO,CAACtK,MAAR,GAAiBA,MAAjB;IACAsK,OAAO,CAAC3K,QAAR,GAAmBA,QAAnB;IACA2K,OAAO,CAACvJ,oBAAR,GAA+BA,oBAA/B;IACAuJ,OAAO,CAAC1J,aAAR,GAAwBA,aAAxB;IACA0J,OAAO,CAACO,OAAR,GAAkBtd,YAAlB;IACU;;IACV,IACE,OAAOH,8BAAP,KAA0C,WAA1C,IACA,OAAOA,8BAA8B,CAAC0d,0BAAtC,KACE,UAHJ,EAIE;MACA1d,8BAA8B,CAAC0d,0BAA/B,CAA0D,IAAIxd,KAAJ,EAA1D;IACD;EAEE,CApqFD;AAqqFD"}, "metadata": {}, "sourceType": "script"}