{"ast": null, "code": "import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = function (url, data) {\n  let headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  return new Promise((resolve, reject) => {\n    const xhr = new XMLHttpRequest();\n    xhr.addEventListener('load', _ref => {\n      let {\n        target\n      } = _ref;\n      const responseStatus = new EmailJSResponseStatus(target);\n\n      if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n        resolve(responseStatus);\n      } else {\n        reject(responseStatus);\n      }\n    });\n    xhr.addEventListener('error', _ref2 => {\n      let {\n        target\n      } = _ref2;\n      reject(new EmailJSResponseStatus(target));\n    });\n    xhr.open('POST', store._origin + url, true);\n    Object.keys(headers).forEach(key => {\n      xhr.setRequestHeader(key, headers[key]);\n    });\n    xhr.send(data);\n  });\n};", "map": {"version": 3, "names": ["EmailJSResponseStatus", "store", "sendPost", "url", "data", "headers", "Promise", "resolve", "reject", "xhr", "XMLHttpRequest", "addEventListener", "target", "responseStatus", "status", "text", "open", "_origin", "Object", "keys", "for<PERSON>ach", "key", "setRequestHeader", "send"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@emailjs/browser/es/api/sendPost.js"], "sourcesContent": ["import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = (url, data, headers = {}) => {\n    return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest();\n        xhr.addEventListener('load', ({ target }) => {\n            const responseStatus = new EmailJSResponseStatus(target);\n            if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n                resolve(responseStatus);\n            }\n            else {\n                reject(responseStatus);\n            }\n        });\n        xhr.addEventListener('error', ({ target }) => {\n            reject(new EmailJSResponseStatus(target));\n        });\n        xhr.open('POST', store._origin + url, true);\n        Object.keys(headers).forEach((key) => {\n            xhr.setRequestHeader(key, headers[key]);\n        });\n        xhr.send(data);\n    });\n};\n"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,iCAAtC;AACA,SAASC,KAAT,QAAsB,gBAAtB;AACA,OAAO,MAAMC,QAAQ,GAAG,UAACC,GAAD,EAAMC,IAAN,EAA6B;EAAA,IAAjBC,OAAiB,uEAAP,EAAO;EACjD,OAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;IACpC,MAAMC,GAAG,GAAG,IAAIC,cAAJ,EAAZ;IACAD,GAAG,CAACE,gBAAJ,CAAqB,MAArB,EAA6B,QAAgB;MAAA,IAAf;QAAEC;MAAF,CAAe;MACzC,MAAMC,cAAc,GAAG,IAAIb,qBAAJ,CAA0BY,MAA1B,CAAvB;;MACA,IAAIC,cAAc,CAACC,MAAf,KAA0B,GAA1B,IAAiCD,cAAc,CAACE,IAAf,KAAwB,IAA7D,EAAmE;QAC/DR,OAAO,CAACM,cAAD,CAAP;MACH,CAFD,MAGK;QACDL,MAAM,CAACK,cAAD,CAAN;MACH;IACJ,CARD;IASAJ,GAAG,CAACE,gBAAJ,CAAqB,OAArB,EAA8B,SAAgB;MAAA,IAAf;QAAEC;MAAF,CAAe;MAC1CJ,MAAM,CAAC,IAAIR,qBAAJ,CAA0BY,MAA1B,CAAD,CAAN;IACH,CAFD;IAGAH,GAAG,CAACO,IAAJ,CAAS,MAAT,EAAiBf,KAAK,CAACgB,OAAN,GAAgBd,GAAjC,EAAsC,IAAtC;IACAe,MAAM,CAACC,IAAP,CAAYd,OAAZ,EAAqBe,OAArB,CAA8BC,GAAD,IAAS;MAClCZ,GAAG,CAACa,gBAAJ,CAAqBD,GAArB,EAA0BhB,OAAO,CAACgB,GAAD,CAAjC;IACH,CAFD;IAGAZ,GAAG,CAACc,IAAJ,CAASnB,IAAT;EACH,CAnBM,CAAP;AAoBH,CArBM"}, "metadata": {}, "sourceType": "module"}