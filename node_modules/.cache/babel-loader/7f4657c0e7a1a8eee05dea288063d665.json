{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar elementWidth_1 = require(\"./elementWidth\");\n\nfunction notEnoughChildren(state) {\n  var slidesToShow = state.slidesToShow;\n  return state.totalItems < slidesToShow;\n}\n\nfunction getInitialState(state, props) {\n  var flexBisis,\n      domLoaded = state.domLoaded,\n      slidesToShow = state.slidesToShow,\n      containerWidth = state.containerWidth,\n      itemWidth = state.itemWidth,\n      deviceType = props.deviceType,\n      responsive = props.responsive,\n      ssr = props.ssr,\n      partialVisbile = props.partialVisbile,\n      partialVisible = props.partialVisible,\n      domFullyLoaded = Boolean(domLoaded && slidesToShow && containerWidth && itemWidth);\n  ssr && deviceType && !domFullyLoaded && (flexBisis = elementWidth_1.getWidthFromDeviceType(deviceType, responsive));\n  var shouldRenderOnSSR = Boolean(ssr && deviceType && !domFullyLoaded && flexBisis);\n  return {\n    shouldRenderOnSSR: shouldRenderOnSSR,\n    flexBisis: flexBisis,\n    domFullyLoaded: domFullyLoaded,\n    partialVisibilityGutter: elementWidth_1.getPartialVisibilityGutter(responsive, partialVisbile || partialVisible, deviceType, state.deviceType),\n    shouldRenderAtAll: shouldRenderOnSSR || domFullyLoaded\n  };\n}\n\nfunction getIfSlideIsVisbile(index, state) {\n  var currentSlide = state.currentSlide,\n      slidesToShow = state.slidesToShow;\n  return currentSlide <= index && index < currentSlide + slidesToShow;\n}\n\nfunction getTransformForCenterMode(state, props, transformPlaceHolder) {\n  var transform = transformPlaceHolder || state.transform;\n  return !props.infinite && 0 === state.currentSlide || notEnoughChildren(state) ? transform : transform + state.itemWidth / 2;\n}\n\nfunction isInLeftEnd(_a) {\n  return !(0 < _a.currentSlide);\n}\n\nfunction isInRightEnd(_a) {\n  var currentSlide = _a.currentSlide,\n      totalItems = _a.totalItems;\n  return !(currentSlide + _a.slidesToShow < totalItems);\n}\n\nfunction getTransformForPartialVsibile(state, partialVisibilityGutter, props, transformPlaceHolder) {\n  void 0 === partialVisibilityGutter && (partialVisibilityGutter = 0);\n  var currentSlide = state.currentSlide,\n      slidesToShow = state.slidesToShow,\n      isRightEndReach = isInRightEnd(state),\n      shouldRemoveRightGutter = !props.infinite && isRightEndReach,\n      baseTransform = transformPlaceHolder || state.transform;\n  if (notEnoughChildren(state)) return baseTransform;\n  var transform = baseTransform + currentSlide * partialVisibilityGutter;\n  return shouldRemoveRightGutter ? transform + (state.containerWidth - (state.itemWidth - partialVisibilityGutter) * slidesToShow) : transform;\n}\n\nfunction parsePosition(props, position) {\n  return props.rtl ? -1 * position : position;\n}\n\nfunction getTransform(state, props, transformPlaceHolder) {\n  var partialVisbile = props.partialVisbile,\n      partialVisible = props.partialVisible,\n      responsive = props.responsive,\n      deviceType = props.deviceType,\n      centerMode = props.centerMode,\n      transform = transformPlaceHolder || state.transform,\n      partialVisibilityGutter = elementWidth_1.getPartialVisibilityGutter(responsive, partialVisbile || partialVisible, deviceType, state.deviceType);\n  return parsePosition(props, partialVisible || partialVisbile ? getTransformForPartialVsibile(state, partialVisibilityGutter, props, transformPlaceHolder) : centerMode ? getTransformForCenterMode(state, props, transformPlaceHolder) : transform);\n}\n\nfunction getSlidesToSlide(state, props) {\n  var domLoaded = state.domLoaded,\n      slidesToShow = state.slidesToShow,\n      containerWidth = state.containerWidth,\n      itemWidth = state.itemWidth,\n      deviceType = props.deviceType,\n      responsive = props.responsive,\n      slidesToScroll = props.slidesToSlide || 1,\n      domFullyLoaded = Boolean(domLoaded && slidesToShow && containerWidth && itemWidth);\n  return props.ssr && props.deviceType && !domFullyLoaded && Object.keys(responsive).forEach(function (device) {\n    var slidesToSlide = responsive[device].slidesToSlide;\n    deviceType === device && slidesToSlide && (slidesToScroll = slidesToSlide);\n  }), domFullyLoaded && Object.keys(responsive).forEach(function (item) {\n    var _a = responsive[item],\n        breakpoint = _a.breakpoint,\n        slidesToSlide = _a.slidesToSlide,\n        max = breakpoint.max,\n        min = breakpoint.min;\n    slidesToSlide && window.innerWidth >= min && window.innerWidth <= max && (slidesToScroll = slidesToSlide);\n  }), slidesToScroll;\n}\n\nexports.notEnoughChildren = notEnoughChildren, exports.getInitialState = getInitialState, exports.getIfSlideIsVisbile = getIfSlideIsVisbile, exports.getTransformForCenterMode = getTransformForCenterMode, exports.isInLeftEnd = isInLeftEnd, exports.isInRightEnd = isInRightEnd, exports.getTransformForPartialVsibile = getTransformForPartialVsibile, exports.parsePosition = parsePosition, exports.getTransform = getTransform, exports.getSlidesToSlide = getSlidesToSlide;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "elementWidth_1", "require", "notEnoughChildren", "state", "slidesToShow", "totalItems", "getInitialState", "props", "flexBisis", "domLoaded", "containerWidth", "itemWidth", "deviceType", "responsive", "ssr", "partialVisbile", "partialVisible", "dom<PERSON>ullyLoaded", "Boolean", "getWidthFromDeviceType", "shouldRenderOnSSR", "partialVisibilityGutter", "getPartialVisibilityGutter", "shouldRenderAtAll", "getIfSlideIsVisbile", "index", "currentSlide", "getTransformForCenterMode", "transformPlaceHolder", "transform", "infinite", "isInLeftEnd", "_a", "isInRightEnd", "getTransformForPartialVsibile", "isRightEndReach", "shouldRemoveRightGutter", "baseTransform", "parsePosition", "position", "rtl", "getTransform", "centerMode", "getSlidesToSlide", "slidesToScroll", "slidesToSlide", "keys", "for<PERSON>ach", "device", "item", "breakpoint", "max", "min", "window", "innerWidth"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/common.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var elementWidth_1=require(\"./elementWidth\");function notEnoughChildren(state){var slidesToShow=state.slidesToShow;return state.totalItems<slidesToShow}function getInitialState(state,props){var flexBisis,domLoaded=state.domLoaded,slidesToShow=state.slidesToShow,containerWidth=state.containerWidth,itemWidth=state.itemWidth,deviceType=props.deviceType,responsive=props.responsive,ssr=props.ssr,partialVisbile=props.partialVisbile,partialVisible=props.partialVisible,domFullyLoaded=Boolean(domLoaded&&slidesToShow&&containerWidth&&itemWidth);ssr&&deviceType&&!domFullyLoaded&&(flexBisis=elementWidth_1.getWidthFromDeviceType(deviceType,responsive));var shouldRenderOnSSR=Boolean(ssr&&deviceType&&!domFullyLoaded&&flexBisis);return{shouldRenderOnSSR:shouldRenderOnSSR,flexBisis:flexBisis,domFullyLoaded:domFullyLoaded,partialVisibilityGutter:elementWidth_1.getPartialVisibilityGutter(responsive,partialVisbile||partialVisible,deviceType,state.deviceType),shouldRenderAtAll:shouldRenderOnSSR||domFullyLoaded}}function getIfSlideIsVisbile(index,state){var currentSlide=state.currentSlide,slidesToShow=state.slidesToShow;return currentSlide<=index&&index<currentSlide+slidesToShow}function getTransformForCenterMode(state,props,transformPlaceHolder){var transform=transformPlaceHolder||state.transform;return!props.infinite&&0===state.currentSlide||notEnoughChildren(state)?transform:transform+state.itemWidth/2}function isInLeftEnd(_a){return!(0<_a.currentSlide)}function isInRightEnd(_a){var currentSlide=_a.currentSlide,totalItems=_a.totalItems;return!(currentSlide+_a.slidesToShow<totalItems)}function getTransformForPartialVsibile(state,partialVisibilityGutter,props,transformPlaceHolder){void 0===partialVisibilityGutter&&(partialVisibilityGutter=0);var currentSlide=state.currentSlide,slidesToShow=state.slidesToShow,isRightEndReach=isInRightEnd(state),shouldRemoveRightGutter=!props.infinite&&isRightEndReach,baseTransform=transformPlaceHolder||state.transform;if(notEnoughChildren(state))return baseTransform;var transform=baseTransform+currentSlide*partialVisibilityGutter;return shouldRemoveRightGutter?transform+(state.containerWidth-(state.itemWidth-partialVisibilityGutter)*slidesToShow):transform}function parsePosition(props,position){return props.rtl?-1*position:position}function getTransform(state,props,transformPlaceHolder){var partialVisbile=props.partialVisbile,partialVisible=props.partialVisible,responsive=props.responsive,deviceType=props.deviceType,centerMode=props.centerMode,transform=transformPlaceHolder||state.transform,partialVisibilityGutter=elementWidth_1.getPartialVisibilityGutter(responsive,partialVisbile||partialVisible,deviceType,state.deviceType);return parsePosition(props,partialVisible||partialVisbile?getTransformForPartialVsibile(state,partialVisibilityGutter,props,transformPlaceHolder):centerMode?getTransformForCenterMode(state,props,transformPlaceHolder):transform)}function getSlidesToSlide(state,props){var domLoaded=state.domLoaded,slidesToShow=state.slidesToShow,containerWidth=state.containerWidth,itemWidth=state.itemWidth,deviceType=props.deviceType,responsive=props.responsive,slidesToScroll=props.slidesToSlide||1,domFullyLoaded=Boolean(domLoaded&&slidesToShow&&containerWidth&&itemWidth);return props.ssr&&props.deviceType&&!domFullyLoaded&&Object.keys(responsive).forEach(function(device){var slidesToSlide=responsive[device].slidesToSlide;deviceType===device&&slidesToSlide&&(slidesToScroll=slidesToSlide)}),domFullyLoaded&&Object.keys(responsive).forEach(function(item){var _a=responsive[item],breakpoint=_a.breakpoint,slidesToSlide=_a.slidesToSlide,max=breakpoint.max,min=breakpoint.min;slidesToSlide&&window.innerWidth>=min&&window.innerWidth<=max&&(slidesToScroll=slidesToSlide)}),slidesToScroll}exports.notEnoughChildren=notEnoughChildren,exports.getInitialState=getInitialState,exports.getIfSlideIsVisbile=getIfSlideIsVisbile,exports.getTransformForCenterMode=getTransformForCenterMode,exports.isInLeftEnd=isInLeftEnd,exports.isInRightEnd=isInRightEnd,exports.getTransformForPartialVsibile=getTransformForPartialVsibile,exports.parsePosition=parsePosition,exports.getTransform=getTransform,exports.getSlidesToSlide=getSlidesToSlide;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,cAAc,GAACC,OAAO,CAAC,gBAAD,CAA1B;;AAA6C,SAASC,iBAAT,CAA2BC,KAA3B,EAAiC;EAAC,IAAIC,YAAY,GAACD,KAAK,CAACC,YAAvB;EAAoC,OAAOD,KAAK,CAACE,UAAN,GAAiBD,YAAxB;AAAqC;;AAAA,SAASE,eAAT,CAAyBH,KAAzB,EAA+BI,KAA/B,EAAqC;EAAC,IAAIC,SAAJ;EAAA,IAAcC,SAAS,GAACN,KAAK,CAACM,SAA9B;EAAA,IAAwCL,YAAY,GAACD,KAAK,CAACC,YAA3D;EAAA,IAAwEM,cAAc,GAACP,KAAK,CAACO,cAA7F;EAAA,IAA4GC,SAAS,GAACR,KAAK,CAACQ,SAA5H;EAAA,IAAsIC,UAAU,GAACL,KAAK,CAACK,UAAvJ;EAAA,IAAkKC,UAAU,GAACN,KAAK,CAACM,UAAnL;EAAA,IAA8LC,GAAG,GAACP,KAAK,CAACO,GAAxM;EAAA,IAA4MC,cAAc,GAACR,KAAK,CAACQ,cAAjO;EAAA,IAAgPC,cAAc,GAACT,KAAK,CAACS,cAArQ;EAAA,IAAoRC,cAAc,GAACC,OAAO,CAACT,SAAS,IAAEL,YAAX,IAAyBM,cAAzB,IAAyCC,SAA1C,CAA1S;EAA+VG,GAAG,IAAEF,UAAL,IAAiB,CAACK,cAAlB,KAAmCT,SAAS,GAACR,cAAc,CAACmB,sBAAf,CAAsCP,UAAtC,EAAiDC,UAAjD,CAA7C;EAA2G,IAAIO,iBAAiB,GAACF,OAAO,CAACJ,GAAG,IAAEF,UAAL,IAAiB,CAACK,cAAlB,IAAkCT,SAAnC,CAA7B;EAA2E,OAAM;IAACY,iBAAiB,EAACA,iBAAnB;IAAqCZ,SAAS,EAACA,SAA/C;IAAyDS,cAAc,EAACA,cAAxE;IAAuFI,uBAAuB,EAACrB,cAAc,CAACsB,0BAAf,CAA0CT,UAA1C,EAAqDE,cAAc,IAAEC,cAArE,EAAoFJ,UAApF,EAA+FT,KAAK,CAACS,UAArG,CAA/G;IAAgOW,iBAAiB,EAACH,iBAAiB,IAAEH;EAArQ,CAAN;AAA2R;;AAAA,SAASO,mBAAT,CAA6BC,KAA7B,EAAmCtB,KAAnC,EAAyC;EAAC,IAAIuB,YAAY,GAACvB,KAAK,CAACuB,YAAvB;EAAA,IAAoCtB,YAAY,GAACD,KAAK,CAACC,YAAvD;EAAoE,OAAOsB,YAAY,IAAED,KAAd,IAAqBA,KAAK,GAACC,YAAY,GAACtB,YAA/C;AAA4D;;AAAA,SAASuB,yBAAT,CAAmCxB,KAAnC,EAAyCI,KAAzC,EAA+CqB,oBAA/C,EAAoE;EAAC,IAAIC,SAAS,GAACD,oBAAoB,IAAEzB,KAAK,CAAC0B,SAA1C;EAAoD,OAAM,CAACtB,KAAK,CAACuB,QAAP,IAAiB,MAAI3B,KAAK,CAACuB,YAA3B,IAAyCxB,iBAAiB,CAACC,KAAD,CAA1D,GAAkE0B,SAAlE,GAA4EA,SAAS,GAAC1B,KAAK,CAACQ,SAAN,GAAgB,CAA5G;AAA8G;;AAAA,SAASoB,WAAT,CAAqBC,EAArB,EAAwB;EAAC,OAAM,EAAE,IAAEA,EAAE,CAACN,YAAP,CAAN;AAA2B;;AAAA,SAASO,YAAT,CAAsBD,EAAtB,EAAyB;EAAC,IAAIN,YAAY,GAACM,EAAE,CAACN,YAApB;EAAA,IAAiCrB,UAAU,GAAC2B,EAAE,CAAC3B,UAA/C;EAA0D,OAAM,EAAEqB,YAAY,GAACM,EAAE,CAAC5B,YAAhB,GAA6BC,UAA/B,CAAN;AAAiD;;AAAA,SAAS6B,6BAAT,CAAuC/B,KAAvC,EAA6CkB,uBAA7C,EAAqEd,KAArE,EAA2EqB,oBAA3E,EAAgG;EAAC,KAAK,CAAL,KAASP,uBAAT,KAAmCA,uBAAuB,GAAC,CAA3D;EAA8D,IAAIK,YAAY,GAACvB,KAAK,CAACuB,YAAvB;EAAA,IAAoCtB,YAAY,GAACD,KAAK,CAACC,YAAvD;EAAA,IAAoE+B,eAAe,GAACF,YAAY,CAAC9B,KAAD,CAAhG;EAAA,IAAwGiC,uBAAuB,GAAC,CAAC7B,KAAK,CAACuB,QAAP,IAAiBK,eAAjJ;EAAA,IAAiKE,aAAa,GAACT,oBAAoB,IAAEzB,KAAK,CAAC0B,SAA3M;EAAqN,IAAG3B,iBAAiB,CAACC,KAAD,CAApB,EAA4B,OAAOkC,aAAP;EAAqB,IAAIR,SAAS,GAACQ,aAAa,GAACX,YAAY,GAACL,uBAAzC;EAAiE,OAAOe,uBAAuB,GAACP,SAAS,IAAE1B,KAAK,CAACO,cAAN,GAAqB,CAACP,KAAK,CAACQ,SAAN,GAAgBU,uBAAjB,IAA0CjB,YAAjE,CAAV,GAAyFyB,SAAvH;AAAiI;;AAAA,SAASS,aAAT,CAAuB/B,KAAvB,EAA6BgC,QAA7B,EAAsC;EAAC,OAAOhC,KAAK,CAACiC,GAAN,GAAU,CAAC,CAAD,GAAGD,QAAb,GAAsBA,QAA7B;AAAsC;;AAAA,SAASE,YAAT,CAAsBtC,KAAtB,EAA4BI,KAA5B,EAAkCqB,oBAAlC,EAAuD;EAAC,IAAIb,cAAc,GAACR,KAAK,CAACQ,cAAzB;EAAA,IAAwCC,cAAc,GAACT,KAAK,CAACS,cAA7D;EAAA,IAA4EH,UAAU,GAACN,KAAK,CAACM,UAA7F;EAAA,IAAwGD,UAAU,GAACL,KAAK,CAACK,UAAzH;EAAA,IAAoI8B,UAAU,GAACnC,KAAK,CAACmC,UAArJ;EAAA,IAAgKb,SAAS,GAACD,oBAAoB,IAAEzB,KAAK,CAAC0B,SAAtM;EAAA,IAAgNR,uBAAuB,GAACrB,cAAc,CAACsB,0BAAf,CAA0CT,UAA1C,EAAqDE,cAAc,IAAEC,cAArE,EAAoFJ,UAApF,EAA+FT,KAAK,CAACS,UAArG,CAAxO;EAAyV,OAAO0B,aAAa,CAAC/B,KAAD,EAAOS,cAAc,IAAED,cAAhB,GAA+BmB,6BAA6B,CAAC/B,KAAD,EAAOkB,uBAAP,EAA+Bd,KAA/B,EAAqCqB,oBAArC,CAA5D,GAAuHc,UAAU,GAACf,yBAAyB,CAACxB,KAAD,EAAOI,KAAP,EAAaqB,oBAAb,CAA1B,GAA6DC,SAArM,CAApB;AAAoO;;AAAA,SAASc,gBAAT,CAA0BxC,KAA1B,EAAgCI,KAAhC,EAAsC;EAAC,IAAIE,SAAS,GAACN,KAAK,CAACM,SAApB;EAAA,IAA8BL,YAAY,GAACD,KAAK,CAACC,YAAjD;EAAA,IAA8DM,cAAc,GAACP,KAAK,CAACO,cAAnF;EAAA,IAAkGC,SAAS,GAACR,KAAK,CAACQ,SAAlH;EAAA,IAA4HC,UAAU,GAACL,KAAK,CAACK,UAA7I;EAAA,IAAwJC,UAAU,GAACN,KAAK,CAACM,UAAzK;EAAA,IAAoL+B,cAAc,GAACrC,KAAK,CAACsC,aAAN,IAAqB,CAAxN;EAAA,IAA0N5B,cAAc,GAACC,OAAO,CAACT,SAAS,IAAEL,YAAX,IAAyBM,cAAzB,IAAyCC,SAA1C,CAAhP;EAAqS,OAAOJ,KAAK,CAACO,GAAN,IAAWP,KAAK,CAACK,UAAjB,IAA6B,CAACK,cAA9B,IAA8CrB,MAAM,CAACkD,IAAP,CAAYjC,UAAZ,EAAwBkC,OAAxB,CAAgC,UAASC,MAAT,EAAgB;IAAC,IAAIH,aAAa,GAAChC,UAAU,CAACmC,MAAD,CAAV,CAAmBH,aAArC;IAAmDjC,UAAU,KAAGoC,MAAb,IAAqBH,aAArB,KAAqCD,cAAc,GAACC,aAApD;EAAmE,CAAvK,CAA9C,EAAuN5B,cAAc,IAAErB,MAAM,CAACkD,IAAP,CAAYjC,UAAZ,EAAwBkC,OAAxB,CAAgC,UAASE,IAAT,EAAc;IAAC,IAAIjB,EAAE,GAACnB,UAAU,CAACoC,IAAD,CAAjB;IAAA,IAAwBC,UAAU,GAAClB,EAAE,CAACkB,UAAtC;IAAA,IAAiDL,aAAa,GAACb,EAAE,CAACa,aAAlE;IAAA,IAAgFM,GAAG,GAACD,UAAU,CAACC,GAA/F;IAAA,IAAmGC,GAAG,GAACF,UAAU,CAACE,GAAlH;IAAsHP,aAAa,IAAEQ,MAAM,CAACC,UAAP,IAAmBF,GAAlC,IAAuCC,MAAM,CAACC,UAAP,IAAmBH,GAA1D,KAAgEP,cAAc,GAACC,aAA/E;EAA8F,CAAnQ,CAAvO,EAA4eD,cAAnf;AAAkgB;;AAAA9C,OAAO,CAACI,iBAAR,GAA0BA,iBAA1B,EAA4CJ,OAAO,CAACQ,eAAR,GAAwBA,eAApE,EAAoFR,OAAO,CAAC0B,mBAAR,GAA4BA,mBAAhH,EAAoI1B,OAAO,CAAC6B,yBAAR,GAAkCA,yBAAtK,EAAgM7B,OAAO,CAACiC,WAAR,GAAoBA,WAApN,EAAgOjC,OAAO,CAACmC,YAAR,GAAqBA,YAArP,EAAkQnC,OAAO,CAACoC,6BAAR,GAAsCA,6BAAxS,EAAsUpC,OAAO,CAACwC,aAAR,GAAsBA,aAA5V,EAA0WxC,OAAO,CAAC2C,YAAR,GAAqBA,YAA/X,EAA4Y3C,OAAO,CAAC6C,gBAAR,GAAyBA,gBAAra"}, "metadata": {}, "sourceType": "script"}