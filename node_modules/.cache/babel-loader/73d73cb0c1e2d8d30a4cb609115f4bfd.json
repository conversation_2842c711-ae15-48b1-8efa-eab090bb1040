{"ast": null, "code": "import styled from \"styled-components\";\nexport const Header = styled.header`\n  background-color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.white;\n}};\n  padding: 15px 0;\n\n  box-shadow: 0 0 10px #00000010;\n\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n\n  z-index: 9999;\n`;\nexport const Container = styled.div``;\nexport const Wtapper = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\nexport const MenuContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\nexport const MenuListWrapper = styled.ul`\n  list-style-type: none;\n  margin-bottom: 0;\n`;\nexport const MenuItem = styled.li`\n  display: inline-block;\n  padding: 0 15px;\n  font-size: ${_ref2 => {\n  let {\n    theme: {\n      font\n    }\n  } = _ref2;\n  return font.main;\n}};\n  font-weight: 600;\n  color: ${_ref3 => {\n  let {\n    active,\n    theme: {\n      colors\n    }\n  } = _ref3;\n  return active ? colors.main.red : colors.black;\n}};\n\n  transition: all .3s ease-in-out;\n`;\nexport const MobileMenuContainer = styled.div``;", "map": {"version": 3, "names": ["styled", "Header", "header", "theme", "colors", "white", "Container", "div", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MenuListWrapper", "ul", "MenuItem", "li", "font", "main", "active", "red", "black", "MobileMenuContainer"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Header = styled.header`\n  background-color: ${({ theme: { colors } }) => colors.white};\n  padding: 15px 0;\n\n  box-shadow: 0 0 10px #00000010;\n\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n\n  z-index: 9999;\n`;\n\nexport const Container = styled.div``;\n\nexport const Wtapper = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\n\nexport const MenuContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\n\nexport const MenuListWrapper = styled.ul`\n  list-style-type: none;\n  margin-bottom: 0;\n`;\n\nexport const MenuItem = styled.li`\n  display: inline-block;\n  padding: 0 15px;\n  font-size: ${({ theme: { font } }) => font.main};\n  font-weight: 600;\n  color: ${({ active, theme: { colors } }) =>\n    active ? colors.main.red : colors.black};\n\n  transition: all .3s ease-in-out;\n`;\n\nexport const MobileMenuContainer = styled.div``;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,MAAM,GAAGD,MAAM,CAACE,MAAO;AACpC,sBAAsB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,KAAlC;AAAA,CAAwC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAZO;AAcP,OAAO,MAAMC,SAAS,GAAGN,MAAM,CAACO,GAAI,EAA7B;AAEP,OAAO,MAAMC,OAAO,GAAGR,MAAM,CAACO,GAAI;AAClC;AACA;AACA;AACA,CAJO;AAMP,OAAO,MAAME,aAAa,GAAGT,MAAM,CAACO,GAAI;AACxC;AACA;AACA;AACA,CAJO;AAMP,OAAO,MAAMG,eAAe,GAAGV,MAAM,CAACW,EAAG;AACzC;AACA;AACA,CAHO;AAKP,OAAO,MAAMC,QAAQ,GAAGZ,MAAM,CAACa,EAAG;AAClC;AACA;AACA,eAAe;EAAA,IAAC;IAAEV,KAAK,EAAE;MAAEW;IAAF;EAAT,CAAD;EAAA,OAAyBA,IAAI,CAACC,IAA9B;AAAA,CAAmC;AAClD;AACA,WAAW;EAAA,IAAC;IAAEC,MAAF;IAAUb,KAAK,EAAE;MAAEC;IAAF;EAAjB,CAAD;EAAA,OACPY,MAAM,GAAGZ,MAAM,CAACW,IAAP,CAAYE,GAAf,GAAqBb,MAAM,CAACc,KAD3B;AAAA,CACiC;AAC5C;AACA;AACA,CATO;AAWP,OAAO,MAAMC,mBAAmB,GAAGnB,MAAM,CAACO,GAAI,EAAvC"}, "metadata": {}, "sourceType": "module"}