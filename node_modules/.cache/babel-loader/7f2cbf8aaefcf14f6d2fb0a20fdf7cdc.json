{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  margin: 50px 0;\n\n  .react-multiple-carousel__arrow.react-multiple-carousel__arrow--left {\n    transform: translateX(-90%);\n  }\n  .react-multiple-carousel__arrow.react-multiple-carousel__arrow--right {\n    transform: translateX(90%);\n  }\n`;\nexport const ListHolder = styled.div`\n  /* display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  } */\n\n  margin: 50px 0;\n`;\nexport const AllListHolder = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n\n  margin: 50px 0;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "ListHolder", "AllListHolder"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  margin: 50px 0;\n\n  .react-multiple-carousel__arrow.react-multiple-carousel__arrow--left {\n    transform: translateX(-90%);\n  }\n  .react-multiple-carousel__arrow.react-multiple-carousel__arrow--right {\n    transform: translateX(90%);\n  }\n`;\n\nexport const ListHolder = styled.div`\n  /* display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  } */\n\n  margin: 50px 0;\n`;\n\nexport const AllListHolder = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n\n  margin: 50px 0;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CATO;AAWP,OAAO,MAAMC,UAAU,GAAGH,MAAM,CAACE,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAdO;AAgBP,OAAO,MAAME,aAAa,GAAGJ,MAAM,CAACE,GAAI;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAdO"}, "metadata": {}, "sourceType": "module"}