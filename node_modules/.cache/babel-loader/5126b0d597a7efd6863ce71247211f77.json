{"ast": null, "code": "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar fails = require('../internals/fails');\n\nvar classof = require('../internals/classof-raw');\n\nvar $Object = Object;\nvar split = uncurryThis(''.split); // fallback for non-array-like ES3 and non-enumerable old V8 strings\n\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !$Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split(it, '') : $Object(it);\n} : $Object;", "map": {"version": 3, "names": ["uncurryThis", "require", "fails", "classof", "$Object", "Object", "split", "module", "exports", "propertyIsEnumerable", "it"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/indexed-object.js"], "sourcesContent": ["var uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar $Object = Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !$Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split(it, '') : $Object(it);\n} : $Object;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAD,CAAzB;;AACA,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAD,CAAnB;;AACA,IAAIE,OAAO,GAAGF,OAAO,CAAC,0BAAD,CAArB;;AAEA,IAAIG,OAAO,GAAGC,MAAd;AACA,IAAIC,KAAK,GAAGN,WAAW,CAAC,GAAGM,KAAJ,CAAvB,C,CAEA;;AACAC,MAAM,CAACC,OAAP,GAAiBN,KAAK,CAAC,YAAY;EACjC;EACA;EACA,OAAO,CAACE,OAAO,CAAC,GAAD,CAAP,CAAaK,oBAAb,CAAkC,CAAlC,CAAR;AACD,CAJqB,CAAL,GAIZ,UAAUC,EAAV,EAAc;EACjB,OAAOP,OAAO,CAACO,EAAD,CAAP,IAAe,QAAf,GAA0BJ,KAAK,CAACI,EAAD,EAAK,EAAL,CAA/B,GAA0CN,OAAO,CAACM,EAAD,CAAxD;AACD,CANgB,GAMbN,OANJ"}, "metadata": {}, "sourceType": "script"}