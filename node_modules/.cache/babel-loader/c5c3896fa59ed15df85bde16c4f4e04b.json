{"ast": null, "code": "import React from\"react\";import*as Styles from\"./styles\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const AboutCard=_ref=>{let{data}=_ref;return/*#__PURE__*/_jsx(Styles.CardContainer,{children:/*#__PURE__*/_jsxs(Styles.Wrapper,{children:[/*#__PURE__*/_jsx(Styles.CardTitle,{children:data.name}),/*#__PURE__*/_jsx(Styles.TextHolder,{children:data.words})]})});};export default AboutCard;", "map": {"version": 3, "names": ["React", "Styles", "jsx", "_jsx", "jsxs", "_jsxs", "AboutCard", "_ref", "data", "CardContainer", "children", "Wrapper", "CardTitle", "name", "TextHolder", "words"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/card.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst AboutCard = ({ data }) => {\n  return (\n    <Styles.CardContainer>\n      <Styles.Wrapper>\n        <Styles.CardTitle>{data.name}</Styles.CardTitle>\n        <Styles.TextHolder>{data.words}</Styles.TextHolder>\n      </Styles.Wrapper>\n    </Styles.CardContainer>\n  );\n};\n\nexport default AboutCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,MAAO,GAAK,CAAAC,MAAM,KAAM,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEnC,KAAM,CAAAC,SAAS,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACzB,mBACEJ,IAAA,CAACF,MAAM,CAACQ,aAAa,EAAAC,QAAA,cACnBL,KAAA,CAACJ,MAAM,CAACU,OAAO,EAAAD,QAAA,eACbP,IAAA,CAACF,MAAM,CAACW,SAAS,EAAAF,QAAA,CAAEF,IAAI,CAACK,IAAI,CAAmB,CAAC,cAChDV,IAAA,CAACF,MAAM,CAACa,UAAU,EAAAJ,QAAA,CAAEF,IAAI,CAACO,KAAK,CAAoB,CAAC,EACrC,CAAC,CACG,CAAC,CAE3B,CAAC,CAED,cAAe,CAAAT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}