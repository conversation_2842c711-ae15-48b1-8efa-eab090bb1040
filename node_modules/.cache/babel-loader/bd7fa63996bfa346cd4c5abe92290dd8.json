{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n\n  background-color: #fefefe;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  min-height: 200px;\n  max-height: 420px;\n\n  margin: 0 10px;\n\n  font-size: ${_ref => {\n  let {\n    theme: {\n      font\n    }\n  } = _ref;\n  return font.title;\n}};\n  font-weight: 700;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ${_ref2 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref2;\n  return `${colors.grey}01`;\n}};\n    transition: all 0.3 ease-in-out;\n\n\n    .title {\n      background-color: ${_ref3 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref3;\n  return colors.white;\n}};\n    }\n  }\n\n  cursor: pointer;\n  transition: all 0.3 ease-in-out;\n`;\nexport const TitleWrapper = styled.div`\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  font-size: 25px;\n\n  padding: 20px 60px;\n`;\nexport const IconHolder = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  margin-right: 10px;\n\n  svg {\n    width: 30px;\n    height: 30px;\n  }\n`;\nexport const TextHolder = styled.span``;\nexport const ListHolder = styled.ul`\n  padding: 20px 60px;\n  width: 100%;\n\n  height: 100%;\n  overflow-y: auto;\n`;\nexport const ListItme = styled.li`\n  padding: 10px;\n\n  display: flex;\n  align-items: center;\n\n  svg {\n    margin-right: 10px;\n  }\n\n  &:hover {\n    background-color: ${_ref4 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref4;\n  return colors.main.blue;\n}};\n    color: #ffffff;\n  }\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "theme", "font", "title", "colors", "grey", "white", "TitleWrapper", "IconHolder", "TextHolder", "span", "ListHolder", "ul", "ListItme", "li", "main", "blue"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n\n  background-color: #fefefe;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  min-height: 200px;\n  max-height: 420px;\n\n  margin: 0 10px;\n\n  font-size: ${({ theme: { font } }) => font.title};\n  font-weight: 700;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ${({ theme: { colors } }) => `${colors.grey}01`};\n    transition: all 0.3 ease-in-out;\n\n\n    .title {\n      background-color: ${({ theme: { colors } }) => colors.white};\n    }\n  }\n\n  cursor: pointer;\n  transition: all 0.3 ease-in-out;\n`;\n\nexport const TitleWrapper = styled.div`\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  font-size: 25px;\n\n  padding: 20px 60px;\n`;\n\nexport const IconHolder = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  margin-right: 10px;\n\n  svg {\n    width: 30px;\n    height: 30px;\n  }\n`;\n\nexport const TextHolder = styled.span``;\n\nexport const ListHolder = styled.ul`\n  padding: 20px 60px;\n  width: 100%;\n\n  height: 100%;\n  overflow-y: auto;\n`;\n\nexport const ListItme = styled.li`\n  padding: 10px;\n\n  display: flex;\n  align-items: center;\n\n  svg {\n    margin-right: 10px;\n  }\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: #ffffff;\n  }\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAAyBA,IAAI,CAACC,KAA9B;AAAA,CAAoC;AACnD;AACA;AACA;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEF,KAAK,EAAE;MAAEG;IAAF;EAAT,CAAD;EAAA,OAA4B,GAAEA,MAAM,CAACC,IAAK,IAA1C;AAAA,CAA8C;AACtE;AACA;AACA;AACA;AACA,0BAA0B;EAAA,IAAC;IAAEJ,KAAK,EAAE;MAAEG;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACE,KAAlC;AAAA,CAAwC;AAClE;AACA;AACA;AACA;AACA;AACA,CAhCO;AAkCP,OAAO,MAAMC,YAAY,GAAGT,MAAM,CAACE,GAAI;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CARO;AAUP,OAAO,MAAMQ,UAAU,GAAGV,MAAM,CAACE,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAXO;AAaP,OAAO,MAAMS,UAAU,GAAGX,MAAM,CAACY,IAAK,EAA/B;AAEP,OAAO,MAAMC,UAAU,GAAGb,MAAM,CAACc,EAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CANO;AAQP,OAAO,MAAMC,QAAQ,GAAGf,MAAM,CAACgB,EAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEb,KAAK,EAAE;MAAEG;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACW,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AACpE;AACA;AACA,CAdO"}, "metadata": {}, "sourceType": "module"}