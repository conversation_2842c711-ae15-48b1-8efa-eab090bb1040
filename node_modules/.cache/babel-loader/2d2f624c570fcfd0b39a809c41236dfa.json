{"ast": null, "code": "import { lazy } from \"react\";\nexport const AllRoutes = [{\n  name: \"Home\",\n  path: \"/\",\n  component: /*#__PURE__*/lazy(() => import(\"../pages/home\")),\n  props: {\n    titel: \"Home\"\n  }\n}, {\n  name: \"About\",\n  path: \"/about\",\n  component: /*#__PURE__*/lazy(() => import(\"../pages/about\")),\n  props: {}\n}, {\n  name: \"Projects\",\n  path: \"/projects\",\n  component: /*#__PURE__*/lazy(() => import(\"../pages/projects\")),\n  props: {}\n}, {\n  name: \"Clients\",\n  path: \"/clients\",\n  component: /*#__PURE__*/lazy(() => import(\"../pages/clients\")),\n  props: {}\n}, {\n  name: \"Projects\",\n  path: \"/projects/:id\",\n  component: /*#__PURE__*/lazy(() => import(\"../pages/projects/details-page\")),\n  props: {}\n}, {\n  name: \"Contact Us\",\n  path: \"/contactus\",\n  component: /*#__PURE__*/lazy(() => import(\"../pages/contact-us\")),\n  props: {}\n}, {\n  name: \"Tearms & Condition\",\n  path: \"/tandc\",\n  component: /*#__PURE__*/lazy(() => import(\"../pages/t&c\")),\n  props: {}\n}, {\n  name: \"Loading\",\n  path: \"*\",\n  component: /*#__PURE__*/lazy(() => import(\"../components/global/loading\")),\n  props: {}\n}];", "map": {"version": 3, "names": ["lazy", "AllRoutes", "name", "path", "component", "props", "titel"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/routeData.js"], "sourcesContent": ["import { lazy } from \"react\";\n\nexport const AllRoutes = [\n  {\n    name: \"Home\",\n    path: \"/\",\n    component: lazy(() => import(\"../pages/home\")),\n    props: {\n      titel: \"Home\",\n    },\n  },\n  {\n    name: \"About\",\n    path: \"/about\",\n    component: lazy(() => import(\"../pages/about\")),\n    props: {},\n  },\n  {\n    name: \"Projects\",\n    path: \"/projects\",\n    component: lazy(() => import(\"../pages/projects\")),\n    props: {},\n  },\n  {\n    name: \"Clients\",\n    path: \"/clients\",\n    component: lazy(() => import(\"../pages/clients\")),\n    props: {},\n  },\n  {\n    name: \"Projects\",\n    path: \"/projects/:id\",\n    component: lazy(() => import(\"../pages/projects/details-page\")),\n    props: {},\n  },\n  {\n    name: \"Contact Us\",\n    path: \"/contactus\",\n    component: lazy(() => import(\"../pages/contact-us\")),\n    props: {},\n  },\n  {\n    name: \"Tearms & Condition\",\n    path: \"/tandc\",\n    component: lazy(() => import(\"../pages/t&c\")),\n    props: {},\n  },\n  {\n    name: \"Loading\",\n    path: \"*\",\n    component: lazy(() => import(\"../components/global/loading\")),\n    props: {},\n  },\n];\n"], "mappings": "AAAA,SAASA,IAAT,QAAqB,OAArB;AAEA,OAAO,MAAMC,SAAS,GAAG,CACvB;EACEC,IAAI,EAAE,MADR;EAEEC,IAAI,EAAE,GAFR;EAGEC,SAAS,eAAEJ,IAAI,CAAC,MAAM,OAAO,eAAP,CAAP,CAHjB;EAIEK,KAAK,EAAE;IACLC,KAAK,EAAE;EADF;AAJT,CADuB,EASvB;EACEJ,IAAI,EAAE,OADR;EAEEC,IAAI,EAAE,QAFR;EAGEC,SAAS,eAAEJ,IAAI,CAAC,MAAM,OAAO,gBAAP,CAAP,CAHjB;EAIEK,KAAK,EAAE;AAJT,CATuB,EAevB;EACEH,IAAI,EAAE,UADR;EAEEC,IAAI,EAAE,WAFR;EAGEC,SAAS,eAAEJ,IAAI,CAAC,MAAM,OAAO,mBAAP,CAAP,CAHjB;EAIEK,KAAK,EAAE;AAJT,CAfuB,EAqBvB;EACEH,IAAI,EAAE,SADR;EAEEC,IAAI,EAAE,UAFR;EAGEC,SAAS,eAAEJ,IAAI,CAAC,MAAM,OAAO,kBAAP,CAAP,CAHjB;EAIEK,KAAK,EAAE;AAJT,CArBuB,EA2BvB;EACEH,IAAI,EAAE,UADR;EAEEC,IAAI,EAAE,eAFR;EAGEC,SAAS,eAAEJ,IAAI,CAAC,MAAM,OAAO,gCAAP,CAAP,CAHjB;EAIEK,KAAK,EAAE;AAJT,CA3BuB,EAiCvB;EACEH,IAAI,EAAE,YADR;EAEEC,IAAI,EAAE,YAFR;EAGEC,SAAS,eAAEJ,IAAI,CAAC,MAAM,OAAO,qBAAP,CAAP,CAHjB;EAIEK,KAAK,EAAE;AAJT,CAjCuB,EAuCvB;EACEH,IAAI,EAAE,oBADR;EAEEC,IAAI,EAAE,QAFR;EAGEC,SAAS,eAAEJ,IAAI,CAAC,MAAM,OAAO,cAAP,CAAP,CAHjB;EAIEK,KAAK,EAAE;AAJT,CAvCuB,EA6CvB;EACEH,IAAI,EAAE,SADR;EAEEC,IAAI,EAAE,GAFR;EAGEC,SAAS,eAAEJ,IAAI,CAAC,MAAM,OAAO,8BAAP,CAAP,CAHjB;EAIEK,KAAK,EAAE;AAJT,CA7CuB,CAAlB"}, "metadata": {}, "sourceType": "module"}