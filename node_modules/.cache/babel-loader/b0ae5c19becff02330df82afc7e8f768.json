{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  margin-top: 100px;\n\n  background-color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.main.blue;\n}};\n  color: #fff;\n\n  position: relative;\n`;\nexport const MainFooter = styled.div`\n  background-color: ${_ref2 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref2;\n  return colors.white;\n}};\n  padding: 15px 0;\n`;\nexport const RowHolder = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media screen and (max-width: 900px) {\n    flex-direction: column;\n    gap: 25px;\n  }\n`;\nexport const ImageWrapper = styled.div`\n  /* margin-bottom: 20px; */\n`;\nexport const MenuContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  padding: 40px 0;\n`;\nexport const MenuListHolder = styled.ul`\n  margin-bottom: 0;\n  padding-left: 0;\n\n  column-count: 3;\n\n  flex: 2;\n`;\nexport const MenuListItem = styled.div`\n  padding: 10px;\n  cursor: pointer;\n`;\nexport const SocialWrapper = styled.div`\n  /* flex: 1; */\n`;\nexport const EmailWrapper = styled.div`\n  display: flex;\n  align-items: center;\n\n   ${_ref3 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref3;\n  return colors.main.blue;\n}};\n\n  &:hover {\n    background-color: ${_ref4 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref4;\n  return colors.main.blue;\n}};\n    color: ${_ref5 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref5;\n  return colors.main.yellow;\n}};\n    padding: 6px 12px;\n    border-radius: 100px;\n  }\n\n  transition: all 0.3s ease-in-out;\n\n  /* margin-bottom: 20px; */\n\n  svg {\n    width: 20px;\n    height: 20px;\n    margin-right: 10px;\n  }\n`;\nexport const TextHolder = styled.span``;\nexport const IconHolder = styled.img`\n  width: 30px;\n  height: 30px;\n  object-fit: contain;\n  margin-right: 10px;\n`;\nexport const SocialHolder = styled.div`\n  a {\n    margin: 5px 20px 5px 0;\n\n    &:last-of-type {\n      margin-right: 0;\n    }\n\n    background-color: ${_ref6 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref6;\n  return colors.main.red;\n}};\n\n    transition: all 0.3s ease-in-out;\n\n    &:hover {\n      background-color: ${_ref7 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref7;\n  return colors.main.blue;\n}};\n      color: ${_ref8 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref8;\n  return colors.main.yellow;\n}};\n    }\n\n    padding: 10px;\n    border-radius: 100px;\n\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n\n    svg {\n      width: 20px;\n      height: 20px;\n      stroke-width: 2px;\n    }\n  }\n`;\nexport const CopyrightWrapper = styled.div`\n  padding: 20px 0;\n  /* margin-top: 70px; */\n\n  border-top: 1px solid ${_ref9 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref9;\n  return colors.white;\n}};\n\n  font-size: ${_ref10 => {\n  let {\n    theme: {\n      font\n    }\n  } = _ref10;\n  return font.main;\n}};\n\n  a {\n    margin-left: 7px;\n    text-decoration: underline;\n    font-weight: 600;\n\n    &:hover {\n      color: ${_ref11 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref11;\n  return colors.main.yellow;\n}};\n    }\n  }\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "theme", "colors", "main", "blue", "MainFooter", "white", "RowHolder", "ImageWrapper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MenuListHolder", "ul", "MenuListItem", "SocialWrapper", "EmailWrapper", "yellow", "TextHolder", "span", "IconHolder", "img", "SocialHolder", "red", "CopyrightWrapper", "font"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  margin-top: 100px;\n\n  background-color: ${({ theme: { colors } }) => colors.main.blue};\n  color: #fff;\n\n  position: relative;\n`;\n\nexport const MainFooter = styled.div`\n  background-color: ${({ theme: { colors } }) => colors.white};\n  padding: 15px 0;\n`;\n\nexport const RowHolder = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media screen and (max-width: 900px) {\n    flex-direction: column;\n    gap: 25px;\n  }\n`;\n\nexport const ImageWrapper = styled.div`\n  /* margin-bottom: 20px; */\n`;\n\nexport const MenuContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  padding: 40px 0;\n`;\n\nexport const MenuListHolder = styled.ul`\n  margin-bottom: 0;\n  padding-left: 0;\n\n  column-count: 3;\n\n  flex: 2;\n`;\n\nexport const MenuListItem = styled.div`\n  padding: 10px;\n  cursor: pointer;\n`;\n\nexport const SocialWrapper = styled.div`\n  /* flex: 1; */\n`;\n\nexport const EmailWrapper = styled.div`\n  display: flex;\n  align-items: center;\n\n   ${({ theme: { colors } }) => colors.main.blue};\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: ${({ theme: { colors } }) => colors.main.yellow};\n    padding: 6px 12px;\n    border-radius: 100px;\n  }\n\n  transition: all 0.3s ease-in-out;\n\n  /* margin-bottom: 20px; */\n\n  svg {\n    width: 20px;\n    height: 20px;\n    margin-right: 10px;\n  }\n`;\n\nexport const TextHolder = styled.span``;\n\nexport const IconHolder = styled.img`\n  width: 30px;\n  height: 30px;\n  object-fit: contain;\n  margin-right: 10px;\n`;\n\nexport const SocialHolder = styled.div`\n  a {\n    margin: 5px 20px 5px 0;\n\n    &:last-of-type {\n      margin-right: 0;\n    }\n\n    background-color: ${({ theme: { colors } }) => colors.main.red};\n\n    transition: all 0.3s ease-in-out;\n\n    &:hover {\n      background-color: ${({ theme: { colors } }) => colors.main.blue};\n      color: ${({ theme: { colors } }) => colors.main.yellow};\n    }\n\n    padding: 10px;\n    border-radius: 100px;\n\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n\n    svg {\n      width: 20px;\n      height: 20px;\n      stroke-width: 2px;\n    }\n  }\n`;\n\nexport const CopyrightWrapper = styled.div`\n  padding: 20px 0;\n  /* margin-top: 70px; */\n\n  border-top: 1px solid ${({ theme: { colors } }) => colors.white};\n\n  font-size: ${({ theme: { font } }) => font.main};\n\n  a {\n    margin-left: 7px;\n    text-decoration: underline;\n    font-weight: 600;\n\n    &:hover {\n      color: ${({ theme: { colors } }) => colors.main.yellow};\n    }\n  }\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA,sBAAsB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AAClE;AACA;AACA;AACA,CAPO;AASP,OAAO,MAAMC,UAAU,GAAGP,MAAM,CAACE,GAAI;AACrC,sBAAsB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACI,KAAlC;AAAA,CAAwC;AAC9D;AACA,CAHO;AAKP,OAAO,MAAMC,SAAS,GAAGT,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CATO;AAWP,OAAO,MAAMQ,YAAY,GAAGV,MAAM,CAACE,GAAI;AACvC;AACA,CAFO;AAIP,OAAO,MAAMS,aAAa,GAAGX,MAAM,CAACE,GAAI;AACxC;AACA;AACA;AACA;AACA;AACA,CANO;AAQP,OAAO,MAAMU,cAAc,GAAGZ,MAAM,CAACa,EAAG;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,CAPO;AASP,OAAO,MAAMC,YAAY,GAAGd,MAAM,CAACE,GAAI;AACvC;AACA;AACA,CAHO;AAKP,OAAO,MAAMa,aAAa,GAAGf,MAAM,CAACE,GAAI;AACxC;AACA,CAFO;AAIP,OAAO,MAAMc,YAAY,GAAGhB,MAAM,CAACE,GAAI;AACvC;AACA;AACA;AACA,KAAK;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AACjD;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEH,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AACpE,aAAa;EAAA,IAAC;IAAEH,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYY,MAAvC;AAAA,CAA8C;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAtBO;AAwBP,OAAO,MAAMC,UAAU,GAAGlB,MAAM,CAACmB,IAAK,EAA/B;AAEP,OAAO,MAAMC,UAAU,GAAGpB,MAAM,CAACqB,GAAI;AACrC;AACA;AACA;AACA;AACA,CALO;AAOP,OAAO,MAAMC,YAAY,GAAGtB,MAAM,CAACE,GAAI;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYkB,GAAvC;AAAA,CAA2C;AACnE;AACA;AACA;AACA;AACA,0BAA0B;EAAA,IAAC;IAAEpB,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AACtE,eAAe;EAAA,IAAC;IAAEH,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYY,MAAvC;AAAA,CAA8C;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CA9BO;AAgCP,OAAO,MAAMO,gBAAgB,GAAGxB,MAAM,CAACE,GAAI;AAC3C;AACA;AACA;AACA,0BAA0B;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACI,KAAlC;AAAA,CAAwC;AAClE;AACA,eAAe;EAAA,IAAC;IAAEL,KAAK,EAAE;MAAEsB;IAAF;EAAT,CAAD;EAAA,OAAyBA,IAAI,CAACpB,IAA9B;AAAA,CAAmC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EAAA,IAAC;IAAEF,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYY,MAAvC;AAAA,CAA8C;AAC7D;AACA;AACA,CAjBO"}, "metadata": {}, "sourceType": "module"}