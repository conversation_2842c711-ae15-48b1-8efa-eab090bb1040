{"ast": null, "code": "import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = function (url, data) {\n  let headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  return new Promise((resolve, reject) => {\n    const xhr = new XMLHttpRequest();\n    xhr.addEventListener('load', _ref => {\n      let {\n        target\n      } = _ref;\n      const responseStatus = new EmailJSResponseStatus(target);\n      if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n        resolve(responseStatus);\n      } else {\n        reject(responseStatus);\n      }\n    });\n    xhr.addEventListener('error', _ref2 => {\n      let {\n        target\n      } = _ref2;\n      reject(new EmailJSResponseStatus(target));\n    });\n    xhr.open('POST', store._origin + url, true);\n    Object.keys(headers).forEach(key => {\n      xhr.setRequestHeader(key, headers[key]);\n    });\n    xhr.send(data);\n  });\n};", "map": {"version": 3, "names": ["EmailJSResponseStatus", "store", "sendPost", "url", "data", "headers", "arguments", "length", "undefined", "Promise", "resolve", "reject", "xhr", "XMLHttpRequest", "addEventListener", "_ref", "target", "responseStatus", "status", "text", "_ref2", "open", "_origin", "Object", "keys", "for<PERSON>ach", "key", "setRequestHeader", "send"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@emailjs/browser/es/api/sendPost.js"], "sourcesContent": ["import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = (url, data, headers = {}) => {\n    return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest();\n        xhr.addEventListener('load', ({ target }) => {\n            const responseStatus = new EmailJSResponseStatus(target);\n            if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n                resolve(responseStatus);\n            }\n            else {\n                reject(responseStatus);\n            }\n        });\n        xhr.addEventListener('error', ({ target }) => {\n            reject(new EmailJSResponseStatus(target));\n        });\n        xhr.open('POST', store._origin + url, true);\n        Object.keys(headers).forEach((key) => {\n            xhr.setRequestHeader(key, headers[key]);\n        });\n        xhr.send(data);\n    });\n};\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,MAAMC,QAAQ,GAAG,SAAAA,CAACC,GAAG,EAAEC,IAAI,EAAmB;EAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC5C,OAAO,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,MAAMC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAChCD,GAAG,CAACE,gBAAgB,CAAC,MAAM,EAAEC,IAAA,IAAgB;MAAA,IAAf;QAAEC;MAAO,CAAC,GAAAD,IAAA;MACpC,MAAME,cAAc,GAAG,IAAIjB,qBAAqB,CAACgB,MAAM,CAAC;MACxD,IAAIC,cAAc,CAACC,MAAM,KAAK,GAAG,IAAID,cAAc,CAACE,IAAI,KAAK,IAAI,EAAE;QAC/DT,OAAO,CAACO,cAAc,CAAC;MAC3B,CAAC,MACI;QACDN,MAAM,CAACM,cAAc,CAAC;MAC1B;IACJ,CAAC,CAAC;IACFL,GAAG,CAACE,gBAAgB,CAAC,OAAO,EAAEM,KAAA,IAAgB;MAAA,IAAf;QAAEJ;MAAO,CAAC,GAAAI,KAAA;MACrCT,MAAM,CAAC,IAAIX,qBAAqB,CAACgB,MAAM,CAAC,CAAC;IAC7C,CAAC,CAAC;IACFJ,GAAG,CAACS,IAAI,CAAC,MAAM,EAAEpB,KAAK,CAACqB,OAAO,GAAGnB,GAAG,EAAE,IAAI,CAAC;IAC3CoB,MAAM,CAACC,IAAI,CAACnB,OAAO,CAAC,CAACoB,OAAO,CAAEC,GAAG,IAAK;MAClCd,GAAG,CAACe,gBAAgB,CAACD,GAAG,EAAErB,OAAO,CAACqB,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC;IACFd,GAAG,CAACgB,IAAI,CAACxB,IAAI,CAAC;EAClB,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}