{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCC,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,+CAAD,CAAxB;AACD,CAFD,MAEO;EACLF,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,4CAAD,CAAxB;AACD"}, "metadata": {}, "sourceType": "script"}