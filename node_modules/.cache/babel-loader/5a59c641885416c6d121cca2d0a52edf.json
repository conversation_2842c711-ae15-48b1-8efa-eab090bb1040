{"ast": null, "code": "var call = require('../internals/function-call');\n\nvar isObject = require('../internals/is-object');\n\nvar isSymbol = require('../internals/is-symbol');\n\nvar getMethod = require('../internals/get-method');\n\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\n\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar $TypeError = TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive'); // `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\n\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw $TypeError(\"Can't convert object to primitive value\");\n  }\n\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};", "map": {"version": 3, "names": ["call", "require", "isObject", "isSymbol", "getMethod", "ordinaryToPrimitive", "wellKnownSymbol", "$TypeError", "TypeError", "TO_PRIMITIVE", "module", "exports", "input", "pref", "exoticToPrim", "result", "undefined"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/to-primitive.js"], "sourcesContent": ["var call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar $TypeError = TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw $TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,4BAAD,CAAlB;;AACA,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAD,CAAtB;;AACA,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAD,CAAtB;;AACA,IAAIG,SAAS,GAAGH,OAAO,CAAC,yBAAD,CAAvB;;AACA,IAAII,mBAAmB,GAAGJ,OAAO,CAAC,oCAAD,CAAjC;;AACA,IAAIK,eAAe,GAAGL,OAAO,CAAC,gCAAD,CAA7B;;AAEA,IAAIM,UAAU,GAAGC,SAAjB;AACA,IAAIC,YAAY,GAAGH,eAAe,CAAC,aAAD,CAAlC,C,CAEA;AACA;;AACAI,MAAM,CAACC,OAAP,GAAiB,UAAUC,KAAV,EAAiBC,IAAjB,EAAuB;EACtC,IAAI,CAACX,QAAQ,CAACU,KAAD,CAAT,IAAoBT,QAAQ,CAACS,KAAD,CAAhC,EAAyC,OAAOA,KAAP;EACzC,IAAIE,YAAY,GAAGV,SAAS,CAACQ,KAAD,EAAQH,YAAR,CAA5B;EACA,IAAIM,MAAJ;;EACA,IAAID,YAAJ,EAAkB;IAChB,IAAID,IAAI,KAAKG,SAAb,EAAwBH,IAAI,GAAG,SAAP;IACxBE,MAAM,GAAGf,IAAI,CAACc,YAAD,EAAeF,KAAf,EAAsBC,IAAtB,CAAb;IACA,IAAI,CAACX,QAAQ,CAACa,MAAD,CAAT,IAAqBZ,QAAQ,CAACY,MAAD,CAAjC,EAA2C,OAAOA,MAAP;IAC3C,MAAMR,UAAU,CAAC,yCAAD,CAAhB;EACD;;EACD,IAAIM,IAAI,KAAKG,SAAb,EAAwBH,IAAI,GAAG,QAAP;EACxB,OAAOR,mBAAmB,CAACO,KAAD,EAAQC,IAAR,CAA1B;AACD,CAZD"}, "metadata": {}, "sourceType": "script"}