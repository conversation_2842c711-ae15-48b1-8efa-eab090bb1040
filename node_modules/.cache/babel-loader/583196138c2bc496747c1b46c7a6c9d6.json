{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/index.jsx\";\nimport React from \"react\";\nimport Carousel from \"react-multi-carousel\";\nimport \"react-multi-carousel/lib/styles.css\";\nimport SeactionHeading from \"../global/seaction-title\";\nimport * as Styles from \"./styles\";\nimport ClientItem from \"./item\";\nimport NameCard from \"./name-card\";\nimport { clientdata, industrialCommercial, residentialData, collegeData, publicTrustData, hotelData, mallsData, allClientsData } from \"./data\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst Clients = () => {\n  const responsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: {\n        max: 4000,\n        min: 3000\n      },\n      items: 4,\n      slidesToSlide: 4\n    },\n    desktop: {\n      breakpoint: {\n        max: 3000,\n        min: 1024\n      },\n      items: 4,\n      slidesToSlide: 4\n    },\n    tablet: {\n      breakpoint: {\n        max: 1024,\n        min: 664\n      },\n      items: 3,\n      slidesToSlide: 3\n    },\n    mobile: {\n      breakpoint: {\n        max: 664,\n        min: 0\n      },\n      items: 1,\n      slidesToSlide: 1\n    }\n  };\n  const allResponsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: {\n        max: 4000,\n        min: 3000\n      },\n      items: 3,\n      slidesToSlide: 3\n    },\n    desktop: {\n      breakpoint: {\n        max: 3000,\n        min: 1024\n      },\n      items: 2,\n      slidesToSlide: 2\n    },\n    tablet: {\n      breakpoint: {\n        max: 1024,\n        min: 664\n      },\n      items: 2,\n      slidesToSlide: 2\n    },\n    mobile: {\n      breakpoint: {\n        max: 664,\n        min: 0\n      },\n      items: 1,\n      slidesToSlide: 1\n    }\n  }; // const hey = residentialData.map((item) => item?.split(\"•\"));\n  // const hey2 = hey[0].map((item) => {\n  //   return { title: item.trim() };\n  // });\n  // console.log({ hey2 });\n\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: [/*#__PURE__*/_jsxDEV(Styles.ListHolder, {\n      children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n        title: \"Clients\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Carousel, {\n        responsive: responsive,\n        autoPlay: true,\n        infinite: true,\n        autoPlaySpeed: 6000,\n        slidesToSlide: 1,\n        transitionDuration: 30000,\n        children: clientdata.map((item, index) => {\n          return /*#__PURE__*/_jsxDEV(ClientItem, {\n            data: item\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 20\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.AllListHolder, {\n      children: allClientsData.map((item, index) => {\n        return /*#__PURE__*/_jsxDEV(NameCard, {\n          client: item\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 18\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n\n_c = Clients;\nexport default Clients;\n\nvar _c;\n\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "Carousel", "SeactionHeading", "Styles", "ClientItem", "NameCard", "clientdata", "industrialCommercial", "residentialData", "collegeData", "publicTrustData", "hotelData", "mallsData", "allClientsData", "Clients", "responsive", "superLargeDesktop", "breakpoint", "max", "min", "items", "slidesToSlide", "desktop", "tablet", "mobile", "allResponsive", "map", "item", "index"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport Carousel from \"react-multi-carousel\";\nimport \"react-multi-carousel/lib/styles.css\";\n\nimport SeactionHeading from \"../global/seaction-title\";\n\nimport * as Styles from \"./styles\";\n\nimport ClientItem from \"./item\";\nimport NameCard from \"./name-card\";\n\nimport {\n  clientdata,\n  industrialCommercial,\n  residentialData,\n  collegeData,\n  publicTrustData,\n  hotelData,\n  mallsData,\n  allClientsData,\n} from \"./data\";\n\nconst Clients = () => {\n  const responsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: { max: 4000, min: 3000 },\n      items: 4,\n      slidesToSlide: 4,\n    },\n    desktop: {\n      breakpoint: { max: 3000, min: 1024 },\n      items: 4,\n      slidesToSlide: 4,\n    },\n    tablet: {\n      breakpoint: { max: 1024, min: 664 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    mobile: {\n      breakpoint: { max: 664, min: 0 },\n      items: 1,\n      slidesToSlide: 1,\n    },\n  };\n\n  const allResponsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: { max: 4000, min: 3000 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    desktop: {\n      breakpoint: { max: 3000, min: 1024 },\n      items: 2,\n      slidesToSlide: 2,\n    },\n    tablet: {\n      breakpoint: { max: 1024, min: 664 },\n      items: 2,\n      slidesToSlide: 2,\n    },\n    mobile: {\n      breakpoint: { max: 664, min: 0 },\n      items: 1,\n      slidesToSlide: 1,\n    },\n  };\n\n  // const hey = residentialData.map((item) => item?.split(\"•\"));\n  // const hey2 = hey[0].map((item) => {\n  //   return { title: item.trim() };\n  // });\n\n  // console.log({ hey2 });\n\n  return (\n    <Styles.Container>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Clients\" />\n        <Carousel\n          responsive={responsive}\n          autoPlay\n          infinite\n          autoPlaySpeed={6000}\n          slidesToSlide={1}\n          transitionDuration={30000}\n        >\n          {clientdata.map((item, index) => {\n            return <ClientItem key={index} data={item} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.AllListHolder>\n        {allClientsData.map((item, index) => {\n          return <NameCard key={index} client={item} />;\n        })}\n      </Styles.AllListHolder>\n      {/* <Styles.ListHolder>\n        <SeactionHeading title=\"Residential\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {residentialData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"School / College\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {collegeData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Public Trust\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {publicTrustData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Hotels\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {hotelData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Malls\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {mallsData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder> */}\n    </Styles.Container>\n  );\n};\n\nexport default Clients;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,QAAP,MAAqB,sBAArB;AACA,OAAO,qCAAP;AAEA,OAAOC,eAAP,MAA4B,0BAA5B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;AAEA,OAAOC,UAAP,MAAuB,QAAvB;AACA,OAAOC,QAAP,MAAqB,aAArB;AAEA,SACEC,UADF,EAEEC,oBAFF,EAGEC,eAHF,EAIEC,WAJF,EAKEC,eALF,EAMEC,SANF,EAOEC,SAPF,EAQEC,cARF,QASO,QATP;;;AAWA,MAAMC,OAAO,GAAG,MAAM;EACpB,MAAMC,UAAU,GAAG;IACjBC,iBAAiB,EAAE;MACjB;MACAC,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CAFK;MAGjBC,KAAK,EAAE,CAHU;MAIjBC,aAAa,EAAE;IAJE,CADF;IAOjBC,OAAO,EAAE;MACPL,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CADL;MAEPC,KAAK,EAAE,CAFA;MAGPC,aAAa,EAAE;IAHR,CAPQ;IAYjBE,MAAM,EAAE;MACNN,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CADN;MAENC,KAAK,EAAE,CAFD;MAGNC,aAAa,EAAE;IAHT,CAZS;IAiBjBG,MAAM,EAAE;MACNP,UAAU,EAAE;QAAEC,GAAG,EAAE,GAAP;QAAYC,GAAG,EAAE;MAAjB,CADN;MAENC,KAAK,EAAE,CAFD;MAGNC,aAAa,EAAE;IAHT;EAjBS,CAAnB;EAwBA,MAAMI,aAAa,GAAG;IACpBT,iBAAiB,EAAE;MACjB;MACAC,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CAFK;MAGjBC,KAAK,EAAE,CAHU;MAIjBC,aAAa,EAAE;IAJE,CADC;IAOpBC,OAAO,EAAE;MACPL,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CADL;MAEPC,KAAK,EAAE,CAFA;MAGPC,aAAa,EAAE;IAHR,CAPW;IAYpBE,MAAM,EAAE;MACNN,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CADN;MAENC,KAAK,EAAE,CAFD;MAGNC,aAAa,EAAE;IAHT,CAZY;IAiBpBG,MAAM,EAAE;MACNP,UAAU,EAAE;QAAEC,GAAG,EAAE,GAAP;QAAYC,GAAG,EAAE;MAAjB,CADN;MAENC,KAAK,EAAE,CAFD;MAGNC,aAAa,EAAE;IAHT;EAjBY,CAAtB,CAzBoB,CAiDpB;EACA;EACA;EACA;EAEA;;EAEA,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;MAAA,wBACE,QAAC,eAAD;QAAiB,KAAK,EAAC;MAAvB;QAAA;QAAA;QAAA;MAAA,QADF,eAEE,QAAC,QAAD;QACE,UAAU,EAAEN,UADd;QAEE,QAAQ,MAFV;QAGE,QAAQ,MAHV;QAIE,aAAa,EAAE,IAJjB;QAKE,aAAa,EAAE,CALjB;QAME,kBAAkB,EAAE,KANtB;QAAA,UAQGT,UAAU,CAACoB,GAAX,CAAe,CAACC,IAAD,EAAOC,KAAP,KAAiB;UAC/B,oBAAO,QAAC,UAAD;YAAwB,IAAI,EAAED;UAA9B,GAAiBC,KAAjB;YAAA;YAAA;YAAA;UAAA,QAAP;QACD,CAFA;MARH;QAAA;QAAA;QAAA;MAAA,QAFF;IAAA;MAAA;MAAA;MAAA;IAAA,QADF,eAgBE,QAAC,MAAD,CAAQ,aAAR;MAAA,UACGf,cAAc,CAACa,GAAf,CAAmB,CAACC,IAAD,EAAOC,KAAP,KAAiB;QACnC,oBAAO,QAAC,QAAD;UAAsB,MAAM,EAAED;QAA9B,GAAeC,KAAf;UAAA;UAAA;UAAA;QAAA,QAAP;MACD,CAFA;IADH;MAAA;MAAA;MAAA;IAAA,QAhBF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAgED,CAxHD;;KAAMd,O;AA0HN,eAAeA,OAAf"}, "metadata": {}, "sourceType": "module"}