{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/index.jsx\";\nimport React from \"react\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst Seaction = _ref => {\n  let {\n    children,\n    flex\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Styles.Section, {\n    className: \"py-5\",\n    flex: flex,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n\n_c = Seaction;\nexport default Seaction;\n\nvar _c;\n\n$RefreshReg$(_c, \"Seaction\");", "map": {"version": 3, "names": ["React", "Styles", "Seaction", "children", "flex"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst Seaction = ({ children, flex }) => {\n  return (\n    <Styles.Section className=\"py-5\" flex={flex}>\n      {children}\n    </Styles.Section>\n  );\n};\n\nexport default Seaction;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,QAAQ,GAAG,QAAwB;EAAA,IAAvB;IAAEC,QAAF;IAAYC;EAAZ,CAAuB;EACvC,oBACE,QAAC,MAAD,CAAQ,OAAR;IAAgB,SAAS,EAAC,MAA1B;IAAiC,IAAI,EAAEA,IAAvC;IAAA,UACGD;EADH;IAAA;IAAA;IAAA;EAAA,QADF;AAKD,CAND;;KAAMD,Q;AAQN,eAAeA,QAAf"}, "metadata": {}, "sourceType": "module"}