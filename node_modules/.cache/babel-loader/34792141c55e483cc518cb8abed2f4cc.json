{"ast": null, "code": "var $ = require('../internals/export');\n\nvar global = require('../internals/global'); // `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n\n\n$({\n  global: true\n}, {\n  globalThis: global\n});", "map": {"version": 3, "names": ["$", "require", "global", "globalThis"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/modules/es.global-this.js"], "sourcesContent": ["var $ = require('../internals/export');\nvar global = require('../internals/global');\n\n// `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n$({ global: true }, {\n  globalThis: global\n});\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,qBAAD,CAAf;;AACA,IAAIC,MAAM,GAAGD,OAAO,CAAC,qBAAD,CAApB,C,CAEA;AACA;;;AACAD,CAAC,CAAC;EAAEE,MAAM,EAAE;AAAV,CAAD,EAAmB;EAClBC,UAAU,EAAED;AADM,CAAnB,CAAD"}, "metadata": {}, "sourceType": "script"}