{"ast": null, "code": "module.exports = require('../full/global-this');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/features/global-this.js"], "sourcesContent": ["module.exports = require('../full/global-this');\n"], "mappings": "AAAAA,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,qBAAD,CAAxB"}, "metadata": {}, "sourceType": "script"}