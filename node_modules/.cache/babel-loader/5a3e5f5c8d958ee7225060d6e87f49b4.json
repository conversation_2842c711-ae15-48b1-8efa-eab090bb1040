{"ast": null, "code": "var uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);", "map": {"version": 3, "names": ["uncurryThis", "require", "module", "exports", "isPrototypeOf"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/object-is-prototype-of.js"], "sourcesContent": ["var uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAD,CAAzB;;AAEAC,MAAM,CAACC,OAAP,GAAiBH,WAAW,CAAC,GAAGI,aAAJ,CAA5B"}, "metadata": {}, "sourceType": "script"}