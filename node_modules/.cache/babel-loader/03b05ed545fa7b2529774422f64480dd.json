{"ast": null, "code": "import styled from 'styled-components';\nexport const Container = styled.div`\n    max-width: 150px;\n    max-height: 80px;\n`;\nexport const ImageHolder = styled.img`\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "ImageHolder", "img"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Container = styled.div`\n    max-width: 150px;\n    max-height: 80px;\n`;\n\nexport const ImageHolder = styled.img`\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA,CAHO;AAKP,OAAO,MAAMC,WAAW,GAAGH,MAAM,CAACI,GAAI;AACtC;AACA;AACA;AACA,CAJO"}, "metadata": {}, "sourceType": "module"}