{"ast": null, "code": "var DESCRIPTORS = require('../internals/descriptors');\n\nvar fails = require('../internals/fails');\n\nvar createElement = require('../internals/document-create-element'); // Thanks to IE8 for its funny defineProperty\n\n\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es-x/no-object-defineproperty -- required for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () {\n      return 7;\n    }\n  }).a != 7;\n});", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "fails", "createElement", "module", "exports", "Object", "defineProperty", "get", "a"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/ie8-dom-define.js"], "sourcesContent": ["var DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thanks to IE8 for its funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es-x/no-object-defineproperty -- required for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a != 7;\n});\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAAD,CAAzB;;AACA,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAD,CAAnB;;AACA,IAAIE,aAAa,GAAGF,OAAO,CAAC,sCAAD,CAA3B,C,CAEA;;;AACAG,MAAM,CAACC,OAAP,GAAiB,CAACL,WAAD,IAAgB,CAACE,KAAK,CAAC,YAAY;EAClD;EACA,OAAOI,MAAM,CAACC,cAAP,CAAsBJ,aAAa,CAAC,KAAD,CAAnC,EAA4C,GAA5C,EAAiD;IACtDK,GAAG,EAAE,YAAY;MAAE,OAAO,CAAP;IAAW;EADwB,CAAjD,EAEJC,CAFI,IAEC,CAFR;AAGD,CALsC,CAAvC"}, "metadata": {}, "sourceType": "script"}