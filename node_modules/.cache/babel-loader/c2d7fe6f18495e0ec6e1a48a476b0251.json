{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/index.jsx\";\nimport React from 'react';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ServicesCard = _ref => {\n  let {\n    item\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: [/*#__PURE__*/_jsxDEV(Styles.ImageWrapper, {\n      className: \"image\",\n      children: /*#__PURE__*/_jsxDEV(Styles.IconHolder, {\n        src: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n      className: \"text\",\n      children: item.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n\n_c = ServicesCard;\nexport default ServicesCard;\n\nvar _c;\n\n$RefreshReg$(_c, \"ServicesCard\");", "map": {"version": 3, "names": ["React", "Styles", "ServicesCard", "item", "icon", "name"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport * as Styles from './styles'\n\nconst ServicesCard = ({ item }) => {\n    return (\n        <Styles.Container>\n            <Styles.ImageWrapper className='image'>\n                <Styles.IconHolder\n                    src={item.icon}\n                />\n            </Styles.ImageWrapper>\n            <Styles.TextHolder className='text'>\n                {item.name}\n            </Styles.TextHolder>\n        </Styles.Container>\n    )\n}\n\nexport default ServicesCard"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,YAAY,GAAG,QAAc;EAAA,IAAb;IAAEC;EAAF,CAAa;EAC/B,oBACI,QAAC,MAAD,CAAQ,SAAR;IAAA,wBACI,QAAC,MAAD,CAAQ,YAAR;MAAqB,SAAS,EAAC,OAA/B;MAAA,uBACI,QAAC,MAAD,CAAQ,UAAR;QACI,GAAG,EAAEA,IAAI,CAACC;MADd;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QADJ,eAMI,QAAC,MAAD,CAAQ,UAAR;MAAmB,SAAS,EAAC,MAA7B;MAAA,UACKD,IAAI,CAACE;IADV;MAAA;MAAA;MAAA;IAAA,QANJ;EAAA;IAAA;IAAA;IAAA;EAAA,QADJ;AAYH,CAbD;;KAAMH,Y;AAeN,eAAeA,YAAf"}, "metadata": {}, "sourceType": "module"}