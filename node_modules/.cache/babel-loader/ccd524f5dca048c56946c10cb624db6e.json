{"ast": null, "code": "import React from'react';import*as Styles from'./styles';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const ServicesCard=_ref=>{let{item}=_ref;return/*#__PURE__*/_jsxs(Styles.Container,{children:[/*#__PURE__*/_jsx(Styles.ImageWrapper,{className:\"image\",children:/*#__PURE__*/_jsx(Styles.IconHolder,{src:item.icon})}),/*#__PURE__*/_jsx(Styles.TextHolder,{className:\"text\",children:item.name})]});};export default ServicesCard;", "map": {"version": 3, "names": ["React", "Styles", "jsx", "_jsx", "jsxs", "_jsxs", "ServicesCard", "_ref", "item", "Container", "children", "ImageWrapper", "className", "IconHolder", "src", "icon", "TextHolder", "name"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport * as Styles from './styles'\n\nconst ServicesCard = ({ item }) => {\n    return (\n        <Styles.Container>\n            <Styles.ImageWrapper className='image'>\n                <Styles.IconHolder\n                    src={item.icon}\n                />\n            </Styles.ImageWrapper>\n            <Styles.TextHolder className='text'>\n                {item.name}\n            </Styles.TextHolder>\n        </Styles.Container>\n    )\n}\n\nexport default ServicesCard"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,MAAO,GAAK,CAAAC,MAAM,KAAM,UAAU,QAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAElC,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CAC1B,mBACIF,KAAA,CAACJ,MAAM,CAACQ,SAAS,EAAAC,QAAA,eACbP,IAAA,CAACF,MAAM,CAACU,YAAY,EAACC,SAAS,CAAC,OAAO,CAAAF,QAAA,cAClCP,IAAA,CAACF,MAAM,CAACY,UAAU,EACdC,GAAG,CAAEN,IAAI,CAACO,IAAK,CAClB,CAAC,CACe,CAAC,cACtBZ,IAAA,CAACF,MAAM,CAACe,UAAU,EAACJ,SAAS,CAAC,MAAM,CAAAF,QAAA,CAC9BF,IAAI,CAACS,IAAI,CACK,CAAC,EACN,CAAC,CAE3B,CAAC,CAED,cAAe,CAAAX,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}