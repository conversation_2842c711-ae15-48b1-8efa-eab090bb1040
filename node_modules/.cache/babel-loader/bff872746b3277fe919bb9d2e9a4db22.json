{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/index.jsx\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { AssetsList } from \"../../elements/assetsList\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst AppLogo = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    \"aria-label\": \"App Logo\",\n    className: \"logo\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/\",\n      children: /*#__PURE__*/_jsxDEV(Styles.ImageHolder, {\n        src: AssetsList.logo.LogoSvg\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n\n_c = AppLogo;\nexport default AppLogo;\n\nvar _c;\n\n$RefreshReg$(_c, \"AppLogo\");", "map": {"version": 3, "names": ["React", "Link", "AssetsList", "Styles", "AppLogo", "logo", "LogoSvg"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { Link } from \"react-router-dom\";\n\nimport { AssetsList } from \"../../elements/assetsList\";\n\nimport * as Styles from \"./styles\";\n\nconst AppLogo = () => {\n  return (\n    <Styles.Container aria-label=\"App Logo\" className=\"logo\">\n      <Link to=\"/\">\n        <Styles.ImageHolder src={AssetsList.logo.LogoSvg} />\n      </Link>\n    </Styles.Container>\n  );\n};\n\nexport default AppLogo;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,IAAT,QAAqB,kBAArB;AAEA,SAASC,UAAT,QAA2B,2BAA3B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,OAAO,GAAG,MAAM;EACpB,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAkB,cAAW,UAA7B;IAAwC,SAAS,EAAC,MAAlD;IAAA,uBACE,QAAC,IAAD;MAAM,EAAE,EAAC,GAAT;MAAA,uBACE,QAAC,MAAD,CAAQ,WAAR;QAAoB,GAAG,EAAEF,UAAU,CAACG,IAAX,CAAgBC;MAAzC;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAOD,CARD;;KAAMF,O;AAUN,eAAeA,OAAf"}, "metadata": {}, "sourceType": "module"}