{"ast": null, "code": "import React from\"react\";import{ArrowRightCircle}from\"react-feather\";import*as Styles from\"./styles\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const NameCard=_ref=>{var _client$data;let{client}=_ref;console.log({client});return/*#__PURE__*/_jsxs(Styles.Container,{children:[/*#__PURE__*/_jsxs(Styles.TitleWrapper,{className:\"title\",children:[/*#__PURE__*/_jsx(Styles.IconHolder,{children:client===null||client===void 0?void 0:client.icon}),/*#__PURE__*/_jsx(Styles.TextHolder,{children:client===null||client===void 0?void 0:client.heading})]}),/*#__PURE__*/_jsx(Styles.ListHolder,{children:client===null||client===void 0?void 0:(_client$data=client.data)===null||_client$data===void 0?void 0:_client$data.map((item,index)=>{return/*#__PURE__*/_jsxs(Styles.ListItme,{className:\"list-item\",children:[/*#__PURE__*/_jsx(ArrowRightCircle,{}),/*#__PURE__*/_jsx(Styles.TextHolder,{children:item===null||item===void 0?void 0:item.title})]},index);})})]});};export default NameCard;", "map": {"version": 3, "names": ["React", "ArrowRightCircle", "Styles", "jsx", "_jsx", "jsxs", "_jsxs", "NameCard", "_ref", "_client$data", "client", "console", "log", "Container", "children", "TitleWrapper", "className", "IconHolder", "icon", "TextHolder", "heading", "ListHolder", "data", "map", "item", "index", "ListItme", "title"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { ArrowRightCircle } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst NameCard = ({ client }) => {\n  console.log({ client });\n  return (\n    <Styles.Container>\n      <Styles.TitleWrapper className=\"title\">\n        <Styles.IconHolder>{client?.icon}</Styles.IconHolder>\n        <Styles.TextHolder>{client?.heading}</Styles.TextHolder>\n      </Styles.TitleWrapper>\n      <Styles.ListHolder>\n        {client?.data?.map((item, index) => {\n          return (\n            <Styles.ListItme className=\"list-item\" key={index}>\n              <ArrowRightCircle />\n              <Styles.TextHolder>{item?.title}</Styles.TextHolder>\n            </Styles.ListItme>\n          );\n        })}\n      </Styles.ListHolder>\n    </Styles.Container>\n  );\n};\n\nexport default NameCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,OAASC,gBAAgB,KAAQ,eAAe,CAEhD,MAAO,GAAK,CAAAC,MAAM,KAAM,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEnC,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAgB,KAAAC,YAAA,IAAf,CAAEC,MAAO,CAAC,CAAAF,IAAA,CAC1BG,OAAO,CAACC,GAAG,CAAC,CAAEF,MAAO,CAAC,CAAC,CACvB,mBACEJ,KAAA,CAACJ,MAAM,CAACW,SAAS,EAAAC,QAAA,eACfR,KAAA,CAACJ,MAAM,CAACa,YAAY,EAACC,SAAS,CAAC,OAAO,CAAAF,QAAA,eACpCV,IAAA,CAACF,MAAM,CAACe,UAAU,EAAAH,QAAA,CAAEJ,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEQ,IAAI,CAAoB,CAAC,cACrDd,IAAA,CAACF,MAAM,CAACiB,UAAU,EAAAL,QAAA,CAAEJ,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEU,OAAO,CAAoB,CAAC,EACrC,CAAC,cACtBhB,IAAA,CAACF,MAAM,CAACmB,UAAU,EAAAP,QAAA,CACfJ,MAAM,SAANA,MAAM,kBAAAD,YAAA,CAANC,MAAM,CAAEY,IAAI,UAAAb,YAAA,iBAAZA,YAAA,CAAcc,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CAClC,mBACEnB,KAAA,CAACJ,MAAM,CAACwB,QAAQ,EAACV,SAAS,CAAC,WAAW,CAAAF,QAAA,eACpCV,IAAA,CAACH,gBAAgB,GAAE,CAAC,cACpBG,IAAA,CAACF,MAAM,CAACiB,UAAU,EAAAL,QAAA,CAAEU,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEG,KAAK,CAAoB,CAAC,GAFVF,KAG3B,CAAC,CAEtB,CAAC,CAAC,CACe,CAAC,EACJ,CAAC,CAEvB,CAAC,CAED,cAAe,CAAAlB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}