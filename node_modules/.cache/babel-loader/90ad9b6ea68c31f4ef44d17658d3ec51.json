{"ast": null, "code": "import styled from 'styled-components';\nexport const Container = styled.div`\n    display: flex;\n    align-items: center;\n`;\nexport const ImageHolder = styled.img`\n    width: 160px;\n    height: 160px;\n    object-fit: contain;\n`;\nexport const TextWrapper = styled.div`\n    margin-left: 20px;\n`;\nexport const TextHolder = styled.p`\n    font-size: ${_ref => {\n  let {\n    heading,\n    theme: {\n      font\n    }\n  } = _ref;\n  return heading ? font.big : font.title;\n}};\n    font-weight: bold;\n    color: #fff;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "ImageHolder", "img", "TextWrapper", "TextHolder", "p", "heading", "theme", "font", "big", "title"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Container = styled.div`\n    display: flex;\n    align-items: center;\n`;\n\nexport const ImageHolder = styled.img`\n    width: 160px;\n    height: 160px;\n    object-fit: contain;\n`;\n\nexport const TextWrapper = styled.div`\n    margin-left: 20px;\n`;\n\nexport const TextHolder = styled.p`\n    font-size: ${({ heading, theme: { font } }) => heading ? font.big : font.title};\n    font-weight: bold;\n    color: #fff;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA,CAHO;AAKP,OAAO,MAAMC,WAAW,GAAGH,MAAM,CAACI,GAAI;AACtC;AACA;AACA;AACA,CAJO;AAMP,OAAO,MAAMC,WAAW,GAAGL,MAAM,CAACE,GAAI;AACtC;AACA,CAFO;AAIP,OAAO,MAAMI,UAAU,GAAGN,MAAM,CAACO,CAAE;AACnC,iBAAiB;EAAA,IAAC;IAAEC,OAAF;IAAWC,KAAK,EAAE;MAAEC;IAAF;EAAlB,CAAD;EAAA,OAAkCF,OAAO,GAAGE,IAAI,CAACC,GAAR,GAAcD,IAAI,CAACE,KAA5D;AAAA,CAAkE;AACnF;AACA;AACA,CAJO"}, "metadata": {}, "sourceType": "module"}