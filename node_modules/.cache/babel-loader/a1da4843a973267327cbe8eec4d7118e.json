{"ast": null, "code": "/**\n * React Router v6.3.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { parsePath, createMemoryHistory, Action } from 'history';\nexport { Action as NavigationType, createPath, parsePath } from 'history';\nimport { createContext, useContext, useMemo, useRef, useEffect, useCallback, createElement, useState, useLayoutEffect, Children, isValidElement, Fragment } from 'react';\nconst NavigationContext = /*#__PURE__*/createContext(null);\n\nif (process.env.NODE_ENV !== \"production\") {\n  NavigationContext.displayName = \"Navigation\";\n}\n\nconst LocationContext = /*#__PURE__*/createContext(null);\n\nif (process.env.NODE_ENV !== \"production\") {\n  LocationContext.displayName = \"Location\";\n}\n\nconst RouteContext = /*#__PURE__*/createContext({\n  outlet: null,\n  matches: []\n});\n\nif (process.env.NODE_ENV !== \"production\") {\n  RouteContext.displayName = \"Route\";\n}\n\nfunction invariant(cond, message) {\n  if (!cond) throw new Error(message);\n}\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nconst alreadyWarned = {};\n\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    process.env.NODE_ENV !== \"production\" ? warning(false, message) : void 0;\n  }\n}\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/docs/en/v6/api#generatepath\n */\n\n\nfunction generatePath(path, params) {\n  if (params === void 0) {\n    params = {};\n  }\n\n  return path.replace(/:(\\w+)/g, (_, key) => {\n    !(params[key] != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Missing \\\":\" + key + \"\\\" param\") : invariant(false) : void 0;\n    return params[key];\n  }).replace(/\\/*\\*$/, _ => params[\"*\"] == null ? \"\" : params[\"*\"].replace(/^\\/*/, \"/\"));\n}\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/docs/en/v6/api#matchroutes\n */\n\n\nfunction matchRoutes(routes, locationArg, basename) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch(branches[i], pathname);\n  }\n\n  return matches;\n}\n\nfunction flattenRoutes(routes, branches, parentsMeta, parentPath) {\n  if (branches === void 0) {\n    branches = [];\n  }\n\n  if (parentsMeta === void 0) {\n    parentsMeta = [];\n  }\n\n  if (parentPath === void 0) {\n    parentPath = \"\";\n  }\n\n  routes.forEach((route, index) => {\n    let meta = {\n      relativePath: route.path || \"\",\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      !meta.relativePath.startsWith(parentPath) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Absolute route path \\\"\" + meta.relativePath + \"\\\" nested under path \" + (\"\\\"\" + parentPath + \"\\\" is not valid. An absolute child route path \") + \"must start with the combined path of all its parent routes.\") : invariant(false) : void 0;\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta); // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n\n    if (route.children && route.children.length > 0) {\n      !(route.index !== true) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Index routes must not have child routes. Please remove \" + (\"all child routes from route path \\\"\" + path + \"\\\".\")) : invariant(false) : void 0;\n      flattenRoutes(route.children, branches, routesMeta, path);\n    } // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n\n\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  });\n  return branches;\n}\n\nfunction rankRouteBranches(branches) {\n  branches.sort((a, b) => a.score !== b.score ? b.score - a.score // Higher score first\n  : compareIndexes(a.routesMeta.map(meta => meta.childrenIndex), b.routesMeta.map(meta => meta.childrenIndex)));\n}\n\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\n\nconst isSplat = s => s === \"*\";\n\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments.filter(s => !isSplat(s)).reduce((score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue), initialScore);\n}\n\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ? // If two routes are siblings, we should try to match the earlier sibling\n  // first. This allows people to have fine-grained control over the matching\n  // behavior by simply putting routes with identical paths in the order they\n  // want them tried.\n  a[a.length - 1] - b[b.length - 1] : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n  // so they sort equally.\n  0;\n}\n\nfunction matchRouteBranch(branch, pathname) {\n  let {\n    routesMeta\n  } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath({\n      path: meta.relativePath,\n      caseSensitive: meta.caseSensitive,\n      end\n    }, remainingPathname);\n    if (!match) return null;\n    Object.assign(matchedParams, match.params);\n    let route = meta.route;\n    matches.push({\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(joinPaths([matchedPathname, match.pathnameBase])),\n      route\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/docs/en/v6/api#matchpath\n */\n\n\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = {\n      path: pattern,\n      caseSensitive: false,\n      end: true\n    };\n  }\n\n  let [matcher, paramNames] = compilePath(pattern.path, pattern.caseSensitive, pattern.end);\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = paramNames.reduce((memo, paramName, index) => {\n    // We need to compute the pathnameBase here using the raw splat value\n    // instead of using params[\"*\"] later because it will be decoded then\n    if (paramName === \"*\") {\n      let splatValue = captureGroups[index] || \"\";\n      pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n    }\n\n    memo[paramName] = safelyDecodeURIComponent(captureGroups[index] || \"\", paramName);\n    return memo;\n  }, {});\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\n\nfunction compilePath(path, caseSensitive, end) {\n  if (caseSensitive === void 0) {\n    caseSensitive = false;\n  }\n\n  if (end === void 0) {\n    end = true;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"), \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\")) : void 0;\n  let paramNames = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n  .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n  .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n  .replace(/:(\\w+)/g, (_, paramName) => {\n    paramNames.push(paramName);\n    return \"([^\\\\/]+)\";\n  });\n\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" // Already matched the initial /, just match the rest\n    : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else {\n    regexpSource += end ? \"\\\\/*$\" // When matching to the end, ignore trailing slashes\n    : // Otherwise, match a word boundary or a proceeding /. The word boundary restricts\n    // parent routes to matching only their own words and nothing more, e.g. parent\n    // route \"/home\" should not match \"/home2\".\n    // Additionally, allow paths starting with `.`, `-`, `~`, and url-encoded entities,\n    // but do not consume the character in the matched path so they can match against\n    // nested paths.\n    \"(?:(?=[.~-]|%[0-9A-F]{2})|\\\\b|\\\\/|$)\";\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n  return [matcher, paramNames];\n}\n\nfunction safelyDecodeURIComponent(value, paramName) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    process.env.NODE_ENV !== \"production\" ? warning(false, \"The value for the URL param \\\"\" + paramName + \"\\\" will not be decoded because\" + (\" the string \\\"\" + value + \"\\\" is a malformed URL segment. This is probably\") + (\" due to a bad percent encoding (\" + error + \").\")) : void 0;\n    return value;\n  }\n}\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/docs/en/v6/api#resolvepath\n */\n\n\nfunction resolvePath(to, fromPathname) {\n  if (fromPathname === void 0) {\n    fromPathname = \"/\";\n  }\n\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\n\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach(segment => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction resolveTo(toArg, routePathnames, locationPathname) {\n  let to = typeof toArg === \"string\" ? parsePath(toArg) : toArg;\n  let toPathname = toArg === \"\" || to.pathname === \"\" ? \"/\" : to.pathname; // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n\n  let from;\n\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\"); // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    } // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from); // Ensure the pathname has a trailing slash if the original to value had one.\n\n  if (toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\") && !path.pathname.endsWith(\"/\")) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\nfunction getToPathname(to) {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || to.pathname === \"\" ? \"/\" : typeof to === \"string\" ? parsePath(to).pathname : to.pathname;\n}\n\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  let nextChar = pathname.charAt(basename.length);\n\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(basename.length) || \"/\";\n}\n\nconst joinPaths = paths => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\nconst normalizePathname = pathname => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\nconst normalizeSearch = search => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\n\nconst normalizeHash = hash => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usehref\n */\n\n\nfunction useHref(to) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useHref() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = useContext(NavigationContext);\n  let {\n    hash,\n    pathname,\n    search\n  } = useResolvedPath(to);\n  let joinedPathname = pathname;\n\n  if (basename !== \"/\") {\n    let toPathname = getToPathname(to);\n    let endsWithSlash = toPathname != null && toPathname.endsWith(\"/\");\n    joinedPathname = pathname === \"/\" ? basename + (endsWithSlash ? \"/\" : \"\") : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({\n    pathname: joinedPathname,\n    search,\n    hash\n  });\n}\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useinroutercontext\n */\n\n\nfunction useInRouterContext() {\n  return useContext(LocationContext) != null;\n}\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/docs/en/v6/api#uselocation\n */\n\n\nfunction useLocation() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useLocation() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  return useContext(LocationContext).location;\n}\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usenavigationtype\n */\n\n\nfunction useNavigationType() {\n  return useContext(LocationContext).navigationType;\n}\n/**\n * Returns true if the URL for the given \"to\" value matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usematch\n */\n\n\nfunction useMatch(pattern) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useMatch() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    pathname\n  } = useLocation();\n  return useMemo(() => matchPath(pattern, pathname), [pathname, pattern]);\n}\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usenavigate\n */\n\n\nfunction useNavigate() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useNavigate() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = useContext(NavigationContext);\n  let {\n    matches\n  } = useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(matches.map(match => match.pathnameBase));\n  let activeRef = useRef(false);\n  useEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(activeRef.current, \"You should call navigate() in a React.useEffect(), not when \" + \"your component is first rendered.\") : void 0;\n    if (!activeRef.current) return;\n\n    if (typeof to === \"number\") {\n      navigator.go(to);\n      return;\n    }\n\n    let path = resolveTo(to, JSON.parse(routePathnamesJson), locationPathname);\n\n    if (basename !== \"/\") {\n      path.pathname = joinPaths([basename, path.pathname]);\n    }\n\n    (!!options.replace ? navigator.replace : navigator.push)(path, options.state);\n  }, [basename, navigator, routePathnamesJson, locationPathname]);\n  return navigate;\n}\n\nconst OutletContext = /*#__PURE__*/createContext(null);\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/docs/en/v6/api#useoutletcontext\n */\n\nfunction useOutletContext() {\n  return useContext(OutletContext);\n}\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useoutlet\n */\n\n\nfunction useOutlet(context) {\n  let outlet = useContext(RouteContext).outlet;\n\n  if (outlet) {\n    return /*#__PURE__*/createElement(OutletContext.Provider, {\n      value: context\n    }, outlet);\n  }\n\n  return outlet;\n}\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useparams\n */\n\n\nfunction useParams() {\n  let {\n    matches\n  } = useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useresolvedpath\n */\n\n\nfunction useResolvedPath(to) {\n  let {\n    matches\n  } = useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(matches.map(match => match.pathnameBase));\n  return useMemo(() => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname), [to, routePathnamesJson, locationPathname]);\n}\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useroutes\n */\n\n\nfunction useRoutes(routes, locationArg) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useRoutes() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    matches: parentMatches\n  } = useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(parentPathname, !parentRoute || parentPath.endsWith(\"*\"), \"You rendered descendant <Routes> (or called `useRoutes()`) at \" + (\"\\\"\" + parentPathname + \"\\\" (under <Route path=\\\"\" + parentPath + \"\\\">) but the \") + \"parent route path has no trailing \\\"*\\\". This means if you navigate \" + \"deeper, the parent won't match anymore and therefore the child \" + \"routes will never render.\\n\\n\" + (\"Please change the parent <Route path=\\\"\" + parentPath + \"\\\"> to <Route \") + (\"path=\\\"\" + (parentPath === \"/\" ? \"*\" : parentPath + \"/*\") + \"\\\">.\"));\n  }\n\n  let locationFromContext = useLocation();\n  let location;\n\n  if (locationArg) {\n    var _parsedLocationArg$pa;\n\n    let parsedLocationArg = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n    !(parentPathnameBase === \"/\" || ((_parsedLocationArg$pa = parsedLocationArg.pathname) == null ? void 0 : _parsedLocationArg$pa.startsWith(parentPathnameBase))) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, \" + \"the location pathname must begin with the portion of the URL pathname that was \" + (\"matched by all parent routes. The current pathname base is \\\"\" + parentPathnameBase + \"\\\" \") + (\"but pathname \\\"\" + parsedLocationArg.pathname + \"\\\" was given in the `location` prop.\")) : invariant(false) : void 0;\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = parentPathnameBase === \"/\" ? pathname : pathname.slice(parentPathnameBase.length) || \"/\";\n  let matches = matchRoutes(routes, {\n    pathname: remainingPathname\n  });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    process.env.NODE_ENV !== \"production\" ? warning(parentRoute || matches != null, \"No routes matched location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(matches == null || matches[matches.length - 1].route.element !== undefined, \"Matched leaf route at location \\\"\" + location.pathname + location.search + location.hash + \"\\\" does not have an element. \" + \"This means it will render an <Outlet /> with a null value by default resulting in an \\\"empty\\\" page.\") : void 0;\n  }\n\n  return _renderMatches(matches && matches.map(match => Object.assign({}, match, {\n    params: Object.assign({}, parentParams, match.params),\n    pathname: joinPaths([parentPathnameBase, match.pathname]),\n    pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : joinPaths([parentPathnameBase, match.pathnameBase])\n  })), parentMatches);\n}\n\nfunction _renderMatches(matches, parentMatches) {\n  if (parentMatches === void 0) {\n    parentMatches = [];\n  }\n\n  if (matches == null) return null;\n  return matches.reduceRight((outlet, match, index) => {\n    return /*#__PURE__*/createElement(RouteContext.Provider, {\n      children: match.route.element !== undefined ? match.route.element : outlet,\n      value: {\n        outlet,\n        matches: parentMatches.concat(matches.slice(0, index + 1))\n      }\n    });\n  }, null);\n}\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/docs/en/v6/api#memoryrouter\n */\n\n\nfunction MemoryRouter(_ref) {\n  let {\n    basename,\n    children,\n    initialEntries,\n    initialIndex\n  } = _ref;\n  let historyRef = useRef();\n\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = useState({\n    action: history.action,\n    location: history.location\n  });\n  useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/docs/en/v6/api#navigate\n */\n\n\nfunction Navigate(_ref2) {\n  let {\n    to,\n    replace,\n    state\n  } = _ref2;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of\n  // the router loaded. We can help them understand how to avoid that.\n  \"<Navigate> may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!useContext(NavigationContext).static, \"<Navigate> must not be used on the initial render in a <StaticRouter>. \" + \"This is a no-op, but you should modify your code so the <Navigate> is \" + \"only ever rendered in response to some user interaction or state change.\") : void 0;\n  let navigate = useNavigate();\n  useEffect(() => {\n    navigate(to, {\n      replace,\n      state\n    });\n  });\n  return null;\n}\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/docs/en/v6/api#outlet\n */\n\n\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/docs/en/v6/api#route\n */\n\n\nfunction Route(_props) {\n  process.env.NODE_ENV !== \"production\" ? invariant(false, \"A <Route> is only ever to be used as the child of <Routes> element, \" + \"never rendered directly. Please wrap your <Route> in a <Routes>.\") : invariant(false);\n}\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/docs/en/v6/api#router\n */\n\n\nfunction Router(_ref3) {\n  let {\n    basename: basenameProp = \"/\",\n    children = null,\n    location: locationProp,\n    navigationType = Action.Pop,\n    navigator,\n    static: staticProp = false\n  } = _ref3;\n  !!useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You cannot render a <Router> inside another <Router>.\" + \" You should never have more than one in your app.\") : invariant(false) : void 0;\n  let basename = normalizePathname(basenameProp);\n  let navigationContext = useMemo(() => ({\n    basename,\n    navigator,\n    static: staticProp\n  }), [basename, navigator, staticProp]);\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let location = useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key\n    };\n  }, [basename, pathname, search, hash, state, key]);\n  process.env.NODE_ENV !== \"production\" ? warning(location != null, \"<Router basename=\\\"\" + basename + \"\\\"> is not able to match the URL \" + (\"\\\"\" + pathname + search + hash + \"\\\" because it does not start with the \") + \"basename, so the <Router> won't render anything.\") : void 0;\n\n  if (location == null) {\n    return null;\n  }\n\n  return /*#__PURE__*/createElement(NavigationContext.Provider, {\n    value: navigationContext\n  }, /*#__PURE__*/createElement(LocationContext.Provider, {\n    children: children,\n    value: {\n      location,\n      navigationType\n    }\n  }));\n}\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#routes\n */\n\n\nfunction Routes(_ref4) {\n  let {\n    children,\n    location\n  } = _ref4;\n  return useRoutes(createRoutesFromChildren(children), location);\n} ///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/docs/en/v6/api#createroutesfromchildren\n */\n\n\nfunction createRoutesFromChildren(children) {\n  let routes = [];\n  Children.forEach(children, element => {\n    if (! /*#__PURE__*/isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    if (element.type === Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(routes, createRoutesFromChildren(element.props.children));\n      return;\n    }\n\n    !(element.type === Route) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"[\" + (typeof element.type === \"string\" ? element.type : element.type.name) + \"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>\") : invariant(false) : void 0;\n    let route = {\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children);\n    }\n\n    routes.push(route);\n  });\n  return routes;\n}\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\n\n\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\n\nexport { MemoryRouter, Navigate, Outlet, Route, Router, Routes, LocationContext as UNSAFE_LocationContext, NavigationContext as UNSAFE_NavigationContext, RouteContext as UNSAFE_RouteContext, createRoutesFromChildren, generatePath, matchPath, matchRoutes, renderMatches, resolvePath, useHref, useInRouterContext, useLocation, useMatch, useNavigate, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;MAuBaA,iBAAiB,gBAAGC,cAC/B,IAD+B;;AAIjC,2CAAa;EACXD,iBAAiB,CAACE,WAAlBF,GAAgC,YAAhCA;AACD;;MAOYG,eAAe,gBAAGF,cAC7B,IAD6B;;AAI/B,2CAAa;EACXE,eAAe,CAACD,WAAhBC,GAA8B,UAA9BA;AACD;;MAOYC,YAAY,gBAAGH,cAAwC;EAClEI,MAAM,EAAE,IAD0D;EAElEC,OAAO,EAAE;AAFyD,CAAxC;;AAK5B,2CAAa;EACXF,YAAY,CAACF,WAAbE,GAA2B,OAA3BA;AACD;;ACrDM,SAASG,SAAT,CAAmBC,IAAnB,EAA8BC,OAA9B,EAA6D;EAClE,IAAI,CAACD,IAAL,EAAW,MAAM,IAAIE,KAAJ,CAAUD,OAAV,CAAN;AACZ;;AAED,SAAgBE,OAAhB,CAAwBH,IAAxB,EAAmCC,OAAnC,EAA0D;EACxD,IAAI,CAACD,IAAL,EAAW;IACT;IACA,IAAI,OAAOI,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAARD,CAAaH,OAAbG;;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIF,KAAJ,CAAUD,OAAV,CAAN,CANE;IAAJ,EAQE,OAAOK,CAAP,EAAU;EACb;AACF;;AAED,MAAMC,aAAsC,GAAG,EAA/C;;AACA,SAAgBC,WAAhB,CAA4BC,GAA5B,EAAyCT,IAAzC,EAAwDC,OAAxD,EAAyE;EACvE,IAAI,CAACD,IAAD,IAAS,CAACO,aAAa,CAACE,GAAD,CAA3B,EAAkC;IAChCF,aAAa,CAACE,GAAD,CAAbF,GAAqB,IAArBA;IACAG,+CAAO,CAAC,KAAD,EAAQT,OAAR,CAAP;EACD;AACF;AAmED;AACA;AACA;AACA;AACA;;;AACA,SAAgBU,YAAhB,CAA6BC,IAA7B,EAA2CC,MAA3C,EAAwE;EAAA,IAA7BA,MAA6B;IAA7BA,MAA6B,GAAZ,EAAjBA;EAA6B;;EACtE,OAAOD,IAAI,CACRE,OADIF,CACI,SADJA,EACe,CAACG,CAAD,EAAIN,GAAJ,KAAY;IAC9B,EAAUI,MAAM,CAACJ,GAAD,CAANI,IAAe,IAAzB,qDAAS,wBAAmCJ,GAAnC,cAAT,YAAS,OAAT;IACA,OAAOI,MAAM,CAACJ,GAAD,CAAb;EAHG,GAKJK,OALIF,CAKI,QALJA,EAKeG,CAAD,IACjBF,MAAM,CAAC,GAAD,CAANA,IAAe,IAAfA,GAAsB,EAAtBA,GAA2BA,MAAM,CAAC,GAAD,CAANA,CAAYC,OAAZD,CAAoB,MAApBA,EAA4B,GAA5BA,CANxBD,CAAP;AAQD;AAED;AACA;AACA;;AAoBA;AACA;AACA;AACA;AACA;;;AACA,SAAgBI,WAAhB,CACEC,MADF,EAEEC,WAFF,EAGEC,QAHF,EAIuB;EAAA,IADrBA,QACqB;IADrBA,QACqB,GADV,GAAXA;EACqB;;EACrB,IAAIC,QAAQ,GACV,OAAOF,WAAP,KAAuB,QAAvB,GAAkCG,SAAS,CAACH,WAAD,CAA3C,GAA2DA,WAD7D;EAGA,IAAII,QAAQ,GAAGC,aAAa,CAACH,QAAQ,CAACE,QAATF,IAAqB,GAAtB,EAA2BD,QAA3B,CAA5B;;EAEA,IAAIG,QAAQ,IAAI,IAAhB,EAAsB;IACpB,OAAO,IAAP;EACD;;EAED,IAAIE,QAAQ,GAAGC,aAAa,CAACR,MAAD,CAA5B;EACAS,iBAAiB,CAACF,QAAD,CAAjBE;EAEA,IAAI5B,OAAO,GAAG,IAAd;;EACA,KAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgB7B,OAAO,IAAI,IAAXA,IAAmB6B,CAAC,GAAGH,QAAQ,CAACI,MAAhD,EAAwD,EAAED,CAA1D,EAA6D;IAC3D7B,OAAO,GAAG+B,gBAAgB,CAACL,QAAQ,CAACG,CAAD,CAAT,EAAcL,QAAd,CAA1BxB;EACD;;EAED,OAAOA,OAAP;AACD;;AAeD,SAAS2B,aAAT,CACER,MADF,EAEEO,QAFF,EAGEM,WAHF,EAIEC,UAJF,EAKiB;EAAA,IAHfP,QAGe;IAHfA,QAGe,GAHW,EAA1BA;EAGe;;EAAA,IAFfM,WAEe;IAFfA,WAEe,GAFY,EAA3BA;EAEe;;EAAA,IADfC,UACe;IADfA,UACe,GADF,EAAbA;EACe;;EACfd,MAAM,CAACe,OAAPf,CAAe,CAACgB,KAAD,EAAQC,KAAR,KAAkB;IAC/B,IAAIC,IAAe,GAAG;MACpBC,YAAY,EAAEH,KAAK,CAACrB,IAANqB,IAAc,EADR;MAEpBI,aAAa,EAAEJ,KAAK,CAACI,aAANJ,KAAwB,IAFnB;MAGpBK,aAAa,EAAEJ,KAHK;MAIpBD;IAJoB,CAAtB;;IAOA,IAAIE,IAAI,CAACC,YAALD,CAAkBI,UAAlBJ,CAA6B,GAA7BA,CAAJ,EAAuC;MACrC,CACEA,IAAI,CAACC,YAALD,CAAkBI,UAAlBJ,CAA6BJ,UAA7BI,CADF,oDAAS,QAEP,2BAAwBA,IAAI,CAACC,YAA7B,qCACML,UADN,oHAFO,CAAT,YAAS,OAAT;MAOAI,IAAI,CAACC,YAALD,GAAoBA,IAAI,CAACC,YAALD,CAAkBK,KAAlBL,CAAwBJ,UAAU,CAACH,MAAnCO,CAApBA;IACD;;IAED,IAAIvB,IAAI,GAAG6B,SAAS,CAAC,CAACV,UAAD,EAAaI,IAAI,CAACC,YAAlB,CAAD,CAApB;IACA,IAAIM,UAAU,GAAGZ,WAAW,CAACa,MAAZb,CAAmBK,IAAnBL,CAAjB,CApB+B;IAuB/B;IACA;;IACA,IAAIG,KAAK,CAACW,QAANX,IAAkBA,KAAK,CAACW,QAANX,CAAeL,MAAfK,GAAwB,CAA9C,EAAiD;MAC/C,EACEA,KAAK,CAACC,KAAND,KAAgB,IADlB,qDAAS,QAEP,qGACuCrB,IADvC,SAFO,CAAT,YAAS,OAAT;MAMAa,aAAa,CAACQ,KAAK,CAACW,QAAP,EAAiBpB,QAAjB,EAA2BkB,UAA3B,EAAuC9B,IAAvC,CAAba;IAhC6B;IAoC/B;;;IACA,IAAIQ,KAAK,CAACrB,IAANqB,IAAc,IAAdA,IAAsB,CAACA,KAAK,CAACC,KAAjC,EAAwC;MACtC;IACD;;IAEDV,QAAQ,CAACqB,IAATrB,CAAc;MAAEZ,IAAF;MAAQkC,KAAK,EAAEC,YAAY,CAACnC,IAAD,EAAOqB,KAAK,CAACC,KAAb,CAA3B;MAAgDQ;IAAhD,CAAdlB;EAzCF;EA4CA,OAAOA,QAAP;AACD;;AAED,SAASE,iBAAT,CAA2BF,QAA3B,EAA0D;EACxDA,QAAQ,CAACwB,IAATxB,CAAc,CAACyB,CAAD,EAAIC,CAAJ,KACZD,CAAC,CAACH,KAAFG,KAAYC,CAAC,CAACJ,KAAdG,GACIC,CAAC,CAACJ,KAAFI,GAAUD,CAAC,CAACH,KADhBG;EAAAA,EAEIE,cAAc,CACZF,CAAC,CAACP,UAAFO,CAAaG,GAAbH,CAAkBd,IAAD,IAAUA,IAAI,CAACG,aAAhCW,CADY,EAEZC,CAAC,CAACR,UAAFQ,CAAaE,GAAbF,CAAkBf,IAAD,IAAUA,IAAI,CAACG,aAAhCY,CAFY,CAHpB1B;AAQD;;AAED,MAAM6B,OAAO,GAAG,QAAhB;AACA,MAAMC,mBAAmB,GAAG,CAA5B;AACA,MAAMC,eAAe,GAAG,CAAxB;AACA,MAAMC,iBAAiB,GAAG,CAA1B;AACA,MAAMC,kBAAkB,GAAG,EAA3B;AACA,MAAMC,YAAY,GAAG,CAAC,CAAtB;;AACA,MAAMC,OAAO,GAAIC,CAAD,IAAeA,CAAC,KAAK,GAArC;;AAEA,SAASb,YAAT,CAAsBnC,IAAtB,EAAoCsB,KAApC,EAAwE;EACtE,IAAI2B,QAAQ,GAAGjD,IAAI,CAACkD,KAALlD,CAAW,GAAXA,CAAf;EACA,IAAImD,YAAY,GAAGF,QAAQ,CAACjC,MAA5B;;EACA,IAAIiC,QAAQ,CAACG,IAATH,CAAcF,OAAdE,CAAJ,EAA4B;IAC1BE,YAAY,IAAIL,YAAhBK;EACD;;EAED,IAAI7B,KAAJ,EAAW;IACT6B,YAAY,IAAIR,eAAhBQ;EACD;;EAED,OAAOF,QAAQ,CACZI,MADIJ,CACID,CAAD,IAAO,CAACD,OAAO,CAACC,CAAD,CADlBC,EAEJK,MAFIL,CAGH,CAACf,KAAD,EAAQqB,OAAR,KACErB,KAAK,IACJO,OAAO,CAACe,IAARf,CAAac,OAAbd,IACGC,mBADHD,GAEGc,OAAO,KAAK,EAAZA,GACAX,iBADAW,GAEAV,kBALC,CAJJI,EAUHE,YAVGF,CAAP;AAYD;;AAED,SAASV,cAAT,CAAwBF,CAAxB,EAAqCC,CAArC,EAA0D;EACxD,IAAImB,QAAQ,GACVpB,CAAC,CAACrB,MAAFqB,KAAaC,CAAC,CAACtB,MAAfqB,IAAyBA,CAAC,CAACT,KAAFS,CAAQ,CAARA,EAAW,CAAC,CAAZA,EAAeqB,KAAfrB,CAAqB,CAACsB,CAAD,EAAI5C,CAAJ,KAAU4C,CAAC,KAAKrB,CAAC,CAACvB,CAAD,CAAtCsB,CAD3B;EAGA,OAAOoB,QAAQ;EAEX;EACA;EACA;EACApB,CAAC,CAACA,CAAC,CAACrB,MAAFqB,GAAW,CAAZ,CAADA,GAAkBC,CAAC,CAACA,CAAC,CAACtB,MAAFsB,GAAW,CAAZ,CALR;EAOX;EACA,CARJ;AASD;;AAED,SAASrB,gBAAT,CACE2C,MADF,EAEElD,QAFF,EAGiC;EAC/B,IAAI;IAAEoB;EAAF,IAAiB8B,MAArB;EAEA,IAAIC,aAAa,GAAG,EAApB;EACA,IAAIC,eAAe,GAAG,GAAtB;EACA,IAAI5E,OAAqB,GAAG,EAA5B;;EACA,KAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGe,UAAU,CAACd,MAA/B,EAAuC,EAAED,CAAzC,EAA4C;IAC1C,IAAIQ,IAAI,GAAGO,UAAU,CAACf,CAAD,CAArB;IACA,IAAIgD,GAAG,GAAGhD,CAAC,KAAKe,UAAU,CAACd,MAAXc,GAAoB,CAApC;IACA,IAAIkC,iBAAiB,GACnBF,eAAe,KAAK,GAApBA,GACIpD,QADJoD,GAEIpD,QAAQ,CAACkB,KAATlB,CAAeoD,eAAe,CAAC9C,MAA/BN,KAA0C,GAHhD;IAIA,IAAIuD,KAAK,GAAGC,SAAS,CACnB;MAAElE,IAAI,EAAEuB,IAAI,CAACC,YAAb;MAA2BC,aAAa,EAAEF,IAAI,CAACE,aAA/C;MAA8DsC;IAA9D,CADmB,EAEnBC,iBAFmB,CAArB;IAKA,IAAI,CAACC,KAAL,EAAY,OAAO,IAAP;IAEZE,MAAM,CAACC,MAAPD,CAAcN,aAAdM,EAA6BF,KAAK,CAAChE,MAAnCkE;IAEA,IAAI9C,KAAK,GAAGE,IAAI,CAACF,KAAjB;IAEAnC,OAAO,CAAC+C,IAAR/C,CAAa;MACXe,MAAM,EAAE4D,aADG;MAEXnD,QAAQ,EAAEmB,SAAS,CAAC,CAACiC,eAAD,EAAkBG,KAAK,CAACvD,QAAxB,CAAD,CAFR;MAGX2D,YAAY,EAAEC,iBAAiB,CAC7BzC,SAAS,CAAC,CAACiC,eAAD,EAAkBG,KAAK,CAACI,YAAxB,CAAD,CADoB,CAHpB;MAMXhD;IANW,CAAbnC;;IASA,IAAI+E,KAAK,CAACI,YAANJ,KAAuB,GAA3B,EAAgC;MAC9BH,eAAe,GAAGjC,SAAS,CAAC,CAACiC,eAAD,EAAkBG,KAAK,CAACI,YAAxB,CAAD,CAA3BP;IACD;EACF;;EAED,OAAO5E,OAAP;AACD;AAED;AACA;AACA;;AA6CA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBgF,SAAhB,CAIEK,OAJF,EAKE7D,QALF,EAM8B;EAC5B,IAAI,OAAO6D,OAAP,KAAmB,QAAvB,EAAiC;IAC/BA,OAAO,GAAG;MAAEvE,IAAI,EAAEuE,OAAR;MAAiB9C,aAAa,EAAE,KAAhC;MAAuCsC,GAAG,EAAE;IAA5C,CAAVQ;EACD;;EAED,IAAI,CAACC,OAAD,EAAUC,UAAV,IAAwBC,WAAW,CACrCH,OAAO,CAACvE,IAD6B,EAErCuE,OAAO,CAAC9C,aAF6B,EAGrC8C,OAAO,CAACR,GAH6B,CAAvC;EAMA,IAAIE,KAAK,GAAGvD,QAAQ,CAACuD,KAATvD,CAAe8D,OAAf9D,CAAZ;EACA,IAAI,CAACuD,KAAL,EAAY,OAAO,IAAP;EAEZ,IAAIH,eAAe,GAAGG,KAAK,CAAC,CAAD,CAA3B;EACA,IAAII,YAAY,GAAGP,eAAe,CAAC5D,OAAhB4D,CAAwB,SAAxBA,EAAmC,IAAnCA,CAAnB;EACA,IAAIa,aAAa,GAAGV,KAAK,CAACrC,KAANqC,CAAY,CAAZA,CAApB;EACA,IAAIhE,MAAc,GAAGwE,UAAU,CAACnB,MAAXmB,CACnB,CAACG,IAAD,EAAOC,SAAP,EAAkBvD,KAAlB,KAA4B;IAC1B;IACA;IACA,IAAIuD,SAAS,KAAK,GAAlB,EAAuB;MACrB,IAAIC,UAAU,GAAGH,aAAa,CAACrD,KAAD,CAAbqD,IAAwB,EAAzC;MACAN,YAAY,GAAGP,eAAe,CAC3BlC,KADYkC,CACN,CADMA,EACHA,eAAe,CAAC9C,MAAhB8C,GAAyBgB,UAAU,CAAC9D,MADjC8C,EAEZ5D,OAFY4D,CAEJ,SAFIA,EAEO,IAFPA,CAAfO;IAGD;;IAEDO,IAAI,CAACC,SAAD,CAAJD,GAAkBG,wBAAwB,CACxCJ,aAAa,CAACrD,KAAD,CAAbqD,IAAwB,EADgB,EAExCE,SAFwC,CAA1CD;IAIA,OAAOA,IAAP;EAfiB,GAiBnB,EAjBmBH,CAArB;EAoBA,OAAO;IACLxE,MADK;IAELS,QAAQ,EAAEoD,eAFL;IAGLO,YAHK;IAILE;EAJK,CAAP;AAMD;;AAED,SAASG,WAAT,CACE1E,IADF,EAEEyB,aAFF,EAGEsC,GAHF,EAIsB;EAAA,IAFpBtC,aAEoB;IAFpBA,aAEoB,GAFJ,KAAhBA;EAEoB;;EAAA,IADpBsC,GACoB;IADpBA,GACoB,GADd,IAANA;EACoB;;EACpBjE,+CAAO,CACLE,IAAI,KAAK,GAATA,IAAgB,CAACA,IAAI,CAACgF,QAALhF,CAAc,GAAdA,CAAjBA,IAAuCA,IAAI,CAACgF,QAALhF,CAAc,IAAdA,CADlC,EAEL,kBAAeA,IAAf,iDACMA,IAAI,CAACE,OAALF,CAAa,KAAbA,EAAoB,IAApBA,CADN,wJAGsCA,IAAI,CAACE,OAALF,CAAa,KAAbA,EAAoB,IAApBA,CAHtC,SAFK,CAAP;EAQA,IAAIyE,UAAoB,GAAG,EAA3B;EACA,IAAIQ,YAAY,GACd,MACAjF,IAAI,CACDE,OADHF,CACW,SADXA,EACsB,EADtBA;EAAAA,CAEGE,OAFHF,CAEW,MAFXA,EAEmB,GAFnBA;EAAAA,CAGGE,OAHHF,CAGW,qBAHXA,EAGkC,MAHlCA;EAAAA,CAIGE,OAJHF,CAIW,SAJXA,EAIsB,CAACG,CAAD,EAAY0E,SAAZ,KAAkC;IACpDJ,UAAU,CAACxC,IAAXwC,CAAgBI,SAAhBJ;IACA,OAAO,WAAP;EANJ,EAFF;;EAWA,IAAIzE,IAAI,CAACgF,QAALhF,CAAc,GAAdA,CAAJ,EAAwB;IACtByE,UAAU,CAACxC,IAAXwC,CAAgB,GAAhBA;IACAQ,YAAY,IACVjF,IAAI,KAAK,GAATA,IAAgBA,IAAI,KAAK,IAAzBA,GACI,OADJA;IAAAA,EAEI,mBAHNiF,CAFsB;EAAxB,OAMO;IACLA,YAAY,IAAIlB,GAAG,GACf,OADe;IAAA;IAGf;IACA;IACA;IACA;IACA;IACA,sCARJkB;EASD;;EAED,IAAIT,OAAO,GAAG,IAAIU,MAAJ,CAAWD,YAAX,EAAyBxD,aAAa,GAAG0D,SAAH,GAAe,GAArD,CAAd;EAEA,OAAO,CAACX,OAAD,EAAUC,UAAV,CAAP;AACD;;AAED,SAASM,wBAAT,CAAkCK,KAAlC,EAAiDP,SAAjD,EAAoE;EAClE,IAAI;IACF,OAAOQ,kBAAkB,CAACD,KAAD,CAAzB;EADF,EAEE,OAAOE,KAAP,EAAc;IACdxF,+CAAO,CACL,KADK,EAEL,mCAAgC+E,SAAhC,0DACkBO,KADlB,8FAEqCE,KAFrC,QAFK,CAAP;IAOA,OAAOF,KAAP;EACD;AACF;AAED;AACA;AACA;AACA;AACA;;;AACA,SAAgBG,WAAhB,CAA4BC,EAA5B,EAAoCC,YAApC,EAA8D;EAAA,IAA1BA,YAA0B;IAA1BA,YAA0B,GAAX,GAAfA;EAA0B;;EAC5D,IAAI;IACF/E,QAAQ,EAAEgF,UADR;IAEFC,MAAM,GAAG,EAFP;IAGFC,IAAI,GAAG;EAHL,IAIA,OAAOJ,EAAP,KAAc,QAAd,GAAyB/E,SAAS,CAAC+E,EAAD,CAAlC,GAAyCA,EAJ7C;EAMA,IAAI9E,QAAQ,GAAGgF,UAAU,GACrBA,UAAU,CAAC/D,UAAX+D,CAAsB,GAAtBA,IACEA,UADFA,GAEEG,eAAe,CAACH,UAAD,EAAaD,YAAb,CAHI,GAIrBA,YAJJ;EAMA,OAAO;IACL/E,QADK;IAELiF,MAAM,EAAEG,eAAe,CAACH,MAAD,CAFlB;IAGLC,IAAI,EAAEG,aAAa,CAACH,IAAD;EAHd,CAAP;AAKD;;AAED,SAASC,eAAT,CAAyBrE,YAAzB,EAA+CiE,YAA/C,EAA6E;EAC3E,IAAIxC,QAAQ,GAAGwC,YAAY,CAACvF,OAAbuF,CAAqB,MAArBA,EAA6B,EAA7BA,EAAiCvC,KAAjCuC,CAAuC,GAAvCA,CAAf;EACA,IAAIO,gBAAgB,GAAGxE,YAAY,CAAC0B,KAAb1B,CAAmB,GAAnBA,CAAvB;EAEAwE,gBAAgB,CAAC5E,OAAjB4E,CAA0BzC,OAAD,IAAa;IACpC,IAAIA,OAAO,KAAK,IAAhB,EAAsB;MACpB;MACA,IAAIN,QAAQ,CAACjC,MAATiC,GAAkB,CAAtB,EAAyBA,QAAQ,CAACgD,GAAThD;IAF3B,OAGO,IAAIM,OAAO,KAAK,GAAhB,EAAqB;MAC1BN,QAAQ,CAAChB,IAATgB,CAAcM,OAAdN;IACD;EANH;EASA,OAAOA,QAAQ,CAACjC,MAATiC,GAAkB,CAAlBA,GAAsBA,QAAQ,CAACiD,IAATjD,CAAc,GAAdA,CAAtBA,GAA2C,GAAlD;AACD;;AAED,SAAgBkD,SAAhB,CACEC,KADF,EAEEC,cAFF,EAGEC,gBAHF,EAIQ;EACN,IAAId,EAAE,GAAG,OAAOY,KAAP,KAAiB,QAAjB,GAA4B3F,SAAS,CAAC2F,KAAD,CAArC,GAA+CA,KAAxD;EACA,IAAIV,UAAU,GAAGU,KAAK,KAAK,EAAVA,IAAgBZ,EAAE,CAAC9E,QAAH8E,KAAgB,EAAhCY,GAAqC,GAArCA,GAA2CZ,EAAE,CAAC9E,QAA/D,CAFM;EAKN;EACA;EACA;EACA;EACA;EACA;;EACA,IAAI6F,IAAJ;;EACA,IAAIb,UAAU,IAAI,IAAlB,EAAwB;IACtBa,IAAI,GAAGD,gBAAPC;EADF,OAEO;IACL,IAAIC,kBAAkB,GAAGH,cAAc,CAACrF,MAAfqF,GAAwB,CAAjD;;IAEA,IAAIX,UAAU,CAAC/D,UAAX+D,CAAsB,IAAtBA,CAAJ,EAAiC;MAC/B,IAAIe,UAAU,GAAGf,UAAU,CAACxC,KAAXwC,CAAiB,GAAjBA,CAAjB,CAD+B;MAI/B;MACA;;MACA,OAAOe,UAAU,CAAC,CAAD,CAAVA,KAAkB,IAAzB,EAA+B;QAC7BA,UAAU,CAACC,KAAXD;QACAD,kBAAkB,IAAI,CAAtBA;MACD;;MAEDhB,EAAE,CAAC9E,QAAH8E,GAAciB,UAAU,CAACP,IAAXO,CAAgB,GAAhBA,CAAdjB;IAdG;IAkBL;;;IACAe,IAAI,GAAGC,kBAAkB,IAAI,CAAtBA,GAA0BH,cAAc,CAACG,kBAAD,CAAxCA,GAA+D,GAAtED;EACD;;EAED,IAAIvG,IAAI,GAAGuF,WAAW,CAACC,EAAD,EAAKe,IAAL,CAAtB,CApCM;;EAuCN,IACEb,UAAU,IACVA,UAAU,KAAK,GADfA,IAEAA,UAAU,CAACV,QAAXU,CAAoB,GAApBA,CAFAA,IAGA,CAAC1F,IAAI,CAACU,QAALV,CAAcgF,QAAdhF,CAAuB,GAAvBA,CAJH,EAKE;IACAA,IAAI,CAACU,QAALV,IAAiB,GAAjBA;EACD;;EAED,OAAOA,IAAP;AACD;;AAED,SAAgB2G,aAAhB,CAA8BnB,EAA9B,EAA0D;EACxD;EACA,OAAOA,EAAE,KAAK,EAAPA,IAAcA,EAAD,CAAa9E,QAAZ8E,KAAyB,EAAvCA,GACH,GADGA,GAEH,OAAOA,EAAP,KAAc,QAAd,GACA/E,SAAS,CAAC+E,EAAD,CAAT/E,CAAcC,QADd,GAEA8E,EAAE,CAAC9E,QAJP;AAKD;;AAED,SAAgBC,aAAhB,CACED,QADF,EAEEH,QAFF,EAGiB;EACf,IAAIA,QAAQ,KAAK,GAAjB,EAAsB,OAAOG,QAAP;;EAEtB,IAAI,CAACA,QAAQ,CAACkG,WAATlG,GAAuBiB,UAAvBjB,CAAkCH,QAAQ,CAACqG,WAATrG,EAAlCG,CAAL,EAAgE;IAC9D,OAAO,IAAP;EACD;;EAED,IAAImG,QAAQ,GAAGnG,QAAQ,CAACoG,MAATpG,CAAgBH,QAAQ,CAACS,MAAzBN,CAAf;;EACA,IAAImG,QAAQ,IAAIA,QAAQ,KAAK,GAA7B,EAAkC;IAChC;IACA,OAAO,IAAP;EACD;;EAED,OAAOnG,QAAQ,CAACkB,KAATlB,CAAeH,QAAQ,CAACS,MAAxBN,KAAmC,GAA1C;AACD;;AAED,MAAamB,SAAS,GAAIkF,KAAD,IACvBA,KAAK,CAACb,IAANa,CAAW,GAAXA,EAAgB7G,OAAhB6G,CAAwB,QAAxBA,EAAkC,GAAlCA,CADF;;AAGA,MAAazC,iBAAiB,GAAI5D,QAAD,IAC/BA,QAAQ,CAACR,OAATQ,CAAiB,MAAjBA,EAAyB,EAAzBA,EAA6BR,OAA7BQ,CAAqC,MAArCA,EAA6C,GAA7CA,CADF;;AAGA,MAAMoF,eAAe,GAAIH,MAAD,IACtB,CAACA,MAAD,IAAWA,MAAM,KAAK,GAAtB,GACI,EADJ,GAEIA,MAAM,CAAChE,UAAPgE,CAAkB,GAAlBA,IACAA,MADAA,GAEA,MAAMA,MALZ;;AAOA,MAAMI,aAAa,GAAIH,IAAD,IACpB,CAACA,IAAD,IAASA,IAAI,KAAK,GAAlB,GAAwB,EAAxB,GAA6BA,IAAI,CAACjE,UAALiE,CAAgB,GAAhBA,IAAuBA,IAAvBA,GAA8B,MAAMA,IADnE;ACtmBA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBoB,OAAhB,CAAwBxB,EAAxB,EAAwC;EACtC,CACEyB,kBAAkB,EADpB,oDAAS;EAGP;EAHO,qEAAT,YAAS,OAAT;EAOA,IAAI;IAAE1G,QAAF;IAAY2G;EAAZ,IAA0BrI,WAAiBD,iBAAjB,CAA9B;EACA,IAAI;IAAEgH,IAAF;IAAQlF,QAAR;IAAkBiF;EAAlB,IAA6BwB,eAAe,CAAC3B,EAAD,CAAhD;EAEA,IAAI4B,cAAc,GAAG1G,QAArB;;EACA,IAAIH,QAAQ,KAAK,GAAjB,EAAsB;IACpB,IAAImF,UAAU,GAAGiB,aAAa,CAACnB,EAAD,CAA9B;IACA,IAAI6B,aAAa,GAAG3B,UAAU,IAAI,IAAdA,IAAsBA,UAAU,CAACV,QAAXU,CAAoB,GAApBA,CAA1C;IACA0B,cAAc,GACZ1G,QAAQ,KAAK,GAAbA,GACIH,QAAQ,IAAI8G,aAAa,GAAG,GAAH,GAAS,EAA1B,CADZ3G,GAEImB,SAAS,CAAC,CAACtB,QAAD,EAAWG,QAAX,CAAD,CAHf0G;EAID;;EAED,OAAOF,SAAS,CAACI,UAAVJ,CAAqB;IAAExG,QAAQ,EAAE0G,cAAZ;IAA4BzB,MAA5B;IAAoCC;EAApC,CAArBsB,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;;AACA,SAAgBD,kBAAhB,GAA8C;EAC5C,OAAOpI,WAAiBE,eAAjB,KAAqC,IAA5C;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBwI,WAAhB,GAAwC;EACtC,CACEN,kBAAkB,EADpB,oDAAS;EAGP;EAHO,yEAAT,YAAS,OAAT;EAOA,OAAOpI,WAAiBE,eAAjB,EAAkCyB,QAAzC;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBgH,iBAAhB,GAAoD;EAClD,OAAO3I,WAAiBE,eAAjB,EAAkC0I,cAAzC;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBC,QAAhB,CAGEnD,OAHF,EAGiE;EAC/D,CACE0C,kBAAkB,EADpB,oDAAS;EAGP;EAHO,sEAAT,YAAS,OAAT;EAOA,IAAI;IAAEvG;EAAF,IAAe6G,WAAW,EAA9B;EACA,OAAO1I,QACL,MAAMqF,SAAS,CAAiBK,OAAjB,EAA0B7D,QAA1B,CADV,EAEL,CAACA,QAAD,EAAW6D,OAAX,CAFK,CAAP;AAID;AAED;AACA;AACA;;AAWA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBoD,WAAhB,GAAgD;EAC9C,CACEV,kBAAkB,EADpB,oDAAS;EAGP;EAHO,yEAAT,YAAS,OAAT;EAOA,IAAI;IAAE1G,QAAF;IAAY2G;EAAZ,IAA0BrI,WAAiBD,iBAAjB,CAA9B;EACA,IAAI;IAAEM;EAAF,IAAcL,WAAiBG,YAAjB,CAAlB;EACA,IAAI;IAAE0B,QAAQ,EAAE4F;EAAZ,IAAiCiB,WAAW,EAAhD;EAEA,IAAIK,kBAAkB,GAAGC,IAAI,CAACC,SAALD,CACvB3I,OAAO,CAACsD,GAARtD,CAAa+E,KAAD,IAAWA,KAAK,CAACI,YAA7BnF,CADuB2I,CAAzB;EAIA,IAAIE,SAAS,GAAGlJ,OAAa,KAAb,CAAhB;EACAA,UAAgB,MAAM;IACpBkJ,SAAS,CAACC,OAAVD,GAAoB,IAApBA;EADF;EAIA,IAAIE,QAA0B,GAAGpJ,YAC/B,UAAC2G,EAAD,EAAkB0C,OAAlB,EAAoD;IAAA,IAAlCA,OAAkC;MAAlCA,OAAkC,GAAP,EAA3BA;IAAkC;;IAClDpI,+CAAO,CACLiI,SAAS,CAACC,OADL,EAEL,oGAFK,CAAP;IAMA,IAAI,CAACD,SAAS,CAACC,OAAf,EAAwB;;IAExB,IAAI,OAAOxC,EAAP,KAAc,QAAlB,EAA4B;MAC1B0B,SAAS,CAACiB,EAAVjB,CAAa1B,EAAb0B;MACA;IACD;;IAED,IAAIlH,IAAI,GAAGmG,SAAS,CAClBX,EADkB,EAElBqC,IAAI,CAACO,KAALP,CAAWD,kBAAXC,CAFkB,EAGlBvB,gBAHkB,CAApB;;IAMA,IAAI/F,QAAQ,KAAK,GAAjB,EAAsB;MACpBP,IAAI,CAACU,QAALV,GAAgB6B,SAAS,CAAC,CAACtB,QAAD,EAAWP,IAAI,CAACU,QAAhB,CAAD,CAAzBV;IACD;;IAED,CAAC,CAAC,CAACkI,OAAO,CAAChI,OAAV,GAAoBgH,SAAS,CAAChH,OAA9B,GAAwCgH,SAAS,CAACjF,IAAnD,EACEjC,IADF,EAEEkI,OAAO,CAACG,KAFV;EAzB6B,GA8B/B,CAAC9H,QAAD,EAAW2G,SAAX,EAAsBU,kBAAtB,EAA0CtB,gBAA1C,CA9B+B,CAAjC;EAiCA,OAAO2B,QAAP;AACD;;AAED,MAAMK,aAAa,gBAAGzJ,cAA6B,IAA7B,CAAtB;AAEA;AACA;AACA;AACA;AACA;;AACA,SAAgB0J,gBAAhB,GAA+D;EAC7D,OAAO1J,WAAiByJ,aAAjB,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBE,SAAhB,CAA0BC,OAA1B,EAAwE;EACtE,IAAIxJ,MAAM,GAAGJ,WAAiBG,YAAjB,EAA+BC,MAA5C;;EACA,IAAIA,MAAJ,EAAY;IACV,oBACEyJ,cAACJ,aAAD,CAAeK,QAAfD;MAAwBtD,KAAK,EAAEqD;IAA/B,GAAyCxJ,MAAzCyJ,CADF;EAGD;;EACD,OAAOzJ,MAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgB2J,SAAhB,GAIE;EACA,IAAI;IAAE1J;EAAF,IAAcL,WAAiBG,YAAjB,CAAlB;EACA,IAAI6J,UAAU,GAAG3J,OAAO,CAACA,OAAO,CAAC8B,MAAR9B,GAAiB,CAAlB,CAAxB;EACA,OAAO2J,UAAU,GAAIA,UAAU,CAAC5I,MAAf,GAAgC,EAAjD;AACD;AAED;AACA;AACA;AACA;AACA;;;AACA,SAAgBkH,eAAhB,CAAgC3B,EAAhC,EAA8C;EAC5C,IAAI;IAAEtG;EAAF,IAAcL,WAAiBG,YAAjB,CAAlB;EACA,IAAI;IAAE0B,QAAQ,EAAE4F;EAAZ,IAAiCiB,WAAW,EAAhD;EAEA,IAAIK,kBAAkB,GAAGC,IAAI,CAACC,SAALD,CACvB3I,OAAO,CAACsD,GAARtD,CAAa+E,KAAD,IAAWA,KAAK,CAACI,YAA7BnF,CADuB2I,CAAzB;EAIA,OAAOhJ,QACL,MAAMsH,SAAS,CAACX,EAAD,EAAKqC,IAAI,CAACO,KAALP,CAAWD,kBAAXC,CAAL,EAAqCvB,gBAArC,CADV,EAEL,CAACd,EAAD,EAAKoC,kBAAL,EAAyBtB,gBAAzB,CAFK,CAAP;AAID;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBwC,SAAhB,CACEzI,MADF,EAEEC,WAFF,EAG6B;EAC3B,CACE2G,kBAAkB,EADpB,oDAAS;EAGP;EAHO,uEAAT,YAAS,OAAT;EAOA,IAAI;IAAE/H,OAAO,EAAE6J;EAAX,IAA6BlK,WAAiBG,YAAjB,CAAjC;EACA,IAAI6J,UAAU,GAAGE,aAAa,CAACA,aAAa,CAAC/H,MAAd+H,GAAuB,CAAxB,CAA9B;EACA,IAAIC,YAAY,GAAGH,UAAU,GAAGA,UAAU,CAAC5I,MAAd,GAAuB,EAApD;EACA,IAAIgJ,cAAc,GAAGJ,UAAU,GAAGA,UAAU,CAACnI,QAAd,GAAyB,GAAxD;EACA,IAAIwI,kBAAkB,GAAGL,UAAU,GAAGA,UAAU,CAACxE,YAAd,GAA6B,GAAhE;EACA,IAAI8E,WAAW,GAAGN,UAAU,IAAIA,UAAU,CAACxH,KAA3C;;EAEA,2CAAa;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIF,UAAU,GAAIgI,WAAW,IAAIA,WAAW,CAACnJ,IAA3BmJ,IAAoC,EAAtD;IACAvJ,WAAW,CACTqJ,cADS,EAET,CAACE,WAAD,IAAgBhI,UAAU,CAAC6D,QAAX7D,CAAoB,GAApBA,CAFP,EAGT,2EACM8H,cADN,gCAC6C9H,UAD7C,kPAK2CA,UAL3C,qCAMWA,UAAU,KAAK,GAAfA,GAAqB,GAArBA,GAA8BA,UAA9B,OANX,WAHS,CAAXvB;EAWD;;EAED,IAAIwJ,mBAAmB,GAAG7B,WAAW,EAArC;EAEA,IAAI/G,QAAJ;;EACA,IAAIF,WAAJ,EAAiB;IAAA;;IACf,IAAI+I,iBAAiB,GACnB,OAAO/I,WAAP,KAAuB,QAAvB,GAAkCG,SAAS,CAACH,WAAD,CAA3C,GAA2DA,WAD7D;IAGA,EACE4I,kBAAkB,KAAK,GAAvBA,8BACEG,iBAAiB,CAAC3I,QADpB,qBACE4I,sBAA4B3H,UAA5B,CAAuCuH,kBAAvC,CADFA,CADF,qDAAS,QAGP,qPAEiEA,kBAFjE,iCAGmBG,iBAAiB,CAAC3I,QAHrC,0CAHO,CAAT,YAAS,OAAT;IASAF,QAAQ,GAAG6I,iBAAX7I;EAbF,OAcO;IACLA,QAAQ,GAAG4I,mBAAX5I;EACD;;EAED,IAAIE,QAAQ,GAAGF,QAAQ,CAACE,QAATF,IAAqB,GAApC;EACA,IAAIwD,iBAAiB,GACnBkF,kBAAkB,KAAK,GAAvBA,GACIxI,QADJwI,GAEIxI,QAAQ,CAACkB,KAATlB,CAAewI,kBAAkB,CAAClI,MAAlCN,KAA6C,GAHnD;EAIA,IAAIxB,OAAO,GAAGkB,WAAW,CAACC,MAAD,EAAS;IAAEK,QAAQ,EAAEsD;EAAZ,CAAT,CAAzB;;EAEA,2CAAa;IACXlE,+CAAO,CACLqJ,WAAW,IAAIjK,OAAO,IAAI,IADrB,oCAE0BsB,QAAQ,CAACE,QAFnC,GAE8CF,QAAQ,CAACmF,MAFvD,GAEgEnF,QAAQ,CAACoF,IAFzE,SAAP;IAKA9F,+CAAO,CACLZ,OAAO,IAAI,IAAXA,IACEA,OAAO,CAACA,OAAO,CAAC8B,MAAR9B,GAAiB,CAAlB,CAAPA,CAA4BmC,KAA5BnC,CAAkCqK,OAAlCrK,KAA8CiG,SAF3C,EAGL,sCAAmC3E,QAAQ,CAACE,QAA5C,GAAuDF,QAAQ,CAACmF,MAAhE,GAAyEnF,QAAQ,CAACoF,IAAlF,2IAHK,CAAP;EAMD;;EAED,OAAO4D,cAAc,CACnBtK,OAAO,IACLA,OAAO,CAACsD,GAARtD,CAAa+E,KAAD,IACVE,MAAM,CAACC,MAAPD,CAAc,EAAdA,EAAkBF,KAAlBE,EAAyB;IACvBlE,MAAM,EAAEkE,MAAM,CAACC,MAAPD,CAAc,EAAdA,EAAkB6E,YAAlB7E,EAAgCF,KAAK,CAAChE,MAAtCkE,CADe;IAEvBzD,QAAQ,EAAEmB,SAAS,CAAC,CAACqH,kBAAD,EAAqBjF,KAAK,CAACvD,QAA3B,CAAD,CAFI;IAGvB2D,YAAY,EACVJ,KAAK,CAACI,YAANJ,KAAuB,GAAvBA,GACIiF,kBADJjF,GAEIpC,SAAS,CAAC,CAACqH,kBAAD,EAAqBjF,KAAK,CAACI,YAA3B,CAAD;EANQ,CAAzBF,CADFjF,CAFiB,EAYnB6J,aAZmB,CAArB;AAcD;;AAED,SAAgBS,cAAhB,CACEtK,OADF,EAEE6J,aAFF,EAG6B;EAAA,IAD3BA,aAC2B;IAD3BA,aAC2B,GADG,EAA9BA;EAC2B;;EAC3B,IAAI7J,OAAO,IAAI,IAAf,EAAqB,OAAO,IAAP;EAErB,OAAOA,OAAO,CAACuK,WAARvK,CAAoB,CAACD,MAAD,EAASgF,KAAT,EAAgB3C,KAAhB,KAA0B;IACnD,oBACEoH,cAAC1J,YAAD,CAAc2J,QAAdD;MACE1G,QAAQ,EACNiC,KAAK,CAAC5C,KAAN4C,CAAYsF,OAAZtF,KAAwBkB,SAAxBlB,GAAoCA,KAAK,CAAC5C,KAAN4C,CAAYsF,OAAhDtF,GAA0DhF,MAF9D;MAIEmG,KAAK,EAAE;QACLnG,MADK;QAELC,OAAO,EAAE6J,aAAa,CAAChH,MAAdgH,CAAqB7J,OAAO,CAAC0C,KAAR1C,CAAc,CAAdA,EAAiBoC,KAAK,GAAG,CAAzBpC,CAArB6J;MAFJ;IAJT,EADF;EADK,GAYJ,IAZI7J,CAAP;AAaD;ACjXD;AACA;AACA;AACA;AACA;;;AACA,SAAgBwK,YAAhB,CAAOC,IAAP,EAK0C;EAAA,IALb;IAC3BpJ,QAD2B;IAE3ByB,QAF2B;IAG3B4H,cAH2B;IAI3BC;EAJ2B,IAKaF;EACxC,IAAIG,UAAU,GAAGjL,QAAjB;;EACA,IAAIiL,UAAU,CAAC9B,OAAX8B,IAAsB,IAA1B,EAAgC;IAC9BA,UAAU,CAAC9B,OAAX8B,GAAqBC,mBAAmB,CAAC;MAAEH,cAAF;MAAkBC;IAAlB,CAAD,CAAxCC;EACD;;EAED,IAAIE,OAAO,GAAGF,UAAU,CAAC9B,OAAzB;EACA,IAAI,CAACK,KAAD,EAAQ4B,QAAR,IAAoBpL,SAAe;IACrCqL,MAAM,EAAEF,OAAO,CAACE,MADqB;IAErC1J,QAAQ,EAAEwJ,OAAO,CAACxJ;EAFmB,CAAf,CAAxB;EAKA3B,gBAAsB,MAAMmL,OAAO,CAACG,MAARH,CAAeC,QAAfD,CAA5B,EAAsD,CAACA,OAAD,CAAtD;EAEA,oBACEtB,cAAC0B,MAAD1B;IACEnI,QAAQ,EAAEA,QADZ;IAEEyB,QAAQ,EAAEA,QAFZ;IAGExB,QAAQ,EAAE6H,KAAK,CAAC7H,QAHlB;IAIEiH,cAAc,EAAEY,KAAK,CAAC6B,MAJxB;IAKEhD,SAAS,EAAE8C;EALb,EADF;AASD;AAQD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBK,QAAhB,CAAOC,KAAP,EAAsE;EAAA,IAA7C;IAAE9E,EAAF;IAAMtF,OAAN;IAAemI;EAAf,IAA6CiC;EACpE,CACErD,kBAAkB,EADpB,oDAAS;EAGP;EAHO,sEAAT,YAAS,OAAT;EAOAnH,+CAAO,CACL,CAACjB,WAAiBD,iBAAjB,EAAoC2L,MADhC,EAEL,iOAFK,CAAP;EAOA,IAAItC,QAAQ,GAAGN,WAAW,EAA1B;EACA9I,UAAgB,MAAM;IACpBoJ,QAAQ,CAACzC,EAAD,EAAK;MAAEtF,OAAF;MAAWmI;IAAX,CAAL,CAARJ;EADF;EAIA,OAAO,IAAP;AACD;AAMD;AACA;AACA;AACA;AACA;;;AACA,SAAgBuC,MAAhB,CAAuBC,KAAvB,EAAsE;EACpE,OAAOjC,SAAS,CAACiC,KAAK,CAAChC,OAAP,CAAhB;AACD;AA4BD;AACA;AACA;AACA;AACA;;;AACA,SAAgBiC,KAAhB,CACEC,MADF,EAE6B;EAC3B7K,iDAAS,QAEP,2IAFO,CAAT,YAAS,OAAT;AAKD;AAWD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgBsK,MAAhB,CAAOQ,KAAP,EAO2C;EAAA,IAPpB;IACrBrK,QAAQ,EAAEsK,YAAY,GAAG,GADJ;IAErB7I,QAAQ,GAAG,IAFU;IAGrBxB,QAAQ,EAAEsK,YAHW;IAIrBrD,cAAc,GAAGsD,MAAc,CAACC,GAJX;IAKrB9D,SALqB;IAMrBqD,MAAM,EAAEU,UAAU,GAAG;EANA,IAOoBL;EACzC,CACE,CAAC3D,kBAAkB,EADrB,oDAAS,QAEP,6GAFO,CAAT,YAAS,OAAT;EAMA,IAAI1G,QAAQ,GAAG+D,iBAAiB,CAACuG,YAAD,CAAhC;EACA,IAAIK,iBAAiB,GAAGrM,QACtB,OAAO;IAAE0B,QAAF;IAAY2G,SAAZ;IAAuBqD,MAAM,EAAEU;EAA/B,CAAP,CADsB,EAEtB,CAAC1K,QAAD,EAAW2G,SAAX,EAAsB+D,UAAtB,CAFsB,CAAxB;;EAKA,IAAI,OAAOH,YAAP,KAAwB,QAA5B,EAAsC;IACpCA,YAAY,GAAGrK,SAAS,CAACqK,YAAD,CAAxBA;EACD;;EAED,IAAI;IACFpK,QAAQ,GAAG,GADT;IAEFiF,MAAM,GAAG,EAFP;IAGFC,IAAI,GAAG,EAHL;IAIFyC,KAAK,GAAG,IAJN;IAKFxI,GAAG,GAAG;EALJ,IAMAiL,YANJ;EAQA,IAAItK,QAAQ,GAAG3B,QAAc,MAAM;IACjC,IAAIsM,gBAAgB,GAAGxK,aAAa,CAACD,QAAD,EAAWH,QAAX,CAApC;;IAEA,IAAI4K,gBAAgB,IAAI,IAAxB,EAA8B;MAC5B,OAAO,IAAP;IACD;;IAED,OAAO;MACLzK,QAAQ,EAAEyK,gBADL;MAELxF,MAFK;MAGLC,IAHK;MAILyC,KAJK;MAKLxI;IALK,CAAP;EAPa,GAcZ,CAACU,QAAD,EAAWG,QAAX,EAAqBiF,MAArB,EAA6BC,IAA7B,EAAmCyC,KAAnC,EAA0CxI,GAA1C,CAdY,CAAf;EAgBAC,+CAAO,CACLU,QAAQ,IAAI,IADP,EAEL,wBAAqBD,QAArB,iDACMG,QADN,GACiBiF,MADjB,GAC0BC,IAD1B,iGAFK,CAAP;;EAOA,IAAIpF,QAAQ,IAAI,IAAhB,EAAsB;IACpB,OAAO,IAAP;EACD;;EAED,oBACEkI,cAAC9J,iBAAD,CAAmB+J,QAAnBD;IAA4BtD,KAAK,EAAE8F;EAAnC,gBACExC,cAAC3J,eAAD,CAAiB4J,QAAjBD;IACE1G,QAAQ,EAAEA,QADZ;IAEEoD,KAAK,EAAE;MAAE5E,QAAF;MAAYiH;IAAZ;EAFT,EADFiB,CADF;AAQD;AAOD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgB0C,MAAhB,CAAOC,KAAP,EAG2C;EAAA,IAHpB;IACrBrJ,QADqB;IAErBxB;EAFqB,IAGoB6K;EACzC,OAAOvC,SAAS,CAACwC,wBAAwB,CAACtJ,QAAD,CAAzB,EAAqCxB,QAArC,CAAhB;AACD,C,CAAA;AAGD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAgB8K,wBAAhB,CACEtJ,QADF,EAEiB;EACf,IAAI3B,MAAqB,GAAG,EAA5B;EAEAxB,SAAeuC,OAAfvC,CAAuBmD,QAAvBnD,EAAkC0K,OAAD,IAAa;IAC5C,IAAI,eAAC1K,eAAqB0K,OAArB,CAAL,EAAoC;MAClC;MACA;MACA;IACD;;IAED,IAAIA,OAAO,CAACgC,IAARhC,KAAiB1K,QAArB,EAAqC;MACnC;MACAwB,MAAM,CAAC4B,IAAP5B,CAAYmL,KAAZnL,CACEA,MADFA,EAEEiL,wBAAwB,CAAC/B,OAAO,CAACkB,KAARlB,CAAcvH,QAAf,CAF1B3B;MAIA;IACD;;IAED,EACEkJ,OAAO,CAACgC,IAARhC,KAAiBmB,KADnB,qDAAS,eAGL,OAAOnB,OAAO,CAACgC,IAAf,KAAwB,QAAxB,GAAmChC,OAAO,CAACgC,IAA3C,GAAkDhC,OAAO,CAACgC,IAARhC,CAAakC,IAH1D,6GAAT,YAAS,OAAT;IAOA,IAAIpK,KAAkB,GAAG;MACvBI,aAAa,EAAE8H,OAAO,CAACkB,KAARlB,CAAc9H,aADN;MAEvB8H,OAAO,EAAEA,OAAO,CAACkB,KAARlB,CAAcA,OAFA;MAGvBjI,KAAK,EAAEiI,OAAO,CAACkB,KAARlB,CAAcjI,KAHE;MAIvBtB,IAAI,EAAEuJ,OAAO,CAACkB,KAARlB,CAAcvJ;IAJG,CAAzB;;IAOA,IAAIuJ,OAAO,CAACkB,KAARlB,CAAcvH,QAAlB,EAA4B;MAC1BX,KAAK,CAACW,QAANX,GAAiBiK,wBAAwB,CAAC/B,OAAO,CAACkB,KAARlB,CAAcvH,QAAf,CAAzCX;IACD;;IAEDhB,MAAM,CAAC4B,IAAP5B,CAAYgB,KAAZhB;EAlCF;EAqCA,OAAOA,MAAP;AACD;AAED;AACA;AACA;;;AACA,SAAgBqL,aAAhB,CACExM,OADF,EAE6B;EAC3B,OAAOsK,cAAc,CAACtK,OAAD,CAArB;AACD", "names": ["NavigationContext", "React", "displayName", "LocationContext", "RouteContext", "outlet", "matches", "invariant", "cond", "message", "Error", "warning", "console", "warn", "e", "alreadyWarned", "warningOnce", "key", "process", "generatePath", "path", "params", "replace", "_", "matchRoutes", "routes", "locationArg", "basename", "location", "parsePath", "pathname", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "i", "length", "matchRouteBranch", "parents<PERSON>eta", "parentPath", "for<PERSON>ach", "route", "index", "meta", "relativePath", "caseSensitive", "childrenIndex", "startsWith", "slice", "joinPaths", "routesMeta", "concat", "children", "push", "score", "computeScore", "sort", "a", "b", "compareIndexes", "map", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "segments", "split", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "n", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "Object", "assign", "pathnameBase", "normalizePathname", "pattern", "matcher", "paramNames", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "safelyDecodeURIComponent", "endsWith", "regexpSource", "RegExp", "undefined", "value", "decodeURIComponent", "error", "<PERSON><PERSON><PERSON>", "to", "fromPathname", "toPathname", "search", "hash", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "join", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "from", "routePathnameIndex", "toSegments", "shift", "getToPathname", "toLowerCase", "nextChar", "char<PERSON>t", "paths", "useHref", "useInRouterContext", "navigator", "useResolvedPath", "joinedPathname", "endsWithSlash", "createHref", "useLocation", "useNavigationType", "navigationType", "useMatch", "useNavigate", "routePathnamesJson", "JSON", "stringify", "activeRef", "current", "navigate", "options", "go", "parse", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "React.createElement", "Provider", "useParams", "routeMatch", "useRoutes", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "parentRoute", "locationFromContext", "parsedLocationArg", "_parsedLocationArg$pa", "element", "_renderMatches", "reduceRight", "MemoryRouter", "_ref", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "history", "setState", "action", "listen", "Router", "Navigate", "_ref2", "static", "Outlet", "props", "Route", "_props", "_ref3", "basenameProp", "locationProp", "NavigationType", "Pop", "staticProp", "navigationContext", "trailingPathname", "Routes", "_ref4", "createRoutesFromChildren", "type", "apply", "name", "renderMatches"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/packages/react-router/lib/context.ts", "/Users/<USER>/Mac/Projects/Frontend/packages/react-router/lib/router.ts", "/Users/<USER>/Mac/Projects/Frontend/packages/react-router/lib/hooks.tsx", "/Users/<USER>/Mac/Projects/Frontend/packages/react-router/lib/components.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { History, Location } from \"history\";\nimport { Action as NavigationType } from \"history\";\n\nimport type { RouteMatch } from \"./router\";\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level <Router> API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport type Navigator = Pick<History, \"go\" | \"push\" | \"replace\" | \"createHref\">;\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\ninterface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n", "import type { Location, Path, To } from \"history\";\nimport { parsePath } from \"history\";\n\nexport function invariant(cond: any, message: string): asserts cond {\n  if (!cond) throw new Error(message);\n}\n\nexport function warning(cond: any, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\nexport function warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n\ntype ParamParseFailed = { failed: true };\n\ntype ParamParseSegment<Segment extends string> =\n  // Check here if there exists a forward slash in the string.\n  Segment extends `${infer LeftSegment}/${infer RightSegment}`\n    ? // If there is a forward slash, then attempt to parse each side of the\n      // forward slash.\n      ParamParseSegment<LeftSegment> extends infer LeftResult\n      ? ParamParseSegment<RightSegment> extends infer RightResult\n        ? LeftResult extends string\n          ? // If the left side is successfully parsed as a param, then check if\n            // the right side can be successfully parsed as well. If both sides\n            // can be parsed, then the result is a union of the two sides\n            // (read: \"foo\" | \"bar\").\n            RightResult extends string\n            ? LeftResult | RightResult\n            : LeftResult\n          : // If the left side is not successfully parsed as a param, then check\n          // if only the right side can be successfully parse as a param. If it\n          // can, then the result is just right, else it's a failure.\n          RightResult extends string\n          ? RightResult\n          : ParamParseFailed\n        : ParamParseFailed\n      : // If the left side didn't parse into a param, then just check the right\n      // side.\n      ParamParseSegment<RightSegment> extends infer RightResult\n      ? RightResult extends string\n        ? RightResult\n        : ParamParseFailed\n      : ParamParseFailed\n    : // If there's no forward slash, then check if this segment starts with a\n    // colon. If it does, then this is a dynamic segment, so the result is\n    // just the remainder of the string. Otherwise, it's a failure.\n    Segment extends `:${infer Remaining}`\n    ? Remaining\n    : ParamParseFailed;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  ParamParseSegment<Segment> extends string\n    ? ParamParseSegment<Segment>\n    : string;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport interface RouteObject {\n  caseSensitive?: boolean;\n  children?: RouteObject[];\n  element?: React.ReactNode;\n  index?: boolean;\n  path?: string;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/docs/en/v6/api#generatepath\n */\nexport function generatePath(path: string, params: Params = {}): string {\n  return path\n    .replace(/:(\\w+)/g, (_, key) => {\n      invariant(params[key] != null, `Missing \":${key}\" param`);\n      return params[key]!;\n    })\n    .replace(/\\/*\\*$/, (_) =>\n      params[\"*\"] == null ? \"\" : params[\"*\"].replace(/^\\/*/, \"/\")\n    );\n}\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface RouteMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObject;\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/docs/en/v6/api#matchroutes\n */\nexport function matchRoutes(\n  routes: RouteObject[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): RouteMatch[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch(branches[i], pathname);\n  }\n\n  return matches;\n}\n\ninterface RouteMeta {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObject;\n}\n\ninterface RouteBranch {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta[];\n}\n\nfunction flattenRoutes(\n  routes: RouteObject[],\n  branches: RouteBranch[] = [],\n  parentsMeta: RouteMeta[] = [],\n  parentPath = \"\"\n): RouteBranch[] {\n  routes.forEach((route, index) => {\n    let meta: RouteMeta = {\n      relativePath: route.path || \"\",\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({ path, score: computeScore(path, route.index), routesMeta });\n  });\n\n  return branches;\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<ParamKey extends string = string>(\n  branch: RouteBranch,\n  pathname: string\n): RouteMatch<ParamKey>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: RouteMatch[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    if (!match) return null;\n\n    Object.assign(matchedParams, match.params);\n\n    let route = meta.route;\n\n    matches.push({\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/docs/en/v6/api#matchpath\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, paramNames] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = paramNames.reduce<Mutable<Params>>(\n    (memo, paramName, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      memo[paramName] = safelyDecodeURIComponent(\n        captureGroups[index] || \"\",\n        paramName\n      );\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, string[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let paramNames: string[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(/:(\\w+)/g, (_: string, paramName: string) => {\n        paramNames.push(paramName);\n        return \"([^\\\\/]+)\";\n      });\n\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else {\n    regexpSource += end\n      ? \"\\\\/*$\" // When matching to the end, ignore trailing slashes\n      : // Otherwise, match a word boundary or a proceeding /. The word boundary restricts\n        // parent routes to matching only their own words and nothing more, e.g. parent\n        // route \"/home\" should not match \"/home2\".\n        // Additionally, allow paths starting with `.`, `-`, `~`, and url-encoded entities,\n        // but do not consume the character in the matched path so they can match against\n        // nested paths.\n        \"(?:(?=[.~-]|%[0-9A-F]{2})|\\\\b|\\\\/|$)\";\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, paramNames];\n}\n\nfunction safelyDecodeURIComponent(value: string, paramName: string) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(\n      false,\n      `The value for the URL param \"${paramName}\" will not be decoded because` +\n        ` the string \"${value}\" is a malformed URL segment. This is probably` +\n        ` due to a bad percent encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/docs/en/v6/api#resolvepath\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string\n): Path {\n  let to = typeof toArg === \"string\" ? parsePath(toArg) : toArg;\n  let toPathname = toArg === \"\" || to.pathname === \"\" ? \"/\" : to.pathname;\n\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  let from: string;\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original to value had one.\n  if (\n    toPathname &&\n    toPathname !== \"/\" &&\n    toPathname.endsWith(\"/\") &&\n    !path.pathname.endsWith(\"/\")\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  let nextChar = pathname.charAt(basename.length);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(basename.length) || \"/\";\n}\n\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\nconst normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\nconst normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n", "import * as React from \"react\";\nimport type { Location, Path, To } from \"history\";\nimport { Action as NavigationType, parsePath } from \"history\";\n\nimport { LocationContext, NavigationContext, RouteContext } from \"./context\";\nimport type {\n  ParamParse<PERSON>ey,\n  Params,\n  PathMatch,\n  PathPattern,\n  RouteMatch,\n  RouteObject,\n} from \"./router\";\nimport {\n  getToPathname,\n  invariant,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  resolveTo,\n  warning,\n  warningOnce,\n} from \"./router\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usehref\n */\nexport function useHref(to: To): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to);\n\n  let joinedPathname = pathname;\n  if (basename !== \"/\") {\n    let toPathname = getToPathname(to);\n    let endsWithSlash = toPathname != null && toPathname.endsWith(\"/\");\n    joinedPathname =\n      pathname === \"/\"\n        ? basename + (endsWithSlash ? \"/\" : \"\")\n        : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useinroutercontext\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/docs/en/v6/api#uselocation\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usenavigationtype\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns true if the URL for the given \"to\" value matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usematch\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n}\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usenavigate\n */\nexport function useNavigate(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    matches.map((match) => match.pathnameBase)\n  );\n\n  let activeRef = React.useRef(false);\n  React.useEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(\n        activeRef.current,\n        `You should call navigate() in a React.useEffect(), not when ` +\n          `your component is first rendered.`\n      );\n\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname\n      );\n\n      if (basename !== \"/\") {\n        path.pathname = joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state\n      );\n    },\n    [basename, navigator, routePathnamesJson, locationPathname]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/docs/en/v6/api#useoutletcontext\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useoutlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useparams\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useresolvedpath\n */\nexport function useResolvedPath(to: To): Path {\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    matches.map((match) => match.pathnameBase)\n  );\n\n  return React.useMemo(\n    () => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname),\n    [to, routePathnamesJson, locationPathname]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useroutes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname =\n    parentPathnameBase === \"/\"\n      ? pathname\n      : pathname.slice(parentPathnameBase.length) || \"/\";\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" does not have an element. ` +\n        `This means it will render an <Outlet /> with a null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  return _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([parentPathnameBase, match.pathname]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([parentPathnameBase, match.pathnameBase]),\n        })\n      ),\n    parentMatches\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = []\n): React.ReactElement | null {\n  if (matches == null) return null;\n\n  return matches.reduceRight((outlet, match, index) => {\n    return (\n      <RouteContext.Provider\n        children={\n          match.route.element !== undefined ? match.route.element : outlet\n        }\n        value={{\n          outlet,\n          matches: parentMatches.concat(matches.slice(0, index + 1)),\n        }}\n      />\n    );\n  }, null as React.ReactElement | null);\n}\n", "import * as React from \"react\";\nimport type { InitialEntry, Location, MemoryHistory, To } from \"history\";\nimport {\n  Action as NavigationType,\n  createMemoryHistory,\n  parsePath,\n} from \"history\";\n\nimport { LocationContext, NavigationContext, Navigator } from \"./context\";\nimport {\n  useInRouterContext,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  _renderMatches,\n} from \"./hooks\";\nimport type { RouteMatch, RouteObject } from \"./router\";\nimport { invariant, normalizePathname, stripBasename, warning } from \"./router\";\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/docs/en/v6/api#memoryrouter\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({ initialEntries, initialIndex });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/docs/en/v6/api#navigate\n */\nexport function Navigate({ to, replace, state }: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  warning(\n    !React.useContext(NavigationContext).static,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let navigate = useNavigate();\n  React.useEffect(() => {\n    navigate(to, { replace, state });\n  });\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/docs/en/v6/api#outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface RouteProps {\n  caseSensitive?: boolean;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  index?: boolean;\n  path?: string;\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: boolean;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  index?: false;\n  path: string;\n}\n\nexport interface LayoutRouteProps {\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n}\n\nexport interface IndexRouteProps {\n  element?: React.ReactNode | null;\n  index: true;\n}\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/docs/en/v6/api#route\n */\nexport function Route(\n  _props: PathRouteProps | LayoutRouteProps | IndexRouteProps\n): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/docs/en/v6/api#router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  let basename = normalizePathname(basenameProp);\n  let navigationContext = React.useMemo(\n    () => ({ basename, navigator, static: staticProp }),\n    [basename, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let location = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key,\n    };\n  }, [basename, pathname, search, hash, state, key]);\n\n  warning(\n    location != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (location == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider\n        children={children}\n        value={{ location, navigationType }}\n      />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/docs/en/v6/api#createroutesfromchildren\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    let route: RouteObject = {\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children);\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n"]}, "metadata": {}, "sourceType": "module"}