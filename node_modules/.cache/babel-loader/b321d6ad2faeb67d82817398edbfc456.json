{"ast": null, "code": "/* eslint-disable es-x/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\n\nvar fails = require('../internals/fails'); // eslint-disable-next-line es-x/no-object-getownpropertysymbols -- required for testing\n\n\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol(); // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n\n  return !String(symbol) || !(Object(symbol) instanceof Symbol) || // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n  !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});", "map": {"version": 3, "names": ["V8_VERSION", "require", "fails", "module", "exports", "Object", "getOwnPropertySymbols", "symbol", "Symbol", "String", "sham"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/native-symbol.js"], "sourcesContent": ["/* eslint-disable es-x/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\nvar fails = require('../internals/fails');\n\n// eslint-disable-next-line es-x/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol();\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  return !String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAGC,OAAO,CAAC,gCAAD,CAAxB;;AACA,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAD,CAAnB,C,CAEA;;;AACAE,MAAM,CAACC,OAAP,GAAiB,CAAC,CAACC,MAAM,CAACC,qBAAT,IAAkC,CAACJ,KAAK,CAAC,YAAY;EACpE,IAAIK,MAAM,GAAGC,MAAM,EAAnB,CADoE,CAEpE;EACA;;EACA,OAAO,CAACC,MAAM,CAACF,MAAD,CAAP,IAAmB,EAAEF,MAAM,CAACE,MAAD,CAAN,YAA0BC,MAA5B,CAAnB,IACL;EACA,CAACA,MAAM,CAACE,IAAR,IAAgBV,UAAhB,IAA8BA,UAAU,GAAG,EAF7C;AAGD,CAPwD,CAAzD"}, "metadata": {}, "sourceType": "script"}