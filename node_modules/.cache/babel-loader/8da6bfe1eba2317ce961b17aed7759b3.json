{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/index.jsx\",\n    _s = $RefreshSig$();\n\nimport { useEffect, useState } from \"react\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ScrollToTop = () => {\n  _s();\n\n  const [makeACtive, setMakeActive] = useState(false);\n\n  const handleCroll = () => {\n    const scrolled = window ? window.pageYOffset : document.documentElement.scrollTop;\n\n    if (scrolled > 300) {\n      setMakeActive(true);\n    } else if (scrolled <= 300) {\n      setMakeActive(false);\n    }\n  };\n\n  console.log({\n    makeACtive\n  });\n  useEffect(() => {\n    window.addEventListener(\"scroll\", handleCroll, {\n      passive: true\n    });\n    return () => {\n      window.removeEventListener(\"scroll\", handleCroll);\n    };\n  }, []);\n\n  const backToTop = e => {\n    e.preventDefault();\n    window.scrollTo({\n      top: 0,\n      behavior: \"smooth\"\n    });\n  };\n\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    onClick: backToTop,\n    active: makeACtive,\n    title: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Styles.Wrapper, {\n      children: [/*#__PURE__*/_jsxDEV(Styles.Trangletop, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.Tranglebottom, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n\n_s(ScrollToTop, \"dbyZCRYaUC+xvYWwJsOtcvetQRo=\");\n\n_c = ScrollToTop;\nexport default ScrollToTop;\n\nvar _c;\n\n$RefreshReg$(_c, \"ScrollToTop\");", "map": {"version": 3, "names": ["useEffect", "useState", "Styles", "ScrollToTop", "makeACtive", "setMakeActive", "handleCroll", "scrolled", "window", "pageYOffset", "document", "documentElement", "scrollTop", "console", "log", "addEventListener", "passive", "removeEventListener", "backToTop", "e", "preventDefault", "scrollTo", "top", "behavior"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/index.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst ScrollToTop = () => {\n  const [makeACtive, setMakeActive] = useState(false);\n\n  const handleCroll = () => {\n    const scrolled = window\n      ? window.pageYOffset\n      : document.documentElement.scrollTop;\n\n    if (scrolled > 300) {\n      setMakeActive(true);\n    } else if (scrolled <= 300) {\n      setMakeActive(false);\n    }\n  };\n\n  console.log({ makeACtive });\n\n  useEffect(() => {\n    window.addEventListener(\"scroll\", handleCroll, { passive: true });\n\n    return () => {\n      window.removeEventListener(\"scroll\", handleCroll);\n    };\n  }, []);\n\n  const backToTop = (e) => {\n    e.preventDefault();\n    window.scrollTo({\n      top: 0,\n      behavior: \"smooth\",\n    });\n  };\n\n  return (\n    <Styles.Container onClick={backToTop} active={makeACtive} title=\"top\">\n      <Styles.Wrapper>\n        <Styles.Trangletop />\n        <Styles.Tranglebottom />\n      </Styles.Wrapper>\n    </Styles.Container>\n  );\n};\n\nexport default ScrollToTop;\n"], "mappings": ";;;AAAA,SAASA,SAAT,EAAoBC,QAApB,QAAoC,OAApC;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,WAAW,GAAG,MAAM;EAAA;;EACxB,MAAM,CAACC,UAAD,EAAaC,aAAb,IAA8BJ,QAAQ,CAAC,KAAD,CAA5C;;EAEA,MAAMK,WAAW,GAAG,MAAM;IACxB,MAAMC,QAAQ,GAAGC,MAAM,GACnBA,MAAM,CAACC,WADY,GAEnBC,QAAQ,CAACC,eAAT,CAAyBC,SAF7B;;IAIA,IAAIL,QAAQ,GAAG,GAAf,EAAoB;MAClBF,aAAa,CAAC,IAAD,CAAb;IACD,CAFD,MAEO,IAAIE,QAAQ,IAAI,GAAhB,EAAqB;MAC1BF,aAAa,CAAC,KAAD,CAAb;IACD;EACF,CAVD;;EAYAQ,OAAO,CAACC,GAAR,CAAY;IAAEV;EAAF,CAAZ;EAEAJ,SAAS,CAAC,MAAM;IACdQ,MAAM,CAACO,gBAAP,CAAwB,QAAxB,EAAkCT,WAAlC,EAA+C;MAAEU,OAAO,EAAE;IAAX,CAA/C;IAEA,OAAO,MAAM;MACXR,MAAM,CAACS,mBAAP,CAA2B,QAA3B,EAAqCX,WAArC;IACD,CAFD;EAGD,CANQ,EAMN,EANM,CAAT;;EAQA,MAAMY,SAAS,GAAIC,CAAD,IAAO;IACvBA,CAAC,CAACC,cAAF;IACAZ,MAAM,CAACa,QAAP,CAAgB;MACdC,GAAG,EAAE,CADS;MAEdC,QAAQ,EAAE;IAFI,CAAhB;EAID,CAND;;EAQA,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAkB,OAAO,EAAEL,SAA3B;IAAsC,MAAM,EAAEd,UAA9C;IAA0D,KAAK,EAAC,KAAhE;IAAA,uBACE,QAAC,MAAD,CAAQ,OAAR;MAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;QAAA;QAAA;QAAA;MAAA,QADF,eAEE,QAAC,MAAD,CAAQ,aAAR;QAAA;QAAA;QAAA;MAAA,QAFF;IAAA;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAQD,CAzCD;;GAAMD,W;;KAAAA,W;AA2CN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}