{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar common_1 = require(\"./common\");\n\nfunction populateNextSlides(state, props, slidesHavePassed) {\n  void 0 === slidesHavePassed && (slidesHavePassed = 0);\n  var nextSlides,\n      nextPosition,\n      slidesToShow = state.slidesToShow,\n      currentSlide = state.currentSlide,\n      itemWidth = state.itemWidth,\n      totalItems = state.totalItems,\n      slidesToSlide = common_1.getSlidesToSlide(state, props),\n      nextMaximumSlides = currentSlide + 1 + slidesHavePassed + slidesToShow + (0 < slidesHavePassed ? 0 : slidesToSlide);\n  return nextPosition = nextMaximumSlides <= totalItems ? -itemWidth * (nextSlides = currentSlide + slidesHavePassed + (0 < slidesHavePassed ? 0 : slidesToSlide)) : totalItems < nextMaximumSlides && currentSlide !== totalItems - slidesToShow ? -itemWidth * (nextSlides = totalItems - slidesToShow) : nextSlides = void 0, {\n    nextSlides: nextSlides,\n    nextPosition: nextPosition\n  };\n}\n\nexports.populateNextSlides = populateNextSlides;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "common_1", "require", "populateNextSlides", "state", "props", "slidesHavePassed", "nextSlides", "nextPosition", "slidesToShow", "currentSlide", "itemWidth", "totalItems", "slidesToSlide", "getSlidesToSlide", "nextMaximumSlides"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/next.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var common_1=require(\"./common\");function populateNextSlides(state,props,slidesHavePassed){void 0===slidesHavePassed&&(slidesHavePassed=0);var nextSlides,nextPosition,slidesToShow=state.slidesToShow,currentSlide=state.currentSlide,itemWidth=state.itemWidth,totalItems=state.totalItems,slidesToSlide=common_1.getSlidesToSlide(state,props),nextMaximumSlides=currentSlide+1+slidesHavePassed+slidesToShow+(0<slidesHavePassed?0:slidesToSlide);return nextPosition=nextMaximumSlides<=totalItems?-itemWidth*(nextSlides=currentSlide+slidesHavePassed+(0<slidesHavePassed?0:slidesToSlide)):totalItems<nextMaximumSlides&&currentSlide!==totalItems-slidesToShow?-itemWidth*(nextSlides=totalItems-slidesToShow):nextSlides=void 0,{nextSlides:nextSlides,nextPosition:nextPosition}}exports.populateNextSlides=populateNextSlides;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,QAAQ,GAACC,OAAO,CAAC,UAAD,CAApB;;AAAiC,SAASC,kBAAT,CAA4BC,KAA5B,EAAkCC,KAAlC,EAAwCC,gBAAxC,EAAyD;EAAC,KAAK,CAAL,KAASA,gBAAT,KAA4BA,gBAAgB,GAAC,CAA7C;EAAgD,IAAIC,UAAJ;EAAA,IAAeC,YAAf;EAAA,IAA4BC,YAAY,GAACL,KAAK,CAACK,YAA/C;EAAA,IAA4DC,YAAY,GAACN,KAAK,CAACM,YAA/E;EAAA,IAA4FC,SAAS,GAACP,KAAK,CAACO,SAA5G;EAAA,IAAsHC,UAAU,GAACR,KAAK,CAACQ,UAAvI;EAAA,IAAkJC,aAAa,GAACZ,QAAQ,CAACa,gBAAT,CAA0BV,KAA1B,EAAgCC,KAAhC,CAAhK;EAAA,IAAuMU,iBAAiB,GAACL,YAAY,GAAC,CAAb,GAAeJ,gBAAf,GAAgCG,YAAhC,IAA8C,IAAEH,gBAAF,GAAmB,CAAnB,GAAqBO,aAAnE,CAAzN;EAA2S,OAAOL,YAAY,GAACO,iBAAiB,IAAEH,UAAnB,GAA8B,CAACD,SAAD,IAAYJ,UAAU,GAACG,YAAY,GAACJ,gBAAb,IAA+B,IAAEA,gBAAF,GAAmB,CAAnB,GAAqBO,aAApD,CAAvB,CAA9B,GAAyHD,UAAU,GAACG,iBAAX,IAA8BL,YAAY,KAAGE,UAAU,GAACH,YAAxD,GAAqE,CAACE,SAAD,IAAYJ,UAAU,GAACK,UAAU,GAACH,YAAlC,CAArE,GAAqHF,UAAU,GAAC,KAAK,CAA3Q,EAA6Q;IAACA,UAAU,EAACA,UAAZ;IAAuBC,YAAY,EAACA;EAApC,CAApR;AAAsU;;AAAAT,OAAO,CAACI,kBAAR,GAA2BA,kBAA3B"}, "metadata": {}, "sourceType": "script"}