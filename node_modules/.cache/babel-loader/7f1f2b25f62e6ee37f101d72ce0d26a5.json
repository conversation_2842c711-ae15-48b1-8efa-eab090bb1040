{"ast": null, "code": "var toPrimitive = require('../internals/to-primitive');\n\nvar isSymbol = require('../internals/is-symbol'); // `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\n\n\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};", "map": {"version": 3, "names": ["toPrimitive", "require", "isSymbol", "module", "exports", "argument", "key"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/to-property-key.js"], "sourcesContent": ["var toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,2BAAD,CAAzB;;AACA,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAD,CAAtB,C,CAEA;AACA;;;AACAE,MAAM,CAACC,OAAP,GAAiB,UAAUC,QAAV,EAAoB;EACnC,IAAIC,GAAG,GAAGN,WAAW,CAACK,QAAD,EAAW,QAAX,CAArB;EACA,OAAOH,QAAQ,CAACI,GAAD,CAAR,GAAgBA,GAAhB,GAAsBA,GAAG,GAAG,EAAnC;AACD,CAHD"}, "metadata": {}, "sourceType": "script"}