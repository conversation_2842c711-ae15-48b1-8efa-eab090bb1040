{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  background-color: #fefefe;\n\n  padding: 20px;\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  max-height: 160px;\n  min-height: 160px;\n\n  margin: 0 10px;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: #ffffff;\n  }\n\n  cursor: pointer;\n  transition: all 0.2 ease-in-out;\n`;\nexport const ImageHolder = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: scale-down;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "ImageHolder", "img"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  background-color: #fefefe;\n\n  padding: 20px;\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  max-height: 160px;\n  min-height: 160px;\n\n  margin: 0 10px;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: #ffffff;\n  }\n\n  cursor: pointer;\n  transition: all 0.2 ease-in-out;\n`;\n\nexport const ImageHolder = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: scale-down;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAxBO;AA0BP,OAAO,MAAMC,WAAW,GAAGH,MAAM,CAACI,GAAI;AACtC;AACA;AACA;AACA,CAJO"}, "metadata": {}, "sourceType": "module"}