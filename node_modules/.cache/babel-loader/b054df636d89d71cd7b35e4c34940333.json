{"ast": null, "code": "var isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};", "map": {"version": 3, "names": ["isCallable", "require", "module", "exports", "it"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/is-object.js"], "sourcesContent": ["var isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAAD,CAAxB;;AAEAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,EAAV,EAAc;EAC7B,OAAO,OAAOA,EAAP,IAAa,QAAb,GAAwBA,EAAE,KAAK,IAA/B,GAAsCJ,UAAU,CAACI,EAAD,CAAvD;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}