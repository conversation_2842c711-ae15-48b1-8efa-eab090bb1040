{"ast": null, "code": "var call = require('../internals/function-call');\n\nvar isCallable = require('../internals/is-callable');\n\nvar isObject = require('../internals/is-object');\n\nvar $TypeError = TypeError; // `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\n\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw $TypeError(\"Can't convert object to primitive value\");\n};", "map": {"version": 3, "names": ["call", "require", "isCallable", "isObject", "$TypeError", "TypeError", "module", "exports", "input", "pref", "fn", "val", "toString", "valueOf"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/ordinary-to-primitive.js"], "sourcesContent": ["var call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar $TypeError = TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw $TypeError(\"Can't convert object to primitive value\");\n};\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,4BAAD,CAAlB;;AACA,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAAD,CAAxB;;AACA,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAD,CAAtB;;AAEA,IAAIG,UAAU,GAAGC,SAAjB,C,CAEA;AACA;;AACAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,KAAV,EAAiBC,IAAjB,EAAuB;EACtC,IAAIC,EAAJ,EAAQC,GAAR;EACA,IAAIF,IAAI,KAAK,QAAT,IAAqBP,UAAU,CAACQ,EAAE,GAAGF,KAAK,CAACI,QAAZ,CAA/B,IAAwD,CAACT,QAAQ,CAACQ,GAAG,GAAGX,IAAI,CAACU,EAAD,EAAKF,KAAL,CAAX,CAArE,EAA8F,OAAOG,GAAP;EAC9F,IAAIT,UAAU,CAACQ,EAAE,GAAGF,KAAK,CAACK,OAAZ,CAAV,IAAkC,CAACV,QAAQ,CAACQ,GAAG,GAAGX,IAAI,CAACU,EAAD,EAAKF,KAAL,CAAX,CAA/C,EAAwE,OAAOG,GAAP;EACxE,IAAIF,IAAI,KAAK,QAAT,IAAqBP,UAAU,CAACQ,EAAE,GAAGF,KAAK,CAACI,QAAZ,CAA/B,IAAwD,CAACT,QAAQ,CAACQ,GAAG,GAAGX,IAAI,CAACU,EAAD,EAAKF,KAAL,CAAX,CAArE,EAA8F,OAAOG,GAAP;EAC9F,MAAMP,UAAU,CAAC,yCAAD,CAAhB;AACD,CAND"}, "metadata": {}, "sourceType": "script"}