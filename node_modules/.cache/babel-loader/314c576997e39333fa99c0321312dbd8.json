{"ast": null, "code": "import styled from \"styled-components\";\nimport bgOne from \"../../../assets/bg/signup-form-bg-1.png\";\nimport bgTwo from \"../../../assets/bg/signup-form-bg-2.png\";\nexport const Container = styled.div``;\nexport const Wrapper = styled.div`\n  display: flex;\n  align-items: center;\n\n  background-color: #3e485a;\n  border-radius: 20px;\n\n  padding: 35px 30px;\n\n  gap: 20px;\n\n  position: relative;\n  overflow: hidden;\n`;\nexport const BgOneWrapper = styled.div`\n  position: absolute;\n  left: 0;\n  top: 0;\n  background-image: url(${bgOne});\n  background-size: contain;\n  background-repeat: no-repeat;\n  width: 180px;\n  height: 300px;\n  z-index: 1;\n  opacity: 0.5;\n`;\nexport const BgTwoWrapper = styled.div`\n  position: absolute;\n  top: 70%;\n  left: 40%;\n  background-image: url(${bgTwo});\n  background-size: contain;\n  background-repeat: no-repeat;\n  width: 500px;\n  height: 400px;\n  opacity: 0.5;\n`;", "map": {"version": 3, "names": ["styled", "bgOne", "bgTwo", "Container", "div", "Wrapper", "BgOneWrapper", "BgTwoWrapper"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\nimport bgOne from \"../../../assets/bg/signup-form-bg-1.png\";\nimport bgTwo from \"../../../assets/bg/signup-form-bg-2.png\";\n\nexport const Container = styled.div``;\n\nexport const Wrapper = styled.div`\n  display: flex;\n  align-items: center;\n\n  background-color: #3e485a;\n  border-radius: 20px;\n\n  padding: 35px 30px;\n\n  gap: 20px;\n\n  position: relative;\n  overflow: hidden;\n`;\n\nexport const BgOneWrapper = styled.div`\n  position: absolute;\n  left: 0;\n  top: 0;\n  background-image: url(${bgOne});\n  background-size: contain;\n  background-repeat: no-repeat;\n  width: 180px;\n  height: 300px;\n  z-index: 1;\n  opacity: 0.5;\n`;\n\nexport const BgTwoWrapper = styled.div`\n  position: absolute;\n  top: 70%;\n  left: 40%;\n  background-image: url(${bgTwo});\n  background-size: contain;\n  background-repeat: no-repeat;\n  width: 500px;\n  height: 400px;\n  opacity: 0.5;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AACA,OAAOC,KAAP,MAAkB,yCAAlB;AACA,OAAOC,KAAP,MAAkB,yCAAlB;AAEA,OAAO,MAAMC,SAAS,GAAGH,MAAM,CAACI,GAAI,EAA7B;AAEP,OAAO,MAAMC,OAAO,GAAGL,MAAM,CAACI,GAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAbO;AAeP,OAAO,MAAME,YAAY,GAAGN,MAAM,CAACI,GAAI;AACvC;AACA;AACA;AACA,0BAA0BH,KAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAXO;AAaP,OAAO,MAAMM,YAAY,GAAGP,MAAM,CAACI,GAAI;AACvC;AACA;AACA;AACA,0BAA0BF,KAAM;AAChC;AACA;AACA;AACA;AACA;AACA,CAVO"}, "metadata": {}, "sourceType": "module"}