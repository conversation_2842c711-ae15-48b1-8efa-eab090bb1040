{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/list.jsx\";\nimport React from \"react\";\nimport SeactionHeading from \"../../global/seaction-title\";\nimport Carousel from \"react-multi-carousel\";\nimport \"react-multi-carousel/lib/styles.css\";\nimport { cardData } from \"./data\";\nimport AboutCard from \"./card\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst CardList = () => {\n  const responsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: {\n        max: 4000,\n        min: 3000\n      },\n      items: 3,\n      slidesToSlide: 3\n    },\n    desktop: {\n      breakpoint: {\n        max: 3000,\n        min: 1024\n      },\n      items: 3,\n      slidesToSlide: 3\n    },\n    tablet: {\n      breakpoint: {\n        max: 1024,\n        min: 664\n      },\n      items: 2,\n      slidesToSlide: 2\n    },\n    mobile: {\n      breakpoint: {\n        max: 664,\n        min: 0\n      },\n      items: 1,\n      slidesToSlide: 1\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Styles.ListContainer, {\n    children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n      title: \"Our Team\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.ListWrapper, {\n      children: /*#__PURE__*/_jsxDEV(Carousel, {\n        responsive: responsive,\n        children: cardData.map((item, index) => {\n          return /*#__PURE__*/_jsxDEV(AboutCard, {\n            data: item\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 20\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n\n_c = CardList;\nexport default CardList;\n\nvar _c;\n\n$RefreshReg$(_c, \"CardList\");", "map": {"version": 3, "names": ["React", "SeactionHeading", "Carousel", "cardData", "AboutCard", "Styles", "CardList", "responsive", "superLargeDesktop", "breakpoint", "max", "min", "items", "slidesToSlide", "desktop", "tablet", "mobile", "map", "item", "index"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/list.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport SeactionHeading from \"../../global/seaction-title\";\nimport Carousel from \"react-multi-carousel\";\nimport \"react-multi-carousel/lib/styles.css\";\nimport { cardData } from \"./data\";\n\nimport AboutCard from \"./card\";\n\nimport * as Styles from \"./styles\";\n\nconst CardList = () => {\n  const responsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: { max: 4000, min: 3000 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    desktop: {\n      breakpoint: { max: 3000, min: 1024 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    tablet: {\n      breakpoint: { max: 1024, min: 664 },\n      items: 2,\n      slidesToSlide: 2,\n    },\n    mobile: {\n      breakpoint: { max: 664, min: 0 },\n      items: 1,\n      slidesToSlide: 1,\n    },\n  };\n\n  return (\n    <Styles.ListContainer>\n      <SeactionHeading title=\"Our Team\" />\n      <Styles.ListWrapper>\n        <Carousel responsive={responsive}>\n          {cardData.map((item, index) => {\n            return <AboutCard key={index} data={item} />;\n          })}\n        </Carousel>\n      </Styles.ListWrapper>\n    </Styles.ListContainer>\n  );\n};\n\nexport default CardList;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,eAAP,MAA4B,6BAA5B;AACA,OAAOC,QAAP,MAAqB,sBAArB;AACA,OAAO,qCAAP;AACA,SAASC,QAAT,QAAyB,QAAzB;AAEA,OAAOC,SAAP,MAAsB,QAAtB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,QAAQ,GAAG,MAAM;EACrB,MAAMC,UAAU,GAAG;IACjBC,iBAAiB,EAAE;MACjB;MACAC,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CAFK;MAGjBC,KAAK,EAAE,CAHU;MAIjBC,aAAa,EAAE;IAJE,CADF;IAOjBC,OAAO,EAAE;MACPL,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CADL;MAEPC,KAAK,EAAE,CAFA;MAGPC,aAAa,EAAE;IAHR,CAPQ;IAYjBE,MAAM,EAAE;MACNN,UAAU,EAAE;QAAEC,GAAG,EAAE,IAAP;QAAaC,GAAG,EAAE;MAAlB,CADN;MAENC,KAAK,EAAE,CAFD;MAGNC,aAAa,EAAE;IAHT,CAZS;IAiBjBG,MAAM,EAAE;MACNP,UAAU,EAAE;QAAEC,GAAG,EAAE,GAAP;QAAYC,GAAG,EAAE;MAAjB,CADN;MAENC,KAAK,EAAE,CAFD;MAGNC,aAAa,EAAE;IAHT;EAjBS,CAAnB;EAwBA,oBACE,QAAC,MAAD,CAAQ,aAAR;IAAA,wBACE,QAAC,eAAD;MAAiB,KAAK,EAAC;IAAvB;MAAA;MAAA;MAAA;IAAA,QADF,eAEE,QAAC,MAAD,CAAQ,WAAR;MAAA,uBACE,QAAC,QAAD;QAAU,UAAU,EAAEN,UAAtB;QAAA,UACGJ,QAAQ,CAACc,GAAT,CAAa,CAACC,IAAD,EAAOC,KAAP,KAAiB;UAC7B,oBAAO,QAAC,SAAD;YAAuB,IAAI,EAAED;UAA7B,GAAgBC,KAAhB;YAAA;YAAA;YAAA;UAAA,QAAP;QACD,CAFA;MADH;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QAFF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAYD,CArCD;;KAAMb,Q;AAuCN,eAAeA,QAAf"}, "metadata": {}, "sourceType": "module"}