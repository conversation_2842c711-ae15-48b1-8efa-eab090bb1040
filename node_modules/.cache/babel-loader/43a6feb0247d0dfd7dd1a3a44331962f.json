{"ast": null, "code": "var $String = String;\n\nmodule.exports = function (argument) {\n  try {\n    return $String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};", "map": {"version": 3, "names": ["$String", "String", "module", "exports", "argument", "error"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/try-to-string.js"], "sourcesContent": ["var $String = String;\n\nmodule.exports = function (argument) {\n  try {\n    return $String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,MAAd;;AAEAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,QAAV,EAAoB;EACnC,IAAI;IACF,OAAOJ,OAAO,CAACI,QAAD,CAAd;EACD,CAFD,CAEE,OAAOC,KAAP,EAAc;IACd,OAAO,QAAP;EACD;AACF,CAND"}, "metadata": {}, "sourceType": "script"}