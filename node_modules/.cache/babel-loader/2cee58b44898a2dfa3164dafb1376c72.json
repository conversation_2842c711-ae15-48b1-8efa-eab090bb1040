{"ast": null, "code": "export const validateParams = (publicKey, serviceID, templateID) => {\n  if (!publicKey) {\n    throw 'The public key is required. Visit https://dashboard.emailjs.com/admin/account';\n  }\n\n  if (!serviceID) {\n    throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n  }\n\n  if (!templateID) {\n    throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n  }\n\n  return true;\n};", "map": {"version": 3, "names": ["validateParams", "public<PERSON>ey", "serviceID", "templateID"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@emailjs/browser/es/utils/validateParams.js"], "sourcesContent": ["export const validateParams = (publicKey, serviceID, templateID) => {\n    if (!publicKey) {\n        throw 'The public key is required. Visit https://dashboard.emailjs.com/admin/account';\n    }\n    if (!serviceID) {\n        throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n    }\n    if (!templateID) {\n        throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n    }\n    return true;\n};\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAG,CAACC,SAAD,EAAYC,SAAZ,EAAuBC,UAAvB,KAAsC;EAChE,IAAI,CAACF,SAAL,EAAgB;IACZ,MAAM,+EAAN;EACH;;EACD,IAAI,CAACC,SAAL,EAAgB;IACZ,MAAM,uEAAN;EACH;;EACD,IAAI,CAACC,UAAL,EAAiB;IACb,MAAM,kFAAN;EACH;;EACD,OAAO,IAAP;AACH,CAXM"}, "metadata": {}, "sourceType": "module"}