{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/t&c/index.jsx\";\nimport React from 'react';\nimport Seaction from '../../components/global/seaction';\nimport TandC from '../../components/t&c';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst TandCPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: /*#__PURE__*/_jsxDEV(Seaction, {\n      children: /*#__PURE__*/_jsxDEV(TandC, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n};\n\n_c = TandCPage;\nexport default TandCPage;\n\nvar _c;\n\n$RefreshReg$(_c, \"TandCPage\");", "map": {"version": 3, "names": ["React", "Seaction", "TandC", "TandCPage"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/t&c/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport Seaction from '../../components/global/seaction'\n\nimport TandC from '../../components/t&c';\n\nconst TandCPage = () => {\n    return (\n        <div className=\"container\">\n            <Seaction>\n                <TandC />\n            </Seaction>\n        </div>\n    )\n}\n\nexport default TandCPage"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,QAAP,MAAqB,kCAArB;AAEA,OAAOC,KAAP,MAAkB,sBAAlB;;;AAEA,MAAMC,SAAS,GAAG,MAAM;EACpB,oBACI;IAAK,SAAS,EAAC,WAAf;IAAA,uBACI,QAAC,QAAD;MAAA,uBACI,QAAC,KAAD;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA;EADJ;IAAA;IAAA;IAAA;EAAA,QADJ;AAOH,CARD;;KAAMA,S;AAUN,eAAeA,SAAf"}, "metadata": {}, "sourceType": "module"}