{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  position: fixed;\n  bottom: 85px;\n  right: ${_ref => {\n  let {\n    active\n  } = _ref;\n  return active ? 0 : \"-50px\";\n}};\n  cursor: pointer;\n  display: inline-block;\n  opacity: ${_ref2 => {\n  let {\n    active\n  } = _ref2;\n  return active ? 1 : 0;\n}};\n  /* visibility: ${_ref3 => {\n  let {\n    active\n  } = _ref3;\n  return active ? \"visible\" : \"hidden\";\n}}; */\n  z-index: 9999;\n\n  transition: all 0.3s ease-in-out;\n`;\nexport const Wrapper = styled.div`\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: ${_ref4 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref4;\n  return colors.main.blue;\n}};\n  padding: 20px 25px;\n  border-top-left-radius: 100px;\n  border-bottom-left-radius: 100px;\n  box-shadow: 0 0 7px rgba(4, 153, 219, 0.4);\n\n`;\nexport const Trangletop = styled.div`\n  position: absolute;\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-bottom: 5px solid ${_ref5 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref5;\n  return colors.main.yellow;\n}}; ;\n`;\nexport const Tranglebottom = styled.div`\n  position: absolute;\n  top: 50%;\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-bottom: 5px solid $white;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "active", "Wrapper", "theme", "colors", "main", "blue", "Trangletop", "yellow", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  position: fixed;\n  bottom: 85px;\n  right: ${({ active }) => (active ? 0 : \"-50px\")};\n  cursor: pointer;\n  display: inline-block;\n  opacity: ${({ active }) => (active ? 1 : 0)};\n  /* visibility: ${({ active }) => (active ? \"visible\" : \"hidden\")}; */\n  z-index: 9999;\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const Wrapper = styled.div`\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: ${({ theme: { colors } }) => colors.main.blue};\n  padding: 20px 25px;\n  border-top-left-radius: 100px;\n  border-bottom-left-radius: 100px;\n  box-shadow: 0 0 7px rgba(4, 153, 219, 0.4);\n\n`;\n\nexport const Trangletop = styled.div`\n  position: absolute;\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-bottom: 5px solid ${({ theme: { colors } }) => colors.main.yellow}; ;\n`;\n\nexport const Tranglebottom = styled.div`\n  position: absolute;\n  top: 50%;\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-bottom: 5px solid $white;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA,WAAW;EAAA,IAAC;IAAEC;EAAF,CAAD;EAAA,OAAiBA,MAAM,GAAG,CAAH,GAAO,OAA9B;AAAA,CAAuC;AAClD;AACA;AACA,aAAa;EAAA,IAAC;IAAEA;EAAF,CAAD;EAAA,OAAiBA,MAAM,GAAG,CAAH,GAAO,CAA9B;AAAA,CAAiC;AAC9C,mBAAmB;EAAA,IAAC;IAAEA;EAAF,CAAD;EAAA,OAAiBA,MAAM,GAAG,SAAH,GAAe,QAAtC;AAAA,CAAgD;AACnE;AACA;AACA;AACA,CAXO;AAaP,OAAO,MAAMC,OAAO,GAAGJ,MAAM,CAACE,GAAI;AAClC;AACA;AACA;AACA;AACA,sBAAsB;EAAA,IAAC;IAAEG,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AAClE;AACA;AACA;AACA;AACA;AACA,CAXO;AAaP,OAAO,MAAMC,UAAU,GAAGT,MAAM,CAACE,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA,6BAA6B;EAAA,IAAC;IAAEG,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYG,MAAvC;AAAA,CAA8C;AAC3E,CAPO;AASP,OAAO,MAAMC,aAAa,GAAGX,MAAM,CAACE,GAAI;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CARO"}, "metadata": {}, "sourceType": "module"}