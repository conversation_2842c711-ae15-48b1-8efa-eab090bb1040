{"ast": null, "code": "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n\n/* globals __webpack_hash__ */\nif (module.hot) {\n  var lastHash;\n\n  var upToDate = function upToDate() {\n    return lastHash.indexOf(__webpack_hash__) >= 0;\n  };\n\n  var log = require(\"./log\");\n\n  var check = function check() {\n    module.hot.check(true).then(function (updatedModules) {\n      if (!updatedModules) {\n        log(\"warning\", \"[HMR] Cannot find update. Need to do a full reload!\");\n        log(\"warning\", \"[HMR] (Probably because of restarting the webpack-dev-server)\");\n        window.location.reload();\n        return;\n      }\n\n      if (!upToDate()) {\n        check();\n      }\n\n      require(\"./log-apply-result\")(updatedModules, updatedModules);\n\n      if (upToDate()) {\n        log(\"info\", \"[HMR] App is up to date.\");\n      }\n    }).catch(function (err) {\n      var status = module.hot.status();\n\n      if ([\"abort\", \"fail\"].indexOf(status) >= 0) {\n        log(\"warning\", \"[HMR] Cannot apply update. Need to do a full reload!\");\n        log(\"warning\", \"[HMR] \" + log.formatError(err));\n        window.location.reload();\n      } else {\n        log(\"warning\", \"[HMR] Update failed: \" + log.formatError(err));\n      }\n    });\n  };\n\n  var hotEmitter = require(\"./emitter\");\n\n  hotEmitter.on(\"webpackHotUpdate\", function (currentHash) {\n    lastHash = currentHash;\n\n    if (!upToDate() && module.hot.status() === \"idle\") {\n      log(\"info\", \"[HMR] Checking for updates on the server...\");\n      check();\n    }\n  });\n  log(\"info\", \"[HMR] Waiting for update signal from WDS...\");\n} else {\n  throw new Error(\"[HMR] Hot Module Replacement is disabled.\");\n}", "map": {"version": 3, "names": ["module", "hot", "lastHash", "upToDate", "indexOf", "__webpack_hash__", "log", "require", "check", "then", "updatedModules", "window", "location", "reload", "catch", "err", "status", "formatError", "hotEmitter", "on", "currentHash", "Error"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/webpack/hot/dev-server.js"], "sourcesContent": ["/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n/* globals __webpack_hash__ */\nif (module.hot) {\n\tvar lastHash;\n\tvar upToDate = function upToDate() {\n\t\treturn lastHash.indexOf(__webpack_hash__) >= 0;\n\t};\n\tvar log = require(\"./log\");\n\tvar check = function check() {\n\t\tmodule.hot\n\t\t\t.check(true)\n\t\t\t.then(function (updatedModules) {\n\t\t\t\tif (!updatedModules) {\n\t\t\t\t\tlog(\"warning\", \"[HMR] Cannot find update. Need to do a full reload!\");\n\t\t\t\t\tlog(\n\t\t\t\t\t\t\"warning\",\n\t\t\t\t\t\t\"[HMR] (Probably because of restarting the webpack-dev-server)\"\n\t\t\t\t\t);\n\t\t\t\t\twindow.location.reload();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!upToDate()) {\n\t\t\t\t\tcheck();\n\t\t\t\t}\n\n\t\t\t\trequire(\"./log-apply-result\")(updatedModules, updatedModules);\n\n\t\t\t\tif (upToDate()) {\n\t\t\t\t\tlog(\"info\", \"[HMR] App is up to date.\");\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(function (err) {\n\t\t\t\tvar status = module.hot.status();\n\t\t\t\tif ([\"abort\", \"fail\"].indexOf(status) >= 0) {\n\t\t\t\t\tlog(\n\t\t\t\t\t\t\"warning\",\n\t\t\t\t\t\t\"[HMR] Cannot apply update. Need to do a full reload!\"\n\t\t\t\t\t);\n\t\t\t\t\tlog(\"warning\", \"[HMR] \" + log.formatError(err));\n\t\t\t\t\twindow.location.reload();\n\t\t\t\t} else {\n\t\t\t\t\tlog(\"warning\", \"[HMR] Update failed: \" + log.formatError(err));\n\t\t\t\t}\n\t\t\t});\n\t};\n\tvar hotEmitter = require(\"./emitter\");\n\thotEmitter.on(\"webpackHotUpdate\", function (currentHash) {\n\t\tlastHash = currentHash;\n\t\tif (!upToDate() && module.hot.status() === \"idle\") {\n\t\t\tlog(\"info\", \"[HMR] Checking for updates on the server...\");\n\t\t\tcheck();\n\t\t}\n\t});\n\tlog(\"info\", \"[HMR] Waiting for update signal from WDS...\");\n} else {\n\tthrow new Error(\"[HMR] Hot Module Replacement is disabled.\");\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AACA;AACA,IAAIA,MAAM,CAACC,GAAX,EAAgB;EACf,IAAIC,QAAJ;;EACA,IAAIC,QAAQ,GAAG,SAASA,QAAT,GAAoB;IAClC,OAAOD,QAAQ,CAACE,OAAT,CAAiBC,gBAAjB,KAAsC,CAA7C;EACA,CAFD;;EAGA,IAAIC,GAAG,GAAGC,OAAO,CAAC,OAAD,CAAjB;;EACA,IAAIC,KAAK,GAAG,SAASA,KAAT,GAAiB;IAC5BR,MAAM,CAACC,GAAP,CACEO,KADF,CACQ,IADR,EAEEC,IAFF,CAEO,UAAUC,cAAV,EAA0B;MAC/B,IAAI,CAACA,cAAL,EAAqB;QACpBJ,GAAG,CAAC,SAAD,EAAY,qDAAZ,CAAH;QACAA,GAAG,CACF,SADE,EAEF,+DAFE,CAAH;QAIAK,MAAM,CAACC,QAAP,CAAgBC,MAAhB;QACA;MACA;;MAED,IAAI,CAACV,QAAQ,EAAb,EAAiB;QAChBK,KAAK;MACL;;MAEDD,OAAO,CAAC,oBAAD,CAAP,CAA8BG,cAA9B,EAA8CA,cAA9C;;MAEA,IAAIP,QAAQ,EAAZ,EAAgB;QACfG,GAAG,CAAC,MAAD,EAAS,0BAAT,CAAH;MACA;IACD,CAtBF,EAuBEQ,KAvBF,CAuBQ,UAAUC,GAAV,EAAe;MACrB,IAAIC,MAAM,GAAGhB,MAAM,CAACC,GAAP,CAAWe,MAAX,EAAb;;MACA,IAAI,CAAC,OAAD,EAAU,MAAV,EAAkBZ,OAAlB,CAA0BY,MAA1B,KAAqC,CAAzC,EAA4C;QAC3CV,GAAG,CACF,SADE,EAEF,sDAFE,CAAH;QAIAA,GAAG,CAAC,SAAD,EAAY,WAAWA,GAAG,CAACW,WAAJ,CAAgBF,GAAhB,CAAvB,CAAH;QACAJ,MAAM,CAACC,QAAP,CAAgBC,MAAhB;MACA,CAPD,MAOO;QACNP,GAAG,CAAC,SAAD,EAAY,0BAA0BA,GAAG,CAACW,WAAJ,CAAgBF,GAAhB,CAAtC,CAAH;MACA;IACD,CAnCF;EAoCA,CArCD;;EAsCA,IAAIG,UAAU,GAAGX,OAAO,CAAC,WAAD,CAAxB;;EACAW,UAAU,CAACC,EAAX,CAAc,kBAAd,EAAkC,UAAUC,WAAV,EAAuB;IACxDlB,QAAQ,GAAGkB,WAAX;;IACA,IAAI,CAACjB,QAAQ,EAAT,IAAeH,MAAM,CAACC,GAAP,CAAWe,MAAX,OAAwB,MAA3C,EAAmD;MAClDV,GAAG,CAAC,MAAD,EAAS,6CAAT,CAAH;MACAE,KAAK;IACL;EACD,CAND;EAOAF,GAAG,CAAC,MAAD,EAAS,6CAAT,CAAH;AACA,CArDD,MAqDO;EACN,MAAM,IAAIe,KAAJ,CAAU,2CAAV,CAAN;AACA"}, "metadata": {}, "sourceType": "script"}