{"ast": null, "code": "import styled from 'styled-components';\nexport const Container = styled.div`\n    display: flex;\n    align-items: flex-start;\n    justify-content: space-between;\n\n    flex-wrap: wrap;\n\n    gap: 20px;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const Container = styled.div`\n    display: flex;\n    align-items: flex-start;\n    justify-content: space-between;\n\n    flex-wrap: wrap;\n\n    gap: 20px;\n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CARO"}, "metadata": {}, "sourceType": "module"}