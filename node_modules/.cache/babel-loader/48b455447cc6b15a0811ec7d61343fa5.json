{"ast": null, "code": "import Logo from \"../../assets/default/logo.png\";\nimport LogoSvg from \"../../assets/default/logo2.svg\";\nimport Bg from \"../../assets/default/bg.png\";\nimport Clients from \"../../assets/default/clients.png\";\nimport SignupFormIcon from \"../../assets/signup/signup-form.png\";\nimport Retro from \"../../assets/services/retro.png\";\nimport Waterproofing from \"../../assets/services/waterproofing.png\";\nimport Envcoating from \"../../assets/services/envcoating.png\";\nimport Industrial from \"../../assets/services/industrial.png\";\nimport Watertank from \"../../assets/services/watertank.png\";\nimport Publicbuildings from \"../../assets/services/publicbuildings.png\";\nimport Save from \"../../assets/projects/save.png\";\nimport ChevronLeft from \"../../assets/projects/chevron-left.png\"; //projects\n\nimport embassy1 from \"../../assets/projects/embassy/1.jpg\";\nimport embassy2 from \"../../assets/projects/embassy/2.jpg\";\nimport embassy3 from \"../../assets/projects/embassy/3.jpg\";\nimport fourSeasonResidancy1 from \"../../assets/projects/four-season-residancy/1.jpg\";\nimport fourSeasonResidancy2 from \"../../assets/projects/four-season-residancy/2.jpg\";\nimport fourSeasonResidancy3 from \"../../assets/projects/four-season-residancy/3.jpg\";\nimport fourSeasonResidancy4 from \"../../assets/projects/four-season-residancy/4.jpg\";\nimport hawre1 from \"../../assets/projects/hawre/1.jpg\";\nimport hawre2 from \"../../assets/projects/hawre/2.jpg\";\nimport hawre3 from \"../../assets/projects/hawre/3.jpg\";\nimport hawre4 from \"../../assets/projects/hawre/4.jpg\";\nimport hawre5 from \"../../assets/projects/hawre/5.jpg\";\nimport megaMall1 from \"../../assets/projects/mega-mall/1.jpg\";\nimport megaMall2 from \"../../assets/projects/mega-mall/2.jpg\";\nimport megaMall3 from \"../../assets/projects/mega-mall/3.jpg\";\nimport metroMall1 from \"../../assets/projects/metro-mall/1.jpg\";\nimport metroMall2 from \"../../assets/projects/metro-mall/2.jpg\";\nimport metroMall3 from \"../../assets/projects/metro-mall/3.jpg\";\nimport metroMall4 from \"../../assets/projects/metro-mall/4.jpg\";\nimport milleniumMall1 from \"../../assets/projects/millenium-mall/1.jpg\";\nimport milleniumMall2 from \"../../assets/projects/millenium-mall/2.jpg\";\nimport milleniumMall3 from \"../../assets/projects/millenium-mall/3.jpg\";\nimport milleniumMall4 from \"../../assets/projects/millenium-mall/4.jpg\";\nimport raghavChs1 from \"../../assets/projects/raghav-chs/1.jpg\";\nimport raghavChs2 from \"../../assets/projects/raghav-chs/2.jpg\";\nimport raghavChs3 from \"../../assets/projects/raghav-chs/3.jpg\";\nimport siyaramMill1 from \"../../assets/projects/siyaram-mill/1.jpg\";\nimport siyaramMill2 from \"../../assets/projects/siyaram-mill/2.jpg\";\nimport siyaramMill3 from \"../../assets/projects/siyaram-mill/3.jpg\";\nimport siyaramMill4 from \"../../assets/projects/siyaram-mill/4.jpg\";\nimport thakurCollege1 from \"../../assets/projects/thakur-college/1.jpg\";\nimport thakurCollege2 from \"../../assets/projects/thakur-college/2.jpg\";\nimport thakurCollege3 from \"../../assets/projects/thakur-college/3.jpg\";\nimport tarapur1 from \"../../assets/projects/tarapur/1.jpg\";\nimport tarapur2 from \"../../assets/projects/tarapur/2.jpg\";\nimport tarapur3 from \"../../assets/projects/tarapur/3.jpg\";\nimport raymond1 from \"../../assets/projects/raymond/1.jpg\";\nimport raymond2 from \"../../assets/projects/raymond/2.jpg\";\nimport raymond3 from \"../../assets/projects/raymond/3.jpg\";\nimport HomeBg from \"../../assets/slider/11.jpg\";\nimport AboutBg from \"../../assets/slider/7.jpg\";\nimport ProjectsBg from \"../../assets/slider/1.jpg\";\nimport ClientBg from \"../../assets/slider/8.jpg\";\nimport ContactBg from \"../../assets/slider/12.jpg\";\nexport const AssetsList = {\n  logo: {\n    Logo,\n    LogoSvg\n  },\n  bg: Bg,\n  client: Clients,\n  signFormIcon: SignupFormIcon,\n  services: {\n    Retro,\n    Waterproofing,\n    Envcoating,\n    Industrial,\n    Watertank,\n    Publicbuildings\n  },\n  save: Save,\n  chevron: {\n    left: ChevronLeft\n  },\n  titleBg: {\n    home: HomeBg,\n    about: AboutBg,\n    projects: ProjectsBg,\n    clients: ClientBg,\n    contact: ContactBg\n  },\n  projects: {\n    embassy: {\n      embassy1,\n      embassy2,\n      embassy3\n    },\n    fourSeasonResidancy: {\n      fourSeasonResidancy1,\n      fourSeasonResidancy2,\n      fourSeasonResidancy3,\n      fourSeasonResidancy4\n    },\n    hawre: {\n      hawre1,\n      hawre2,\n      hawre3,\n      hawre4,\n      hawre5\n    },\n    megaMall: {\n      megaMall1,\n      megaMall2,\n      megaMall3\n    },\n    metroMall: {\n      metroMall1,\n      metroMall2,\n      metroMall3,\n      metroMall4\n    },\n    milleniumMall: {\n      milleniumMall1,\n      milleniumMall2,\n      milleniumMall3,\n      milleniumMall4\n    },\n    raghavChs: {\n      raghavChs1,\n      raghavChs2,\n      raghavChs3\n    },\n    siyaramMill: {\n      siyaramMill1,\n      siyaramMill2,\n      siyaramMill3,\n      siyaramMill4\n    },\n    thakurCollege: {\n      thakurCollege1,\n      thakurCollege2,\n      thakurCollege3\n    },\n    tarapur: {\n      tarapur1,\n      tarapur2,\n      tarapur3\n    },\n    raymond: {\n      raymond1,\n      raymond2,\n      raymond3\n    }\n  }\n};", "map": {"version": 3, "names": ["Logo", "LogoSvg", "Bg", "Clients", "SignupFormIcon", "Retro", "Waterproofing", "Envcoating", "Industrial", "Watertank", "Publicbuildings", "Save", "ChevronLeft", "embassy1", "embassy2", "embassy3", "fourSeasonResidancy1", "fourSeasonResidancy2", "fourSeasonResidancy3", "fourSeasonResidancy4", "hawre1", "hawre2", "hawre3", "hawre4", "hawre5", "megaMall1", "megaMall2", "megaMall3", "metroMall1", "metroMall2", "metroMall3", "metroMall4", "milleniumMall1", "milleniumMall2", "milleniumMall3", "milleniumMall4", "raghavChs1", "raghavChs2", "raghavChs3", "siyaramMill1", "siyaramMill2", "siyaramMill3", "siyaramMill4", "thakurCollege1", "thakurCollege2", "thakurCollege3", "tarapur1", "tarapur2", "tarapur3", "raymond1", "raymond2", "raymond3", "HomeBg", "AboutBg", "ProjectsBg", "ClientBg", "ContactBg", "AssetsList", "logo", "bg", "client", "signFormIcon", "services", "save", "chevron", "left", "titleBg", "home", "about", "projects", "clients", "contact", "embassy", "fourSeasonResidancy", "hawre", "megaMall", "metroMall", "milleniumMall", "raghavChs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thak<PERSON><PERSON><PERSON><PERSON>", "tarapur", "<PERSON><PERSON>"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/assetsList.js"], "sourcesContent": ["import Logo from \"../../assets/default/logo.png\";\nimport LogoSvg from \"../../assets/default/logo2.svg\";\nimport Bg from \"../../assets/default/bg.png\";\nimport Clients from \"../../assets/default/clients.png\";\nimport SignupFormIcon from \"../../assets/signup/signup-form.png\";\n\nimport Retro from \"../../assets/services/retro.png\";\nimport Waterproofing from \"../../assets/services/waterproofing.png\";\nimport Envcoating from \"../../assets/services/envcoating.png\";\nimport Industrial from \"../../assets/services/industrial.png\";\nimport Watertank from \"../../assets/services/watertank.png\";\nimport Publicbuildings from \"../../assets/services/publicbuildings.png\";\n\nimport Save from \"../../assets/projects/save.png\";\nimport ChevronLeft from \"../../assets/projects/chevron-left.png\";\n\n//projects\nimport embassy1 from \"../../assets/projects/embassy/1.jpg\";\nimport embassy2 from \"../../assets/projects/embassy/2.jpg\";\nimport embassy3 from \"../../assets/projects/embassy/3.jpg\";\nimport fourSeasonResidancy1 from \"../../assets/projects/four-season-residancy/1.jpg\";\nimport fourSeasonResidancy2 from \"../../assets/projects/four-season-residancy/2.jpg\";\nimport fourSeasonResidancy3 from \"../../assets/projects/four-season-residancy/3.jpg\";\nimport fourSeasonResidancy4 from \"../../assets/projects/four-season-residancy/4.jpg\";\nimport hawre1 from \"../../assets/projects/hawre/1.jpg\";\nimport hawre2 from \"../../assets/projects/hawre/2.jpg\";\nimport hawre3 from \"../../assets/projects/hawre/3.jpg\";\nimport hawre4 from \"../../assets/projects/hawre/4.jpg\";\nimport hawre5 from \"../../assets/projects/hawre/5.jpg\";\nimport megaMall1 from \"../../assets/projects/mega-mall/1.jpg\";\nimport megaMall2 from \"../../assets/projects/mega-mall/2.jpg\";\nimport megaMall3 from \"../../assets/projects/mega-mall/3.jpg\";\nimport metroMall1 from \"../../assets/projects/metro-mall/1.jpg\";\nimport metroMall2 from \"../../assets/projects/metro-mall/2.jpg\";\nimport metroMall3 from \"../../assets/projects/metro-mall/3.jpg\";\nimport metroMall4 from \"../../assets/projects/metro-mall/4.jpg\";\nimport milleniumMall1 from \"../../assets/projects/millenium-mall/1.jpg\";\nimport milleniumMall2 from \"../../assets/projects/millenium-mall/2.jpg\";\nimport milleniumMall3 from \"../../assets/projects/millenium-mall/3.jpg\";\nimport milleniumMall4 from \"../../assets/projects/millenium-mall/4.jpg\";\nimport raghavChs1 from \"../../assets/projects/raghav-chs/1.jpg\";\nimport raghavChs2 from \"../../assets/projects/raghav-chs/2.jpg\";\nimport raghavChs3 from \"../../assets/projects/raghav-chs/3.jpg\";\nimport siyaramMill1 from \"../../assets/projects/siyaram-mill/1.jpg\";\nimport siyaramMill2 from \"../../assets/projects/siyaram-mill/2.jpg\";\nimport siyaramMill3 from \"../../assets/projects/siyaram-mill/3.jpg\";\nimport siyaramMill4 from \"../../assets/projects/siyaram-mill/4.jpg\";\nimport thakurCollege1 from \"../../assets/projects/thakur-college/1.jpg\";\nimport thakurCollege2 from \"../../assets/projects/thakur-college/2.jpg\";\nimport thakurCollege3 from \"../../assets/projects/thakur-college/3.jpg\";\n\nimport tarapur1 from \"../../assets/projects/tarapur/1.jpg\";\nimport tarapur2 from \"../../assets/projects/tarapur/2.jpg\";\nimport tarapur3 from \"../../assets/projects/tarapur/3.jpg\";\n\nimport raymond1 from \"../../assets/projects/raymond/1.jpg\";\nimport raymond2 from \"../../assets/projects/raymond/2.jpg\";\nimport raymond3 from \"../../assets/projects/raymond/3.jpg\";\n\nimport HomeBg from \"../../assets/slider/11.jpg\";\nimport AboutBg from \"../../assets/slider/7.jpg\";\nimport ProjectsBg from \"../../assets/slider/1.jpg\";\nimport ClientBg from \"../../assets/slider/8.jpg\";\nimport ContactBg from \"../../assets/slider/12.jpg\";\n\nexport const AssetsList = {\n  logo: {\n    Logo,\n    LogoSvg,\n  },\n  bg: Bg,\n  client: Clients,\n  signFormIcon: SignupFormIcon,\n  services: {\n    Retro,\n    Waterproofing,\n    Envcoating,\n    Industrial,\n    Watertank,\n    Publicbuildings,\n  },\n  save: Save,\n  chevron: {\n    left: ChevronLeft,\n  },\n  titleBg: {\n    home: HomeBg,\n    about: AboutBg,\n    projects: ProjectsBg,\n    clients: ClientBg,\n    contact: ContactBg,\n  },\n  projects: {\n    embassy: {\n      embassy1,\n      embassy2,\n      embassy3,\n    },\n    fourSeasonResidancy: {\n      fourSeasonResidancy1,\n      fourSeasonResidancy2,\n      fourSeasonResidancy3,\n      fourSeasonResidancy4,\n    },\n    hawre: {\n      hawre1,\n      hawre2,\n      hawre3,\n      hawre4,\n      hawre5,\n    },\n    megaMall: {\n      megaMall1,\n      megaMall2,\n      megaMall3,\n    },\n    metroMall: {\n      metroMall1,\n      metroMall2,\n      metroMall3,\n      metroMall4,\n    },\n    milleniumMall: {\n      milleniumMall1,\n      milleniumMall2,\n      milleniumMall3,\n      milleniumMall4,\n    },\n    raghavChs: {\n      raghavChs1,\n      raghavChs2,\n      raghavChs3,\n    },\n    siyaramMill: {\n      siyaramMill1,\n      siyaramMill2,\n      siyaramMill3,\n      siyaramMill4,\n    },\n    thakurCollege: {\n      thakurCollege1,\n      thakurCollege2,\n      thakurCollege3,\n    },\n    tarapur: {\n      tarapur1,\n      tarapur2,\n      tarapur3,\n    },\n    raymond: {\n      raymond1,\n      raymond2,\n      raymond3,\n    },\n  },\n};\n"], "mappings": "AAAA,OAAOA,IAAP,MAAiB,+BAAjB;AACA,OAAOC,OAAP,MAAoB,gCAApB;AACA,OAAOC,EAAP,MAAe,6BAAf;AACA,OAAOC,OAAP,MAAoB,kCAApB;AACA,OAAOC,cAAP,MAA2B,qCAA3B;AAEA,OAAOC,KAAP,MAAkB,iCAAlB;AACA,OAAOC,aAAP,MAA0B,yCAA1B;AACA,OAAOC,UAAP,MAAuB,sCAAvB;AACA,OAAOC,UAAP,MAAuB,sCAAvB;AACA,OAAOC,SAAP,MAAsB,qCAAtB;AACA,OAAOC,eAAP,MAA4B,2CAA5B;AAEA,OAAOC,IAAP,MAAiB,gCAAjB;AACA,OAAOC,WAAP,MAAwB,wCAAxB,C,CAEA;;AACA,OAAOC,QAAP,MAAqB,qCAArB;AACA,OAAOC,QAAP,MAAqB,qCAArB;AACA,OAAOC,QAAP,MAAqB,qCAArB;AACA,OAAOC,oBAAP,MAAiC,mDAAjC;AACA,OAAOC,oBAAP,MAAiC,mDAAjC;AACA,OAAOC,oBAAP,MAAiC,mDAAjC;AACA,OAAOC,oBAAP,MAAiC,mDAAjC;AACA,OAAOC,MAAP,MAAmB,mCAAnB;AACA,OAAOC,MAAP,MAAmB,mCAAnB;AACA,OAAOC,MAAP,MAAmB,mCAAnB;AACA,OAAOC,MAAP,MAAmB,mCAAnB;AACA,OAAOC,MAAP,MAAmB,mCAAnB;AACA,OAAOC,SAAP,MAAsB,uCAAtB;AACA,OAAOC,SAAP,MAAsB,uCAAtB;AACA,OAAOC,SAAP,MAAsB,uCAAtB;AACA,OAAOC,UAAP,MAAuB,wCAAvB;AACA,OAAOC,UAAP,MAAuB,wCAAvB;AACA,OAAOC,UAAP,MAAuB,wCAAvB;AACA,OAAOC,UAAP,MAAuB,wCAAvB;AACA,OAAOC,cAAP,MAA2B,4CAA3B;AACA,OAAOC,cAAP,MAA2B,4CAA3B;AACA,OAAOC,cAAP,MAA2B,4CAA3B;AACA,OAAOC,cAAP,MAA2B,4CAA3B;AACA,OAAOC,UAAP,MAAuB,wCAAvB;AACA,OAAOC,UAAP,MAAuB,wCAAvB;AACA,OAAOC,UAAP,MAAuB,wCAAvB;AACA,OAAOC,YAAP,MAAyB,0CAAzB;AACA,OAAOC,YAAP,MAAyB,0CAAzB;AACA,OAAOC,YAAP,MAAyB,0CAAzB;AACA,OAAOC,YAAP,MAAyB,0CAAzB;AACA,OAAOC,cAAP,MAA2B,4CAA3B;AACA,OAAOC,cAAP,MAA2B,4CAA3B;AACA,OAAOC,cAAP,MAA2B,4CAA3B;AAEA,OAAOC,QAAP,MAAqB,qCAArB;AACA,OAAOC,QAAP,MAAqB,qCAArB;AACA,OAAOC,QAAP,MAAqB,qCAArB;AAEA,OAAOC,QAAP,MAAqB,qCAArB;AACA,OAAOC,QAAP,MAAqB,qCAArB;AACA,OAAOC,QAAP,MAAqB,qCAArB;AAEA,OAAOC,MAAP,MAAmB,4BAAnB;AACA,OAAOC,OAAP,MAAoB,2BAApB;AACA,OAAOC,UAAP,MAAuB,2BAAvB;AACA,OAAOC,QAAP,MAAqB,2BAArB;AACA,OAAOC,SAAP,MAAsB,4BAAtB;AAEA,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE;IACJ1D,IADI;IAEJC;EAFI,CADkB;EAKxB0D,EAAE,EAAEzD,EALoB;EAMxB0D,MAAM,EAAEzD,OANgB;EAOxB0D,YAAY,EAAEzD,cAPU;EAQxB0D,QAAQ,EAAE;IACRzD,KADQ;IAERC,aAFQ;IAGRC,UAHQ;IAIRC,UAJQ;IAKRC,SALQ;IAMRC;EANQ,CARc;EAgBxBqD,IAAI,EAAEpD,IAhBkB;EAiBxBqD,OAAO,EAAE;IACPC,IAAI,EAAErD;EADC,CAjBe;EAoBxBsD,OAAO,EAAE;IACPC,IAAI,EAAEf,MADC;IAEPgB,KAAK,EAAEf,OAFA;IAGPgB,QAAQ,EAAEf,UAHH;IAIPgB,OAAO,EAAEf,QAJF;IAKPgB,OAAO,EAAEf;EALF,CApBe;EA2BxBa,QAAQ,EAAE;IACRG,OAAO,EAAE;MACP3D,QADO;MAEPC,QAFO;MAGPC;IAHO,CADD;IAMR0D,mBAAmB,EAAE;MACnBzD,oBADmB;MAEnBC,oBAFmB;MAGnBC,oBAHmB;MAInBC;IAJmB,CANb;IAYRuD,KAAK,EAAE;MACLtD,MADK;MAELC,MAFK;MAGLC,MAHK;MAILC,MAJK;MAKLC;IALK,CAZC;IAmBRmD,QAAQ,EAAE;MACRlD,SADQ;MAERC,SAFQ;MAGRC;IAHQ,CAnBF;IAwBRiD,SAAS,EAAE;MACThD,UADS;MAETC,UAFS;MAGTC,UAHS;MAITC;IAJS,CAxBH;IA8BR8C,aAAa,EAAE;MACb7C,cADa;MAEbC,cAFa;MAGbC,cAHa;MAIbC;IAJa,CA9BP;IAoCR2C,SAAS,EAAE;MACT1C,UADS;MAETC,UAFS;MAGTC;IAHS,CApCH;IAyCRyC,WAAW,EAAE;MACXxC,YADW;MAEXC,YAFW;MAGXC,YAHW;MAIXC;IAJW,CAzCL;IA+CRsC,aAAa,EAAE;MACbrC,cADa;MAEbC,cAFa;MAGbC;IAHa,CA/CP;IAoDRoC,OAAO,EAAE;MACPnC,QADO;MAEPC,QAFO;MAGPC;IAHO,CApDD;IAyDRkC,OAAO,EAAE;MACPjC,QADO;MAEPC,QAFO;MAGPC;IAHO;EAzDD;AA3Bc,CAAnB"}, "metadata": {}, "sourceType": "module"}