{"ast": null, "code": "\"use strict\";\n\nfunction getOriginalCounterPart(index, _a, childrenArr) {\n  var slidesToShow = _a.slidesToShow,\n      currentSlide = _a.currentSlide;\n  return childrenArr.length > 2 * slidesToShow ? index + 2 * slidesToShow : currentSlide >= childrenArr.length ? childrenArr.length + index : index;\n}\n\nfunction getOriginalIndexLookupTableByClones(slidesToShow, childrenArr) {\n  if (childrenArr.length > 2 * slidesToShow) {\n    for (var table = {}, firstBeginningOfClones = childrenArr.length - 2 * slidesToShow, firstEndOfClones = childrenArr.length - firstBeginningOfClones, firstCount = firstBeginningOfClones, i = 0; i < firstEndOfClones; i++) table[i] = firstCount, firstCount++;\n\n    var secondBeginningOfClones = childrenArr.length + firstEndOfClones,\n        secondEndOfClones = secondBeginningOfClones + childrenArr.slice(0, 2 * slidesToShow).length,\n        secondCount = 0;\n\n    for (i = secondBeginningOfClones; i <= secondEndOfClones; i++) table[i] = secondCount, secondCount++;\n\n    var originalEnd = secondBeginningOfClones,\n        originalCounter = 0;\n\n    for (i = firstEndOfClones; i < originalEnd; i++) table[i] = originalCounter, originalCounter++;\n\n    return table;\n  }\n\n  table = {};\n  var totalSlides = 3 * childrenArr.length,\n      count = 0;\n\n  for (i = 0; i < totalSlides; i++) table[i] = count, ++count === childrenArr.length && (count = 0);\n\n  return table;\n}\n\nfunction getClones(slidesToShow, childrenArr) {\n  return childrenArr.length < slidesToShow ? childrenArr : childrenArr.length > 2 * slidesToShow ? childrenArr.slice(childrenArr.length - 2 * slidesToShow, childrenArr.length).concat(childrenArr, childrenArr.slice(0, 2 * slidesToShow)) : childrenArr.concat(childrenArr, childrenArr);\n}\n\nfunction getInitialSlideInInfiniteMode(slidesToShow, childrenArr) {\n  return childrenArr.length > 2 * slidesToShow ? 2 * slidesToShow : childrenArr.length;\n}\n\nfunction checkClonesPosition(_a, childrenArr, props) {\n  var isReachingTheEnd,\n      currentSlide = _a.currentSlide,\n      slidesToShow = _a.slidesToShow,\n      itemWidth = _a.itemWidth,\n      totalItems = _a.totalItems,\n      nextSlide = 0,\n      nextPosition = 0,\n      isReachingTheStart = 0 === currentSlide,\n      originalFirstSlide = childrenArr.length - (childrenArr.length - 2 * slidesToShow);\n  return childrenArr.length < slidesToShow ? (nextPosition = nextSlide = 0, isReachingTheStart = isReachingTheEnd = !1) : childrenArr.length > 2 * slidesToShow ? ((isReachingTheEnd = currentSlide >= originalFirstSlide + childrenArr.length) && (nextPosition = -itemWidth * (nextSlide = currentSlide - childrenArr.length)), isReachingTheStart && (nextPosition = -itemWidth * (nextSlide = originalFirstSlide + (childrenArr.length - 2 * slidesToShow)))) : ((isReachingTheEnd = currentSlide >= 2 * childrenArr.length) && (nextPosition = -itemWidth * (nextSlide = currentSlide - childrenArr.length)), isReachingTheStart && (nextPosition = props.showDots ? -itemWidth * (nextSlide = childrenArr.length) : -itemWidth * (nextSlide = totalItems / 3))), {\n    isReachingTheEnd: isReachingTheEnd,\n    isReachingTheStart: isReachingTheStart,\n    nextSlide: nextSlide,\n    nextPosition: nextPosition\n  };\n}\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n}), exports.getOriginalCounterPart = getOriginalCounterPart, exports.getOriginalIndexLookupTableByClones = getOriginalIndexLookupTableByClones, exports.getClones = getClones, exports.getInitialSlideInInfiniteMode = getInitialSlideInInfiniteMode, exports.checkClonesPosition = checkClonesPosition;", "map": {"version": 3, "names": ["getOriginalCounterPart", "index", "_a", "childrenArr", "slidesToShow", "currentSlide", "length", "getOriginalIndexLookupTableByClones", "table", "firstBeginningOfClones", "firstEndOfClones", "firstCount", "i", "secondBeginningOfClones", "secondEndOfClones", "slice", "secondCount", "originalEnd", "originalCounter", "totalSlides", "count", "getClones", "concat", "getInitialSlideInInfiniteMode", "checkClonesPosition", "props", "isReachingTheEnd", "itemWidth", "totalItems", "nextSlide", "nextPosition", "isReachingTheStart", "originalFirstSlide", "showDots", "Object", "defineProperty", "exports", "value"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/clones.js"], "sourcesContent": ["\"use strict\";function getOriginalCounterPart(index,_a,childrenArr){var slidesToShow=_a.slidesToShow,currentSlide=_a.currentSlide;return childrenArr.length>2*slidesToShow?index+2*slidesToShow:currentSlide>=childrenArr.length?childrenArr.length+index:index}function getOriginalIndexLookupTableByClones(slidesToShow,childrenArr){if(childrenArr.length>2*slidesToShow){for(var table={},firstBeginningOfClones=childrenArr.length-2*slidesToShow,firstEndOfClones=childrenArr.length-firstBeginningOfClones,firstCount=firstBeginningOfClones,i=0;i<firstEndOfClones;i++)table[i]=firstCount,firstCount++;var secondBeginningOfClones=childrenArr.length+firstEndOfClones,secondEndOfClones=secondBeginningOfClones+childrenArr.slice(0,2*slidesToShow).length,secondCount=0;for(i=secondBeginningOfClones;i<=secondEndOfClones;i++)table[i]=secondCount,secondCount++;var originalEnd=secondBeginningOfClones,originalCounter=0;for(i=firstEndOfClones;i<originalEnd;i++)table[i]=originalCounter,originalCounter++;return table}table={};var totalSlides=3*childrenArr.length,count=0;for(i=0;i<totalSlides;i++)table[i]=count,++count===childrenArr.length&&(count=0);return table}function getClones(slidesToShow,childrenArr){return childrenArr.length<slidesToShow?childrenArr:childrenArr.length>2*slidesToShow?childrenArr.slice(childrenArr.length-2*slidesToShow,childrenArr.length).concat(childrenArr,childrenArr.slice(0,2*slidesToShow)):childrenArr.concat(childrenArr,childrenArr)}function getInitialSlideInInfiniteMode(slidesToShow,childrenArr){return childrenArr.length>2*slidesToShow?2*slidesToShow:childrenArr.length}function checkClonesPosition(_a,childrenArr,props){var isReachingTheEnd,currentSlide=_a.currentSlide,slidesToShow=_a.slidesToShow,itemWidth=_a.itemWidth,totalItems=_a.totalItems,nextSlide=0,nextPosition=0,isReachingTheStart=0===currentSlide,originalFirstSlide=childrenArr.length-(childrenArr.length-2*slidesToShow);return childrenArr.length<slidesToShow?(nextPosition=nextSlide=0,isReachingTheStart=isReachingTheEnd=!1):childrenArr.length>2*slidesToShow?((isReachingTheEnd=currentSlide>=originalFirstSlide+childrenArr.length)&&(nextPosition=-itemWidth*(nextSlide=currentSlide-childrenArr.length)),isReachingTheStart&&(nextPosition=-itemWidth*(nextSlide=originalFirstSlide+(childrenArr.length-2*slidesToShow)))):((isReachingTheEnd=currentSlide>=2*childrenArr.length)&&(nextPosition=-itemWidth*(nextSlide=currentSlide-childrenArr.length)),isReachingTheStart&&(nextPosition=props.showDots?-itemWidth*(nextSlide=childrenArr.length):-itemWidth*(nextSlide=totalItems/3))),{isReachingTheEnd:isReachingTheEnd,isReachingTheStart:isReachingTheStart,nextSlide:nextSlide,nextPosition:nextPosition}}Object.defineProperty(exports,\"__esModule\",{value:!0}),exports.getOriginalCounterPart=getOriginalCounterPart,exports.getOriginalIndexLookupTableByClones=getOriginalIndexLookupTableByClones,exports.getClones=getClones,exports.getInitialSlideInInfiniteMode=getInitialSlideInInfiniteMode,exports.checkClonesPosition=checkClonesPosition;"], "mappings": "AAAA;;AAAa,SAASA,sBAAT,CAAgCC,KAAhC,EAAsCC,EAAtC,EAAyCC,WAAzC,EAAqD;EAAC,IAAIC,YAAY,GAACF,EAAE,CAACE,YAApB;EAAA,IAAiCC,YAAY,GAACH,EAAE,CAACG,YAAjD;EAA8D,OAAOF,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAArB,GAAkCH,KAAK,GAAC,IAAEG,YAA1C,GAAuDC,YAAY,IAAEF,WAAW,CAACG,MAA1B,GAAiCH,WAAW,CAACG,MAAZ,GAAmBL,KAApD,GAA0DA,KAAxH;AAA8H;;AAAA,SAASM,mCAAT,CAA6CH,YAA7C,EAA0DD,WAA1D,EAAsE;EAAC,IAAGA,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAAxB,EAAqC;IAAC,KAAI,IAAII,KAAK,GAAC,EAAV,EAAaC,sBAAsB,GAACN,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAAzD,EAAsEM,gBAAgB,GAACP,WAAW,CAACG,MAAZ,GAAmBG,sBAA1G,EAAiIE,UAAU,GAACF,sBAA5I,EAAmKG,CAAC,GAAC,CAAzK,EAA2KA,CAAC,GAACF,gBAA7K,EAA8LE,CAAC,EAA/L,EAAkMJ,KAAK,CAACI,CAAD,CAAL,GAASD,UAAT,EAAoBA,UAAU,EAA9B;;IAAiC,IAAIE,uBAAuB,GAACV,WAAW,CAACG,MAAZ,GAAmBI,gBAA/C;IAAA,IAAgEI,iBAAiB,GAACD,uBAAuB,GAACV,WAAW,CAACY,KAAZ,CAAkB,CAAlB,EAAoB,IAAEX,YAAtB,EAAoCE,MAA9I;IAAA,IAAqJU,WAAW,GAAC,CAAjK;;IAAmK,KAAIJ,CAAC,GAACC,uBAAN,EAA8BD,CAAC,IAAEE,iBAAjC,EAAmDF,CAAC,EAApD,EAAuDJ,KAAK,CAACI,CAAD,CAAL,GAASI,WAAT,EAAqBA,WAAW,EAAhC;;IAAmC,IAAIC,WAAW,GAACJ,uBAAhB;IAAA,IAAwCK,eAAe,GAAC,CAAxD;;IAA0D,KAAIN,CAAC,GAACF,gBAAN,EAAuBE,CAAC,GAACK,WAAzB,EAAqCL,CAAC,EAAtC,EAAyCJ,KAAK,CAACI,CAAD,CAAL,GAASM,eAAT,EAAyBA,eAAe,EAAxC;;IAA2C,OAAOV,KAAP;EAAa;;EAAAA,KAAK,GAAC,EAAN;EAAS,IAAIW,WAAW,GAAC,IAAEhB,WAAW,CAACG,MAA9B;EAAA,IAAqCc,KAAK,GAAC,CAA3C;;EAA6C,KAAIR,CAAC,GAAC,CAAN,EAAQA,CAAC,GAACO,WAAV,EAAsBP,CAAC,EAAvB,EAA0BJ,KAAK,CAACI,CAAD,CAAL,GAASQ,KAAT,EAAe,EAAEA,KAAF,KAAUjB,WAAW,CAACG,MAAtB,KAA+Bc,KAAK,GAAC,CAArC,CAAf;;EAAuD,OAAOZ,KAAP;AAAa;;AAAA,SAASa,SAAT,CAAmBjB,YAAnB,EAAgCD,WAAhC,EAA4C;EAAC,OAAOA,WAAW,CAACG,MAAZ,GAAmBF,YAAnB,GAAgCD,WAAhC,GAA4CA,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAArB,GAAkCD,WAAW,CAACY,KAAZ,CAAkBZ,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAAvC,EAAoDD,WAAW,CAACG,MAAhE,EAAwEgB,MAAxE,CAA+EnB,WAA/E,EAA2FA,WAAW,CAACY,KAAZ,CAAkB,CAAlB,EAAoB,IAAEX,YAAtB,CAA3F,CAAlC,GAAkKD,WAAW,CAACmB,MAAZ,CAAmBnB,WAAnB,EAA+BA,WAA/B,CAArN;AAAiQ;;AAAA,SAASoB,6BAAT,CAAuCnB,YAAvC,EAAoDD,WAApD,EAAgE;EAAC,OAAOA,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAArB,GAAkC,IAAEA,YAApC,GAAiDD,WAAW,CAACG,MAApE;AAA2E;;AAAA,SAASkB,mBAAT,CAA6BtB,EAA7B,EAAgCC,WAAhC,EAA4CsB,KAA5C,EAAkD;EAAC,IAAIC,gBAAJ;EAAA,IAAqBrB,YAAY,GAACH,EAAE,CAACG,YAArC;EAAA,IAAkDD,YAAY,GAACF,EAAE,CAACE,YAAlE;EAAA,IAA+EuB,SAAS,GAACzB,EAAE,CAACyB,SAA5F;EAAA,IAAsGC,UAAU,GAAC1B,EAAE,CAAC0B,UAApH;EAAA,IAA+HC,SAAS,GAAC,CAAzI;EAAA,IAA2IC,YAAY,GAAC,CAAxJ;EAAA,IAA0JC,kBAAkB,GAAC,MAAI1B,YAAjL;EAAA,IAA8L2B,kBAAkB,GAAC7B,WAAW,CAACG,MAAZ,IAAoBH,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAAzC,CAAjN;EAAwQ,OAAOD,WAAW,CAACG,MAAZ,GAAmBF,YAAnB,IAAiC0B,YAAY,GAACD,SAAS,GAAC,CAAvB,EAAyBE,kBAAkB,GAACL,gBAAgB,GAAC,CAAC,CAA/F,IAAkGvB,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAArB,IAAmC,CAACsB,gBAAgB,GAACrB,YAAY,IAAE2B,kBAAkB,GAAC7B,WAAW,CAACG,MAA/D,MAAyEwB,YAAY,GAAC,CAACH,SAAD,IAAYE,SAAS,GAACxB,YAAY,GAACF,WAAW,CAACG,MAA/C,CAAtF,GAA8IyB,kBAAkB,KAAGD,YAAY,GAAC,CAACH,SAAD,IAAYE,SAAS,GAACG,kBAAkB,IAAE7B,WAAW,CAACG,MAAZ,GAAmB,IAAEF,YAAvB,CAAxC,CAAhB,CAAnM,KAAoS,CAACsB,gBAAgB,GAACrB,YAAY,IAAE,IAAEF,WAAW,CAACG,MAA9C,MAAwDwB,YAAY,GAAC,CAACH,SAAD,IAAYE,SAAS,GAACxB,YAAY,GAACF,WAAW,CAACG,MAA/C,CAArE,GAA6HyB,kBAAkB,KAAGD,YAAY,GAACL,KAAK,CAACQ,QAAN,GAAe,CAACN,SAAD,IAAYE,SAAS,GAAC1B,WAAW,CAACG,MAAlC,CAAf,GAAyD,CAACqB,SAAD,IAAYE,SAAS,GAACD,UAAU,GAAC,CAAjC,CAAzE,CAAnb,CAAlG,EAAooB;IAACF,gBAAgB,EAACA,gBAAlB;IAAmCK,kBAAkB,EAACA,kBAAtD;IAAyEF,SAAS,EAACA,SAAnF;IAA6FC,YAAY,EAACA;EAA1G,CAA3oB;AAAmwB;;AAAAI,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C,GAAuDD,OAAO,CAACpC,sBAAR,GAA+BA,sBAAtF,EAA6GoC,OAAO,CAAC7B,mCAAR,GAA4CA,mCAAzJ,EAA6L6B,OAAO,CAACf,SAAR,GAAkBA,SAA/M,EAAyNe,OAAO,CAACb,6BAAR,GAAsCA,6BAA/P,EAA6Ra,OAAO,CAACZ,mBAAR,GAA4BA,mBAAzT"}, "metadata": {}, "sourceType": "script"}