{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/index.jsx\";\nimport React from \"react\";\nimport SeactionHeading from \"../global/seaction-title\";\nimport ServicesList from \"./list\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst Services = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n      title: \"Our Services\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ServicesList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n_c = Services;\nexport default Services;\n\nvar _c;\n\n$RefreshReg$(_c, \"Services\");", "map": {"version": 3, "names": ["React", "SeactionHeading", "ServicesList", "Services"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport SeactionHeading from \"../global/seaction-title\";\nimport ServicesList from \"./list\";\n\n\nconst Services = () => {\n  return (\n    <>\n      <SeactionHeading title=\"Our Services\" />\n      <ServicesList />\n    </>\n  );\n};\n\nexport default Services;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,eAAP,MAA4B,0BAA5B;AACA,OAAOC,YAAP,MAAyB,QAAzB;;;;AAGA,MAAMC,QAAQ,GAAG,MAAM;EACrB,oBACE;IAAA,wBACE,QAAC,eAAD;MAAiB,KAAK,EAAC;IAAvB;MAAA;MAAA;MAAA;IAAA,QADF,eAEE,QAAC,YAAD;MAAA;MAAA;MAAA;IAAA,QAFF;EAAA,gBADF;AAMD,CAPD;;KAAMA,Q;AASN,eAAeA,QAAf"}, "metadata": {}, "sourceType": "module"}