{"ast": null, "code": "var IS_PURE = require('../internals/is-pure');\n\nvar store = require('../internals/shared-store');\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.24.1',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2022 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.24.1/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});", "map": {"version": 3, "names": ["IS_PURE", "require", "store", "module", "exports", "key", "value", "undefined", "push", "version", "mode", "copyright", "license", "source"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/shared.js"], "sourcesContent": ["var IS_PURE = require('../internals/is-pure');\nvar store = require('../internals/shared-store');\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.24.1',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2022 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.24.1/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,sBAAD,CAArB;;AACA,IAAIC,KAAK,GAAGD,OAAO,CAAC,2BAAD,CAAnB;;AAEA,CAACE,MAAM,CAACC,OAAP,GAAiB,UAAUC,GAAV,EAAeC,KAAf,EAAsB;EACtC,OAAOJ,KAAK,CAACG,GAAD,CAAL,KAAeH,KAAK,CAACG,GAAD,CAAL,GAAaC,KAAK,KAAKC,SAAV,GAAsBD,KAAtB,GAA8B,EAA1D,CAAP;AACD,CAFD,EAEG,UAFH,EAEe,EAFf,EAEmBE,IAFnB,CAEwB;EACtBC,OAAO,EAAE,QADa;EAEtBC,IAAI,EAAEV,OAAO,GAAG,MAAH,GAAY,QAFH;EAGtBW,SAAS,EAAE,2CAHW;EAItBC,OAAO,EAAE,0DAJa;EAKtBC,MAAM,EAAE;AALc,CAFxB"}, "metadata": {}, "sourceType": "script"}