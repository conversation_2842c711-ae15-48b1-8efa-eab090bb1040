{"ast": null, "code": "var NATIVE_BIND = require('../internals/function-bind-native');\n\nvar call = Function.prototype.call;\nmodule.exports = NATIVE_BIND ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};", "map": {"version": 3, "names": ["NATIVE_BIND", "require", "call", "Function", "prototype", "module", "exports", "bind", "apply", "arguments"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/function-call.js"], "sourcesContent": ["var NATIVE_BIND = require('../internals/function-bind-native');\n\nvar call = Function.prototype.call;\n\nmodule.exports = NATIVE_BIND ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,mCAAD,CAAzB;;AAEA,IAAIC,IAAI,GAAGC,QAAQ,CAACC,SAAT,CAAmBF,IAA9B;AAEAG,MAAM,CAACC,OAAP,GAAiBN,WAAW,GAAGE,IAAI,CAACK,IAAL,CAAUL,IAAV,CAAH,GAAqB,YAAY;EAC3D,OAAOA,IAAI,CAACM,KAAL,CAAWN,IAAX,EAAiBO,SAAjB,CAAP;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}