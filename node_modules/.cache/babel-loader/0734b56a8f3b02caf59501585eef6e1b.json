{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/index.jsx\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\"; // import { iconList } from \"../../elements/iconList\";\n\nimport { Mail, Facebook, Instagram, Twitter, Linkedin } from \"react-feather\"; // import { AssetsList } from \"../../elements/assetsList\";\n\nimport Logo from \"../logo\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst AppFooter = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: [/*#__PURE__*/_jsxDEV(Styles.MainFooter, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(Styles.RowHolder, {\n          children: [/*#__PURE__*/_jsxDEV(Styles.ImageWrapper, {\n            children: /*#__PURE__*/_jsxDEV(Logo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"mailto:<EMAIL>\",\n            children: /*#__PURE__*/_jsxDEV(Styles.EmailWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(Mail, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Styles.SocialWrapper, {\n            className: \"mt-3 mt-md-0\",\n            children: /*#__PURE__*/_jsxDEV(Styles.SocialHolder, {\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"facebook.com\",\n                children: /*#__PURE__*/_jsxDEV(Facebook, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"facebook.com\",\n                children: /*#__PURE__*/_jsxDEV(Instagram, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"facebook.com\",\n                children: /*#__PURE__*/_jsxDEV(Twitter, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"facebook.com\",\n                children: /*#__PURE__*/_jsxDEV(Linkedin, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.CopyrightWrapper, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n          children: [\"Copyright \\xA9 2022 CapaStrength. All rights reserved.\", \" \", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/tandc\",\n            children: \"read T & C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n\n_c = AppFooter;\nexport default AppFooter;\n\nvar _c;\n\n$RefreshReg$(_c, \"AppFooter\");", "map": {"version": 3, "names": ["React", "Link", "Mail", "Facebook", "Instagram", "Twitter", "Linkedin", "Logo", "Styles", "AppFooter"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { <PERSON> } from \"react-router-dom\";\n\n// import { iconList } from \"../../elements/iconList\";\n\nimport { Mail, Facebook, Instagram, Twitter, Linkedin } from \"react-feather\";\n\n// import { AssetsList } from \"../../elements/assetsList\";\n\nimport Logo from \"../logo\";\n\nimport * as Styles from \"./styles\";\n\nconst AppFooter = () => {\n  return (\n    <Styles.Container>\n      <Styles.MainFooter>\n        <div className=\"container\">\n          <Styles.RowHolder>\n            <Styles.ImageWrapper>\n              <Logo />\n            </Styles.ImageWrapper>\n            <a href=\"mailto:<EMAIL>\">\n              <Styles.EmailWrapper>\n                <Mail />\n                <Styles.TextHolder><EMAIL></Styles.TextHolder>\n              </Styles.EmailWrapper>\n            </a>\n\n            <Styles.SocialWrapper className=\"mt-3 mt-md-0\">\n              <Styles.SocialHolder>\n                <Link to=\"facebook.com\">\n                  <Facebook />\n                </Link>\n                <Link to=\"facebook.com\">\n                  <Instagram />\n                </Link>\n                <Link to=\"facebook.com\">\n                  <Twitter />\n                </Link>\n                <Link to=\"facebook.com\">\n                  <Linkedin />\n                </Link>\n              </Styles.SocialHolder>\n            </Styles.SocialWrapper>\n          </Styles.RowHolder>\n        </div>\n      </Styles.MainFooter>\n      {/* <Styles.MenuContainer className=\"flex-wrap flex-md-nowrap\">\n        <div className=\"container\">\n          <Styles.MenuListHolder>\n            <Styles.MenuListItem>\n              <Link to=\"/\">Home</Link>\n            </Styles.MenuListItem>\n            <Styles.MenuListItem>\n              <Link to=\"/about\">About</Link>\n            </Styles.MenuListItem>\n            <Styles.MenuListItem>\n              <Link to=\"/contactus\">Contact</Link>\n            </Styles.MenuListItem>\n            <Styles.MenuListItem>\n              <Link to=\"/projects\">Projects</Link>\n            </Styles.MenuListItem>\n            <Styles.MenuListItem>\n              <Link to=\"/\">Join us</Link>\n            </Styles.MenuListItem>\n            <Styles.MenuListItem>\n              <Link to=\"/tandc\">T{\"&\"}C</Link>\n            </Styles.MenuListItem>\n          </Styles.MenuListHolder>\n        </div>\n      </Styles.MenuContainer> */}\n      <Styles.CopyrightWrapper>\n        <div className=\"container\">\n          <Styles.TextHolder>\n            Copyright © 2022 CapaStrength. All rights reserved.{\" \"}\n            <Link to=\"/tandc\">read T & C</Link>\n          </Styles.TextHolder>\n        </div>\n      </Styles.CopyrightWrapper>\n    </Styles.Container>\n  );\n};\n\nexport default AppFooter;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,IAAT,QAAqB,kBAArB,C,CAEA;;AAEA,SAASC,IAAT,EAAeC,QAAf,EAAyBC,SAAzB,EAAoCC,OAApC,EAA6CC,QAA7C,QAA6D,eAA7D,C,CAEA;;AAEA,OAAOC,IAAP,MAAiB,SAAjB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,wBACE,QAAC,MAAD,CAAQ,UAAR;MAAA,uBACE;QAAK,SAAS,EAAC,WAAf;QAAA,uBACE,QAAC,MAAD,CAAQ,SAAR;UAAA,wBACE,QAAC,MAAD,CAAQ,YAAR;YAAA,uBACE,QAAC,IAAD;cAAA;cAAA;cAAA;YAAA;UADF;YAAA;YAAA;YAAA;UAAA,QADF,eAIE;YAAG,IAAI,EAAC,iCAAR;YAAA,uBACE,QAAC,MAAD,CAAQ,YAAR;cAAA,wBACE,QAAC,IAAD;gBAAA;gBAAA;gBAAA;cAAA,QADF,eAEE,QAAC,MAAD,CAAQ,UAAR;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAFF;YAAA;cAAA;cAAA;cAAA;YAAA;UADF;YAAA;YAAA;YAAA;UAAA,QAJF,eAWE,QAAC,MAAD,CAAQ,aAAR;YAAsB,SAAS,EAAC,cAAhC;YAAA,uBACE,QAAC,MAAD,CAAQ,YAAR;cAAA,wBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,cAAT;gBAAA,uBACE,QAAC,QAAD;kBAAA;kBAAA;kBAAA;gBAAA;cADF;gBAAA;gBAAA;gBAAA;cAAA,QADF,eAIE,QAAC,IAAD;gBAAM,EAAE,EAAC,cAAT;gBAAA,uBACE,QAAC,SAAD;kBAAA;kBAAA;kBAAA;gBAAA;cADF;gBAAA;gBAAA;gBAAA;cAAA,QAJF,eAOE,QAAC,IAAD;gBAAM,EAAE,EAAC,cAAT;gBAAA,uBACE,QAAC,OAAD;kBAAA;kBAAA;kBAAA;gBAAA;cADF;gBAAA;gBAAA;gBAAA;cAAA,QAPF,eAUE,QAAC,IAAD;gBAAM,EAAE,EAAC,cAAT;gBAAA,uBACE,QAAC,QAAD;kBAAA;kBAAA;kBAAA;gBAAA;cADF;gBAAA;gBAAA;gBAAA;cAAA,QAVF;YAAA;cAAA;cAAA;cAAA;YAAA;UADF;YAAA;YAAA;YAAA;UAAA,QAXF;QAAA;UAAA;UAAA;UAAA;QAAA;MADF;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QADF,eAyDE,QAAC,MAAD,CAAQ,gBAAR;MAAA,uBACE;QAAK,SAAS,EAAC,WAAf;QAAA,uBACE,QAAC,MAAD,CAAQ,UAAR;UAAA,qEACsD,GADtD,eAEE,QAAC,IAAD;YAAM,EAAE,EAAC,QAAT;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAFF;QAAA;UAAA;UAAA;UAAA;QAAA;MADF;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QAzDF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAoED,CArED;;KAAMA,S;AAuEN,eAAeA,SAAf"}, "metadata": {}, "sourceType": "module"}