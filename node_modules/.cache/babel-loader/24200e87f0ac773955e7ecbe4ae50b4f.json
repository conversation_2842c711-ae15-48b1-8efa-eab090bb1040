{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/index.jsx\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { AssetsList } from \"../../elements/assetsList\";\nimport Button from \"../../elements/button\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ClientsArea = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    className: \"flex-wrap flex-md-nowrap\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col col-12 col-md-7\",\n      children: /*#__PURE__*/_jsxDEV(Styles.ImageHolder, {\n        src: AssetsList.client\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col col-12 col-md-5 ps-md-4\",\n      children: [/*#__PURE__*/_jsxDEV(Styles.TitleWrapper, {\n        children: /*#__PURE__*/_jsxDEV(Styles.TitleHolder, {\n          children: \"Our Client\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.TextHolder, {\n        className: \"pe-md-5\",\n        children: \"Capastrength is one of the India\\u2019s leading service providers of Retrofitting, Structural Strengthening services for structures of many regions key public and private organisations. We have worked closely and successfully with Organisations and major companies, and pride ourselves on meeting and exceeding our clients high expectations. Here, you can find a selection of the many client organisations we already work closely alongside.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/clients\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"my-3\",\n          children: \"See Clients\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n\n_c = ClientsArea;\nexport default ClientsArea;\n\nvar _c;\n\n$RefreshReg$(_c, \"ClientsArea\");", "map": {"version": 3, "names": ["React", "Link", "AssetsList", "<PERSON><PERSON>", "Styles", "ClientsArea", "client"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { Link } from \"react-router-dom\";\nimport { AssetsList } from \"../../elements/assetsList\";\n\nimport Button from \"../../elements/button\";\n\nimport * as Styles from \"./styles\";\n\nconst ClientsArea = () => {\n  return (\n    <Styles.Container className=\"flex-wrap flex-md-nowrap\">\n      <div className=\"col col-12 col-md-7\">\n        <Styles.ImageHolder src={AssetsList.client} />\n      </div>\n      <div className=\"col col-12 col-md-5 ps-md-4\">\n        <Styles.TitleWrapper>\n          <Styles.TitleHolder>Our Client</Styles.TitleHolder>\n        </Styles.TitleWrapper>\n        <Styles.TextHolder className=\"pe-md-5\">\n          Capastrength is one of the India’s leading service providers of\n          Retrofitting, Structural Strengthening services for structures of many\n          regions key public and private organisations. We have worked closely\n          and successfully with Organisations and major companies, and pride\n          ourselves on meeting and exceeding our clients high expectations.\n          Here, you can find a selection of the many client organisations we\n          already work closely alongside.\n        </Styles.TextHolder>\n        <Link to=\"/clients\">\n          <Button className=\"my-3\">See Clients</Button>\n        </Link>\n      </div>\n    </Styles.Container>\n  );\n};\n\nexport default ClientsArea;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,IAAT,QAAqB,kBAArB;AACA,SAASC,UAAT,QAA2B,2BAA3B;AAEA,OAAOC,MAAP,MAAmB,uBAAnB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,WAAW,GAAG,MAAM;EACxB,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAkB,SAAS,EAAC,0BAA5B;IAAA,wBACE;MAAK,SAAS,EAAC,qBAAf;MAAA,uBACE,QAAC,MAAD,CAAQ,WAAR;QAAoB,GAAG,EAAEH,UAAU,CAACI;MAApC;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QADF,eAIE;MAAK,SAAS,EAAC,6BAAf;MAAA,wBACE,QAAC,MAAD,CAAQ,YAAR;QAAA,uBACE,QAAC,MAAD,CAAQ,WAAR;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA;MADF;QAAA;QAAA;QAAA;MAAA,QADF,eAIE,QAAC,MAAD,CAAQ,UAAR;QAAmB,SAAS,EAAC,SAA7B;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAJF,eAaE,QAAC,IAAD;QAAM,EAAE,EAAC,UAAT;QAAA,uBACE,QAAC,MAAD;UAAQ,SAAS,EAAC,MAAlB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA;MADF;QAAA;QAAA;QAAA;MAAA,QAbF;IAAA;MAAA;MAAA;MAAA;IAAA,QAJF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAwBD,CAzBD;;KAAMD,W;AA2BN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}