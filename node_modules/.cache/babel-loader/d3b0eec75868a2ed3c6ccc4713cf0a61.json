{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/index.jsx\";\nimport React from \"react\";\nimport InputField from \"./input-field\";\nimport InfoField from \"./info\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst Signup = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(Styles.Wrapper, {\n        className: \"flex-wrap flex-md-nowrap\",\n        children: [/*#__PURE__*/_jsxDEV(Styles.BgOneWrapper, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.BgTwoWrapper, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          style: {\n            zIndex: 999\n          },\n          children: /*#__PURE__*/_jsxDEV(InfoField, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(InputField, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n\n_c = Signup;\nexport default Signup;\n\nvar _c;\n\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["React", "InputField", "InfoField", "Styles", "Signup", "zIndex"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport InputField from \"./input-field\";\nimport InfoField from \"./info\";\n\nimport * as Styles from \"./styles\";\n\nconst Signup = () => {\n  return (\n    <Styles.Container>\n      <div className=\"container\">\n        <Styles.Wrapper className=\"flex-wrap flex-md-nowrap\">\n          <Styles.BgOneWrapper />\n          <Styles.BgTwoWrapper />\n          <div className=\"col\" style={{ zIndex: 999 }}>\n            <InfoField />\n          </div>\n          <div className=\"col\">\n            <InputField />\n          </div>\n        </Styles.Wrapper>\n      </div>\n    </Styles.Container>\n  );\n};\n\nexport default Signup;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,UAAP,MAAuB,eAAvB;AACA,OAAOC,SAAP,MAAsB,QAAtB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,MAAM,GAAG,MAAM;EACnB,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,uBACE;MAAK,SAAS,EAAC,WAAf;MAAA,uBACE,QAAC,MAAD,CAAQ,OAAR;QAAgB,SAAS,EAAC,0BAA1B;QAAA,wBACE,QAAC,MAAD,CAAQ,YAAR;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,YAAR;UAAA;UAAA;UAAA;QAAA,QAFF,eAGE;UAAK,SAAS,EAAC,KAAf;UAAqB,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAV,CAA5B;UAAA,uBACE,QAAC,SAAD;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAHF,eAME;UAAK,SAAS,EAAC,KAAf;UAAA,uBACE,QAAC,UAAD;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QANF;MAAA;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAgBD,CAjBD;;KAAMD,M;AAmBN,eAAeA,MAAf"}, "metadata": {}, "sourceType": "module"}