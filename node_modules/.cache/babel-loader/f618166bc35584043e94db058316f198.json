{"ast": null, "code": "var global = require('../internals/global');\n\nvar isObject = require('../internals/is-object');\n\nvar document = global.document; // typeof document.createElement is 'object' in old IE\n\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};", "map": {"version": 3, "names": ["global", "require", "isObject", "document", "EXISTS", "createElement", "module", "exports", "it"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/document-create-element.js"], "sourcesContent": ["var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAD,CAApB;;AACA,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAD,CAAtB;;AAEA,IAAIE,QAAQ,GAAGH,MAAM,CAACG,QAAtB,C,CACA;;AACA,IAAIC,MAAM,GAAGF,QAAQ,CAACC,QAAD,CAAR,IAAsBD,QAAQ,CAACC,QAAQ,CAACE,aAAV,CAA3C;;AAEAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,EAAV,EAAc;EAC7B,OAAOJ,MAAM,GAAGD,QAAQ,CAACE,aAAT,CAAuBG,EAAvB,CAAH,GAAgC,EAA7C;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}