{"ast": null, "code": "/* eslint-disable es-x/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\n\nmodule.exports = NATIVE_SYMBOL && !Symbol.sham && typeof Symbol.iterator == 'symbol';", "map": {"version": 3, "names": ["NATIVE_SYMBOL", "require", "module", "exports", "Symbol", "sham", "iterator"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/use-symbol-as-uid.js"], "sourcesContent": ["/* eslint-disable es-x/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\n\nmodule.exports = NATIVE_SYMBOL\n  && !Symbol.sham\n  && typeof Symbol.iterator == 'symbol';\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAGC,OAAO,CAAC,4BAAD,CAA3B;;AAEAC,MAAM,CAACC,OAAP,GAAiBH,aAAa,IACzB,CAACI,MAAM,CAACC,IADI,IAEZ,OAAOD,MAAM,CAACE,QAAd,IAA0B,QAF/B"}, "metadata": {}, "sourceType": "script"}