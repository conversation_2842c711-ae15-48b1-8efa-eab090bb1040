{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/index.jsx\";\nimport React from \"react\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst Slider = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: \"Slider\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 10\n  }, this);\n};\n\n_c = Slider;\nexport default Slider;\n\nvar _c;\n\n$RefreshReg$(_c, \"Slider\");", "map": {"version": 3, "names": ["React", "Styles", "Slide<PERSON>"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst Slider = () => {\n  return <Styles.Container>Slider</Styles.Container>;\n};\n\nexport default Slider;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,MAAM,GAAG,MAAM;EACnB,oBAAO,QAAC,MAAD,CAAQ,SAAR;IAAA;EAAA;IAAA;IAAA;IAAA;EAAA,QAAP;AACD,CAFD;;KAAMA,M;AAIN,eAAeA,MAAf"}, "metadata": {}, "sourceType": "module"}