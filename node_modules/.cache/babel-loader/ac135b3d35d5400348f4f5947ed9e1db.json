{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar printWarning = function () {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\n  var loggedTypeFailures = {};\n\n  var has = require('./lib/has');\n\n  printWarning = function (text) {\n    var message = 'Warning: ' + text;\n\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {\n      /**/\n    }\n  };\n}\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\n\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n\n        if (error && !(error instanceof Error)) {\n          printWarning((componentName || 'React class') + ': type specification of ' + location + ' `' + typeSpecName + '` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a ' + typeof error + '. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).');\n        }\n\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n          var stack = getStack ? getStack() : '';\n          printWarning('Failed ' + location + ' type: ' + error.message + (stack != null ? stack : ''));\n        }\n      }\n    }\n  }\n}\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\n\n\ncheckPropTypes.resetWarningCache = function () {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n};\n\nmodule.exports = checkPropTypes;", "map": {"version": 3, "names": ["printWarning", "process", "env", "NODE_ENV", "ReactPropTypesSecret", "require", "loggedTypeFailures", "has", "text", "message", "console", "error", "Error", "x", "checkPropTypes", "typeSpecs", "values", "location", "componentName", "getStack", "typeSpecName", "err", "name", "ex", "stack", "resetWarningCache", "module", "exports"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/prop-types/checkPropTypes.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA,IAAIA,YAAY,GAAG,YAAW,CAAE,CAAhC;;AAEA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzC,IAAIC,oBAAoB,GAAGC,OAAO,CAAC,4BAAD,CAAlC;;EACA,IAAIC,kBAAkB,GAAG,EAAzB;;EACA,IAAIC,GAAG,GAAGF,OAAO,CAAC,WAAD,CAAjB;;EAEAL,YAAY,GAAG,UAASQ,IAAT,EAAe;IAC5B,IAAIC,OAAO,GAAG,cAAcD,IAA5B;;IACA,IAAI,OAAOE,OAAP,KAAmB,WAAvB,EAAoC;MAClCA,OAAO,CAACC,KAAR,CAAcF,OAAd;IACD;;IACD,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAIG,KAAJ,CAAUH,OAAV,CAAN;IACD,CALD,CAKE,OAAOI,CAAP,EAAU;MAAE;IAAM;EACrB,CAXD;AAYD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,cAAT,CAAwBC,SAAxB,EAAmCC,MAAnC,EAA2CC,QAA3C,EAAqDC,aAArD,EAAoEC,QAApE,EAA8E;EAC5E,IAAIlB,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;IACzC,KAAK,IAAIiB,YAAT,IAAyBL,SAAzB,EAAoC;MAClC,IAAIR,GAAG,CAACQ,SAAD,EAAYK,YAAZ,CAAP,EAAkC;QAChC,IAAIT,KAAJ,CADgC,CAEhC;QACA;QACA;;QACA,IAAI;UACF;UACA;UACA,IAAI,OAAOI,SAAS,CAACK,YAAD,CAAhB,KAAmC,UAAvC,EAAmD;YACjD,IAAIC,GAAG,GAAGT,KAAK,CACb,CAACM,aAAa,IAAI,aAAlB,IAAmC,IAAnC,GAA0CD,QAA1C,GAAqD,SAArD,GAAiEG,YAAjE,GAAgF,gBAAhF,GACA,8EADA,GACiF,OAAOL,SAAS,CAACK,YAAD,CADjG,GACkH,IADlH,GAEA,+FAHa,CAAf;YAKAC,GAAG,CAACC,IAAJ,GAAW,qBAAX;YACA,MAAMD,GAAN;UACD;;UACDV,KAAK,GAAGI,SAAS,CAACK,YAAD,CAAT,CAAwBJ,MAAxB,EAAgCI,YAAhC,EAA8CF,aAA9C,EAA6DD,QAA7D,EAAuE,IAAvE,EAA6Eb,oBAA7E,CAAR;QACD,CAbD,CAaE,OAAOmB,EAAP,EAAW;UACXZ,KAAK,GAAGY,EAAR;QACD;;QACD,IAAIZ,KAAK,IAAI,EAAEA,KAAK,YAAYC,KAAnB,CAAb,EAAwC;UACtCZ,YAAY,CACV,CAACkB,aAAa,IAAI,aAAlB,IAAmC,0BAAnC,GACAD,QADA,GACW,IADX,GACkBG,YADlB,GACiC,iCADjC,GAEA,2DAFA,GAE8D,OAAOT,KAFrE,GAE6E,IAF7E,GAGA,iEAHA,GAIA,gEAJA,GAKA,iCANU,CAAZ;QAQD;;QACD,IAAIA,KAAK,YAAYC,KAAjB,IAA0B,EAAED,KAAK,CAACF,OAAN,IAAiBH,kBAAnB,CAA9B,EAAsE;UACpE;UACA;UACAA,kBAAkB,CAACK,KAAK,CAACF,OAAP,CAAlB,GAAoC,IAApC;UAEA,IAAIe,KAAK,GAAGL,QAAQ,GAAGA,QAAQ,EAAX,GAAgB,EAApC;UAEAnB,YAAY,CACV,YAAYiB,QAAZ,GAAuB,SAAvB,GAAmCN,KAAK,CAACF,OAAzC,IAAoDe,KAAK,IAAI,IAAT,GAAgBA,KAAhB,GAAwB,EAA5E,CADU,CAAZ;QAGD;MACF;IACF;EACF;AACF;AAED;AACA;AACA;AACA;AACA;;;AACAV,cAAc,CAACW,iBAAf,GAAmC,YAAW;EAC5C,IAAIxB,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;IACzCG,kBAAkB,GAAG,EAArB;EACD;AACF,CAJD;;AAMAoB,MAAM,CAACC,OAAP,GAAiBb,cAAjB"}, "metadata": {}, "sourceType": "script"}