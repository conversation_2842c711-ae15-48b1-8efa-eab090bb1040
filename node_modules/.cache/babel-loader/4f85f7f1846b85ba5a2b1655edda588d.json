{"ast": null, "code": "'use strict';\n\nvar m = require('react-dom');\n\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\n  exports.createRoot = function (c, o) {\n    i.usingClientEntryPoint = true;\n\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n\n  exports.hydrateRoot = function (c, h, o) {\n    i.usingClientEntryPoint = true;\n\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}", "map": {"version": 3, "names": ["m", "require", "process", "env", "NODE_ENV", "exports", "createRoot", "hydrateRoot", "i", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "c", "o", "usingClientEntryPoint", "h"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-dom/client.js"], "sourcesContent": ["'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAD,CAAf;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCC,OAAO,CAACC,UAAR,GAAqBN,CAAC,CAACM,UAAvB;EACAD,OAAO,CAACE,WAAR,GAAsBP,CAAC,CAACO,WAAxB;AACD,CAHD,MAGO;EACL,IAAIC,CAAC,GAAGR,CAAC,CAACS,kDAAV;;EACAJ,OAAO,CAACC,UAAR,GAAqB,UAASI,CAAT,EAAYC,CAAZ,EAAe;IAClCH,CAAC,CAACI,qBAAF,GAA0B,IAA1B;;IACA,IAAI;MACF,OAAOZ,CAAC,CAACM,UAAF,CAAaI,CAAb,EAAgBC,CAAhB,CAAP;IACD,CAFD,SAEU;MACRH,CAAC,CAACI,qBAAF,GAA0B,KAA1B;IACD;EACF,CAPD;;EAQAP,OAAO,CAACE,WAAR,GAAsB,UAASG,CAAT,EAAYG,CAAZ,EAAeF,CAAf,EAAkB;IACtCH,CAAC,CAACI,qBAAF,GAA0B,IAA1B;;IACA,IAAI;MACF,OAAOZ,CAAC,CAACO,WAAF,CAAcG,CAAd,EAAiBG,CAAjB,EAAoBF,CAApB,CAAP;IACD,CAFD,SAEU;MACRH,CAAC,CAACI,qBAAF,GAA0B,KAA1B;IACD;EACF,CAPD;AAQD"}, "metadata": {}, "sourceType": "script"}