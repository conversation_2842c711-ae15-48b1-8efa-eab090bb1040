{"ast": null, "code": "export const cardData = [{\n  name: \"Mr.<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  words: \"Mr. <PERSON><PERSON><PERSON><PERSON><PERSON> has pursued his graduation in Civil engineering from Shivaji University. He is having 15 years of experience in High-rise construction as well as retrofitting, structural strengthening.\"\n}, {\n  name: \"Mr. <PERSON><PERSON>\",\n  words: \"Mr. <PERSON> graduated from Mumbai University is been with the firm for past 7 years. His hardworking nature and striving to accomplish the given work in time, is what very valued and inspires the firm.He is capable of catering the various Detailing requirements for industries. His expertise in billing of work on construction site.\"\n}, {\n  name: \"Mr. <PERSON><PERSON> <PERSON><PERSON> (Our Associate)\",\n  words: \"Mr. <PERSON><PERSON> <PERSON><PERSON> holds a M.E. (structure), from Sardar Patel college of Engineering, B.E. (civil) in Datta Megha College of Engineering from Mumbai. He also is an authorized Charted Engineer on board Institution of Engineers . He has in-depth knowledge of RCC Design, FRP Design, Civil construction, which includes Retrofitting, Structural Strengthening, Environmental coatings and rehabilitation of old/damaged structures.\"\n}, {\n  name: \"Mr <PERSON><PERSON><PERSON>\",\n  words: \"Mr. <PERSON><PERSON><PERSON> has pursued his graduation in Civil Engineering from Mumbai University. He has more than 15 years of experience in field of Fit-out, High Rise Construction, Restoration, Rehabilitation projects across India\"\n}];", "map": {"version": 3, "names": ["cardData", "name", "words"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/data.js"], "sourcesContent": ["export const cardData = [\n  {\n    name: \"Mr.<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    words:\n      \"Mr. <PERSON><PERSON><PERSON><PERSON><PERSON> has pursued his graduation in Civil engineering from Shivaji University. He is having 15 years of experience in High-rise construction as well as retrofitting, structural strengthening.\",\n  },\n  {\n    name: \"Mr. <PERSON><PERSON>\",\n    words:\n      \"Mr. <PERSON> graduated from Mumbai University is been with the firm for past 7 years. His hardworking nature and striving to accomplish the given work in time, is what very valued and inspires the firm.He is capable of catering the various Detailing requirements for industries. His expertise in billing of work on construction site.\",\n  },\n  {\n    name: \"Mr. <PERSON><PERSON> <PERSON><PERSON> (Our Associate)\",\n    words:\n      \"Mr. <PERSON><PERSON> <PERSON><PERSON> holds a M.E. (structure), from Sardar Patel college of Engineering, B.E. (civil) in Datta Megha College of Engineering from Mumbai. He also is an authorized Charted Engineer on board Institution of Engineers . He has in-depth knowledge of RCC Design, FRP Design, Civil construction, which includes Retrofitting, Structural Strengthening, Environmental coatings and rehabilitation of old/damaged structures.\",\n  },\n  {\n    name: \"Mr <PERSON><PERSON><PERSON>\",\n    words:\n      \"Mr. <PERSON><PERSON><PERSON> has pursued his graduation in Civil Engineering from Mumbai University. He has more than 15 years of experience in field of Fit-out, High Rise Construction, Restoration, Rehabilitation projects across India\",\n  },\n];\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG,CACtB;EACEC,IAAI,EAAE,qBADR;EAEEC,KAAK,EACH;AAHJ,CADsB,EAMtB;EACED,IAAI,EAAE,uBADR;EAEEC,KAAK,EACH;AAHJ,CANsB,EAWtB;EACED,IAAI,EAAE,iCADR;EAEEC,KAAK,EACH;AAHJ,CAXsB,EAgBtB;EACED,IAAI,EAAE,kBADR;EAEEC,KAAK,EACH;AAHJ,CAhBsB,CAAjB"}, "metadata": {}, "sourceType": "module"}