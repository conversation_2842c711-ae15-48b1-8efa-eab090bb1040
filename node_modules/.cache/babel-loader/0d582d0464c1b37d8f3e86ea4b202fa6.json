{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar React = require(\"react\"),\n    utils_1 = require(\"./utils\"),\n    CarouselItems = function (_a) {\n  var props = _a.props,\n      state = _a.state,\n      goToSlide = _a.goToSlide,\n      clones = _a.clones,\n      notEnoughChildren = _a.notEnoughChildren,\n      itemWidth = state.itemWidth,\n      children = props.children,\n      infinite = props.infinite,\n      itemClass = props.itemClass,\n      itemAriaLabel = props.itemAriaLabel,\n      partialVisbile = props.partialVisbile,\n      partialVisible = props.partialVisible,\n      _b = utils_1.getInitialState(state, props),\n      flexBisis = _b.flexBisis,\n      shouldRenderOnSSR = _b.shouldRenderOnSSR,\n      domFullyLoaded = _b.domFullyLoaded,\n      partialVisibilityGutter = _b.partialVisibilityGutter;\n\n  return _b.shouldRenderAtAll ? (partialVisbile && console.warn('WARNING: Please correct props name: \"partialVisible\" as old typo will be removed in future versions!'), React.createElement(React.Fragment, null, (infinite ? clones : React.Children.toArray(children)).map(function (child, index) {\n    return React.createElement(\"li\", {\n      key: index,\n      \"data-index\": index,\n      onClick: function () {\n        props.focusOnSelect && goToSlide(index);\n      },\n      \"aria-hidden\": utils_1.getIfSlideIsVisbile(index, state) ? \"false\" : \"true\",\n      \"aria-label\": itemAriaLabel || (child.props.ariaLabel ? child.props.ariaLabel : null),\n      style: {\n        flex: shouldRenderOnSSR ? \"1 0 \" + flexBisis + \"%\" : \"auto\",\n        position: \"relative\",\n        width: domFullyLoaded ? ((partialVisbile || partialVisible) && partialVisibilityGutter && !notEnoughChildren ? itemWidth - partialVisibilityGutter : itemWidth) + \"px\" : \"auto\"\n      },\n      className: \"react-multi-carousel-item \" + (utils_1.getIfSlideIsVisbile(index, state) ? \"react-multi-carousel-item--active\" : \"\") + \" \" + itemClass\n    }, child);\n  }))) : null;\n};\n\nexports.default = CarouselItems;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "React", "require", "utils_1", "CarouselItems", "_a", "props", "state", "goToSlide", "clones", "notEnoughChildren", "itemWidth", "children", "infinite", "itemClass", "itemAriaLabel", "partialVisbile", "partialVisible", "_b", "getInitialState", "flexBisis", "shouldRenderOnSSR", "dom<PERSON>ullyLoaded", "partialVisibilityGutter", "shouldRenderAtAll", "console", "warn", "createElement", "Fragment", "Children", "toArray", "map", "child", "index", "key", "onClick", "focusOnSelect", "getIfSlideIsVisbile", "aria<PERSON><PERSON><PERSON>", "style", "flex", "position", "width", "className", "default"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/CarouselItems.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var React=require(\"react\"),utils_1=require(\"./utils\"),CarouselItems=function(_a){var props=_a.props,state=_a.state,goToSlide=_a.goToSlide,clones=_a.clones,notEnoughChildren=_a.notEnoughChildren,itemWidth=state.itemWidth,children=props.children,infinite=props.infinite,itemClass=props.itemClass,itemAriaLabel=props.itemAriaLabel,partialVisbile=props.partialVisbile,partialVisible=props.partialVisible,_b=utils_1.getInitialState(state,props),flexBisis=_b.flexBisis,shouldRenderOnSSR=_b.shouldRenderOnSSR,domFullyLoaded=_b.domFullyLoaded,partialVisibilityGutter=_b.partialVisibilityGutter;return _b.shouldRenderAtAll?(partialVisbile&&console.warn('WARNING: Please correct props name: \"partialVisible\" as old typo will be removed in future versions!'),React.createElement(React.Fragment,null,(infinite?clones:React.Children.toArray(children)).map(function(child,index){return React.createElement(\"li\",{key:index,\"data-index\":index,onClick:function(){props.focusOnSelect&&goToSlide(index)},\"aria-hidden\":utils_1.getIfSlideIsVisbile(index,state)?\"false\":\"true\",\"aria-label\":itemAriaLabel||(child.props.ariaLabel?child.props.ariaLabel:null),style:{flex:shouldRenderOnSSR?\"1 0 \"+flexBisis+\"%\":\"auto\",position:\"relative\",width:domFullyLoaded?((partialVisbile||partialVisible)&&partialVisibilityGutter&&!notEnoughChildren?itemWidth-partialVisibilityGutter:itemWidth)+\"px\":\"auto\"},className:\"react-multi-carousel-item \"+(utils_1.getIfSlideIsVisbile(index,state)?\"react-multi-carousel-item--active\":\"\")+\" \"+itemClass},child)}))):null};exports.default=CarouselItems;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,KAAK,GAACC,OAAO,CAAC,OAAD,CAAjB;AAAA,IAA2BC,OAAO,GAACD,OAAO,CAAC,SAAD,CAA1C;AAAA,IAAsDE,aAAa,GAAC,UAASC,EAAT,EAAY;EAAC,IAAIC,KAAK,GAACD,EAAE,CAACC,KAAb;EAAA,IAAmBC,KAAK,GAACF,EAAE,CAACE,KAA5B;EAAA,IAAkCC,SAAS,GAACH,EAAE,CAACG,SAA/C;EAAA,IAAyDC,MAAM,GAACJ,EAAE,CAACI,MAAnE;EAAA,IAA0EC,iBAAiB,GAACL,EAAE,CAACK,iBAA/F;EAAA,IAAiHC,SAAS,GAACJ,KAAK,CAACI,SAAjI;EAAA,IAA2IC,QAAQ,GAACN,KAAK,CAACM,QAA1J;EAAA,IAAmKC,QAAQ,GAACP,KAAK,CAACO,QAAlL;EAAA,IAA2LC,SAAS,GAACR,KAAK,CAACQ,SAA3M;EAAA,IAAqNC,aAAa,GAACT,KAAK,CAACS,aAAzO;EAAA,IAAuPC,cAAc,GAACV,KAAK,CAACU,cAA5Q;EAAA,IAA2RC,cAAc,GAACX,KAAK,CAACW,cAAhT;EAAA,IAA+TC,EAAE,GAACf,OAAO,CAACgB,eAAR,CAAwBZ,KAAxB,EAA8BD,KAA9B,CAAlU;EAAA,IAAuWc,SAAS,GAACF,EAAE,CAACE,SAApX;EAAA,IAA8XC,iBAAiB,GAACH,EAAE,CAACG,iBAAnZ;EAAA,IAAqaC,cAAc,GAACJ,EAAE,CAACI,cAAvb;EAAA,IAAscC,uBAAuB,GAACL,EAAE,CAACK,uBAAje;;EAAyf,OAAOL,EAAE,CAACM,iBAAH,IAAsBR,cAAc,IAAES,OAAO,CAACC,IAAR,CAAa,sGAAb,CAAhB,EAAqIzB,KAAK,CAAC0B,aAAN,CAAoB1B,KAAK,CAAC2B,QAA1B,EAAmC,IAAnC,EAAwC,CAACf,QAAQ,GAACJ,MAAD,GAAQR,KAAK,CAAC4B,QAAN,CAAeC,OAAf,CAAuBlB,QAAvB,CAAjB,EAAmDmB,GAAnD,CAAuD,UAASC,KAAT,EAAeC,KAAf,EAAqB;IAAC,OAAOhC,KAAK,CAAC0B,aAAN,CAAoB,IAApB,EAAyB;MAACO,GAAG,EAACD,KAAL;MAAW,cAAaA,KAAxB;MAA8BE,OAAO,EAAC,YAAU;QAAC7B,KAAK,CAAC8B,aAAN,IAAqB5B,SAAS,CAACyB,KAAD,CAA9B;MAAsC,CAAvF;MAAwF,eAAc9B,OAAO,CAACkC,mBAAR,CAA4BJ,KAA5B,EAAkC1B,KAAlC,IAAyC,OAAzC,GAAiD,MAAvJ;MAA8J,cAAaQ,aAAa,KAAGiB,KAAK,CAAC1B,KAAN,CAAYgC,SAAZ,GAAsBN,KAAK,CAAC1B,KAAN,CAAYgC,SAAlC,GAA4C,IAA/C,CAAxL;MAA6OC,KAAK,EAAC;QAACC,IAAI,EAACnB,iBAAiB,GAAC,SAAOD,SAAP,GAAiB,GAAlB,GAAsB,MAA7C;QAAoDqB,QAAQ,EAAC,UAA7D;QAAwEC,KAAK,EAACpB,cAAc,GAAC,CAAC,CAACN,cAAc,IAAEC,cAAjB,KAAkCM,uBAAlC,IAA2D,CAACb,iBAA5D,GAA8EC,SAAS,GAACY,uBAAxF,GAAgHZ,SAAjH,IAA4H,IAA7H,GAAkI;MAA9N,CAAnP;MAAydgC,SAAS,EAAC,gCAA8BxC,OAAO,CAACkC,mBAAR,CAA4BJ,KAA5B,EAAkC1B,KAAlC,IAAyC,mCAAzC,GAA6E,EAA3G,IAA+G,GAA/G,GAAmHO;IAAtlB,CAAzB,EAA0nBkB,KAA1nB,CAAP;EAAwoB,CAArtB,CAAxC,CAA3J,IAA45B,IAAn6B;AAAw6B,CAAl/C;;AAAm/CjC,OAAO,CAAC6C,OAAR,GAAgBxC,aAAhB"}, "metadata": {}, "sourceType": "script"}