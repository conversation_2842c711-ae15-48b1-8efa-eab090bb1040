{"ast": null, "code": "// TODO: remove from `core-js@4`\nrequire('../modules/esnext.global-this');\n\nvar parent = require('../actual/global-this');\n\nmodule.exports = parent;", "map": {"version": 3, "names": ["require", "parent", "module", "exports"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/full/global-this.js"], "sourcesContent": ["// TODO: remove from `core-js@4`\nrequire('../modules/esnext.global-this');\n\nvar parent = require('../actual/global-this');\n\nmodule.exports = parent;\n"], "mappings": "AAAA;AACAA,OAAO,CAAC,+BAAD,CAAP;;AAEA,IAAIC,MAAM,GAAGD,OAAO,CAAC,uBAAD,CAApB;;AAEAE,MAAM,CAACC,OAAP,GAAiBF,MAAjB"}, "metadata": {}, "sourceType": "script"}