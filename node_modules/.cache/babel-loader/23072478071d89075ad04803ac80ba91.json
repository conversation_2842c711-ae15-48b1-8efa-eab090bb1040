{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/about/index.jsx\";\nimport React from \"react\";\nimport About from \"../../components/about\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst AboutPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n\n_c = AboutPage;\nexport default AboutPage;\n\nvar _c;\n\n$RefreshReg$(_c, \"AboutPage\");", "map": {"version": 3, "names": ["React", "About", "AboutPage"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/about/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport About from \"../../components/about\";\n\nconst AboutPage = () => {\n  return (\n    <div className=\"container\">\n      <About />\n    </div>\n  );\n};\n\nexport default AboutPage;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,KAAP,MAAkB,wBAAlB;;;AAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,oBACE;IAAK,SAAS,EAAC,WAAf;IAAA,uBACE,QAAC,KAAD;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAKD,CAND;;KAAMA,S;AAQN,eAAeA,SAAf"}, "metadata": {}, "sourceType": "module"}