{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (url, options) {\n  if (!options) {\n    options = {};\n  }\n\n  if (!url) {\n    return url;\n  }\n\n  url = String(url.__esModule ? url.default : url); // If url is already wrapped in quotes, remove them\n\n  if (/^['\"].*['\"]$/.test(url)) {\n    url = url.slice(1, -1);\n  }\n\n  if (options.hash) {\n    url += options.hash;\n  } // Should url be wrapped?\n  // See https://drafts.csswg.org/css-values-3/#urls\n\n\n  if (/[\"'() \\t\\n]|(%20)/.test(url) || options.needQuotes) {\n    return \"\\\"\".concat(url.replace(/\"/g, '\\\\\"').replace(/\\n/g, \"\\\\n\"), \"\\\"\");\n  }\n\n  return url;\n};", "map": {"version": 3, "names": ["module", "exports", "url", "options", "String", "__esModule", "default", "test", "slice", "hash", "needQuotes", "concat", "replace"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/css-loader/dist/runtime/getUrl.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function (url, options) {\n  if (!options) {\n    options = {};\n  }\n\n  if (!url) {\n    return url;\n  }\n\n  url = String(url.__esModule ? url.default : url); // If url is already wrapped in quotes, remove them\n\n  if (/^['\"].*['\"]$/.test(url)) {\n    url = url.slice(1, -1);\n  }\n\n  if (options.hash) {\n    url += options.hash;\n  } // Should url be wrapped?\n  // See https://drafts.csswg.org/css-values-3/#urls\n\n\n  if (/[\"'() \\t\\n]|(%20)/.test(url) || options.needQuotes) {\n    return \"\\\"\".concat(url.replace(/\"/g, '\\\\\"').replace(/\\n/g, \"\\\\n\"), \"\\\"\");\n  }\n\n  return url;\n};"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAP,GAAiB,UAAUC,GAAV,EAAeC,OAAf,EAAwB;EACvC,IAAI,CAACA,OAAL,EAAc;IACZA,OAAO,GAAG,EAAV;EACD;;EAED,IAAI,CAACD,GAAL,EAAU;IACR,OAAOA,GAAP;EACD;;EAEDA,GAAG,GAAGE,MAAM,CAACF,GAAG,CAACG,UAAJ,GAAiBH,GAAG,CAACI,OAArB,GAA+BJ,GAAhC,CAAZ,CATuC,CASW;;EAElD,IAAI,eAAeK,IAAf,CAAoBL,GAApB,CAAJ,EAA8B;IAC5BA,GAAG,GAAGA,GAAG,CAACM,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd,CAAN;EACD;;EAED,IAAIL,OAAO,CAACM,IAAZ,EAAkB;IAChBP,GAAG,IAAIC,OAAO,CAACM,IAAf;EACD,CAjBsC,CAiBrC;EACF;;;EAGA,IAAI,oBAAoBF,IAApB,CAAyBL,GAAzB,KAAiCC,OAAO,CAACO,UAA7C,EAAyD;IACvD,OAAO,KAAKC,MAAL,CAAYT,GAAG,CAACU,OAAJ,CAAY,IAAZ,EAAkB,KAAlB,EAAyBA,OAAzB,CAAiC,KAAjC,EAAwC,KAAxC,CAAZ,EAA4D,IAA5D,CAAP;EACD;;EAED,OAAOV,GAAP;AACD,CA1BD"}, "metadata": {}, "sourceType": "script"}