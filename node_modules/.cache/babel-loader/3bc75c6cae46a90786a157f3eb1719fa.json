{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar ReactIs = require('react-is');\n\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nvar has = require('./lib/has');\n\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function () {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function (text) {\n    var message = 'Warning: ' + text;\n\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function (isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n\n  var ANONYMOUS = '<<anonymous>>'; // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker\n  };\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n\n  /*eslint-disable no-self-compare*/\n\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n\n\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data : {};\n    this.stack = '';\n  } // Make `instanceof Error` still work for returned errors.\n\n\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use `PropTypes.checkPropTypes()` to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n\n          if (!manualPropTypeCallCache[cacheKey] && // Avoid spamming the console because they are often not actionable except for lib authors\n          manualPropTypeWarningCount < 3) {\n            printWarning('You are manually calling a React.PropTypes validation ' + 'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' + 'and will throw in the standalone `prop-types` package. ' + 'You may be seeing this warning due to a third-party PropTypes ' + 'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.');\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'), {\n          expectedType: expectedType\n        });\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n\n      var propValue = props[propName];\n\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning('Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' + 'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).');\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n\n        if (type === 'symbol') {\n          return String(value);\n        }\n\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n\n      if (typeof checker !== 'function') {\n        printWarning('Invalid argument supplied to oneOfType. Expected an array of check functions, but ' + 'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.');\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n\n        if (checkerResult == null) {\n          return null;\n        }\n\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n\n      var expectedTypesMessage = expectedTypes.length > 0 ? ', expected one of type [' + expectedTypes.join(', ') + ']' : '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError((componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + type + '`.');\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\n        if (error) {\n          return error;\n        }\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      } // We need to check all keys in case some are required but missing from props.\n\n\n      var allKeys = assign({}, props[propName], shapeTypes);\n\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n\n        if (!checker) {\n          return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' + '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') + '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  '));\n        }\n\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\n        if (error) {\n          return error;\n        }\n      }\n\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n\n      case 'boolean':\n        return !propValue;\n\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    } // falsy value can't be a Symbol\n\n\n    if (!propValue) {\n      return false;\n    } // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n\n\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    } // Fallback for non-spec compliant Symbols which are polyfilled.\n\n\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  } // Equivalent of `typeof` but with special handling for array and regexp.\n\n\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n\n    return propType;\n  } // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n\n\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n\n    var propType = getPropType(propValue);\n\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n\n    return propType;\n  } // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n\n\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n\n      default:\n        return type;\n    }\n  } // Returns class name of the object, if any.\n\n\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n  return ReactPropTypes;\n};", "map": {"version": 3, "names": ["ReactIs", "require", "assign", "ReactPropTypesSecret", "has", "checkPropTypes", "printWarning", "process", "env", "NODE_ENV", "text", "message", "console", "error", "Error", "x", "emptyFunctionThatReturnsNull", "module", "exports", "isValidElement", "throwOnDirectAccess", "ITERATOR_SYMBOL", "Symbol", "iterator", "FAUX_ITERATOR_SYMBOL", "getIteratorFn", "maybeIterable", "iteratorFn", "ANONYMOUS", "ReactPropTypes", "array", "createPrimitiveTypeChecker", "bigint", "bool", "func", "number", "object", "string", "symbol", "any", "createAnyTypeChecker", "arrayOf", "createArrayOfTypeChecker", "element", "createElementTypeChecker", "elementType", "createElementTypeTypeChecker", "instanceOf", "createInstanceTypeChecker", "node", "createNodeChecker", "objectOf", "createObjectOfTypeChecker", "oneOf", "createEnumTypeChecker", "oneOfType", "createUnionTypeChecker", "shape", "createShapeTypeChecker", "exact", "createStrictShapeTypeChecker", "is", "y", "PropTypeError", "data", "stack", "prototype", "createChainableTypeChecker", "validate", "manualPropTypeCallCache", "manualPropTypeWarningCount", "checkType", "isRequired", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "err", "name", "cache<PERSON>ey", "chainedCheckType", "bind", "expectedType", "propValue", "propType", "getPropType", "preciseType", "getPreciseType", "typeC<PERSON>cker", "Array", "isArray", "i", "length", "isValidElementType", "expectedClass", "expectedClassName", "actualClassName", "getClassName", "expectedV<PERSON>ues", "arguments", "valuesString", "JSON", "stringify", "replacer", "key", "value", "type", "String", "arrayOfTypeCheckers", "checker", "getPostfixForTypeWarning", "expectedTypes", "checkerResult", "push", "expectedTypesMessage", "join", "isNode", "invalidValidatorError", "shapeTypes", "allKeys", "Object", "keys", "every", "call", "step", "entries", "next", "done", "entry", "isSymbol", "RegExp", "Date", "constructor", "resetWarningCache", "PropTypes"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/prop-types/factoryWithTypeCheckers.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAAC,UAAD,CAArB;;AACA,IAAIC,MAAM,GAAGD,OAAO,CAAC,eAAD,CAApB;;AAEA,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,4BAAD,CAAlC;;AACA,IAAIG,GAAG,GAAGH,OAAO,CAAC,WAAD,CAAjB;;AACA,IAAII,cAAc,GAAGJ,OAAO,CAAC,kBAAD,CAA5B;;AAEA,IAAIK,YAAY,GAAG,YAAW,CAAE,CAAhC;;AAEA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,YAAY,GAAG,UAASI,IAAT,EAAe;IAC5B,IAAIC,OAAO,GAAG,cAAcD,IAA5B;;IACA,IAAI,OAAOE,OAAP,KAAmB,WAAvB,EAAoC;MAClCA,OAAO,CAACC,KAAR,CAAcF,OAAd;IACD;;IACD,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAIG,KAAJ,CAAUH,OAAV,CAAN;IACD,CALD,CAKE,OAAOI,CAAP,EAAU,CAAE;EACf,CAXD;AAYD;;AAED,SAASC,4BAAT,GAAwC;EACtC,OAAO,IAAP;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiB,UAASC,cAAT,EAAyBC,mBAAzB,EAA8C;EAC7D;EACA,IAAIC,eAAe,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,QAA7D;EACA,IAAIC,oBAAoB,GAAG,YAA3B,CAH6D,CAGpB;;EAEzC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,SAASC,aAAT,CAAuBC,aAAvB,EAAsC;IACpC,IAAIC,UAAU,GAAGD,aAAa,KAAKL,eAAe,IAAIK,aAAa,CAACL,eAAD,CAAhC,IAAqDK,aAAa,CAACF,oBAAD,CAAvE,CAA9B;;IACA,IAAI,OAAOG,UAAP,KAAsB,UAA1B,EAAsC;MACpC,OAAOA,UAAP;IACD;EACF;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EAEE,IAAIC,SAAS,GAAG,eAAhB,CAzE6D,CA2E7D;EACA;;EACA,IAAIC,cAAc,GAAG;IACnBC,KAAK,EAAEC,0BAA0B,CAAC,OAAD,CADd;IAEnBC,MAAM,EAAED,0BAA0B,CAAC,QAAD,CAFf;IAGnBE,IAAI,EAAEF,0BAA0B,CAAC,SAAD,CAHb;IAInBG,IAAI,EAAEH,0BAA0B,CAAC,UAAD,CAJb;IAKnBI,MAAM,EAAEJ,0BAA0B,CAAC,QAAD,CALf;IAMnBK,MAAM,EAAEL,0BAA0B,CAAC,QAAD,CANf;IAOnBM,MAAM,EAAEN,0BAA0B,CAAC,QAAD,CAPf;IAQnBO,MAAM,EAAEP,0BAA0B,CAAC,QAAD,CARf;IAUnBQ,GAAG,EAAEC,oBAAoB,EAVN;IAWnBC,OAAO,EAAEC,wBAXU;IAYnBC,OAAO,EAAEC,wBAAwB,EAZd;IAanBC,WAAW,EAAEC,4BAA4B,EAbtB;IAcnBC,UAAU,EAAEC,yBAdO;IAenBC,IAAI,EAAEC,iBAAiB,EAfJ;IAgBnBC,QAAQ,EAAEC,yBAhBS;IAiBnBC,KAAK,EAAEC,qBAjBY;IAkBnBC,SAAS,EAAEC,sBAlBQ;IAmBnBC,KAAK,EAAEC,sBAnBY;IAoBnBC,KAAK,EAAEC;EApBY,CAArB;EAuBA;AACF;AACA;AACA;;EACE;;EACA,SAASC,EAAT,CAAY9C,CAAZ,EAAe+C,CAAf,EAAkB;IAChB;IACA,IAAI/C,CAAC,KAAK+C,CAAV,EAAa;MACX;MACA;MACA,OAAO/C,CAAC,KAAK,CAAN,IAAW,IAAIA,CAAJ,KAAU,IAAI+C,CAAhC;IACD,CAJD,MAIO;MACL;MACA,OAAO/C,CAAC,KAAKA,CAAN,IAAW+C,CAAC,KAAKA,CAAxB;IACD;EACF;EACD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE,SAASC,aAAT,CAAuBpD,OAAvB,EAAgCqD,IAAhC,EAAsC;IACpC,KAAKrD,OAAL,GAAeA,OAAf;IACA,KAAKqD,IAAL,GAAYA,IAAI,IAAI,OAAOA,IAAP,KAAgB,QAAxB,GAAmCA,IAAnC,GAAyC,EAArD;IACA,KAAKC,KAAL,GAAa,EAAb;EACD,CAjI4D,CAkI7D;;;EACAF,aAAa,CAACG,SAAd,GAA0BpD,KAAK,CAACoD,SAAhC;;EAEA,SAASC,0BAAT,CAAoCC,QAApC,EAA8C;IAC5C,IAAI7D,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;MACzC,IAAI4D,uBAAuB,GAAG,EAA9B;MACA,IAAIC,0BAA0B,GAAG,CAAjC;IACD;;IACD,SAASC,SAAT,CAAmBC,UAAnB,EAA+BC,KAA/B,EAAsCC,QAAtC,EAAgDC,aAAhD,EAA+DC,QAA/D,EAAyEC,YAAzE,EAAuFC,MAAvF,EAA+F;MAC7FH,aAAa,GAAGA,aAAa,IAAI/C,SAAjC;MACAiD,YAAY,GAAGA,YAAY,IAAIH,QAA/B;;MAEA,IAAII,MAAM,KAAK3E,oBAAf,EAAqC;QACnC,IAAIiB,mBAAJ,EAAyB;UACvB;UACA,IAAI2D,GAAG,GAAG,IAAIjE,KAAJ,CACR,yFACA,iDADA,GAEA,gDAHQ,CAAV;UAKAiE,GAAG,CAACC,IAAJ,GAAW,qBAAX;UACA,MAAMD,GAAN;QACD,CATD,MASO,IAAIxE,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,IAAyC,OAAOG,OAAP,KAAmB,WAAhE,EAA6E;UAClF;UACA,IAAIqE,QAAQ,GAAGN,aAAa,GAAG,GAAhB,GAAsBD,QAArC;;UACA,IACE,CAACL,uBAAuB,CAACY,QAAD,CAAxB,IACA;UACAX,0BAA0B,GAAG,CAH/B,EAIE;YACAhE,YAAY,CACV,2DACA,oBADA,GACuBuE,YADvB,GACsC,aADtC,GACsDF,aADtD,GACsE,wBADtE,GAEA,yDAFA,GAGA,gEAHA,GAIA,+DAJA,GAIkE,cALxD,CAAZ;YAOAN,uBAAuB,CAACY,QAAD,CAAvB,GAAoC,IAApC;YACAX,0BAA0B;UAC3B;QACF;MACF;;MACD,IAAIG,KAAK,CAACC,QAAD,CAAL,IAAmB,IAAvB,EAA6B;QAC3B,IAAIF,UAAJ,EAAgB;UACd,IAAIC,KAAK,CAACC,QAAD,CAAL,KAAoB,IAAxB,EAA8B;YAC5B,OAAO,IAAIX,aAAJ,CAAkB,SAASa,QAAT,GAAoB,IAApB,GAA2BC,YAA3B,GAA0C,0BAA1C,IAAwE,SAASF,aAAT,GAAyB,6BAAjG,CAAlB,CAAP;UACD;;UACD,OAAO,IAAIZ,aAAJ,CAAkB,SAASa,QAAT,GAAoB,IAApB,GAA2BC,YAA3B,GAA0C,6BAA1C,IAA2E,MAAMF,aAAN,GAAsB,kCAAjG,CAAlB,CAAP;QACD;;QACD,OAAO,IAAP;MACD,CARD,MAQO;QACL,OAAOP,QAAQ,CAACK,KAAD,EAAQC,QAAR,EAAkBC,aAAlB,EAAiCC,QAAjC,EAA2CC,YAA3C,CAAf;MACD;IACF;;IAED,IAAIK,gBAAgB,GAAGX,SAAS,CAACY,IAAV,CAAe,IAAf,EAAqB,KAArB,CAAvB;IACAD,gBAAgB,CAACV,UAAjB,GAA8BD,SAAS,CAACY,IAAV,CAAe,IAAf,EAAqB,IAArB,CAA9B;IAEA,OAAOD,gBAAP;EACD;;EAED,SAASnD,0BAAT,CAAoCqD,YAApC,EAAkD;IAChD,SAAShB,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0EC,MAA1E,EAAkF;MAChF,IAAIO,SAAS,GAAGZ,KAAK,CAACC,QAAD,CAArB;MACA,IAAIY,QAAQ,GAAGC,WAAW,CAACF,SAAD,CAA1B;;MACA,IAAIC,QAAQ,KAAKF,YAAjB,EAA+B;QAC7B;QACA;QACA;QACA,IAAII,WAAW,GAAGC,cAAc,CAACJ,SAAD,CAAhC;QAEA,OAAO,IAAItB,aAAJ,CACL,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,YAA9C,IAA8D,MAAMW,WAAN,GAAoB,iBAApB,GAAwCb,aAAxC,GAAwD,cAAtH,KAAyI,MAAMS,YAAN,GAAqB,IAA9J,CADK,EAEL;UAACA,YAAY,EAAEA;QAAf,CAFK,CAAP;MAID;;MACD,OAAO,IAAP;IACD;;IACD,OAAOjB,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAAS5B,oBAAT,GAAgC;IAC9B,OAAO2B,0BAA0B,CAACnD,4BAAD,CAAjC;EACD;;EAED,SAAS0B,wBAAT,CAAkCgD,WAAlC,EAA+C;IAC7C,SAAStB,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAI,OAAOa,WAAP,KAAuB,UAA3B,EAAuC;QACrC,OAAO,IAAI3B,aAAJ,CAAkB,eAAec,YAAf,GAA8B,kBAA9B,GAAmDF,aAAnD,GAAmE,iDAArF,CAAP;MACD;;MACD,IAAIU,SAAS,GAAGZ,KAAK,CAACC,QAAD,CAArB;;MACA,IAAI,CAACiB,KAAK,CAACC,OAAN,CAAcP,SAAd,CAAL,EAA+B;QAC7B,IAAIC,QAAQ,GAAGC,WAAW,CAACF,SAAD,CAA1B;QACA,OAAO,IAAItB,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,YAA9C,IAA8D,MAAMS,QAAN,GAAiB,iBAAjB,GAAqCX,aAArC,GAAqD,uBAAnH,CAAlB,CAAP;MACD;;MACD,KAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,SAAS,CAACS,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;QACzC,IAAIhF,KAAK,GAAG6E,WAAW,CAACL,SAAD,EAAYQ,CAAZ,EAAelB,aAAf,EAA8BC,QAA9B,EAAwCC,YAAY,GAAG,GAAf,GAAqBgB,CAArB,GAAyB,GAAjE,EAAsE1F,oBAAtE,CAAvB;;QACA,IAAIU,KAAK,YAAYC,KAArB,EAA4B;UAC1B,OAAOD,KAAP;QACD;MACF;;MACD,OAAO,IAAP;IACD;;IACD,OAAOsD,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAASxB,wBAAT,GAAoC;IAClC,SAASwB,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAD,CAArB;;MACA,IAAI,CAACvD,cAAc,CAACkE,SAAD,CAAnB,EAAgC;QAC9B,IAAIC,QAAQ,GAAGC,WAAW,CAACF,SAAD,CAA1B;QACA,OAAO,IAAItB,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,YAA9C,IAA8D,MAAMS,QAAN,GAAiB,iBAAjB,GAAqCX,aAArC,GAAqD,oCAAnH,CAAlB,CAAP;MACD;;MACD,OAAO,IAAP;IACD;;IACD,OAAOR,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAAStB,4BAAT,GAAwC;IACtC,SAASsB,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAD,CAArB;;MACA,IAAI,CAAC1E,OAAO,CAAC+F,kBAAR,CAA2BV,SAA3B,CAAL,EAA4C;QAC1C,IAAIC,QAAQ,GAAGC,WAAW,CAACF,SAAD,CAA1B;QACA,OAAO,IAAItB,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,YAA9C,IAA8D,MAAMS,QAAN,GAAiB,iBAAjB,GAAqCX,aAArC,GAAqD,yCAAnH,CAAlB,CAAP;MACD;;MACD,OAAO,IAAP;IACD;;IACD,OAAOR,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAASpB,yBAAT,CAAmCgD,aAAnC,EAAkD;IAChD,SAAS5B,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAI,EAAEJ,KAAK,CAACC,QAAD,CAAL,YAA2BsB,aAA7B,CAAJ,EAAiD;QAC/C,IAAIC,iBAAiB,GAAGD,aAAa,CAAChB,IAAd,IAAsBpD,SAA9C;QACA,IAAIsE,eAAe,GAAGC,YAAY,CAAC1B,KAAK,CAACC,QAAD,CAAN,CAAlC;QACA,OAAO,IAAIX,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,YAA9C,IAA8D,MAAMqB,eAAN,GAAwB,iBAAxB,GAA4CvB,aAA5C,GAA4D,cAA1H,KAA6I,kBAAkBsB,iBAAlB,GAAsC,IAAnL,CAAlB,CAAP;MACD;;MACD,OAAO,IAAP;IACD;;IACD,OAAO9B,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAASd,qBAAT,CAA+B8C,cAA/B,EAA+C;IAC7C,IAAI,CAACT,KAAK,CAACC,OAAN,CAAcQ,cAAd,CAAL,EAAoC;MAClC,IAAI7F,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;QACzC,IAAI4F,SAAS,CAACP,MAAV,GAAmB,CAAvB,EAA0B;UACxBxF,YAAY,CACV,iEAAiE+F,SAAS,CAACP,MAA3E,GAAoF,cAApF,GACA,0EAFU,CAAZ;QAID,CALD,MAKO;UACLxF,YAAY,CAAC,wDAAD,CAAZ;QACD;MACF;;MACD,OAAOU,4BAAP;IACD;;IAED,SAASoD,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAD,CAArB;;MACA,KAAK,IAAImB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGO,cAAc,CAACN,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;QAC9C,IAAIhC,EAAE,CAACwB,SAAD,EAAYe,cAAc,CAACP,CAAD,CAA1B,CAAN,EAAsC;UACpC,OAAO,IAAP;QACD;MACF;;MAED,IAAIS,YAAY,GAAGC,IAAI,CAACC,SAAL,CAAeJ,cAAf,EAA+B,SAASK,QAAT,CAAkBC,GAAlB,EAAuBC,KAAvB,EAA8B;QAC9E,IAAIC,IAAI,GAAGnB,cAAc,CAACkB,KAAD,CAAzB;;QACA,IAAIC,IAAI,KAAK,QAAb,EAAuB;UACrB,OAAOC,MAAM,CAACF,KAAD,CAAb;QACD;;QACD,OAAOA,KAAP;MACD,CANkB,CAAnB;MAOA,OAAO,IAAI5C,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,cAA9C,GAA+DgC,MAAM,CAACxB,SAAD,CAArE,GAAmF,IAAnF,IAA2F,kBAAkBV,aAAlB,GAAkC,qBAAlC,GAA0D2B,YAA1D,GAAyE,GAApK,CAAlB,CAAP;IACD;;IACD,OAAOnC,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAAShB,yBAAT,CAAmCsC,WAAnC,EAAgD;IAC9C,SAAStB,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAI,OAAOa,WAAP,KAAuB,UAA3B,EAAuC;QACrC,OAAO,IAAI3B,aAAJ,CAAkB,eAAec,YAAf,GAA8B,kBAA9B,GAAmDF,aAAnD,GAAmE,kDAArF,CAAP;MACD;;MACD,IAAIU,SAAS,GAAGZ,KAAK,CAACC,QAAD,CAArB;MACA,IAAIY,QAAQ,GAAGC,WAAW,CAACF,SAAD,CAA1B;;MACA,IAAIC,QAAQ,KAAK,QAAjB,EAA2B;QACzB,OAAO,IAAIvB,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,YAA9C,IAA8D,MAAMS,QAAN,GAAiB,iBAAjB,GAAqCX,aAArC,GAAqD,wBAAnH,CAAlB,CAAP;MACD;;MACD,KAAK,IAAI+B,GAAT,IAAgBrB,SAAhB,EAA2B;QACzB,IAAIjF,GAAG,CAACiF,SAAD,EAAYqB,GAAZ,CAAP,EAAyB;UACvB,IAAI7F,KAAK,GAAG6E,WAAW,CAACL,SAAD,EAAYqB,GAAZ,EAAiB/B,aAAjB,EAAgCC,QAAhC,EAA0CC,YAAY,GAAG,GAAf,GAAqB6B,GAA/D,EAAoEvG,oBAApE,CAAvB;;UACA,IAAIU,KAAK,YAAYC,KAArB,EAA4B;YAC1B,OAAOD,KAAP;UACD;QACF;MACF;;MACD,OAAO,IAAP;IACD;;IACD,OAAOsD,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAASZ,sBAAT,CAAgCsD,mBAAhC,EAAqD;IACnD,IAAI,CAACnB,KAAK,CAACC,OAAN,CAAckB,mBAAd,CAAL,EAAyC;MACvCvG,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAAzB,GAAwCH,YAAY,CAAC,wEAAD,CAApD,GAAiI,KAAK,CAAtI;MACA,OAAOU,4BAAP;IACD;;IAED,KAAK,IAAI6E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,mBAAmB,CAAChB,MAAxC,EAAgDD,CAAC,EAAjD,EAAqD;MACnD,IAAIkB,OAAO,GAAGD,mBAAmB,CAACjB,CAAD,CAAjC;;MACA,IAAI,OAAOkB,OAAP,KAAmB,UAAvB,EAAmC;QACjCzG,YAAY,CACV,uFACA,WADA,GACc0G,wBAAwB,CAACD,OAAD,CADtC,GACkD,YADlD,GACiElB,CADjE,GACqE,GAF3D,CAAZ;QAIA,OAAO7E,4BAAP;MACD;IACF;;IAED,SAASoD,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAIoC,aAAa,GAAG,EAApB;;MACA,KAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,mBAAmB,CAAChB,MAAxC,EAAgDD,CAAC,EAAjD,EAAqD;QACnD,IAAIkB,OAAO,GAAGD,mBAAmB,CAACjB,CAAD,CAAjC;QACA,IAAIqB,aAAa,GAAGH,OAAO,CAACtC,KAAD,EAAQC,QAAR,EAAkBC,aAAlB,EAAiCC,QAAjC,EAA2CC,YAA3C,EAAyD1E,oBAAzD,CAA3B;;QACA,IAAI+G,aAAa,IAAI,IAArB,EAA2B;UACzB,OAAO,IAAP;QACD;;QACD,IAAIA,aAAa,CAAClD,IAAd,IAAsB5D,GAAG,CAAC8G,aAAa,CAAClD,IAAf,EAAqB,cAArB,CAA7B,EAAmE;UACjEiD,aAAa,CAACE,IAAd,CAAmBD,aAAa,CAAClD,IAAd,CAAmBoB,YAAtC;QACD;MACF;;MACD,IAAIgC,oBAAoB,GAAIH,aAAa,CAACnB,MAAd,GAAuB,CAAxB,GAA6B,6BAA6BmB,aAAa,CAACI,IAAd,CAAmB,IAAnB,CAA7B,GAAwD,GAArF,GAA0F,EAArH;MACA,OAAO,IAAItD,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,gBAA9C,IAAkE,MAAMF,aAAN,GAAsB,GAAtB,GAA4ByC,oBAA5B,GAAmD,GAArH,CAAlB,CAAP;IACD;;IACD,OAAOjD,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAASlB,iBAAT,GAA6B;IAC3B,SAASkB,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAI,CAACyC,MAAM,CAAC7C,KAAK,CAACC,QAAD,CAAN,CAAX,EAA8B;QAC5B,OAAO,IAAIX,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,gBAA9C,IAAkE,MAAMF,aAAN,GAAsB,0BAAxF,CAAlB,CAAP;MACD;;MACD,OAAO,IAAP;IACD;;IACD,OAAOR,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAASmD,qBAAT,CAA+B5C,aAA/B,EAA8CC,QAA9C,EAAwDC,YAAxD,EAAsE6B,GAAtE,EAA2EE,IAA3E,EAAiF;IAC/E,OAAO,IAAI7C,aAAJ,CACL,CAACY,aAAa,IAAI,aAAlB,IAAmC,IAAnC,GAA0CC,QAA1C,GAAqD,SAArD,GAAiEC,YAAjE,GAAgF,GAAhF,GAAsF6B,GAAtF,GAA4F,gBAA5F,GACA,8EADA,GACiFE,IADjF,GACwF,IAFnF,CAAP;EAID;;EAED,SAASlD,sBAAT,CAAgC8D,UAAhC,EAA4C;IAC1C,SAASpD,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAD,CAArB;MACA,IAAIY,QAAQ,GAAGC,WAAW,CAACF,SAAD,CAA1B;;MACA,IAAIC,QAAQ,KAAK,QAAjB,EAA2B;QACzB,OAAO,IAAIvB,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,aAA9C,GAA8DS,QAA9D,GAAyE,IAAzE,IAAiF,kBAAkBX,aAAlB,GAAkC,uBAAnH,CAAlB,CAAP;MACD;;MACD,KAAK,IAAI+B,GAAT,IAAgBc,UAAhB,EAA4B;QAC1B,IAAIT,OAAO,GAAGS,UAAU,CAACd,GAAD,CAAxB;;QACA,IAAI,OAAOK,OAAP,KAAmB,UAAvB,EAAmC;UACjC,OAAOQ,qBAAqB,CAAC5C,aAAD,EAAgBC,QAAhB,EAA0BC,YAA1B,EAAwC6B,GAAxC,EAA6CjB,cAAc,CAACsB,OAAD,CAA3D,CAA5B;QACD;;QACD,IAAIlG,KAAK,GAAGkG,OAAO,CAAC1B,SAAD,EAAYqB,GAAZ,EAAiB/B,aAAjB,EAAgCC,QAAhC,EAA0CC,YAAY,GAAG,GAAf,GAAqB6B,GAA/D,EAAoEvG,oBAApE,CAAnB;;QACA,IAAIU,KAAJ,EAAW;UACT,OAAOA,KAAP;QACD;MACF;;MACD,OAAO,IAAP;IACD;;IACD,OAAOsD,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAASR,4BAAT,CAAsC4D,UAAtC,EAAkD;IAChD,SAASpD,QAAT,CAAkBK,KAAlB,EAAyBC,QAAzB,EAAmCC,aAAnC,EAAkDC,QAAlD,EAA4DC,YAA5D,EAA0E;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAD,CAArB;MACA,IAAIY,QAAQ,GAAGC,WAAW,CAACF,SAAD,CAA1B;;MACA,IAAIC,QAAQ,KAAK,QAAjB,EAA2B;QACzB,OAAO,IAAIvB,aAAJ,CAAkB,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,aAA9C,GAA8DS,QAA9D,GAAyE,IAAzE,IAAiF,kBAAkBX,aAAlB,GAAkC,uBAAnH,CAAlB,CAAP;MACD,CALuE,CAMxE;;;MACA,IAAI8C,OAAO,GAAGvH,MAAM,CAAC,EAAD,EAAKuE,KAAK,CAACC,QAAD,CAAV,EAAsB8C,UAAtB,CAApB;;MACA,KAAK,IAAId,GAAT,IAAgBe,OAAhB,EAAyB;QACvB,IAAIV,OAAO,GAAGS,UAAU,CAACd,GAAD,CAAxB;;QACA,IAAItG,GAAG,CAACoH,UAAD,EAAad,GAAb,CAAH,IAAwB,OAAOK,OAAP,KAAmB,UAA/C,EAA2D;UACzD,OAAOQ,qBAAqB,CAAC5C,aAAD,EAAgBC,QAAhB,EAA0BC,YAA1B,EAAwC6B,GAAxC,EAA6CjB,cAAc,CAACsB,OAAD,CAA3D,CAA5B;QACD;;QACD,IAAI,CAACA,OAAL,EAAc;UACZ,OAAO,IAAIhD,aAAJ,CACL,aAAaa,QAAb,GAAwB,IAAxB,GAA+BC,YAA/B,GAA8C,SAA9C,GAA0D6B,GAA1D,GAAgE,iBAAhE,GAAoF/B,aAApF,GAAoG,IAApG,GACA,gBADA,GACmB4B,IAAI,CAACC,SAAL,CAAe/B,KAAK,CAACC,QAAD,CAApB,EAAgC,IAAhC,EAAsC,IAAtC,CADnB,GAEA,gBAFA,GAEmB6B,IAAI,CAACC,SAAL,CAAekB,MAAM,CAACC,IAAP,CAAYH,UAAZ,CAAf,EAAwC,IAAxC,EAA8C,IAA9C,CAHd,CAAP;QAKD;;QACD,IAAI3G,KAAK,GAAGkG,OAAO,CAAC1B,SAAD,EAAYqB,GAAZ,EAAiB/B,aAAjB,EAAgCC,QAAhC,EAA0CC,YAAY,GAAG,GAAf,GAAqB6B,GAA/D,EAAoEvG,oBAApE,CAAnB;;QACA,IAAIU,KAAJ,EAAW;UACT,OAAOA,KAAP;QACD;MACF;;MACD,OAAO,IAAP;IACD;;IAED,OAAOsD,0BAA0B,CAACC,QAAD,CAAjC;EACD;;EAED,SAASkD,MAAT,CAAgBjC,SAAhB,EAA2B;IACzB,QAAQ,OAAOA,SAAf;MACE,KAAK,QAAL;MACA,KAAK,QAAL;MACA,KAAK,WAAL;QACE,OAAO,IAAP;;MACF,KAAK,SAAL;QACE,OAAO,CAACA,SAAR;;MACF,KAAK,QAAL;QACE,IAAIM,KAAK,CAACC,OAAN,CAAcP,SAAd,CAAJ,EAA8B;UAC5B,OAAOA,SAAS,CAACuC,KAAV,CAAgBN,MAAhB,CAAP;QACD;;QACD,IAAIjC,SAAS,KAAK,IAAd,IAAsBlE,cAAc,CAACkE,SAAD,CAAxC,EAAqD;UACnD,OAAO,IAAP;QACD;;QAED,IAAI1D,UAAU,GAAGF,aAAa,CAAC4D,SAAD,CAA9B;;QACA,IAAI1D,UAAJ,EAAgB;UACd,IAAIJ,QAAQ,GAAGI,UAAU,CAACkG,IAAX,CAAgBxC,SAAhB,CAAf;UACA,IAAIyC,IAAJ;;UACA,IAAInG,UAAU,KAAK0D,SAAS,CAAC0C,OAA7B,EAAsC;YACpC,OAAO,CAAC,CAACD,IAAI,GAAGvG,QAAQ,CAACyG,IAAT,EAAR,EAAyBC,IAAjC,EAAuC;cACrC,IAAI,CAACX,MAAM,CAACQ,IAAI,CAACnB,KAAN,CAAX,EAAyB;gBACvB,OAAO,KAAP;cACD;YACF;UACF,CAND,MAMO;YACL;YACA,OAAO,CAAC,CAACmB,IAAI,GAAGvG,QAAQ,CAACyG,IAAT,EAAR,EAAyBC,IAAjC,EAAuC;cACrC,IAAIC,KAAK,GAAGJ,IAAI,CAACnB,KAAjB;;cACA,IAAIuB,KAAJ,EAAW;gBACT,IAAI,CAACZ,MAAM,CAACY,KAAK,CAAC,CAAD,CAAN,CAAX,EAAuB;kBACrB,OAAO,KAAP;gBACD;cACF;YACF;UACF;QACF,CApBD,MAoBO;UACL,OAAO,KAAP;QACD;;QAED,OAAO,IAAP;;MACF;QACE,OAAO,KAAP;IA1CJ;EA4CD;;EAED,SAASC,QAAT,CAAkB7C,QAAlB,EAA4BD,SAA5B,EAAuC;IACrC;IACA,IAAIC,QAAQ,KAAK,QAAjB,EAA2B;MACzB,OAAO,IAAP;IACD,CAJoC,CAMrC;;;IACA,IAAI,CAACD,SAAL,EAAgB;MACd,OAAO,KAAP;IACD,CAToC,CAWrC;;;IACA,IAAIA,SAAS,CAAC,eAAD,CAAT,KAA+B,QAAnC,EAA6C;MAC3C,OAAO,IAAP;IACD,CAdoC,CAgBrC;;;IACA,IAAI,OAAO/D,MAAP,KAAkB,UAAlB,IAAgC+D,SAAS,YAAY/D,MAAzD,EAAiE;MAC/D,OAAO,IAAP;IACD;;IAED,OAAO,KAAP;EACD,CAzf4D,CA2f7D;;;EACA,SAASiE,WAAT,CAAqBF,SAArB,EAAgC;IAC9B,IAAIC,QAAQ,GAAG,OAAOD,SAAtB;;IACA,IAAIM,KAAK,CAACC,OAAN,CAAcP,SAAd,CAAJ,EAA8B;MAC5B,OAAO,OAAP;IACD;;IACD,IAAIA,SAAS,YAAY+C,MAAzB,EAAiC;MAC/B;MACA;MACA;MACA,OAAO,QAAP;IACD;;IACD,IAAID,QAAQ,CAAC7C,QAAD,EAAWD,SAAX,CAAZ,EAAmC;MACjC,OAAO,QAAP;IACD;;IACD,OAAOC,QAAP;EACD,CA3gB4D,CA6gB7D;EACA;;;EACA,SAASG,cAAT,CAAwBJ,SAAxB,EAAmC;IACjC,IAAI,OAAOA,SAAP,KAAqB,WAArB,IAAoCA,SAAS,KAAK,IAAtD,EAA4D;MAC1D,OAAO,KAAKA,SAAZ;IACD;;IACD,IAAIC,QAAQ,GAAGC,WAAW,CAACF,SAAD,CAA1B;;IACA,IAAIC,QAAQ,KAAK,QAAjB,EAA2B;MACzB,IAAID,SAAS,YAAYgD,IAAzB,EAA+B;QAC7B,OAAO,MAAP;MACD,CAFD,MAEO,IAAIhD,SAAS,YAAY+C,MAAzB,EAAiC;QACtC,OAAO,QAAP;MACD;IACF;;IACD,OAAO9C,QAAP;EACD,CA5hB4D,CA8hB7D;EACA;;;EACA,SAAS0B,wBAAT,CAAkCL,KAAlC,EAAyC;IACvC,IAAIC,IAAI,GAAGnB,cAAc,CAACkB,KAAD,CAAzB;;IACA,QAAQC,IAAR;MACE,KAAK,OAAL;MACA,KAAK,QAAL;QACE,OAAO,QAAQA,IAAf;;MACF,KAAK,SAAL;MACA,KAAK,MAAL;MACA,KAAK,QAAL;QACE,OAAO,OAAOA,IAAd;;MACF;QACE,OAAOA,IAAP;IATJ;EAWD,CA7iB4D,CA+iB7D;;;EACA,SAAST,YAAT,CAAsBd,SAAtB,EAAiC;IAC/B,IAAI,CAACA,SAAS,CAACiD,WAAX,IAA0B,CAACjD,SAAS,CAACiD,WAAV,CAAsBtD,IAArD,EAA2D;MACzD,OAAOpD,SAAP;IACD;;IACD,OAAOyD,SAAS,CAACiD,WAAV,CAAsBtD,IAA7B;EACD;;EAEDnD,cAAc,CAACxB,cAAf,GAAgCA,cAAhC;EACAwB,cAAc,CAAC0G,iBAAf,GAAmClI,cAAc,CAACkI,iBAAlD;EACA1G,cAAc,CAAC2G,SAAf,GAA2B3G,cAA3B;EAEA,OAAOA,cAAP;AACD,CA5jBD"}, "metadata": {}, "sourceType": "script"}