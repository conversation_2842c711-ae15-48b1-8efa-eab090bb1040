{"ast": null, "code": "'use strict';\n\nvar global = require('../internals/global');\n\nvar apply = require('../internals/function-apply');\n\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar isCallable = require('../internals/is-callable');\n\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\n\nvar isForced = require('../internals/is-forced');\n\nvar path = require('../internals/path');\n\nvar bind = require('../internals/function-bind-context');\n\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\nvar hasOwn = require('../internals/has-own-property');\n\nvar wrapConstructor = function (NativeConstructor) {\n  var Wrapper = function (a, b, c) {\n    if (this instanceof Wrapper) {\n      switch (arguments.length) {\n        case 0:\n          return new NativeConstructor();\n\n        case 1:\n          return new NativeConstructor(a);\n\n        case 2:\n          return new NativeConstructor(a, b);\n      }\n\n      return new NativeConstructor(a, b, c);\n    }\n\n    return apply(NativeConstructor, this, arguments);\n  };\n\n  Wrapper.prototype = NativeConstructor.prototype;\n  return Wrapper;\n};\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\n\n\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var PROTO = options.proto;\n  var nativeSource = GLOBAL ? global : STATIC ? global[TARGET] : (global[TARGET] || {}).prototype;\n  var target = GLOBAL ? path : path[TARGET] || createNonEnumerableProperty(path, TARGET, {})[TARGET];\n  var targetPrototype = target.prototype;\n  var FORCED, USE_NATIVE, VIRTUAL_PROTOTYPE;\n  var key, sourceProperty, targetProperty, nativeProperty, resultProperty, descriptor;\n\n  for (key in source) {\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced); // contains in native\n\n    USE_NATIVE = !FORCED && nativeSource && hasOwn(nativeSource, key);\n    targetProperty = target[key];\n    if (USE_NATIVE) if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(nativeSource, key);\n      nativeProperty = descriptor && descriptor.value;\n    } else nativeProperty = nativeSource[key]; // export native or implementation\n\n    sourceProperty = USE_NATIVE && nativeProperty ? nativeProperty : source[key];\n    if (USE_NATIVE && typeof targetProperty == typeof sourceProperty) continue; // bind timers to global for call from export context\n\n    if (options.bind && USE_NATIVE) resultProperty = bind(sourceProperty, global); // wrap global constructors for prevent changs in this version\n    else if (options.wrap && USE_NATIVE) resultProperty = wrapConstructor(sourceProperty); // make static versions for prototype methods\n    else if (PROTO && isCallable(sourceProperty)) resultProperty = uncurryThis(sourceProperty); // default case\n    else resultProperty = sourceProperty; // add a flag to not completely full polyfills\n\n    if (options.sham || sourceProperty && sourceProperty.sham || targetProperty && targetProperty.sham) {\n      createNonEnumerableProperty(resultProperty, 'sham', true);\n    }\n\n    createNonEnumerableProperty(target, key, resultProperty);\n\n    if (PROTO) {\n      VIRTUAL_PROTOTYPE = TARGET + 'Prototype';\n\n      if (!hasOwn(path, VIRTUAL_PROTOTYPE)) {\n        createNonEnumerableProperty(path, VIRTUAL_PROTOTYPE, {});\n      } // export virtual prototype methods\n\n\n      createNonEnumerableProperty(path[VIRTUAL_PROTOTYPE], key, sourceProperty); // export real prototype methods\n\n      if (options.real && targetPrototype && !targetPrototype[key]) {\n        createNonEnumerableProperty(targetPrototype, key, sourceProperty);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["global", "require", "apply", "uncurryThis", "isCallable", "getOwnPropertyDescriptor", "f", "isForced", "path", "bind", "createNonEnumerableProperty", "hasOwn", "wrapConstructor", "NativeConstructor", "Wrapper", "a", "b", "c", "arguments", "length", "prototype", "module", "exports", "options", "source", "TARGET", "target", "GLOBAL", "STATIC", "stat", "PROTO", "proto", "nativeSource", "targetPrototype", "FORCED", "USE_NATIVE", "VIRTUAL_PROTOTYPE", "key", "sourceProperty", "targetProperty", "nativeProperty", "resultProperty", "descriptor", "forced", "dontCallGetSet", "value", "wrap", "sham", "real"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/export.js"], "sourcesContent": ["'use strict';\nvar global = require('../internals/global');\nvar apply = require('../internals/function-apply');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar isForced = require('../internals/is-forced');\nvar path = require('../internals/path');\nvar bind = require('../internals/function-bind-context');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\n\nvar wrapConstructor = function (NativeConstructor) {\n  var Wrapper = function (a, b, c) {\n    if (this instanceof Wrapper) {\n      switch (arguments.length) {\n        case 0: return new NativeConstructor();\n        case 1: return new NativeConstructor(a);\n        case 2: return new NativeConstructor(a, b);\n      } return new NativeConstructor(a, b, c);\n    } return apply(NativeConstructor, this, arguments);\n  };\n  Wrapper.prototype = NativeConstructor.prototype;\n  return Wrapper;\n};\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var PROTO = options.proto;\n\n  var nativeSource = GLOBAL ? global : STATIC ? global[TARGET] : (global[TARGET] || {}).prototype;\n\n  var target = GLOBAL ? path : path[TARGET] || createNonEnumerableProperty(path, TARGET, {})[TARGET];\n  var targetPrototype = target.prototype;\n\n  var FORCED, USE_NATIVE, VIRTUAL_PROTOTYPE;\n  var key, sourceProperty, targetProperty, nativeProperty, resultProperty, descriptor;\n\n  for (key in source) {\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contains in native\n    USE_NATIVE = !FORCED && nativeSource && hasOwn(nativeSource, key);\n\n    targetProperty = target[key];\n\n    if (USE_NATIVE) if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(nativeSource, key);\n      nativeProperty = descriptor && descriptor.value;\n    } else nativeProperty = nativeSource[key];\n\n    // export native or implementation\n    sourceProperty = (USE_NATIVE && nativeProperty) ? nativeProperty : source[key];\n\n    if (USE_NATIVE && typeof targetProperty == typeof sourceProperty) continue;\n\n    // bind timers to global for call from export context\n    if (options.bind && USE_NATIVE) resultProperty = bind(sourceProperty, global);\n    // wrap global constructors for prevent changs in this version\n    else if (options.wrap && USE_NATIVE) resultProperty = wrapConstructor(sourceProperty);\n    // make static versions for prototype methods\n    else if (PROTO && isCallable(sourceProperty)) resultProperty = uncurryThis(sourceProperty);\n    // default case\n    else resultProperty = sourceProperty;\n\n    // add a flag to not completely full polyfills\n    if (options.sham || (sourceProperty && sourceProperty.sham) || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(resultProperty, 'sham', true);\n    }\n\n    createNonEnumerableProperty(target, key, resultProperty);\n\n    if (PROTO) {\n      VIRTUAL_PROTOTYPE = TARGET + 'Prototype';\n      if (!hasOwn(path, VIRTUAL_PROTOTYPE)) {\n        createNonEnumerableProperty(path, VIRTUAL_PROTOTYPE, {});\n      }\n      // export virtual prototype methods\n      createNonEnumerableProperty(path[VIRTUAL_PROTOTYPE], key, sourceProperty);\n      // export real prototype methods\n      if (options.real && targetPrototype && !targetPrototype[key]) {\n        createNonEnumerableProperty(targetPrototype, key, sourceProperty);\n      }\n    }\n  }\n};\n"], "mappings": "AAAA;;AACA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAD,CAApB;;AACA,IAAIC,KAAK,GAAGD,OAAO,CAAC,6BAAD,CAAnB;;AACA,IAAIE,WAAW,GAAGF,OAAO,CAAC,oCAAD,CAAzB;;AACA,IAAIG,UAAU,GAAGH,OAAO,CAAC,0BAAD,CAAxB;;AACA,IAAII,wBAAwB,GAAGJ,OAAO,CAAC,iDAAD,CAAP,CAA2DK,CAA1F;;AACA,IAAIC,QAAQ,GAAGN,OAAO,CAAC,wBAAD,CAAtB;;AACA,IAAIO,IAAI,GAAGP,OAAO,CAAC,mBAAD,CAAlB;;AACA,IAAIQ,IAAI,GAAGR,OAAO,CAAC,oCAAD,CAAlB;;AACA,IAAIS,2BAA2B,GAAGT,OAAO,CAAC,6CAAD,CAAzC;;AACA,IAAIU,MAAM,GAAGV,OAAO,CAAC,+BAAD,CAApB;;AAEA,IAAIW,eAAe,GAAG,UAAUC,iBAAV,EAA6B;EACjD,IAAIC,OAAO,GAAG,UAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmB;IAC/B,IAAI,gBAAgBH,OAApB,EAA6B;MAC3B,QAAQI,SAAS,CAACC,MAAlB;QACE,KAAK,CAAL;UAAQ,OAAO,IAAIN,iBAAJ,EAAP;;QACR,KAAK,CAAL;UAAQ,OAAO,IAAIA,iBAAJ,CAAsBE,CAAtB,CAAP;;QACR,KAAK,CAAL;UAAQ,OAAO,IAAIF,iBAAJ,CAAsBE,CAAtB,EAAyBC,CAAzB,CAAP;MAHV;;MAIE,OAAO,IAAIH,iBAAJ,CAAsBE,CAAtB,EAAyBC,CAAzB,EAA4BC,CAA5B,CAAP;IACH;;IAAC,OAAOf,KAAK,CAACW,iBAAD,EAAoB,IAApB,EAA0BK,SAA1B,CAAZ;EACH,CARD;;EASAJ,OAAO,CAACM,SAAR,GAAoBP,iBAAiB,CAACO,SAAtC;EACA,OAAON,OAAP;AACD,CAZD;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAO,MAAM,CAACC,OAAP,GAAiB,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;EAC1C,IAAIC,MAAM,GAAGF,OAAO,CAACG,MAArB;EACA,IAAIC,MAAM,GAAGJ,OAAO,CAACvB,MAArB;EACA,IAAI4B,MAAM,GAAGL,OAAO,CAACM,IAArB;EACA,IAAIC,KAAK,GAAGP,OAAO,CAACQ,KAApB;EAEA,IAAIC,YAAY,GAAGL,MAAM,GAAG3B,MAAH,GAAY4B,MAAM,GAAG5B,MAAM,CAACyB,MAAD,CAAT,GAAoB,CAACzB,MAAM,CAACyB,MAAD,CAAN,IAAkB,EAAnB,EAAuBL,SAAtF;EAEA,IAAIM,MAAM,GAAGC,MAAM,GAAGnB,IAAH,GAAUA,IAAI,CAACiB,MAAD,CAAJ,IAAgBf,2BAA2B,CAACF,IAAD,EAAOiB,MAAP,EAAe,EAAf,CAA3B,CAA8CA,MAA9C,CAA7C;EACA,IAAIQ,eAAe,GAAGP,MAAM,CAACN,SAA7B;EAEA,IAAIc,MAAJ,EAAYC,UAAZ,EAAwBC,iBAAxB;EACA,IAAIC,GAAJ,EAASC,cAAT,EAAyBC,cAAzB,EAAyCC,cAAzC,EAAyDC,cAAzD,EAAyEC,UAAzE;;EAEA,KAAKL,GAAL,IAAYb,MAAZ,EAAoB;IAClBU,MAAM,GAAG3B,QAAQ,CAACoB,MAAM,GAAGU,GAAH,GAASZ,MAAM,IAAIG,MAAM,GAAG,GAAH,GAAS,GAAnB,CAAN,GAAgCS,GAAhD,EAAqDd,OAAO,CAACoB,MAA7D,CAAjB,CADkB,CAElB;;IACAR,UAAU,GAAG,CAACD,MAAD,IAAWF,YAAX,IAA2BrB,MAAM,CAACqB,YAAD,EAAeK,GAAf,CAA9C;IAEAE,cAAc,GAAGb,MAAM,CAACW,GAAD,CAAvB;IAEA,IAAIF,UAAJ,EAAgB,IAAIZ,OAAO,CAACqB,cAAZ,EAA4B;MAC1CF,UAAU,GAAGrC,wBAAwB,CAAC2B,YAAD,EAAeK,GAAf,CAArC;MACAG,cAAc,GAAGE,UAAU,IAAIA,UAAU,CAACG,KAA1C;IACD,CAHe,MAGTL,cAAc,GAAGR,YAAY,CAACK,GAAD,CAA7B,CAVW,CAYlB;;IACAC,cAAc,GAAIH,UAAU,IAAIK,cAAf,GAAiCA,cAAjC,GAAkDhB,MAAM,CAACa,GAAD,CAAzE;IAEA,IAAIF,UAAU,IAAI,OAAOI,cAAP,IAAyB,OAAOD,cAAlD,EAAkE,SAfhD,CAiBlB;;IACA,IAAIf,OAAO,CAACd,IAAR,IAAgB0B,UAApB,EAAgCM,cAAc,GAAGhC,IAAI,CAAC6B,cAAD,EAAiBtC,MAAjB,CAArB,CAAhC,CACA;IADA,KAEK,IAAIuB,OAAO,CAACuB,IAAR,IAAgBX,UAApB,EAAgCM,cAAc,GAAG7B,eAAe,CAAC0B,cAAD,CAAhC,CAAhC,CACL;IADK,KAEA,IAAIR,KAAK,IAAI1B,UAAU,CAACkC,cAAD,CAAvB,EAAyCG,cAAc,GAAGtC,WAAW,CAACmC,cAAD,CAA5B,CAAzC,CACL;IADK,KAEAG,cAAc,GAAGH,cAAjB,CAxBa,CA0BlB;;IACA,IAAIf,OAAO,CAACwB,IAAR,IAAiBT,cAAc,IAAIA,cAAc,CAACS,IAAlD,IAA4DR,cAAc,IAAIA,cAAc,CAACQ,IAAjG,EAAwG;MACtGrC,2BAA2B,CAAC+B,cAAD,EAAiB,MAAjB,EAAyB,IAAzB,CAA3B;IACD;;IAED/B,2BAA2B,CAACgB,MAAD,EAASW,GAAT,EAAcI,cAAd,CAA3B;;IAEA,IAAIX,KAAJ,EAAW;MACTM,iBAAiB,GAAGX,MAAM,GAAG,WAA7B;;MACA,IAAI,CAACd,MAAM,CAACH,IAAD,EAAO4B,iBAAP,CAAX,EAAsC;QACpC1B,2BAA2B,CAACF,IAAD,EAAO4B,iBAAP,EAA0B,EAA1B,CAA3B;MACD,CAJQ,CAKT;;;MACA1B,2BAA2B,CAACF,IAAI,CAAC4B,iBAAD,CAAL,EAA0BC,GAA1B,EAA+BC,cAA/B,CAA3B,CANS,CAOT;;MACA,IAAIf,OAAO,CAACyB,IAAR,IAAgBf,eAAhB,IAAmC,CAACA,eAAe,CAACI,GAAD,CAAvD,EAA8D;QAC5D3B,2BAA2B,CAACuB,eAAD,EAAkBI,GAAlB,EAAuBC,cAAvB,CAA3B;MACD;IACF;EACF;AACF,CA5DD"}, "metadata": {}, "sourceType": "script"}