{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar React = require(\"react\"),\n    common_1 = require(\"./common\"),\n    common_2 = require(\"./common\");\n\nfunction populatePreviousSlides(state, props, slidesHavePassed) {\n  void 0 === slidesHavePassed && (slidesHavePassed = 0);\n  var nextSlides,\n      nextPosition,\n      currentSlide = state.currentSlide,\n      itemWidth = state.itemWidth,\n      slidesToShow = state.slidesToShow,\n      children = props.children,\n      showDots = props.showDots,\n      infinite = props.infinite,\n      slidesToSlide = common_1.getSlidesToSlide(state, props),\n      nextMaximumSlides = currentSlide - slidesHavePassed - (0 < slidesHavePassed ? 0 : slidesToSlide),\n      additionalSlides = (React.Children.toArray(children).length - slidesToShow) % slidesToSlide;\n  return nextPosition = 0 <= nextMaximumSlides ? (nextSlides = nextMaximumSlides, showDots && !infinite && 0 < additionalSlides && common_2.isInRightEnd(state) && (nextSlides = currentSlide - additionalSlides), -itemWidth * nextSlides) : nextSlides = nextMaximumSlides < 0 && 0 !== currentSlide ? 0 : void 0, {\n    nextSlides: nextSlides,\n    nextPosition: nextPosition\n  };\n}\n\nexports.populatePreviousSlides = populatePreviousSlides;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "React", "require", "common_1", "common_2", "populatePreviousSlides", "state", "props", "slidesHavePassed", "nextSlides", "nextPosition", "currentSlide", "itemWidth", "slidesToShow", "children", "showDots", "infinite", "slidesToSlide", "getSlidesToSlide", "nextMaximumSlides", "additionalSlides", "Children", "toArray", "length", "isInRightEnd"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/previous.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var React=require(\"react\"),common_1=require(\"./common\"),common_2=require(\"./common\");function populatePreviousSlides(state,props,slidesHavePassed){void 0===slidesHavePassed&&(slidesHavePassed=0);var nextSlides,nextPosition,currentSlide=state.currentSlide,itemWidth=state.itemWidth,slidesToShow=state.slidesToShow,children=props.children,showDots=props.showDots,infinite=props.infinite,slidesToSlide=common_1.getSlidesToSlide(state,props),nextMaximumSlides=currentSlide-slidesHavePassed-(0<slidesHavePassed?0:slidesToSlide),additionalSlides=(React.Children.toArray(children).length-slidesToShow)%slidesToSlide;return nextPosition=0<=nextMaximumSlides?(nextSlides=nextMaximumSlides,showDots&&!infinite&&0<additionalSlides&&common_2.isInRightEnd(state)&&(nextSlides=currentSlide-additionalSlides),-itemWidth*nextSlides):nextSlides=nextMaximumSlides<0&&0!==currentSlide?0:void 0,{nextSlides:nextSlides,nextPosition:nextPosition}}exports.populatePreviousSlides=populatePreviousSlides;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,KAAK,GAACC,OAAO,CAAC,OAAD,CAAjB;AAAA,IAA2BC,QAAQ,GAACD,OAAO,CAAC,UAAD,CAA3C;AAAA,IAAwDE,QAAQ,GAACF,OAAO,CAAC,UAAD,CAAxE;;AAAqF,SAASG,sBAAT,CAAgCC,KAAhC,EAAsCC,KAAtC,EAA4CC,gBAA5C,EAA6D;EAAC,KAAK,CAAL,KAASA,gBAAT,KAA4BA,gBAAgB,GAAC,CAA7C;EAAgD,IAAIC,UAAJ;EAAA,IAAeC,YAAf;EAAA,IAA4BC,YAAY,GAACL,KAAK,CAACK,YAA/C;EAAA,IAA4DC,SAAS,GAACN,KAAK,CAACM,SAA5E;EAAA,IAAsFC,YAAY,GAACP,KAAK,CAACO,YAAzG;EAAA,IAAsHC,QAAQ,GAACP,KAAK,CAACO,QAArI;EAAA,IAA8IC,QAAQ,GAACR,KAAK,CAACQ,QAA7J;EAAA,IAAsKC,QAAQ,GAACT,KAAK,CAACS,QAArL;EAAA,IAA8LC,aAAa,GAACd,QAAQ,CAACe,gBAAT,CAA0BZ,KAA1B,EAAgCC,KAAhC,CAA5M;EAAA,IAAmPY,iBAAiB,GAACR,YAAY,GAACH,gBAAb,IAA+B,IAAEA,gBAAF,GAAmB,CAAnB,GAAqBS,aAApD,CAArQ;EAAA,IAAwUG,gBAAgB,GAAC,CAACnB,KAAK,CAACoB,QAAN,CAAeC,OAAf,CAAuBR,QAAvB,EAAiCS,MAAjC,GAAwCV,YAAzC,IAAuDI,aAAhZ;EAA8Z,OAAOP,YAAY,GAAC,KAAGS,iBAAH,IAAsBV,UAAU,GAACU,iBAAX,EAA6BJ,QAAQ,IAAE,CAACC,QAAX,IAAqB,IAAEI,gBAAvB,IAAyChB,QAAQ,CAACoB,YAAT,CAAsBlB,KAAtB,CAAzC,KAAwEG,UAAU,GAACE,YAAY,GAACS,gBAAhG,CAA7B,EAA+I,CAACR,SAAD,GAAWH,UAAhL,IAA4LA,UAAU,GAACU,iBAAiB,GAAC,CAAlB,IAAqB,MAAIR,YAAzB,GAAsC,CAAtC,GAAwC,KAAK,CAAjQ,EAAmQ;IAACF,UAAU,EAACA,UAAZ;IAAuBC,YAAY,EAACA;EAApC,CAA1Q;AAA4T;;AAAAX,OAAO,CAACM,sBAAR,GAA+BA,sBAA/B"}, "metadata": {}, "sourceType": "script"}