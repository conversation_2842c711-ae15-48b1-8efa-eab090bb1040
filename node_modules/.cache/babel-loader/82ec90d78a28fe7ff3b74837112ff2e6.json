{"ast": null, "code": "import { store } from '../../store/store';\n/**\n * Initiation\n * @param {string} publicKey - set the EmailJS public key\n * @param {string} origin - set the EmailJS origin\n */\n\nexport const init = function (publicKey) {\n  let origin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'https://api.emailjs.com';\n  store._userID = publicKey;\n  store._origin = origin;\n};", "map": {"version": 3, "names": ["store", "init", "public<PERSON>ey", "origin", "_userID", "_origin"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@emailjs/browser/es/methods/init/init.js"], "sourcesContent": ["import { store } from '../../store/store';\n/**\n * Initiation\n * @param {string} publicKey - set the EmailJS public key\n * @param {string} origin - set the EmailJS origin\n */\nexport const init = (publicKey, origin = 'https://api.emailjs.com') => {\n    store._userID = publicKey;\n    store._origin = origin;\n};\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,mBAAtB;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,MAAMC,IAAI,GAAG,UAACC,SAAD,EAAmD;EAAA,IAAvCC,MAAuC,uEAA9B,yBAA8B;EACnEH,KAAK,CAACI,OAAN,GAAgBF,SAAhB;EACAF,KAAK,CAACK,OAAN,GAAgBF,MAAhB;AACH,CAHM"}, "metadata": {}, "sourceType": "module"}