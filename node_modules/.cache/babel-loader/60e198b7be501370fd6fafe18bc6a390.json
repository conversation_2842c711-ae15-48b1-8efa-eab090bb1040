{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/index.jsx\";\nimport React from \"react\";\nimport { AssetsList } from \"../../../elements/assetsList\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ServiceItem = _ref => {\n  let {\n    title\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: /*#__PURE__*/_jsxDEV(Styles.TextWrapper, {\n      title: title,\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n\n_c = ServiceItem;\nexport default ServiceItem;\n\nvar _c;\n\n$RefreshReg$(_c, \"ServiceItem\");", "map": {"version": 3, "names": ["React", "AssetsList", "Styles", "ServiceItem", "title"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { AssetsList } from \"../../../elements/assetsList\";\n\nimport * as Styles from \"./styles\";\n\nconst ServiceItem = ({ title }) => {\n  return (\n    <Styles.Container>\n      {/* <Styles.ImageHolder src={AssetsList.logo} /> */}\n      <Styles.TextWrapper title={title}>{title}</Styles.TextWrapper>\n    </Styles.Container>\n  );\n};\n\nexport default ServiceItem;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,UAAT,QAA2B,8BAA3B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,WAAW,GAAG,QAAe;EAAA,IAAd;IAAEC;EAAF,CAAc;EACjC,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,uBAEE,QAAC,MAAD,CAAQ,WAAR;MAAoB,KAAK,EAAEA,KAA3B;MAAA,UAAmCA;IAAnC;MAAA;MAAA;MAAA;IAAA;EAFF;IAAA;IAAA;IAAA;EAAA,QADF;AAMD,CAPD;;KAAMD,W;AASN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}