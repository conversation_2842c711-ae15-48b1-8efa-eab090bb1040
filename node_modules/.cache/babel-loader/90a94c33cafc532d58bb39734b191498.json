{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/index.jsx\";\nimport React from 'react';\nimport { AssetsList } from '../../elements/assetsList';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst Loading = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: /*#__PURE__*/_jsxDEV(Styles.ImageContainer, {\n      children: /*#__PURE__*/_jsxDEV(Styles.ImageHolder, {\n        src: AssetsList.logo.LogoSvg\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n};\n\n_c = Loading;\nexport default Loading;\n\nvar _c;\n\n$RefreshReg$(_c, \"Loading\");", "map": {"version": 3, "names": ["React", "AssetsList", "Styles", "Loading", "logo", "LogoSvg"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport { AssetsList } from '../../elements/assetsList';\n\nimport * as Styles from './styles'\n\nconst Loading = () => {\n    return (\n        <Styles.Container>\n            <Styles.ImageContainer>\n                <Styles.ImageHolder\n                    src={AssetsList.logo.LogoSvg}\n                />\n            </Styles.ImageContainer>\n        </Styles.Container>\n    )\n}\n\nexport default Loading"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,UAAT,QAA2B,2BAA3B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,OAAO,GAAG,MAAM;EAClB,oBACI,QAAC,MAAD,CAAQ,SAAR;IAAA,uBACI,QAAC,MAAD,CAAQ,cAAR;MAAA,uBACI,QAAC,MAAD,CAAQ,WAAR;QACI,GAAG,EAAEF,UAAU,CAACG,IAAX,CAAgBC;MADzB;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA;EADJ;IAAA;IAAA;IAAA;EAAA,QADJ;AASH,CAVD;;KAAMF,O;AAYN,eAAeA,OAAf"}, "metadata": {}, "sourceType": "module"}