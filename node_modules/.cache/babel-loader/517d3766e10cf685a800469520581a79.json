{"ast": null, "code": "\"use strict\";\n\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    return (extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) b.hasOwnProperty(p) && (d[p] = b[p]);\n    })(d, b);\n  };\n\n  return function (d, b) {\n    function __() {\n      this.constructor = d;\n    }\n\n    extendStatics(d, b), d.prototype = null === b ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar React = require(\"react\");\n\nfunction isMouseMoveEvent(e) {\n  return \"clientY\" in e;\n}\n\nexports.isMouseMoveEvent = isMouseMoveEvent;\n\nvar Carousel = function (_super) {\n  function Carousel() {\n    return null !== _super && _super.apply(this, arguments) || this;\n  }\n\n  return __extends(Carousel, _super), Carousel;\n}(React.Component);\n\nexports.default = Carousel;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "defineProperty", "exports", "value", "React", "require", "isMouseMoveEvent", "e", "Carousel", "_super", "apply", "arguments", "Component", "default"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/types.js"], "sourcesContent": ["\"use strict\";var __extends=this&&this.__extends||function(){var extendStatics=function(d,b){return(extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)b.hasOwnProperty(p)&&(d[p]=b[p])})(d,b)};return function(d,b){function __(){this.constructor=d}extendStatics(d,b),d.prototype=null===b?Object.create(b):(__.prototype=b.prototype,new __)}}();Object.defineProperty(exports,\"__esModule\",{value:!0});var React=require(\"react\");function isMouseMoveEvent(e){return\"clientY\"in e}exports.isMouseMoveEvent=isMouseMoveEvent;var Carousel=function(_super){function Carousel(){return null!==_super&&_super.apply(this,arguments)||this}return __extends(Carousel,_super),Carousel}(React.Component);exports.default=Carousel;"], "mappings": "AAAA;;AAAa,IAAIA,SAAS,GAAC,QAAM,KAAKA,SAAX,IAAsB,YAAU;EAAC,IAAIC,aAAa,GAAC,UAASC,CAAT,EAAWC,CAAX,EAAa;IAAC,OAAM,CAACF,aAAa,GAACG,MAAM,CAACC,cAAP,IAAuB;MAACC,SAAS,EAAC;IAAX,aAAyBC,KAAzB,IAAgC,UAASL,CAAT,EAAWC,CAAX,EAAa;MAACD,CAAC,CAACI,SAAF,GAAYH,CAAZ;IAAc,CAAnF,IAAqF,UAASD,CAAT,EAAWC,CAAX,EAAa;MAAC,KAAI,IAAIK,CAAR,IAAaL,CAAb,EAAeA,CAAC,CAACM,cAAF,CAAiBD,CAAjB,MAAsBN,CAAC,CAACM,CAAD,CAAD,GAAKL,CAAC,CAACK,CAAD,CAA5B;IAAiC,CAAlK,EAAoKN,CAApK,EAAsKC,CAAtK,CAAN;EAA+K,CAA/M;;EAAgN,OAAO,UAASD,CAAT,EAAWC,CAAX,EAAa;IAAC,SAASO,EAAT,GAAa;MAAC,KAAKC,WAAL,GAAiBT,CAAjB;IAAmB;;IAAAD,aAAa,CAACC,CAAD,EAAGC,CAAH,CAAb,EAAmBD,CAAC,CAACU,SAAF,GAAY,SAAOT,CAAP,GAASC,MAAM,CAACS,MAAP,CAAcV,CAAd,CAAT,IAA2BO,EAAE,CAACE,SAAH,GAAaT,CAAC,CAACS,SAAf,EAAyB,IAAIF,EAAJ,EAApD,CAA/B;EAA2F,CAAjJ;AAAkJ,CAA7W,EAApC;;AAAoZN,MAAM,CAACU,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,KAAK,GAACC,OAAO,CAAC,OAAD,CAAjB;;AAA2B,SAASC,gBAAT,CAA0BC,CAA1B,EAA4B;EAAC,OAAM,aAAYA,CAAlB;AAAoB;;AAAAL,OAAO,CAACI,gBAAR,GAAyBA,gBAAzB;;AAA0C,IAAIE,QAAQ,GAAC,UAASC,MAAT,EAAgB;EAAC,SAASD,QAAT,GAAmB;IAAC,OAAO,SAAOC,MAAP,IAAeA,MAAM,CAACC,KAAP,CAAa,IAAb,EAAkBC,SAAlB,CAAf,IAA6C,IAApD;EAAyD;;EAAA,OAAOxB,SAAS,CAACqB,QAAD,EAAUC,MAAV,CAAT,EAA2BD,QAAlC;AAA2C,CAAzI,CAA0IJ,KAAK,CAACQ,SAAhJ,CAAb;;AAAwKV,OAAO,CAACW,OAAR,GAAgBL,QAAhB"}, "metadata": {}, "sourceType": "script"}