{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/App.js\",\n    _s = $RefreshSig$();\n\nimport { useEffect } from \"react\";\nimport \"./App.css\";\nimport AppRoute from \"./route\";\nimport { ThemeProvider } from \"styled-components\";\nimport { GlobalStyle } from \"./styles/global.styles\";\nimport { useLocation } from \"react-router-dom\";\nimport { colors, font } from \"./styles/variables\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nfunction App() {\n  _s();\n\n  const {\n    pathname\n  } = useLocation();\n  useEffect(() => {\n    window.scrollTo(0, 0);\n  }, [pathname]);\n  const theme = {\n    colors,\n    font\n  };\n  console.log({\n    theme\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(GlobalStyle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AppRoute, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n\n_s(App, \"+8VPq4+XDMjo/kjL3WLkbwU2Amg=\", false, function () {\n  return [useLocation];\n});\n\n_c = App;\nexport default App;\n\nvar _c;\n\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useEffect", "AppRoute", "ThemeProvider", "GlobalStyle", "useLocation", "colors", "font", "App", "pathname", "window", "scrollTo", "theme", "console", "log"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/App.js"], "sourcesContent": ["import { useEffect } from \"react\";\n\nimport \"./App.css\";\n\nimport AppRoute from \"./route\";\nimport { ThemeProvider } from \"styled-components\";\n\nimport { GlobalStyle } from \"./styles/global.styles\";\n\nimport { useLocation } from \"react-router-dom\";\n\nimport { colors, font } from \"./styles/variables\";\n\nfunction App() {\n  const { pathname } = useLocation();\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n  }, [pathname]);\n\n  const theme = {\n    colors,\n    font,\n  };\n\n  console.log({ theme });\n\n  return (\n    <div className=\"App\">\n      <ThemeProvider theme={theme}>\n        <GlobalStyle />\n        <AppRoute />\n      </ThemeProvider>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,SAASA,SAAT,QAA0B,OAA1B;AAEA,OAAO,WAAP;AAEA,OAAOC,QAAP,MAAqB,SAArB;AACA,SAASC,aAAT,QAA8B,mBAA9B;AAEA,SAASC,WAAT,QAA4B,wBAA5B;AAEA,SAASC,WAAT,QAA4B,kBAA5B;AAEA,SAASC,MAAT,EAAiBC,IAAjB,QAA6B,oBAA7B;;;AAEA,SAASC,GAAT,GAAe;EAAA;;EACb,MAAM;IAAEC;EAAF,IAAeJ,WAAW,EAAhC;EAEAJ,SAAS,CAAC,MAAM;IACdS,MAAM,CAACC,QAAP,CAAgB,CAAhB,EAAmB,CAAnB;EACD,CAFQ,EAEN,CAACF,QAAD,CAFM,CAAT;EAIA,MAAMG,KAAK,GAAG;IACZN,MADY;IAEZC;EAFY,CAAd;EAKAM,OAAO,CAACC,GAAR,CAAY;IAAEF;EAAF,CAAZ;EAEA,oBACE;IAAK,SAAS,EAAC,KAAf;IAAA,uBACE,QAAC,aAAD;MAAe,KAAK,EAAEA,KAAtB;MAAA,wBACE,QAAC,WAAD;QAAA;QAAA;QAAA;MAAA,QADF,eAEE,QAAC,QAAD;QAAA;QAAA;QAAA;MAAA,QAFF;IAAA;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAQD;;GAtBQJ,G;UACcH,W;;;KADdG,G;AAwBT,eAAeA,GAAf"}, "metadata": {}, "sourceType": "module"}