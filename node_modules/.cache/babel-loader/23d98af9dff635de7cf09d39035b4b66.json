{"ast": null, "code": "var $TypeError = TypeError; // `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\n\nmodule.exports = function (it) {\n  if (it == undefined) throw $TypeError(\"Can't call method on \" + it);\n  return it;\n};", "map": {"version": 3, "names": ["$TypeError", "TypeError", "module", "exports", "it", "undefined"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/require-object-coercible.js"], "sourcesContent": ["var $TypeError = TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (it == undefined) throw $TypeError(\"Can't call method on \" + it);\n  return it;\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,SAAjB,C,CAEA;AACA;;AACAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,EAAV,EAAc;EAC7B,IAAIA,EAAE,IAAIC,SAAV,EAAqB,MAAML,UAAU,CAAC,0BAA0BI,EAA3B,CAAhB;EACrB,OAAOA,EAAP;AACD,CAHD"}, "metadata": {}, "sourceType": "script"}