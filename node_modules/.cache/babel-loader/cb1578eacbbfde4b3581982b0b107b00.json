{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div``;\nexport const Wrapper = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n`;\nexport const AboutCard = styled.div`\n  background-color: #ffffff;\n\n  display: flex;\n  flex-direction: column;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);\n\n  min-height: 250px;\n  max-height: ${_ref => {\n  let {\n    hovered\n  } = _ref;\n  return hovered ? \"1000px\" : \"465px\";\n}};\n\n  margin: 0 10px;\n\n  overflow: hidden;\n\n  padding: 20px;\n\n  font-size: ${_ref2 => {\n  let {\n    theme: {\n      font\n    }\n  } = _ref2;\n  return font.title;\n}};\n  font-weight: 700;\n\n  text-align: left;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ${_ref3 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref3;\n  return colors.main.blue;\n}};\n    color: #ffffff;\n\n    .paragraph-holder {\n      &::before {\n        top: 0;\n        left: 0;\n      }\n    }\n  }\n\n  position: relative;\n\n  transition: all 0.3s ease-in-out;\n`;\nexport const ParagraphWrapper = styled.div`\n  height: 100%;\n\n  overflow-y: auto;\n\n  &::before {\n    content: \"\";\n    background-color: ${_ref4 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref4;\n  return colors.main.yellow;\n}};\n    width: 50px;\n    height: 50px;\n    position: absolute;\n    top: -50px;\n    left: -50px;\n    transition: all 0.3s ease-in-out;\n    border-bottom-right-radius: 10px;\n  }\n\n  & > div {\n    height: 100%;\n    overflow-y: auto;\n  }\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "Wrapper", "AboutCard", "hovered", "theme", "font", "title", "colors", "main", "blue", "ParagraphWrapper", "yellow"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div``;\nexport const Wrapper = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 20px;\n\n  @media screen and (max-width: 900px) {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  @media screen and (max-width: 600px) {\n    grid: none;\n  }\n`;\n\nexport const AboutCard = styled.div`\n  background-color: #ffffff;\n\n  display: flex;\n  flex-direction: column;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);\n\n  min-height: 250px;\n  max-height: ${({ hovered }) => (hovered ? \"1000px\" : \"465px\")};\n\n  margin: 0 10px;\n\n  overflow: hidden;\n\n  padding: 20px;\n\n  font-size: ${({ theme: { font } }) => font.title};\n  font-weight: 700;\n\n  text-align: left;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: #ffffff;\n\n    .paragraph-holder {\n      &::before {\n        top: 0;\n        left: 0;\n      }\n    }\n  }\n\n  position: relative;\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const ParagraphWrapper = styled.div`\n  height: 100%;\n\n  overflow-y: auto;\n\n  &::before {\n    content: \"\";\n    background-color: ${({ theme: { colors } }) => colors.main.yellow};\n    width: 50px;\n    height: 50px;\n    position: absolute;\n    top: -50px;\n    left: -50px;\n    transition: all 0.3s ease-in-out;\n    border-bottom-right-radius: 10px;\n  }\n\n  & > div {\n    height: 100%;\n    overflow-y: auto;\n  }\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI,EAA7B;AACP,OAAO,MAAMC,OAAO,GAAGH,MAAM,CAACE,GAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAZO;AAcP,OAAO,MAAME,SAAS,GAAGJ,MAAM,CAACE,GAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;EAAA,IAAC;IAAEG;EAAF,CAAD;EAAA,OAAkBA,OAAO,GAAG,QAAH,GAAc,OAAvC;AAAA,CAAgD;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAAyBA,IAAI,CAACC,KAA9B;AAAA,CAAoC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEF,KAAK,EAAE;MAAEG;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,IAAvC;AAAA,CAA4C;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAxCO;AA0CP,OAAO,MAAMC,gBAAgB,GAAGZ,MAAM,CAACE,GAAI;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEI,KAAK,EAAE;MAAEG;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYG,MAAvC;AAAA,CAA8C;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CArBO"}, "metadata": {}, "sourceType": "module"}