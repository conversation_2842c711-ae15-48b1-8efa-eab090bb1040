{"ast": null, "code": "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty); // `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\n// eslint-disable-next-line es-x/no-object-hasown -- safe\n\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "toObject", "hasOwnProperty", "module", "exports", "Object", "hasOwn", "it", "key"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/has-own-property.js"], "sourcesContent": ["var uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\n// eslint-disable-next-line es-x/no-object-hasown -- safe\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAD,CAAzB;;AACA,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAD,CAAtB;;AAEA,IAAIE,cAAc,GAAGH,WAAW,CAAC,GAAGG,cAAJ,CAAhC,C,CAEA;AACA;AACA;;AACAC,MAAM,CAACC,OAAP,GAAiBC,MAAM,CAACC,MAAP,IAAiB,SAASA,MAAT,CAAgBC,EAAhB,EAAoBC,GAApB,EAAyB;EACzD,OAAON,cAAc,CAACD,QAAQ,CAACM,EAAD,CAAT,EAAeC,GAAf,CAArB;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}