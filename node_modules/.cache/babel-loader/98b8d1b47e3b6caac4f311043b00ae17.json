{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  background-image: ${_ref => {\n  let {\n    img\n  } = _ref;\n  return `url(${img})`;\n}};\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: cover;\n  height: ${_ref2 => {\n  let {\n    small\n  } = _ref2;\n  return small ? \"200px\" : \"300px\";\n}};\n  padding: ${_ref3 => {\n  let {\n    small\n  } = _ref3;\n  return small ? \"200px 0\" : \"300px 0\";\n}};\n\n  position: relative;\n`;\nexport const Overlay = styled.div`\n  width: 100%;\n  height: 100%;\n  background-color: #00000095;\n\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n`;\nexport const Wrapper = styled.div`\n  width: 90%;\n  margin: auto;\n\n  text-align: center;\n\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  position: relative;\n  z-index: 999;\n`;\nexport const Holder = styled.div``;\nexport const Title = styled.div`\n  font-size: 30px;\n  color: ${_ref4 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref4;\n  return colors.white;\n}};\n  font-weight: bold;\n`;\nexport const SubTitle = styled.div`\n  font-size: ${_ref5 => {\n  let {\n    theme: {\n      font\n    }\n  } = _ref5;\n  return font.main;\n}};\n  color: ${_ref6 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref6;\n  return colors.white;\n}};\n  font-weight: bold;\n`;\nexport const Button = styled.button`\n  background-color: ${_ref7 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref7;\n  return colors.main.yellow;\n}};\n  padding: 5px 10px;\n  border-radius: 5px;\n  border: none;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "img", "small", "Overlay", "Wrapper", "Holder", "Title", "theme", "colors", "white", "SubTitle", "font", "main", "<PERSON><PERSON>", "button", "yellow"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  background-image: ${({ img }) => `url(${img})`};\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: cover;\n  height: ${({ small }) => (small ? \"200px\" : \"300px\")};\n  padding: ${({ small }) => (small ? \"200px 0\" : \"300px 0\")};\n\n  position: relative;\n`;\n\nexport const Overlay = styled.div`\n  width: 100%;\n  height: 100%;\n  background-color: #00000095;\n\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n`;\n\n\nexport const Wrapper = styled.div`\n  width: 90%;\n  margin: auto;\n\n  text-align: center;\n\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  position: relative;\n  z-index: 999;\n`;\n\nexport const Holder = styled.div``;\n\nexport const Title = styled.div`\n  font-size: 30px;\n  color: ${({ theme: { colors } }) => colors.white};\n  font-weight: bold;\n`;\n\nexport const SubTitle = styled.div`\n  font-size: ${({ theme: { font } }) => font.main};\n  color: ${({ theme: { colors } }) => colors.white};\n  font-weight: bold;\n`;\n\nexport const Button = styled.button`\n  background-color: ${({ theme: { colors } }) => colors.main.yellow};\n  padding: 5px 10px;\n  border-radius: 5px;\n  border: none;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC,sBAAsB;EAAA,IAAC;IAAEC;EAAF,CAAD;EAAA,OAAc,OAAMA,GAAI,GAAxB;AAAA,CAA2B;AACjD;AACA;AACA;AACA,YAAY;EAAA,IAAC;IAAEC;EAAF,CAAD;EAAA,OAAgBA,KAAK,GAAG,OAAH,GAAa,OAAlC;AAAA,CAA2C;AACvD,aAAa;EAAA,IAAC;IAAEA;EAAF,CAAD;EAAA,OAAgBA,KAAK,GAAG,SAAH,GAAe,SAApC;AAAA,CAA+C;AAC5D;AACA;AACA,CATO;AAWP,OAAO,MAAMC,OAAO,GAAGL,MAAM,CAACE,GAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAVO;AAaP,OAAO,MAAMI,OAAO,GAAGN,MAAM,CAACE,GAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAbO;AAeP,OAAO,MAAMK,MAAM,GAAGP,MAAM,CAACE,GAAI,EAA1B;AAEP,OAAO,MAAMM,KAAK,GAAGR,MAAM,CAACE,GAAI;AAChC;AACA,WAAW;EAAA,IAAC;IAAEO,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,KAAlC;AAAA,CAAwC;AACnD;AACA,CAJO;AAMP,OAAO,MAAMC,QAAQ,GAAGZ,MAAM,CAACE,GAAI;AACnC,eAAe;EAAA,IAAC;IAAEO,KAAK,EAAE;MAAEI;IAAF;EAAT,CAAD;EAAA,OAAyBA,IAAI,CAACC,IAA9B;AAAA,CAAmC;AAClD,WAAW;EAAA,IAAC;IAAEL,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,KAAlC;AAAA,CAAwC;AACnD;AACA,CAJO;AAMP,OAAO,MAAMI,MAAM,GAAGf,MAAM,CAACgB,MAAO;AACpC,sBAAsB;EAAA,IAAC;IAAEP,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACI,IAAP,CAAYG,MAAvC;AAAA,CAA8C;AACpE;AACA;AACA;AACA,CALO"}, "metadata": {}, "sourceType": "module"}