{"ast": null, "code": "import React from\"react\";import{ServicesData}from\"../servicesData\";import ServicesCard from\"../card\";import ServiceItem from\"../../projects/project-details/service-item\";import*as Styles from\"./styles\";import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";const ServicesList=()=>{return/*#__PURE__*/_jsx(Styles.Container,{children:ServicesData.map((item,key)=>{return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(ServiceItem,{title:item===null||item===void 0?void 0:item.name})});})});};export default ServicesList;", "map": {"version": 3, "names": ["React", "ServicesData", "ServicesCard", "ServiceItem", "Styles", "jsx", "_jsx", "Fragment", "_Fragment", "ServicesList", "Container", "children", "map", "item", "key", "title", "name"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { ServicesData } from \"../servicesData\";\n\nimport ServicesCard from \"../card\";\n\nimport ServiceItem from \"../../projects/project-details/service-item\";\n\nimport * as Styles from \"./styles\";\n\nconst ServicesList = () => {\n  return (\n    <Styles.Container>\n      {ServicesData.map((item, key) => {\n        return (\n          <>\n            <ServiceItem title={item?.name} />\n            {/* <ServicesCard\n                            key={key}\n                            item={item}\n                        /> */}\n          </>\n        );\n      })}\n    </Styles.Container>\n  );\n};\n\nexport default ServicesList;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,OAASC,YAAY,KAAQ,iBAAiB,CAE9C,MAAO,CAAAC,YAAY,KAAM,SAAS,CAElC,MAAO,CAAAC,WAAW,KAAM,6CAA6C,CAErE,MAAO,GAAK,CAAAC,MAAM,KAAM,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,QAAA,IAAAC,SAAA,yBAEnC,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,mBACEH,IAAA,CAACF,MAAM,CAACM,SAAS,EAAAC,QAAA,CACdV,YAAY,CAACW,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,GAAK,CAC/B,mBACER,IAAA,CAAAE,SAAA,EAAAG,QAAA,cACEL,IAAA,CAACH,WAAW,EAACY,KAAK,CAAEF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEG,IAAK,CAAE,CAAC,CAKlC,CAAC,CAEP,CAAC,CAAC,CACc,CAAC,CAEvB,CAAC,CAED,cAAe,CAAAP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}