{"ast": null, "code": "//\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n\n  if (ret !== void 0) {\n    return !!ret;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB); // Test for A's keys different from B.\n\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = objA[key];\n    var valueB = objB[key];\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n\n    if (ret === false || ret === void 0 && valueA !== valueB) {\n      return false;\n    }\n  }\n\n  return true;\n};", "map": {"version": 3, "names": ["module", "exports", "shallowEqual", "objA", "objB", "compare", "compareContext", "ret", "call", "keysA", "Object", "keys", "keysB", "length", "bHasOwnProperty", "prototype", "hasOwnProperty", "bind", "idx", "key", "valueA", "valueB"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/shallowequal/index.js"], "sourcesContent": ["//\n\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n\n  if (ret !== void 0) {\n    return !!ret;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = objA[key];\n    var valueB = objB[key];\n\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n\n    if (ret === false || (ret === void 0 && valueA !== valueB)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n"], "mappings": "AAAA;AAEAA,MAAM,CAACC,OAAP,GAAiB,SAASC,YAAT,CAAsBC,IAAtB,EAA4BC,IAA5B,EAAkCC,OAAlC,EAA2CC,cAA3C,EAA2D;EAC1E,IAAIC,GAAG,GAAGF,OAAO,GAAGA,OAAO,CAACG,IAAR,CAAaF,cAAb,EAA6BH,IAA7B,EAAmCC,IAAnC,CAAH,GAA8C,KAAK,CAApE;;EAEA,IAAIG,GAAG,KAAK,KAAK,CAAjB,EAAoB;IAClB,OAAO,CAAC,CAACA,GAAT;EACD;;EAED,IAAIJ,IAAI,KAAKC,IAAb,EAAmB;IACjB,OAAO,IAAP;EACD;;EAED,IAAI,OAAOD,IAAP,KAAgB,QAAhB,IAA4B,CAACA,IAA7B,IAAqC,OAAOC,IAAP,KAAgB,QAArD,IAAiE,CAACA,IAAtE,EAA4E;IAC1E,OAAO,KAAP;EACD;;EAED,IAAIK,KAAK,GAAGC,MAAM,CAACC,IAAP,CAAYR,IAAZ,CAAZ;EACA,IAAIS,KAAK,GAAGF,MAAM,CAACC,IAAP,CAAYP,IAAZ,CAAZ;;EAEA,IAAIK,KAAK,CAACI,MAAN,KAAiBD,KAAK,CAACC,MAA3B,EAAmC;IACjC,OAAO,KAAP;EACD;;EAED,IAAIC,eAAe,GAAGJ,MAAM,CAACK,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCb,IAArC,CAAtB,CAtB0E,CAwB1E;;EACA,KAAK,IAAIc,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGT,KAAK,CAACI,MAA9B,EAAsCK,GAAG,EAAzC,EAA6C;IAC3C,IAAIC,GAAG,GAAGV,KAAK,CAACS,GAAD,CAAf;;IAEA,IAAI,CAACJ,eAAe,CAACK,GAAD,CAApB,EAA2B;MACzB,OAAO,KAAP;IACD;;IAED,IAAIC,MAAM,GAAGjB,IAAI,CAACgB,GAAD,CAAjB;IACA,IAAIE,MAAM,GAAGjB,IAAI,CAACe,GAAD,CAAjB;IAEAZ,GAAG,GAAGF,OAAO,GAAGA,OAAO,CAACG,IAAR,CAAaF,cAAb,EAA6Bc,MAA7B,EAAqCC,MAArC,EAA6CF,GAA7C,CAAH,GAAuD,KAAK,CAAzE;;IAEA,IAAIZ,GAAG,KAAK,KAAR,IAAkBA,GAAG,KAAK,KAAK,CAAb,IAAkBa,MAAM,KAAKC,MAAnD,EAA4D;MAC1D,OAAO,KAAP;IACD;EACF;;EAED,OAAO,IAAP;AACD,CA3CD"}, "metadata": {}, "sourceType": "script"}