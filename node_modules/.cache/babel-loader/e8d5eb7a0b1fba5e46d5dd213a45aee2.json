{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\nvar React = require(\"react\"),\n  common_1 = require(\"./common\"),\n  common_2 = require(\"./common\");\nfunction populatePreviousSlides(state, props, slidesHavePassed) {\n  void 0 === slidesHavePassed && (slidesHavePassed = 0);\n  var nextSlides,\n    nextPosition,\n    currentSlide = state.currentSlide,\n    itemWidth = state.itemWidth,\n    slidesToShow = state.slidesToShow,\n    children = props.children,\n    showDots = props.showDots,\n    infinite = props.infinite,\n    slidesToSlide = common_1.getSlidesToSlide(state, props),\n    nextMaximumSlides = currentSlide - slidesHavePassed - (0 < slidesHavePassed ? 0 : slidesToSlide),\n    additionalSlides = (React.Children.toArray(children).length - slidesToShow) % slidesToSlide;\n  return nextPosition = 0 <= nextMaximumSlides ? (nextSlides = nextMaximumSlides, showDots && !infinite && 0 < additionalSlides && common_2.isInRightEnd(state) && (nextSlides = currentSlide - additionalSlides), -itemWidth * nextSlides) : nextSlides = nextMaximumSlides < 0 && 0 !== currentSlide ? 0 : void 0, {\n    nextSlides: nextSlides,\n    nextPosition: nextPosition\n  };\n}\nexports.populatePreviousSlides = populatePreviousSlides;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "React", "require", "common_1", "common_2", "populatePreviousSlides", "state", "props", "slidesHavePassed", "nextSlides", "nextPosition", "currentSlide", "itemWidth", "slidesToShow", "children", "showDots", "infinite", "slidesToSlide", "getSlidesToSlide", "nextMaximumSlides", "additionalSlides", "Children", "toArray", "length", "isInRightEnd"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/previous.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var React=require(\"react\"),common_1=require(\"./common\"),common_2=require(\"./common\");function populatePreviousSlides(state,props,slidesHavePassed){void 0===slidesHavePassed&&(slidesHavePassed=0);var nextSlides,nextPosition,currentSlide=state.currentSlide,itemWidth=state.itemWidth,slidesToShow=state.slidesToShow,children=props.children,showDots=props.showDots,infinite=props.infinite,slidesToSlide=common_1.getSlidesToSlide(state,props),nextMaximumSlides=currentSlide-slidesHavePassed-(0<slidesHavePassed?0:slidesToSlide),additionalSlides=(React.Children.toArray(children).length-slidesToShow)%slidesToSlide;return nextPosition=0<=nextMaximumSlides?(nextSlides=nextMaximumSlides,showDots&&!infinite&&0<additionalSlides&&common_2.isInRightEnd(state)&&(nextSlides=currentSlide-additionalSlides),-itemWidth*nextSlides):nextSlides=nextMaximumSlides<0&&0!==currentSlide?0:void 0,{nextSlides:nextSlides,nextPosition:nextPosition}}exports.populatePreviousSlides=populatePreviousSlides;"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC,CAAC;AAAC,CAAC,CAAC;AAAC,IAAIC,KAAK,GAACC,OAAO,CAAC,OAAO,CAAC;EAACC,QAAQ,GAACD,OAAO,CAAC,UAAU,CAAC;EAACE,QAAQ,GAACF,OAAO,CAAC,UAAU,CAAC;AAAC,SAASG,sBAAsBA,CAACC,KAAK,EAACC,KAAK,EAACC,gBAAgB,EAAC;EAAC,KAAK,CAAC,KAAGA,gBAAgB,KAAGA,gBAAgB,GAAC,CAAC,CAAC;EAAC,IAAIC,UAAU;IAACC,YAAY;IAACC,YAAY,GAACL,KAAK,CAACK,YAAY;IAACC,SAAS,GAACN,KAAK,CAACM,SAAS;IAACC,YAAY,GAACP,KAAK,CAACO,YAAY;IAACC,QAAQ,GAACP,KAAK,CAAC<PERSON>,QAAQ;IAACC,QAAQ,GAACR,KAAK,CAACQ,QAAQ;IAACC,QAAQ,GAACT,KAAK,CAACS,QAAQ;IAACC,aAAa,GAACd,QAAQ,CAACe,gBAAgB,CAACZ,KAAK,EAACC,KAAK,CAAC;IAACY,iBAAiB,GAACR,YAAY,GAACH,gBAAgB,IAAE,CAAC,GAACA,gBAAgB,GAAC,CAAC,GAACS,aAAa,CAAC;IAACG,gBAAgB,GAAC,CAACnB,KAAK,CAACoB,QAAQ,CAACC,OAAO,CAACR,QAAQ,CAAC,CAACS,MAAM,GAACV,YAAY,IAAEI,aAAa;EAAC,OAAOP,YAAY,GAAC,CAAC,IAAES,iBAAiB,IAAEV,UAAU,GAACU,iBAAiB,EAACJ,QAAQ,IAAE,CAACC,QAAQ,IAAE,CAAC,GAACI,gBAAgB,IAAEhB,QAAQ,CAACoB,YAAY,CAAClB,KAAK,CAAC,KAAGG,UAAU,GAACE,YAAY,GAACS,gBAAgB,CAAC,EAAC,CAACR,SAAS,GAACH,UAAU,IAAEA,UAAU,GAACU,iBAAiB,GAAC,CAAC,IAAE,CAAC,KAAGR,YAAY,GAAC,CAAC,GAAC,KAAK,CAAC,EAAC;IAACF,UAAU,EAACA,UAAU;IAACC,YAAY,EAACA;EAAY,CAAC;AAAA;AAACX,OAAO,CAACM,sBAAsB,GAACA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}