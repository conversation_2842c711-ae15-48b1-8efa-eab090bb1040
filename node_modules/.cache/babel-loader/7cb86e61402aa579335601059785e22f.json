{"ast": null, "code": "var DESCRIPTORS = require('../internals/descriptors');\n\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\n\nvar anObject = require('../internals/an-object');\n\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar $TypeError = TypeError; // eslint-disable-next-line es-x/no-object-defineproperty -- safe\n\nvar $defineProperty = Object.defineProperty; // eslint-disable-next-line es-x/no-object-getownpropertydescriptor -- safe\n\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar ENUMERABLE = 'enumerable';\nvar CONFIGURABLE = 'configurable';\nvar WRITABLE = 'writable'; // `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\n\nexports.f = DESCRIPTORS ? V8_PROTOTYPE_DEFINE_BUG ? function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n\n  if (typeof O === 'function' && P === 'prototype' && 'value' in Attributes && WRITABLE in Attributes && !Attributes[WRITABLE]) {\n    var current = $getOwnPropertyDescriptor(O, P);\n\n    if (current && current[WRITABLE]) {\n      O[P] = Attributes.value;\n      Attributes = {\n        configurable: CONFIGURABLE in Attributes ? Attributes[CONFIGURABLE] : current[CONFIGURABLE],\n        enumerable: ENUMERABLE in Attributes ? Attributes[ENUMERABLE] : current[ENUMERABLE],\n        writable: false\n      };\n    }\n  }\n\n  return $defineProperty(O, P, Attributes);\n} : $defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return $defineProperty(O, P, Attributes);\n  } catch (error) {\n    /* empty */\n  }\n  if ('get' in Attributes || 'set' in Attributes) throw $TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "IE8_DOM_DEFINE", "V8_PROTOTYPE_DEFINE_BUG", "anObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$TypeError", "TypeError", "$defineProperty", "Object", "defineProperty", "$getOwnPropertyDescriptor", "getOwnPropertyDescriptor", "ENUMERABLE", "CONFIGURABLE", "WRITABLE", "exports", "f", "O", "P", "Attributes", "current", "value", "configurable", "enumerable", "writable", "error"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/object-define-property.js"], "sourcesContent": ["var DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\nvar anObject = require('../internals/an-object');\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar $TypeError = TypeError;\n// eslint-disable-next-line es-x/no-object-defineproperty -- safe\nvar $defineProperty = Object.defineProperty;\n// eslint-disable-next-line es-x/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar ENUMERABLE = 'enumerable';\nvar CONFIGURABLE = 'configurable';\nvar WRITABLE = 'writable';\n\n// `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? V8_PROTOTYPE_DEFINE_BUG ? function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (typeof O === 'function' && P === 'prototype' && 'value' in Attributes && WRITABLE in Attributes && !Attributes[WRITABLE]) {\n    var current = $getOwnPropertyDescriptor(O, P);\n    if (current && current[WRITABLE]) {\n      O[P] = Attributes.value;\n      Attributes = {\n        configurable: CONFIGURABLE in Attributes ? Attributes[CONFIGURABLE] : current[CONFIGURABLE],\n        enumerable: ENUMERABLE in Attributes ? Attributes[ENUMERABLE] : current[ENUMERABLE],\n        writable: false\n      };\n    }\n  } return $defineProperty(O, P, Attributes);\n} : $defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return $defineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw $TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAAD,CAAzB;;AACA,IAAIC,cAAc,GAAGD,OAAO,CAAC,6BAAD,CAA5B;;AACA,IAAIE,uBAAuB,GAAGF,OAAO,CAAC,sCAAD,CAArC;;AACA,IAAIG,QAAQ,GAAGH,OAAO,CAAC,wBAAD,CAAtB;;AACA,IAAII,aAAa,GAAGJ,OAAO,CAAC,8BAAD,CAA3B;;AAEA,IAAIK,UAAU,GAAGC,SAAjB,C,CACA;;AACA,IAAIC,eAAe,GAAGC,MAAM,CAACC,cAA7B,C,CACA;;AACA,IAAIC,yBAAyB,GAAGF,MAAM,CAACG,wBAAvC;AACA,IAAIC,UAAU,GAAG,YAAjB;AACA,IAAIC,YAAY,GAAG,cAAnB;AACA,IAAIC,QAAQ,GAAG,UAAf,C,CAEA;AACA;;AACAC,OAAO,CAACC,CAAR,GAAYjB,WAAW,GAAGG,uBAAuB,GAAG,SAASO,cAAT,CAAwBQ,CAAxB,EAA2BC,CAA3B,EAA8BC,UAA9B,EAA0C;EAC5FhB,QAAQ,CAACc,CAAD,CAAR;EACAC,CAAC,GAAGd,aAAa,CAACc,CAAD,CAAjB;EACAf,QAAQ,CAACgB,UAAD,CAAR;;EACA,IAAI,OAAOF,CAAP,KAAa,UAAb,IAA2BC,CAAC,KAAK,WAAjC,IAAgD,WAAWC,UAA3D,IAAyEL,QAAQ,IAAIK,UAArF,IAAmG,CAACA,UAAU,CAACL,QAAD,CAAlH,EAA8H;IAC5H,IAAIM,OAAO,GAAGV,yBAAyB,CAACO,CAAD,EAAIC,CAAJ,CAAvC;;IACA,IAAIE,OAAO,IAAIA,OAAO,CAACN,QAAD,CAAtB,EAAkC;MAChCG,CAAC,CAACC,CAAD,CAAD,GAAOC,UAAU,CAACE,KAAlB;MACAF,UAAU,GAAG;QACXG,YAAY,EAAET,YAAY,IAAIM,UAAhB,GAA6BA,UAAU,CAACN,YAAD,CAAvC,GAAwDO,OAAO,CAACP,YAAD,CADlE;QAEXU,UAAU,EAAEX,UAAU,IAAIO,UAAd,GAA2BA,UAAU,CAACP,UAAD,CAArC,GAAoDQ,OAAO,CAACR,UAAD,CAF5D;QAGXY,QAAQ,EAAE;MAHC,CAAb;IAKD;EACF;;EAAC,OAAOjB,eAAe,CAACU,CAAD,EAAIC,CAAJ,EAAOC,UAAP,CAAtB;AACH,CAfgD,GAe7CZ,eAfmB,GAeD,SAASE,cAAT,CAAwBQ,CAAxB,EAA2BC,CAA3B,EAA8BC,UAA9B,EAA0C;EAC9DhB,QAAQ,CAACc,CAAD,CAAR;EACAC,CAAC,GAAGd,aAAa,CAACc,CAAD,CAAjB;EACAf,QAAQ,CAACgB,UAAD,CAAR;EACA,IAAIlB,cAAJ,EAAoB,IAAI;IACtB,OAAOM,eAAe,CAACU,CAAD,EAAIC,CAAJ,EAAOC,UAAP,CAAtB;EACD,CAFmB,CAElB,OAAOM,KAAP,EAAc;IAAE;EAAa;EAC/B,IAAI,SAASN,UAAT,IAAuB,SAASA,UAApC,EAAgD,MAAMd,UAAU,CAAC,yBAAD,CAAhB;EAChD,IAAI,WAAWc,UAAf,EAA2BF,CAAC,CAACC,CAAD,CAAD,GAAOC,UAAU,CAACE,KAAlB;EAC3B,OAAOJ,CAAP;AACD,CAzBD"}, "metadata": {}, "sourceType": "script"}