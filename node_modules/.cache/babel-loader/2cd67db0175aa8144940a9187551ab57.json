{"ast": null, "code": "export class EmailJSResponseStatus {\n  constructor(httpResponse) {\n    this.status = httpResponse?.status || 0;\n    this.text = httpResponse?.responseText || 'Network Error';\n  }\n\n}", "map": {"version": 3, "names": ["EmailJSResponseStatus", "constructor", "httpResponse", "status", "text", "responseText"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@emailjs/browser/es/models/EmailJSResponseStatus.js"], "sourcesContent": ["export class EmailJSResponseStatus {\n    constructor(httpResponse) {\n        this.status = httpResponse?.status || 0;\n        this.text = httpResponse?.responseText || 'Network Error';\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAN,CAA4B;EAC/BC,WAAW,CAACC,YAAD,EAAe;IACtB,KAAKC,MAAL,GAAcD,YAAY,EAAEC,MAAd,IAAwB,CAAtC;IACA,KAAKC,IAAL,GAAYF,YAAY,EAAEG,YAAd,IAA8B,eAA1C;EACH;;AAJ8B"}, "metadata": {}, "sourceType": "module"}