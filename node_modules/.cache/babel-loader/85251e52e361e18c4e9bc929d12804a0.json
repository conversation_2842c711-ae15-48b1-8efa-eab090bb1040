{"ast": null, "code": "var ansiRegex = new RegExp([\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\", \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-nq-uy=><~]))\"].join(\"|\"), \"g\");\n/**\n *\n * Strip [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) from a string.\n * Adapted from code originally released by Sindre Sorhus\n * Licensed the MIT License\n *\n * @param {string} string\n * @return {string}\n */\n\nfunction stripAnsi(string) {\n  if (typeof string !== \"string\") {\n    throw new TypeError(\"Expected a `string`, got `\".concat(typeof string, \"`\"));\n  }\n\n  return string.replace(ansiRegex, \"\");\n}\n\nexport default stripAnsi;", "map": {"version": 3, "names": ["ansiRegex", "RegExp", "join", "stripAnsi", "string", "TypeError", "concat", "replace"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/webpack-dev-server/client/utils/stripAnsi.js"], "sourcesContent": ["var ansiRegex = new RegExp([\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\", \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-nq-uy=><~]))\"].join(\"|\"), \"g\");\n/**\n *\n * Strip [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) from a string.\n * Adapted from code originally released by Sindre Sorhus\n * Licensed the MIT License\n *\n * @param {string} string\n * @return {string}\n */\n\nfunction stripAnsi(string) {\n  if (typeof string !== \"string\") {\n    throw new TypeError(\"Expected a `string`, got `\".concat(typeof string, \"`\"));\n  }\n\n  return string.replace(ansiRegex, \"\");\n}\n\nexport default stripAnsi;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,IAAIC,MAAJ,CAAW,CAAC,8HAAD,EAAiI,0DAAjI,EAA6LC,IAA7L,CAAkM,GAAlM,CAAX,EAAmN,GAAnN,CAAhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,SAAT,CAAmBC,MAAnB,EAA2B;EACzB,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;IAC9B,MAAM,IAAIC,SAAJ,CAAc,6BAA6BC,MAA7B,CAAoC,OAAOF,MAA3C,EAAmD,GAAnD,CAAd,CAAN;EACD;;EAED,OAAOA,MAAM,CAACG,OAAP,CAAeP,SAAf,EAA0B,EAA1B,CAAP;AACD;;AAED,eAAeG,SAAf"}, "metadata": {}, "sourceType": "module"}