{"ast": null, "code": "module.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "bitmap", "value", "enumerable", "configurable", "writable"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/create-property-descriptor.js"], "sourcesContent": ["module.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n"], "mappings": "AAAAA,MAAM,CAACC,OAAP,GAAiB,UAAUC,MAAV,EAAkBC,KAAlB,EAAyB;EACxC,OAAO;IACLC,UAAU,EAAE,EAAEF,MAAM,GAAG,CAAX,CADP;IAELG,YAAY,EAAE,EAAEH,MAAM,GAAG,CAAX,CAFT;IAGLI,QAAQ,EAAE,EAAEJ,MAAM,GAAG,CAAX,CAHL;IAILC,KAAK,EAAEA;EAJF,CAAP;AAMD,CAPD"}, "metadata": {}, "sourceType": "script"}