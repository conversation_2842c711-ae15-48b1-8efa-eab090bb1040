{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/clients/index.js\";\nimport React from \"react\";\nimport Clients from \"../../components/clients\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ClientsPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: /*#__PURE__*/_jsxDEV(Clients, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n\n_c = ClientsPage;\nexport default ClientsPage;\n\nvar _c;\n\n$RefreshReg$(_c, \"ClientsPage\");", "map": {"version": 3, "names": ["React", "Clients", "ClientsPage"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/clients/index.js"], "sourcesContent": ["import React from \"react\";\n\nimport Clients from \"../../components/clients\";\n\nconst ClientsPage = () => {\n  return (\n    <div className=\"container\">\n      <Clients />\n    </div>\n  );\n};\n\nexport default ClientsPage;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,OAAP,MAAoB,0BAApB;;;AAEA,MAAMC,WAAW,GAAG,MAAM;EACxB,oBACE;IAAK,SAAS,EAAC,WAAf;IAAA,uBACE,QAAC,OAAD;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAKD,CAND;;KAAMA,W;AAQN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}