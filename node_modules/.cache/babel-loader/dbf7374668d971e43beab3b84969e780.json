{"ast": null, "code": "var global = require('../internals/global');\n\nvar userAgent = require('../internals/engine-user-agent');\n\nvar process = global.process;\nvar Deno = global.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.'); // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n} // BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\n\n\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;", "map": {"version": 3, "names": ["global", "require", "userAgent", "process", "<PERSON><PERSON>", "versions", "version", "v8", "match", "split", "module", "exports"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/engine-v8-version.js"], "sourcesContent": ["var global = require('../internals/global');\nvar userAgent = require('../internals/engine-user-agent');\n\nvar process = global.process;\nvar Deno = global.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAD,CAApB;;AACA,IAAIC,SAAS,GAAGD,OAAO,CAAC,gCAAD,CAAvB;;AAEA,IAAIE,OAAO,GAAGH,MAAM,CAACG,OAArB;AACA,IAAIC,IAAI,GAAGJ,MAAM,CAACI,IAAlB;AACA,IAAIC,QAAQ,GAAGF,OAAO,IAAIA,OAAO,CAACE,QAAnB,IAA+BD,IAAI,IAAIA,IAAI,CAACE,OAA3D;AACA,IAAIC,EAAE,GAAGF,QAAQ,IAAIA,QAAQ,CAACE,EAA9B;AACA,IAAIC,KAAJ,EAAWF,OAAX;;AAEA,IAAIC,EAAJ,EAAQ;EACNC,KAAK,GAAGD,EAAE,CAACE,KAAH,CAAS,GAAT,CAAR,CADM,CAEN;EACA;;EACAH,OAAO,GAAGE,KAAK,CAAC,CAAD,CAAL,GAAW,CAAX,IAAgBA,KAAK,CAAC,CAAD,CAAL,GAAW,CAA3B,GAA+B,CAA/B,GAAmC,EAAEA,KAAK,CAAC,CAAD,CAAL,GAAWA,KAAK,CAAC,CAAD,CAAlB,CAA7C;AACD,C,CAED;AACA;;;AACA,IAAI,CAACF,OAAD,IAAYJ,SAAhB,EAA2B;EACzBM,KAAK,GAAGN,SAAS,CAACM,KAAV,CAAgB,aAAhB,CAAR;;EACA,IAAI,CAACA,KAAD,IAAUA,KAAK,CAAC,CAAD,CAAL,IAAY,EAA1B,EAA8B;IAC5BA,KAAK,GAAGN,SAAS,CAACM,KAAV,CAAgB,eAAhB,CAAR;IACA,IAAIA,KAAJ,EAAWF,OAAO,GAAG,CAACE,KAAK,CAAC,CAAD,CAAhB;EACZ;AACF;;AAEDE,MAAM,CAACC,OAAP,GAAiBL,OAAjB"}, "metadata": {}, "sourceType": "script"}