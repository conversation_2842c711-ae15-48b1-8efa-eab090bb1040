{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/index.jsx\",\n    _s = $RefreshSig$();\n\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport AppLogo from \"../logo\";\nimport Button from \"../../elements/button\";\nimport MobileMenu from \"./mobile\";\nimport PageDimensions from \"../../../styles/pageDimensions\";\nimport { Menu } from \"react-feather\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst AppHeader = () => {\n  _s();\n\n  const [mobileMenu, setMobileMenu] = useState(false);\n  const {\n    pathname\n  } = useLocation();\n  const {\n    width\n  } = PageDimensions();\n  const isMobile = width < 659;\n\n  const handleMobileMenu = () => {\n    if (isMobile) {\n      setMobileMenu(!mobileMenu);\n    }\n  };\n\n  useEffect(() => {\n    setMobileMenu(false);\n  }, [pathname]);\n  return /*#__PURE__*/_jsxDEV(Styles.Header, {\n    children: /*#__PURE__*/_jsxDEV(Styles.Container, {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(Styles.Wtapper, {\n        children: [/*#__PURE__*/_jsxDEV(AppLogo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), isMobile ? /*#__PURE__*/_jsxDEV(Styles.MobileMenuContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Menu, {\n            onClick: handleMobileMenu\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(MobileMenu, {\n            handleMobileMenu: handleMobileMenu,\n            active: mobileMenu\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Styles.MenuContainer, {\n          children: /*#__PURE__*/_jsxDEV(Styles.MenuListWrapper, {\n            children: [/*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/about\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                children: \"About\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/projects\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/projects\",\n                children: \"Projects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/clients\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/clients\",\n                children: \"Clients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/contactus\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contactus\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  children: \"Contact us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n\n_s(AppHeader, \"yKl7/96QbnNNfz7t44nkkoX/zhc=\", false, function () {\n  return [useLocation];\n});\n\n_c = AppHeader;\nexport default AppHeader;\n\nvar _c;\n\n$RefreshReg$(_c, \"AppHeader\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Link", "useLocation", "AppLogo", "<PERSON><PERSON>", "MobileMenu", "PageDimensions", "<PERSON><PERSON>", "Styles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobileMenu", "setMobileMenu", "pathname", "width", "isMobile", "handleMobileMenu"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/index.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport AppLogo from \"../logo\";\n\nimport Button from \"../../elements/button\";\n\nimport MobileMenu from \"./mobile\";\nimport PageDimensions from \"../../../styles/pageDimensions\";\nimport { Menu } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst AppHeader = () => {\n  const [mobileMenu, setMobileMenu] = useState(false);\n\n  const { pathname } = useLocation();\n\n  const { width } = PageDimensions();\n\n  const isMobile = width < 659;\n\n  const handleMobileMenu = () => {\n    if (isMobile) {\n      setMobileMenu(!mobileMenu);\n    }\n  };\n\n  useEffect(() => {\n    setMobileMenu(false);\n  }, [pathname]);\n\n  return (\n    <Styles.Header>\n      <Styles.Container className=\"container\">\n        <Styles.Wtapper>\n          <AppLogo />\n          {isMobile ? (\n            <Styles.MobileMenuContainer>\n              <Menu onClick={handleMobileMenu} />\n              <MobileMenu\n                handleMobileMenu={handleMobileMenu}\n                active={mobileMenu}\n              />\n            </Styles.MobileMenuContainer>\n          ) : (\n            <Styles.MenuContainer>\n              <Styles.MenuListWrapper>\n                <Styles.MenuItem active={pathname === \"/\"}>\n                  <Link to=\"/\">Home</Link>\n                </Styles.MenuItem>\n                <Styles.MenuItem active={pathname === \"/about\"}>\n                  <Link to=\"/about\">About</Link>\n                </Styles.MenuItem>\n                <Styles.MenuItem active={pathname === \"/projects\"}>\n                  <Link to=\"/projects\">Projects</Link>\n                </Styles.MenuItem>\n                <Styles.MenuItem active={pathname === \"/clients\"}>\n                  <Link to=\"/clients\">Clients</Link>\n                </Styles.MenuItem>\n                <Styles.MenuItem active={pathname === \"/contactus\"}>\n                  <Link to=\"/contactus\">\n                    <Button>Contact us</Button>\n                  </Link>\n                </Styles.MenuItem>\n              </Styles.MenuListWrapper>\n            </Styles.MenuContainer>\n          )}\n        </Styles.Wtapper>\n      </Styles.Container>\n    </Styles.Header>\n  );\n};\n\nexport default AppHeader;\n"], "mappings": ";;;AAAA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,SAA1B,EAAqCC,WAArC,QAAwD,OAAxD;AACA,SAASC,IAAT,EAAeC,WAAf,QAAkC,kBAAlC;AACA,OAAOC,OAAP,MAAoB,SAApB;AAEA,OAAOC,MAAP,MAAmB,uBAAnB;AAEA,OAAOC,UAAP,MAAuB,UAAvB;AACA,OAAOC,cAAP,MAA2B,gCAA3B;AACA,SAASC,IAAT,QAAqB,eAArB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,SAAS,GAAG,MAAM;EAAA;;EACtB,MAAM,CAACC,UAAD,EAAaC,aAAb,IAA8Bb,QAAQ,CAAC,KAAD,CAA5C;EAEA,MAAM;IAAEc;EAAF,IAAeV,WAAW,EAAhC;EAEA,MAAM;IAAEW;EAAF,IAAYP,cAAc,EAAhC;EAEA,MAAMQ,QAAQ,GAAGD,KAAK,GAAG,GAAzB;;EAEA,MAAME,gBAAgB,GAAG,MAAM;IAC7B,IAAID,QAAJ,EAAc;MACZH,aAAa,CAAC,CAACD,UAAF,CAAb;IACD;EACF,CAJD;;EAMAX,SAAS,CAAC,MAAM;IACdY,aAAa,CAAC,KAAD,CAAb;EACD,CAFQ,EAEN,CAACC,QAAD,CAFM,CAAT;EAIA,oBACE,QAAC,MAAD,CAAQ,MAAR;IAAA,uBACE,QAAC,MAAD,CAAQ,SAAR;MAAkB,SAAS,EAAC,WAA5B;MAAA,uBACE,QAAC,MAAD,CAAQ,OAAR;QAAA,wBACE,QAAC,OAAD;UAAA;UAAA;UAAA;QAAA,QADF,EAEGE,QAAQ,gBACP,QAAC,MAAD,CAAQ,mBAAR;UAAA,wBACE,QAAC,IAAD;YAAM,OAAO,EAAEC;UAAf;YAAA;YAAA;YAAA;UAAA,QADF,eAEE,QAAC,UAAD;YACE,gBAAgB,EAAEA,gBADpB;YAEE,MAAM,EAAEL;UAFV;YAAA;YAAA;YAAA;UAAA,QAFF;QAAA;UAAA;UAAA;UAAA;QAAA,QADO,gBASP,QAAC,MAAD,CAAQ,aAAR;UAAA,uBACE,QAAC,MAAD,CAAQ,eAAR;YAAA,wBACE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEE,QAAQ,KAAK,GAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,GAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QADF,eAIE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEA,QAAQ,KAAK,QAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,QAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QAJF,eAOE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEA,QAAQ,KAAK,WAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,WAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QAPF,eAUE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEA,QAAQ,KAAK,UAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,UAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QAVF,eAaE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEA,QAAQ,KAAK,YAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,YAAT;gBAAA,uBACE,QAAC,MAAD;kBAAA;gBAAA;kBAAA;kBAAA;kBAAA;gBAAA;cADF;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QAbF;UAAA;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAXJ;MAAA;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAwCD,CA3DD;;GAAMH,S;UAGiBP,W;;;KAHjBO,S;AA6DN,eAAeA,SAAf"}, "metadata": {}, "sourceType": "module"}