{"ast": null, "code": "// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\n\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};", "map": {"version": 3, "names": ["IndexedObject", "require", "requireObjectCoercible", "module", "exports", "it"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/to-indexed-object.js"], "sourcesContent": ["// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAGC,OAAO,CAAC,6BAAD,CAA3B;;AACA,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,uCAAD,CAApC;;AAEAE,MAAM,CAACC,OAAP,GAAiB,UAAUC,EAAV,EAAc;EAC7B,OAAOL,aAAa,CAACE,sBAAsB,CAACG,EAAD,CAAvB,CAApB;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}