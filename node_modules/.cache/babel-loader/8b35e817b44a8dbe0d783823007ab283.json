{"ast": null, "code": "import styled from 'styled-components';\nexport const CardGrid = styled.div`\n    display: grid;\n    grid-template-columns: 1fr 1fr 1fr;\n\n    grid-gap: 40px;\n\n    @media screen and (max-width: 900px) {\n        grid-template-columns: 1fr 1fr;\n    }\n\n    @media screen and (max-width: 600px) {\n        grid: none;\n    }\n`;\nexport const VideoContainer = styled.div`\n  \n`;", "map": {"version": 3, "names": ["styled", "CardGrid", "div", "VideoContainer"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/styles.js"], "sourcesContent": ["import styled from 'styled-components';\n\nexport const CardGrid = styled.div`\n    display: grid;\n    grid-template-columns: 1fr 1fr 1fr;\n\n    grid-gap: 40px;\n\n    @media screen and (max-width: 900px) {\n        grid-template-columns: 1fr 1fr;\n    }\n\n    @media screen and (max-width: 600px) {\n        grid: none;\n    }\n`;\n\nexport const VideoContainer = styled.div`\n  \n`;"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,QAAQ,GAAGD,MAAM,CAACE,GAAI;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAbO;AAeP,OAAO,MAAMC,cAAc,GAAGH,MAAM,CAACE,GAAI;AACzC;AACA,CAFO"}, "metadata": {}, "sourceType": "module"}