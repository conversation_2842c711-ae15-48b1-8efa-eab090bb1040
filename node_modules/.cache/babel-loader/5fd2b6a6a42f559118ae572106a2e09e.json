{"ast": null, "code": "var check = function (it) {\n  return it && it.Math == Math && it;\n}; // https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\n\n\nmodule.exports = // eslint-disable-next-line es-x/no-global-this -- safe\ncheck(typeof globalThis == 'object' && globalThis) || check(typeof window == 'object' && window) || // eslint-disable-next-line no-restricted-globals -- safe\ncheck(typeof self == 'object' && self) || check(typeof global == 'object' && global) || // eslint-disable-next-line no-new-func -- fallback\nfunction () {\n  return this;\n}() || Function('return this')();", "map": {"version": 3, "names": ["check", "it", "Math", "module", "exports", "globalThis", "window", "self", "global", "Function"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/global.js"], "sourcesContent": ["var check = function (it) {\n  return it && it.Math == Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line es-x/no-global-this -- safe\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  // eslint-disable-next-line no-restricted-globals -- safe\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n"], "mappings": "AAAA,IAAIA,KAAK,GAAG,UAAUC,EAAV,EAAc;EACxB,OAAOA,EAAE,IAAIA,EAAE,CAACC,IAAH,IAAWA,IAAjB,IAAyBD,EAAhC;AACD,CAFD,C,CAIA;;;AACAE,MAAM,CAACC,OAAP,GACE;AACAJ,KAAK,CAAC,OAAOK,UAAP,IAAqB,QAArB,IAAiCA,UAAlC,CAAL,IACAL,KAAK,CAAC,OAAOM,MAAP,IAAiB,QAAjB,IAA6BA,MAA9B,CADL,IAEA;AACAN,KAAK,CAAC,OAAOO,IAAP,IAAe,QAAf,IAA2BA,IAA5B,CAHL,IAIAP,KAAK,CAAC,OAAOQ,MAAP,IAAiB,QAAjB,IAA6BA,MAA9B,CAJL,IAKA;AACC,YAAY;EAAE,OAAO,IAAP;AAAc,CAA7B,EANA,IAMoCC,QAAQ,CAAC,aAAD,CAAR,EARtC"}, "metadata": {}, "sourceType": "script"}