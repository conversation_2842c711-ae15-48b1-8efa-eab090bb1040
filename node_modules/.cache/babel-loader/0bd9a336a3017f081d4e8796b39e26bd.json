{"ast": null, "code": "// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\nmodule.exports = function (argument) {\n  return typeof argument == 'function';\n};", "map": {"version": 3, "names": ["module", "exports", "argument"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/is-callable.js"], "sourcesContent": ["// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\nmodule.exports = function (argument) {\n  return typeof argument == 'function';\n};\n"], "mappings": "AAAA;AACA;AACAA,MAAM,CAACC,OAAP,GAAiB,UAAUC,QAAV,EAAoB;EACnC,OAAO,OAAOA,QAAP,IAAmB,UAA1B;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}