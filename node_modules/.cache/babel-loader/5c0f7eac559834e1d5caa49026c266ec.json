{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/index.jsx\";\nimport React from 'react';\nimport ProjectDetails from '../../../components/projects/project-details';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst DetailsPage = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    className: \"container\",\n    children: /*#__PURE__*/_jsxDEV(ProjectDetails, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n};\n\n_c = DetailsPage;\nexport default DetailsPage;\n\nvar _c;\n\n$RefreshReg$(_c, \"DetailsPage\");", "map": {"version": 3, "names": ["React", "ProjectDetails", "Styles", "DetailsPage"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport ProjectDetails from '../../../components/projects/project-details'\n\nimport * as Styles from './styles'\n\nconst DetailsPage = () => {\n    return (\n        <Styles.Container className=\"container\">\n            <ProjectDetails />\n        </Styles.Container>\n    )\n}\n\nexport default DetailsPage"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,cAAP,MAA2B,8CAA3B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,WAAW,GAAG,MAAM;EACtB,oBACI,QAAC,MAAD,CAAQ,SAAR;IAAkB,SAAS,EAAC,WAA5B;IAAA,uBACI,QAAC,cAAD;MAAA;MAAA;MAAA;IAAA;EADJ;IAAA;IAAA;IAAA;EAAA,QADJ;AAKH,CAND;;KAAMA,W;AAQN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}