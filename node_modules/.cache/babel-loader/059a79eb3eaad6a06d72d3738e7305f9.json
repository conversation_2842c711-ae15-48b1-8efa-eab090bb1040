{"ast": null, "code": "\"use strict\";\n\nfunction throwError(state, props) {\n  var partialVisbile = props.partialVisbile,\n      partialVisible = props.partialVisible,\n      centerMode = props.centerMode,\n      ssr = props.ssr,\n      responsive = props.responsive;\n  if ((partialVisbile || partialVisible) && centerMode) throw new Error(\"center mode can not be used at the same time with partialVisible\");\n  if (!responsive) throw ssr ? new Error(\"ssr mode need to be used in conjunction with responsive prop\") : new Error(\"Responsive prop is needed for deciding the amount of items to show on the screen\");\n  if (responsive && \"object\" != typeof responsive) throw new Error(\"responsive prop must be an object\");\n}\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n}), exports.default = throwError;", "map": {"version": 3, "names": ["throwError", "state", "props", "partialVisbile", "partialVisible", "centerMode", "ssr", "responsive", "Error", "Object", "defineProperty", "exports", "value", "default"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/throwError.js"], "sourcesContent": ["\"use strict\";function throwError(state,props){var partialVisbile=props.partialVisbile,partialVisible=props.partialVisible,centerMode=props.centerMode,ssr=props.ssr,responsive=props.responsive;if((partialVisbile||partialVisible)&&centerMode)throw new Error(\"center mode can not be used at the same time with partialVisible\");if(!responsive)throw ssr?new Error(\"ssr mode need to be used in conjunction with responsive prop\"):new Error(\"Responsive prop is needed for deciding the amount of items to show on the screen\");if(responsive&&\"object\"!=typeof responsive)throw new Error(\"responsive prop must be an object\")}Object.defineProperty(exports,\"__esModule\",{value:!0}),exports.default=throwError;"], "mappings": "AAAA;;AAAa,SAASA,UAAT,CAAoBC,KAApB,EAA0BC,KAA1B,EAAgC;EAAC,IAAIC,cAAc,GAACD,KAAK,CAACC,cAAzB;EAAA,IAAwCC,cAAc,GAACF,KAAK,CAACE,cAA7D;EAAA,IAA4EC,UAAU,GAACH,KAAK,CAACG,UAA7F;EAAA,IAAwGC,GAAG,GAACJ,KAAK,CAACI,GAAlH;EAAA,IAAsHC,UAAU,GAACL,KAAK,CAACK,UAAvI;EAAkJ,IAAG,CAACJ,cAAc,IAAEC,cAAjB,KAAkCC,UAArC,EAAgD,MAAM,IAAIG,KAAJ,CAAU,kEAAV,CAAN;EAAoF,IAAG,CAACD,UAAJ,EAAe,MAAMD,GAAG,GAAC,IAAIE,KAAJ,CAAU,8DAAV,CAAD,GAA2E,IAAIA,KAAJ,CAAU,kFAAV,CAApF;EAAkL,IAAGD,UAAU,IAAE,YAAU,OAAOA,UAAhC,EAA2C,MAAM,IAAIC,KAAJ,CAAU,mCAAV,CAAN;AAAqD;;AAAAC,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C,GAAuDD,OAAO,CAACE,OAAR,GAAgBb,UAAvE"}, "metadata": {}, "sourceType": "script"}