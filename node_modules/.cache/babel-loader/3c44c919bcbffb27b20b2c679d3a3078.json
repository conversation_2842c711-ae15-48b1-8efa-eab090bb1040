{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/index.jsx\";\nimport React from 'react';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst InputField = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: /*#__PURE__*/_jsxDEV(Styles.InputWrapper, {\n      children: [/*#__PURE__*/_jsxDEV(Styles.InputHolder, {\n        name: \"signup\",\n        placeholder: \"Enter Email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Styles.Button, {\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n\n_c = InputField;\nexport default InputField;\n\nvar _c;\n\n$RefreshReg$(_c, \"InputField\");", "map": {"version": 3, "names": ["React", "Styles", "InputField"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport * as Styles from './styles'\n\nconst InputField = () => {\n    return (\n        <Styles.Container>\n            <Styles.InputWrapper>\n                <Styles.InputHolder\n                    name=\"signup\"\n                    placeholder=\"Enter Email\"\n                />\n                <Styles.Button>\n                    Send\n                </Styles.Button>\n            </Styles.InputWrapper>\n        </Styles.Container>\n    )\n}\n\nexport default InputField"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,UAAU,GAAG,MAAM;EACrB,oBACI,QAAC,MAAD,CAAQ,SAAR;IAAA,uBACI,QAAC,MAAD,CAAQ,YAAR;MAAA,wBACI,QAAC,MAAD,CAAQ,WAAR;QACI,IAAI,EAAC,QADT;QAEI,WAAW,EAAC;MAFhB;QAAA;QAAA;QAAA;MAAA,QADJ,eAKI,QAAC,MAAD,CAAQ,MAAR;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QALJ;IAAA;MAAA;MAAA;MAAA;IAAA;EADJ;IAAA;IAAA;IAAA;EAAA,QADJ;AAaH,CAdD;;KAAMA,U;AAgBN,eAAeA,UAAf"}, "metadata": {}, "sourceType": "module"}