{"ast": null, "code": "'use strict';\n\nvar $propertyIsEnumerable = {}.propertyIsEnumerable; // eslint-disable-next-line es-x/no-object-getownpropertydescriptor -- safe\n\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor; // Nashorn ~ JDK8 bug\n\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({\n  1: 2\n}, 1); // `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\n\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;", "map": {"version": 3, "names": ["$propertyIsEnumerable", "propertyIsEnumerable", "getOwnPropertyDescriptor", "Object", "NASHORN_BUG", "call", "exports", "f", "V", "descriptor", "enumerable"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/object-property-is-enumerable.js"], "sourcesContent": ["'use strict';\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es-x/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;\n"], "mappings": "AAAA;;AACA,IAAIA,qBAAqB,GAAG,GAAGC,oBAA/B,C,CACA;;AACA,IAAIC,wBAAwB,GAAGC,MAAM,CAACD,wBAAtC,C,CAEA;;AACA,IAAIE,WAAW,GAAGF,wBAAwB,IAAI,CAACF,qBAAqB,CAACK,IAAtB,CAA2B;EAAE,GAAG;AAAL,CAA3B,EAAqC,CAArC,CAA/C,C,CAEA;AACA;;AACAC,OAAO,CAACC,CAAR,GAAYH,WAAW,GAAG,SAASH,oBAAT,CAA8BO,CAA9B,EAAiC;EACzD,IAAIC,UAAU,GAAGP,wBAAwB,CAAC,IAAD,EAAOM,CAAP,CAAzC;EACA,OAAO,CAAC,CAACC,UAAF,IAAgBA,UAAU,CAACC,UAAlC;AACD,CAHsB,GAGnBV,qBAHJ"}, "metadata": {}, "sourceType": "script"}