{"ast": null, "code": "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "id", "postfix", "Math", "random", "toString", "module", "exports", "key", "undefined"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/uid.js"], "sourcesContent": ["var uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAD,CAAzB;;AAEA,IAAIC,EAAE,GAAG,CAAT;AACA,IAAIC,OAAO,GAAGC,IAAI,CAACC,MAAL,EAAd;AACA,IAAIC,QAAQ,GAAGN,WAAW,CAAC,IAAIM,QAAL,CAA1B;;AAEAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,GAAV,EAAe;EAC9B,OAAO,aAAaA,GAAG,KAAKC,SAAR,GAAoB,EAApB,GAAyBD,GAAtC,IAA6C,IAA7C,GAAoDH,QAAQ,CAAC,EAAEJ,EAAF,GAAOC,OAAR,EAAiB,EAAjB,CAAnE;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}