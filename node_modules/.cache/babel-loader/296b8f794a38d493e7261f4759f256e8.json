{"ast": null, "code": "var isCallable = require('../internals/is-callable');\n\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError; // `Assert: IsCallable(argument) is true`\n\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw $TypeError(tryToString(argument) + ' is not a function');\n};", "map": {"version": 3, "names": ["isCallable", "require", "tryToString", "$TypeError", "TypeError", "module", "exports", "argument"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/a-callable.js"], "sourcesContent": ["var isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw $TypeError(tryToString(argument) + ' is not a function');\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAAD,CAAxB;;AACA,IAAIC,WAAW,GAAGD,OAAO,CAAC,4BAAD,CAAzB;;AAEA,IAAIE,UAAU,GAAGC,SAAjB,C,CAEA;;AACAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,QAAV,EAAoB;EACnC,IAAIP,UAAU,CAACO,QAAD,CAAd,EAA0B,OAAOA,QAAP;EAC1B,MAAMJ,UAAU,CAACD,WAAW,CAACK,QAAD,CAAX,GAAwB,oBAAzB,CAAhB;AACD,CAHD"}, "metadata": {}, "sourceType": "script"}