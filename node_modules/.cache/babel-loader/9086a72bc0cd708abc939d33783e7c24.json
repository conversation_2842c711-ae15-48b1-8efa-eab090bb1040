{"ast": null, "code": "import styled from \"styled-components\";\nexport const Container = styled.div`\n  background-color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.main.yellow;\n}};\n  padding: 15px 20px;\n\n  border-radius: 10px;\n\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\n\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  /* max-width: 150px; */\n  min-width: 140px;\n\n  &:hover {\n    background-color: #fff;\n\n    cursor: pointer;\n    transition: all 0.1s ease-in-out;\n  }\n`;\nexport const ImageHolder = styled.img`\n  width: 50px;\n  height: 50px;\n  object-fit: contain;\n\n  margin: 10px 0;\n`;\nexport const TextWrapper = styled.p`\n  margin-bottom: 0;\n  text-align: center;\n\n  font-weight: 600;\n\n  white-space: nowrap;\n  text-overflow: ellipsis;\n`;", "map": {"version": 3, "names": ["styled", "Container", "div", "theme", "colors", "main", "yellow", "ImageHolder", "img", "TextWrapper", "p"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  background-color: ${({ theme: { colors } }) => colors.main.yellow};\n  padding: 15px 20px;\n\n  border-radius: 10px;\n\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\n\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  /* max-width: 150px; */\n  min-width: 140px;\n\n  &:hover {\n    background-color: #fff;\n\n    cursor: pointer;\n    transition: all 0.1s ease-in-out;\n  }\n`;\n\nexport const ImageHolder = styled.img`\n  width: 50px;\n  height: 50px;\n  object-fit: contain;\n\n  margin: 10px 0;\n`;\n\nexport const TextWrapper = styled.p`\n  margin-bottom: 0;\n  text-align: center;\n\n  font-weight: 600;\n\n  white-space: nowrap;\n  text-overflow: ellipsis;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAI;AACpC,sBAAsB;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAP,CAAYC,MAAvC;AAAA,CAA8C;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAtBO;AAwBP,OAAO,MAAMC,WAAW,GAAGP,MAAM,CAACQ,GAAI;AACtC;AACA;AACA;AACA;AACA;AACA,CANO;AAQP,OAAO,MAAMC,WAAW,GAAGT,MAAM,CAACU,CAAE;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CARO"}, "metadata": {}, "sourceType": "module"}