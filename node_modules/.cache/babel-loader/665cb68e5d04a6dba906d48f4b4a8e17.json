{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/index.jsx\";\nimport React from \"react\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst Button = _ref => {\n  let {\n    children,\n    ...otherProps\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Styles.Button, { ...otherProps,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 10\n  }, this);\n};\n\n_c = Button;\nexport default Button;\n\nvar _c;\n\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "Styles", "<PERSON><PERSON>", "children", "otherProps"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport * as Styles from \"./styles\";\n\nconst Button = ({ children, ...otherProps }) => {\n  return <Styles.Button {...otherProps}>{children}</Styles.Button>;\n};\n\nexport default Button;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,MAAM,GAAG,QAAiC;EAAA,IAAhC;IAAEC,QAAF;IAAY,GAAGC;EAAf,CAAgC;EAC9C,oBAAO,QAAC,MAAD,CAAQ,MAAR,OAAmBA,UAAnB;IAAA,UAAgCD;EAAhC;IAAA;IAAA;IAAA;EAAA,QAAP;AACD,CAFD;;KAAMD,M;AAIN,eAAeA,MAAf"}, "metadata": {}, "sourceType": "module"}