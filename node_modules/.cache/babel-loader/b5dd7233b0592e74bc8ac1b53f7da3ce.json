{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar throttle = function (func, limit, setIsInThrottle) {\n  var inThrottle;\n  return function () {\n    var args = arguments;\n    inThrottle || (func.apply(this, args), inThrottle = !0, \"function\" == typeof setIsInThrottle && setIsInThrottle(!0), setTimeout(function () {\n      inThrottle = !1, \"function\" == typeof setIsInThrottle && setIsInThrottle(!1);\n    }, limit));\n  };\n};\n\nexports.default = throttle;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "throttle", "func", "limit", "setIsInThrottle", "inThrottle", "args", "arguments", "apply", "setTimeout", "default"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/utils/throttle.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var throttle=function(func,limit,setIsInThrottle){var inThrottle;return function(){var args=arguments;inThrottle||(func.apply(this,args),inThrottle=!0,\"function\"==typeof setIsInThrottle&&setIsInThrottle(!0),setTimeout(function(){inThrottle=!1,\"function\"==typeof setIsInThrottle&&setIsInThrottle(!1)},limit))}};exports.default=throttle;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,QAAQ,GAAC,UAASC,IAAT,EAAcC,KAAd,EAAoBC,eAApB,EAAoC;EAAC,IAAIC,UAAJ;EAAe,OAAO,YAAU;IAAC,IAAIC,IAAI,GAACC,SAAT;IAAmBF,UAAU,KAAGH,IAAI,CAACM,KAAL,CAAW,IAAX,EAAgBF,IAAhB,GAAsBD,UAAU,GAAC,CAAC,CAAlC,EAAoC,cAAY,OAAOD,eAAnB,IAAoCA,eAAe,CAAC,CAAC,CAAF,CAAvF,EAA4FK,UAAU,CAAC,YAAU;MAACJ,UAAU,GAAC,CAAC,CAAZ,EAAc,cAAY,OAAOD,eAAnB,IAAoCA,eAAe,CAAC,CAAC,CAAF,CAAjE;IAAsE,CAAlF,EAAmFD,KAAnF,CAAzG,CAAV;EAA8M,CAAnP;AAAoP,CAArT;;AAAsTJ,OAAO,CAACW,OAAR,GAAgBT,QAAhB"}, "metadata": {}, "sourceType": "script"}