{"ast": null, "code": "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "toString", "stringSlice", "slice", "module", "exports", "it"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/classof-raw.js"], "sourcesContent": ["var uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAD,CAAzB;;AAEA,IAAIC,QAAQ,GAAGF,WAAW,CAAC,GAAGE,QAAJ,CAA1B;AACA,IAAIC,WAAW,GAAGH,WAAW,CAAC,GAAGI,KAAJ,CAA7B;;AAEAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,EAAV,EAAc;EAC7B,OAAOJ,WAAW,CAACD,QAAQ,CAACK,EAAD,CAAT,EAAe,CAAf,EAAkB,CAAC,CAAnB,CAAlB;AACD,CAFD"}, "metadata": {}, "sourceType": "script"}