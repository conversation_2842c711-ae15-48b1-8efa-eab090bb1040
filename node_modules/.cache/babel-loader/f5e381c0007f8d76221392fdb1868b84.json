{"ast": null, "code": "var global = require('../internals/global'); // eslint-disable-next-line es-x/no-object-defineproperty -- safe\n\n\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, {\n      value: value,\n      configurable: true,\n      writable: true\n    });\n  } catch (error) {\n    global[key] = value;\n  }\n\n  return value;\n};", "map": {"version": 3, "names": ["global", "require", "defineProperty", "Object", "module", "exports", "key", "value", "configurable", "writable", "error"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/define-global-property.js"], "sourcesContent": ["var global = require('../internals/global');\n\n// eslint-disable-next-line es-x/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAD,CAApB,C,CAEA;;;AACA,IAAIC,cAAc,GAAGC,MAAM,CAACD,cAA5B;;AAEAE,MAAM,CAACC,OAAP,GAAiB,UAAUC,GAAV,EAAeC,KAAf,EAAsB;EACrC,IAAI;IACFL,cAAc,CAACF,MAAD,EAASM,GAAT,EAAc;MAAEC,KAAK,EAAEA,KAAT;MAAgBC,YAAY,EAAE,IAA9B;MAAoCC,QAAQ,EAAE;IAA9C,CAAd,CAAd;EACD,CAFD,CAEE,OAAOC,KAAP,EAAc;IACdV,MAAM,CAACM,GAAD,CAAN,GAAcC,KAAd;EACD;;EAAC,OAAOA,KAAP;AACH,CAND"}, "metadata": {}, "sourceType": "script"}