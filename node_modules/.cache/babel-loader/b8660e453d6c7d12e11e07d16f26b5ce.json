{"ast": null, "code": "var getBuiltIn = require('../internals/get-built-in');\n\nvar isCallable = require('../internals/is-callable');\n\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\n\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar $Object = Object;\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));\n};", "map": {"version": 3, "names": ["getBuiltIn", "require", "isCallable", "isPrototypeOf", "USE_SYMBOL_AS_UID", "$Object", "Object", "module", "exports", "it", "$Symbol", "prototype"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/is-symbol.js"], "sourcesContent": ["var getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar $Object = Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,2BAAD,CAAxB;;AACA,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAAD,CAAxB;;AACA,IAAIE,aAAa,GAAGF,OAAO,CAAC,qCAAD,CAA3B;;AACA,IAAIG,iBAAiB,GAAGH,OAAO,CAAC,gCAAD,CAA/B;;AAEA,IAAII,OAAO,GAAGC,MAAd;AAEAC,MAAM,CAACC,OAAP,GAAiBJ,iBAAiB,GAAG,UAAUK,EAAV,EAAc;EACjD,OAAO,OAAOA,EAAP,IAAa,QAApB;AACD,CAFiC,GAE9B,UAAUA,EAAV,EAAc;EAChB,IAAIC,OAAO,GAAGV,UAAU,CAAC,QAAD,CAAxB;EACA,OAAOE,UAAU,CAACQ,OAAD,CAAV,IAAuBP,aAAa,CAACO,OAAO,CAACC,SAAT,EAAoBN,OAAO,CAACI,EAAD,CAA3B,CAA3C;AACD,CALD"}, "metadata": {}, "sourceType": "script"}