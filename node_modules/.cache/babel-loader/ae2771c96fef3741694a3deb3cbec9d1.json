{"ast": null, "code": "/* global __react_refresh_library__ */\nconst safeThis = require('core-js-pure/features/global-this');\n\nconst RefreshRuntime = require('react-refresh/runtime');\n\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof safeThis !== 'undefined') {\n    var $RefreshInjected$ = '__reactRefreshInjected'; // Namespace the injected flag (if necessary) for monorepo compatibility\n\n    if (typeof __react_refresh_library__ !== 'undefined' && __react_refresh_library__) {\n      $RefreshInjected$ += '_' + __react_refresh_library__;\n    } // Only inject the runtime if it hasn't been injected\n\n\n    if (!safeThis[$RefreshInjected$]) {\n      // Inject refresh runtime into global scope\n      RefreshRuntime.injectIntoGlobalHook(safeThis); // Mark the runtime as injected to prevent double-injection\n\n      safeThis[$RefreshInjected$] = true;\n    }\n  }\n}", "map": {"version": 3, "names": ["safeThis", "require", "RefreshRuntime", "process", "env", "NODE_ENV", "$RefreshInjected$", "__react_refresh_library__", "injectIntoGlobalHook"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@pmmmwh/react-refresh-webpack-plugin/client/ReactRefreshEntry.js"], "sourcesContent": ["/* global __react_refresh_library__ */\n\nconst safeThis = require('core-js-pure/features/global-this');\nconst RefreshRuntime = require('react-refresh/runtime');\n\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof safeThis !== 'undefined') {\n    var $RefreshInjected$ = '__reactRefreshInjected';\n    // Namespace the injected flag (if necessary) for monorepo compatibility\n    if (typeof __react_refresh_library__ !== 'undefined' && __react_refresh_library__) {\n      $RefreshInjected$ += '_' + __react_refresh_library__;\n    }\n\n    // Only inject the runtime if it hasn't been injected\n    if (!safeThis[$RefreshInjected$]) {\n      // Inject refresh runtime into global scope\n      RefreshRuntime.injectIntoGlobalHook(safeThis);\n\n      // Mark the runtime as injected to prevent double-injection\n      safeThis[$RefreshInjected$] = true;\n    }\n  }\n}\n"], "mappings": "AAAA;AAEA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,mCAAD,CAAxB;;AACA,MAAMC,cAAc,GAAGD,OAAO,CAAC,uBAAD,CAA9B;;AAEA,IAAIE,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzC,IAAI,OAAOL,QAAP,KAAoB,WAAxB,EAAqC;IACnC,IAAIM,iBAAiB,GAAG,wBAAxB,CADmC,CAEnC;;IACA,IAAI,OAAOC,yBAAP,KAAqC,WAArC,IAAoDA,yBAAxD,EAAmF;MACjFD,iBAAiB,IAAI,MAAMC,yBAA3B;IACD,CALkC,CAOnC;;;IACA,IAAI,CAACP,QAAQ,CAACM,iBAAD,CAAb,EAAkC;MAChC;MACAJ,cAAc,CAACM,oBAAf,CAAoCR,QAApC,EAFgC,CAIhC;;MACAA,QAAQ,CAACM,iBAAD,CAAR,GAA8B,IAA9B;IACD;EACF;AACF"}, "metadata": {}, "sourceType": "script"}