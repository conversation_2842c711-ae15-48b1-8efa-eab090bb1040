{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/index.jsx\";\nimport React from 'react';\nimport Seaction from '../../components/global/seaction';\nimport SeactionHeading from '../../components/global/seaction-title';\nimport { ProjectData } from '../../components/projects/projectData';\nimport ProjectCard from '../../components/projects/card';\nimport Projects from '../../components/projects';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst ProjectPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: /*#__PURE__*/_jsxDEV(Seaction, {\n      children: [/*#__PURE__*/_jsxDEV(SeactionHeading, {\n        title: \"Explore New features\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Styles.CardGrid, {\n        className: \"grid-md-none\",\n        children: ProjectData.map((item, key) => {\n          return /*#__PURE__*/_jsxDEV(ProjectCard, {\n            item: item\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 9\n  }, this);\n};\n\n_c = ProjectPage;\nexport default ProjectPage;\n\nvar _c;\n\n$RefreshReg$(_c, \"ProjectPage\");", "map": {"version": 3, "names": ["React", "Seaction", "SeactionHeading", "ProjectData", "ProjectCard", "Projects", "Styles", "ProjectPage", "map", "item", "key"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport Seaction from '../../components/global/seaction'\n\nimport SeactionHeading from '../../components/global/seaction-title'\n\nimport { ProjectData } from '../../components/projects/projectData'\n\nimport ProjectCard from '../../components/projects/card';\nimport Projects from '../../components/projects';\n\nimport * as Styles from './styles'\n\nconst ProjectPage = () => {\n    return (\n        <div className=\"container\">\n            <Seaction>\n                <SeactionHeading title=\"Explore New features\" />\n                <Styles.CardGrid className=\"grid-md-none\">\n                    {\n                        ProjectData.map((item, key) => {\n                            return (\n                                <ProjectCard\n                                    key={key}\n                                    item={item}\n                                />\n                            )\n                        })\n                    }\n                </Styles.CardGrid>\n            </Seaction>\n            {/* <Seaction>\n                <Styles.VideoContainer>\n                    <SeactionHeading title=\"Short Glimpse Of the Working\" />\n                    <iframe width=\"100%\" height=\"615\" src=\"https://www.youtube.com/embed/KbTjl1PNCzg\" title=\"YouTube video player\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>\n                </Styles.VideoContainer>\n            </Seaction> */}\n        </div>\n    )\n}\n\nexport default ProjectPage"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAOC,QAAP,MAAqB,kCAArB;AAEA,OAAOC,eAAP,MAA4B,wCAA5B;AAEA,SAASC,WAAT,QAA4B,uCAA5B;AAEA,OAAOC,WAAP,MAAwB,gCAAxB;AACA,OAAOC,QAAP,MAAqB,2BAArB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,WAAW,GAAG,MAAM;EACtB,oBACI;IAAK,SAAS,EAAC,WAAf;IAAA,uBACI,QAAC,QAAD;MAAA,wBACI,QAAC,eAAD;QAAiB,KAAK,EAAC;MAAvB;QAAA;QAAA;QAAA;MAAA,QADJ,eAEI,QAAC,MAAD,CAAQ,QAAR;QAAiB,SAAS,EAAC,cAA3B;QAAA,UAEQJ,WAAW,CAACK,GAAZ,CAAgB,CAACC,IAAD,EAAOC,GAAP,KAAe;UAC3B,oBACI,QAAC,WAAD;YAEI,IAAI,EAAED;UAFV,GACSC,GADT;YAAA;YAAA;YAAA;UAAA,QADJ;QAMH,CAPD;MAFR;QAAA;QAAA;QAAA;MAAA,QAFJ;IAAA;MAAA;MAAA;MAAA;IAAA;EADJ;IAAA;IAAA;IAAA;EAAA,QADJ;AAyBH,CA1BD;;KAAMH,W;AA4BN,eAAeA,WAAf"}, "metadata": {}, "sourceType": "module"}