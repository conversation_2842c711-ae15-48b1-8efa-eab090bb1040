{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/index.js\";\nimport React, { Suspense } from \"react\";\nimport { Route, Routes } from \"react-router-dom\";\nimport Loading from \"../components/global/loading\";\nimport ScrollToTop from \"../components/global/scroll-to-top\";\nimport AppHeader from \"../components/global/header\";\nimport BgTitle from \"../components/global/bg-titile\";\nimport Signup from \"../components/global/signup\";\nimport AppFooter from \"../components/global/footer\";\nimport Slider from \"../components/global/slider\";\nimport { AllRoutes } from \"./routeData\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst AppRoute = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(AppHeader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BgTitle, {\n      routes: AllRoutes\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 27\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: AllRoutes.map((route, index) => {\n          return /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: route.path,\n            element: /*#__PURE__*/_jsxDEV(route.component, { ...route.props\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 26\n            }, this)\n          }, route.path + index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppFooter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n_c = AppRoute;\nexport default AppRoute;\n\nvar _c;\n\n$RefreshReg$(_c, \"AppRoute\");", "map": {"version": 3, "names": ["React", "Suspense", "Route", "Routes", "Loading", "ScrollToTop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BgTitle", "Signup", "AppFooter", "Slide<PERSON>", "AllRoutes", "AppRoute", "map", "route", "index", "path", "props"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/index.js"], "sourcesContent": ["import React, { Suspense } from \"react\";\nimport { Route, Routes } from \"react-router-dom\";\n\nimport Loading from \"../components/global/loading\";\n\nimport ScrollToTop from \"../components/global/scroll-to-top\";\n\nimport AppHeader from \"../components/global/header\";\nimport BgTitle from \"../components/global/bg-titile\";\nimport Signup from \"../components/global/signup\";\nimport AppFooter from \"../components/global/footer\";\n\nimport Slider from \"../components/global/slider\";\n\nimport { AllRoutes } from \"./routeData\";\n\nconst AppRoute = () => {\n  return (\n    <>\n      <AppHeader />\n      <BgTitle routes={AllRoutes} />\n      {/* <Slider /> */}\n      <ScrollToTop />\n      <Suspense fallback={<Loading />}>\n        <Routes>\n          {AllRoutes.map((route, index) => {\n            return (\n              <Route\n                key={route.path + index}\n                exact\n                path={route.path}\n                element={<route.component {...route.props} />}\n              />\n            );\n          })}\n        </Routes>\n      </Suspense>\n      {/* <Signup /> */}\n      <AppFooter />\n    </>\n  );\n};\n\nexport default AppRoute;\n"], "mappings": ";AAAA,OAAOA,KAAP,IAAgBC,QAAhB,QAAgC,OAAhC;AACA,SAASC,KAAT,EAAgBC,MAAhB,QAA8B,kBAA9B;AAEA,OAAOC,OAAP,MAAoB,8BAApB;AAEA,OAAOC,WAAP,MAAwB,oCAAxB;AAEA,OAAOC,SAAP,MAAsB,6BAAtB;AACA,OAAOC,OAAP,MAAoB,gCAApB;AACA,OAAOC,MAAP,MAAmB,6BAAnB;AACA,OAAOC,SAAP,MAAsB,6BAAtB;AAEA,OAAOC,MAAP,MAAmB,6BAAnB;AAEA,SAASC,SAAT,QAA0B,aAA1B;;;;AAEA,MAAMC,QAAQ,GAAG,MAAM;EACrB,oBACE;IAAA,wBACE,QAAC,SAAD;MAAA;MAAA;MAAA;IAAA,QADF,eAEE,QAAC,OAAD;MAAS,MAAM,EAAED;IAAjB;MAAA;MAAA;MAAA;IAAA,QAFF,eAIE,QAAC,WAAD;MAAA;MAAA;MAAA;IAAA,QAJF,eAKE,QAAC,QAAD;MAAU,QAAQ,eAAE,QAAC,OAAD;QAAA;QAAA;QAAA;MAAA,QAApB;MAAA,uBACE,QAAC,MAAD;QAAA,UACGA,SAAS,CAACE,GAAV,CAAc,CAACC,KAAD,EAAQC,KAAR,KAAkB;UAC/B,oBACE,QAAC,KAAD;YAEE,KAAK,MAFP;YAGE,IAAI,EAAED,KAAK,CAACE,IAHd;YAIE,OAAO,eAAE,QAAC,KAAD,CAAO,SAAP,OAAqBF,KAAK,CAACG;YAA3B;cAAA;cAAA;cAAA;YAAA;UAJX,GACOH,KAAK,CAACE,IAAN,GAAaD,KADpB;YAAA;YAAA;YAAA;UAAA,QADF;QAQD,CATA;MADH;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QALF,eAoBE,QAAC,SAAD;MAAA;MAAA;MAAA;IAAA,QApBF;EAAA,gBADF;AAwBD,CAzBD;;KAAMH,Q;AA2BN,eAAeA,QAAf"}, "metadata": {}, "sourceType": "module"}