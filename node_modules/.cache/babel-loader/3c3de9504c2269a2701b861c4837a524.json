{"ast": null, "code": "import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templatePrams - the template params, what will be set to the EmailJS template\n * @param {string} publicKey - the EmailJS public key\n * @returns {Promise<EmailJSResponseStatus>}\n */\n\nexport const send = (serviceID, templateID, templatePrams, publicKey) => {\n  const uID = publicKey || store._userID;\n  validateParams(uID, serviceID, templateID);\n  const params = {\n    lib_version: '3.9.0',\n    user_id: uID,\n    service_id: serviceID,\n    template_id: templateID,\n    template_params: templatePrams\n  };\n  return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n    'Content-type': 'application/json'\n  });\n};", "map": {"version": 3, "names": ["store", "validateParams", "sendPost", "send", "serviceID", "templateID", "templatePrams", "public<PERSON>ey", "uID", "_userID", "params", "lib_version", "user_id", "service_id", "template_id", "template_params", "JSON", "stringify"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@emailjs/browser/es/methods/send/send.js"], "sourcesContent": ["import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templatePrams - the template params, what will be set to the EmailJS template\n * @param {string} publicKey - the EmailJS public key\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const send = (serviceID, templateID, templatePrams, publicKey) => {\n    const uID = publicKey || store._userID;\n    validateParams(uID, serviceID, templateID);\n    const params = {\n        lib_version: '3.9.0',\n        user_id: uID,\n        service_id: serviceID,\n        template_id: templateID,\n        template_params: templatePrams,\n    };\n    return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n        'Content-type': 'application/json',\n    });\n};\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,mBAAtB;AACA,SAASC,cAAT,QAA+B,4BAA/B;AACA,SAASC,QAAT,QAAyB,oBAAzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,MAAMC,IAAI,GAAG,CAACC,SAAD,EAAYC,UAAZ,EAAwBC,aAAxB,EAAuCC,SAAvC,KAAqD;EACrE,MAAMC,GAAG,GAAGD,SAAS,IAAIP,KAAK,CAACS,OAA/B;EACAR,cAAc,CAACO,GAAD,EAAMJ,SAAN,EAAiBC,UAAjB,CAAd;EACA,MAAMK,MAAM,GAAG;IACXC,WAAW,EAAE,OADF;IAEXC,OAAO,EAAEJ,GAFE;IAGXK,UAAU,EAAET,SAHD;IAIXU,WAAW,EAAET,UAJF;IAKXU,eAAe,EAAET;EALN,CAAf;EAOA,OAAOJ,QAAQ,CAAC,sBAAD,EAAyBc,IAAI,CAACC,SAAL,CAAeP,MAAf,CAAzB,EAAiD;IAC5D,gBAAgB;EAD4C,CAAjD,CAAf;AAGH,CAbM"}, "metadata": {}, "sourceType": "module"}