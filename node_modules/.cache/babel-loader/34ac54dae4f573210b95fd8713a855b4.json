{"ast": null, "code": "var aCallable = require('../internals/a-callable'); // `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\n\n\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return func == null ? undefined : aCallable(func);\n};", "map": {"version": 3, "names": ["aCallable", "require", "module", "exports", "V", "P", "func", "undefined"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/get-method.js"], "sourcesContent": ["var aCallable = require('../internals/a-callable');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return func == null ? undefined : aCallable(func);\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,yBAAD,CAAvB,C,CAEA;AACA;;;AACAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAC/B,IAAIC,IAAI,GAAGF,CAAC,CAACC,CAAD,CAAZ;EACA,OAAOC,IAAI,IAAI,IAAR,GAAeC,SAAf,GAA2BP,SAAS,CAACM,IAAD,CAA3C;AACD,CAHD"}, "metadata": {}, "sourceType": "script"}