{"ast": null, "code": "var global = require('../internals/global');\n\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || defineGlobalProperty(SHARED, {});\nmodule.exports = store;", "map": {"version": 3, "names": ["global", "require", "defineGlobalProperty", "SHARED", "store", "module", "exports"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/shared-store.js"], "sourcesContent": ["var global = require('../internals/global');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || defineGlobalProperty(SHARED, {});\n\nmodule.exports = store;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAD,CAApB;;AACA,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,qCAAD,CAAlC;;AAEA,IAAIE,MAAM,GAAG,oBAAb;AACA,IAAIC,KAAK,GAAGJ,MAAM,CAACG,MAAD,CAAN,IAAkBD,oBAAoB,CAACC,MAAD,EAAS,EAAT,CAAlD;AAEAE,MAAM,CAACC,OAAP,GAAiBF,KAAjB"}, "metadata": {}, "sourceType": "script"}