{"ast": null, "code": "import styled from \"styled-components\";\nexport const Contaier = styled.div``;\nexport const FormHolder = styled.form``;\nexport const RowHolder = styled.div`\n  margin: 20px 0;\n`;\nexport const LabelHolder = styled.label`\n  display: block;\n  margin-left: 10px;\n  margin-bottom: 10px;\n\n  color: ${_ref => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref;\n  return colors.grey;\n}};\n`;\nexport const InputHolder = styled.input`\n  border: 2px solid\n    ${_ref2 => {\n  let {\n    error,\n    theme: {\n      colors\n    }\n  } = _ref2;\n  return error ? colors.main.red : colors.grey;\n}};\n  padding: 10px 16px;\n  border-radius: 100px;\n\n  width: 100%;\n\n  &:focus {\n    outline: none;\n    border: 2px solid ${_ref3 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref3;\n  return colors.main.blue;\n}};\n  }\n`;\nexport const Button = styled.button`\n  background-color: ${_ref4 => {\n  let {\n    success,\n    theme: {\n      colors\n    }\n  } = _ref4;\n  return success ? \"#a2ff54\" : colors.main.yellow;\n}};\n  border: none;\n  padding: 10px;\n  width: 100%;\n  font-weight: 600;\n\n  border-radius: 100px;\n\n  box-shadow: 0 0 20px 2px rgba(0, 0, 0, 0.1);\n\n  margin-top: 20px;\n\n  color: ${_ref5 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref5;\n  return colors.main.blue;\n}};\n\n  &:hover {\n    background-color: ${_ref6 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref6;\n  return colors.main.blue;\n}};\n    color: ${_ref7 => {\n  let {\n    theme: {\n      colors\n    }\n  } = _ref7;\n  return colors.white;\n}};\n  }\n\n  transition: all 0.3s ease-in-out;\n`;\nexport const ShowError = styled.p`\n  color: red;\n`;\nexport const ShowSuccess = styled.p`\n  color: green;\n  margin-top: 10px;\n  text-align: center;\n`;", "map": {"version": 3, "names": ["styled", "<PERSON><PERSON><PERSON>", "div", "FormHolder", "form", "RowHolder", "LabelHolder", "label", "theme", "colors", "grey", "InputHolder", "input", "error", "main", "red", "blue", "<PERSON><PERSON>", "button", "success", "yellow", "white", "ShowError", "p", "ShowSuccess"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Contaier = styled.div``;\n\nexport const FormHolder = styled.form``;\n\nexport const RowHolder = styled.div`\n  margin: 20px 0;\n`;\n\nexport const LabelHolder = styled.label`\n  display: block;\n  margin-left: 10px;\n  margin-bottom: 10px;\n\n  color: ${({ theme: { colors } }) => colors.grey};\n`;\n\nexport const InputHolder = styled.input`\n  border: 2px solid\n    ${({ error, theme: { colors } }) => (error ? colors.main.red : colors.grey)};\n  padding: 10px 16px;\n  border-radius: 100px;\n\n  width: 100%;\n\n  &:focus {\n    outline: none;\n    border: 2px solid ${({ theme: { colors } }) => colors.main.blue};\n  }\n`;\n\nexport const Button = styled.button`\n  background-color: ${({ success, theme: { colors } }) =>\n    success ? \"#a2ff54\" : colors.main.yellow};\n  border: none;\n  padding: 10px;\n  width: 100%;\n  font-weight: 600;\n\n  border-radius: 100px;\n\n  box-shadow: 0 0 20px 2px rgba(0, 0, 0, 0.1);\n\n  margin-top: 20px;\n\n  color: ${({ theme: { colors } }) => colors.main.blue};\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: ${({ theme: { colors } }) => colors.white};\n  }\n\n  transition: all 0.3s ease-in-out;\n`;\n\nexport const ShowError = styled.p`\n  color: red;\n`;\n\nexport const ShowSuccess = styled.p`\n  color: green;\n  margin-top: 10px;\n  text-align: center;\n`;\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,mBAAnB;AAEA,OAAO,MAAMC,QAAQ,GAAGD,MAAM,CAACE,GAAI,EAA5B;AAEP,OAAO,MAAMC,UAAU,GAAGH,MAAM,CAACI,IAAK,EAA/B;AAEP,OAAO,MAAMC,SAAS,GAAGL,MAAM,CAACE,GAAI;AACpC;AACA,CAFO;AAIP,OAAO,MAAMI,WAAW,GAAGN,MAAM,CAACO,KAAM;AACxC;AACA;AACA;AACA;AACA,WAAW;EAAA,IAAC;IAAEC,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACC,IAAlC;AAAA,CAAuC;AAClD,CANO;AAQP,OAAO,MAAMC,WAAW,GAAGX,MAAM,CAACY,KAAM;AACxC;AACA,MAAM;EAAA,IAAC;IAAEC,KAAF;IAASL,KAAK,EAAE;MAAEC;IAAF;EAAhB,CAAD;EAAA,OAAmCI,KAAK,GAAGJ,MAAM,CAACK,IAAP,CAAYC,GAAf,GAAqBN,MAAM,CAACC,IAApE;AAAA,CAA0E;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;EAAA,IAAC;IAAEF,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACK,IAAP,CAAYE,IAAvC;AAAA,CAA4C;AACpE;AACA,CAZO;AAcP,OAAO,MAAMC,MAAM,GAAGjB,MAAM,CAACkB,MAAO;AACpC,sBAAsB;EAAA,IAAC;IAAEC,OAAF;IAAWX,KAAK,EAAE;MAAEC;IAAF;EAAlB,CAAD;EAAA,OAClBU,OAAO,GAAG,SAAH,GAAeV,MAAM,CAACK,IAAP,CAAYM,MADhB;AAAA,CACuB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;EAAA,IAAC;IAAEZ,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACK,IAAP,CAAYE,IAAvC;AAAA,CAA4C;AACvD;AACA;AACA,wBAAwB;EAAA,IAAC;IAAER,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACK,IAAP,CAAYE,IAAvC;AAAA,CAA4C;AACpE,aAAa;EAAA,IAAC;IAAER,KAAK,EAAE;MAAEC;IAAF;EAAT,CAAD;EAAA,OAA2BA,MAAM,CAACY,KAAlC;AAAA,CAAwC;AACrD;AACA;AACA;AACA,CAtBO;AAwBP,OAAO,MAAMC,SAAS,GAAGtB,MAAM,CAACuB,CAAE;AAClC;AACA,CAFO;AAIP,OAAO,MAAMC,WAAW,GAAGxB,MAAM,CAACuB,CAAE;AACpC;AACA;AACA;AACA,CAJO"}, "metadata": {}, "sourceType": "module"}