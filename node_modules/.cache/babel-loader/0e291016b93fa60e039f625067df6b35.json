{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/index.jsx\",\n    _s = $RefreshSig$();\n\nimport React from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport AppLogo from \"../../logo\";\nimport { X } from \"react-feather\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst MobileMenu = _ref => {\n  _s();\n\n  let {\n    active,\n    handleMobileMenu\n  } = _ref;\n  const {\n    pathname\n  } = useLocation();\n  console.log({\n    active\n  });\n  return /*#__PURE__*/_jsxDEV(Styles.Header, {\n    active: active,\n    children: /*#__PURE__*/_jsxDEV(Styles.Container, {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(Styles.MobileMenuContainer, {\n        children: /*#__PURE__*/_jsxDEV(X, {\n          onClick: handleMobileMenu\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Styles.Wtapper, {\n        children: [/*#__PURE__*/_jsxDEV(AppLogo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Styles.MenuContainer, {\n          children: /*#__PURE__*/_jsxDEV(Styles.MenuListWrapper, {\n            children: [/*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/about\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                children: \"About\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/projects\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/projects\",\n                children: \"Projects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/clients\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/clients\",\n                children: \"Clients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Styles.MenuItem, {\n              active: pathname === \"/contactus\",\n              className: \"button\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contactus\",\n                children: \"Contact us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n\n_s(MobileMenu, \"qVMqkCpYCjknUqSjfMln5RFSkbo=\", false, function () {\n  return [useLocation];\n});\n\n_c = MobileMenu;\nexport default MobileMenu;\n\nvar _c;\n\n$RefreshReg$(_c, \"MobileMenu\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "AppLogo", "X", "Styles", "MobileMenu", "active", "handleMobileMenu", "pathname", "console", "log"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/index.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport AppLogo from \"../../logo\";\n\nimport { X } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst MobileMenu = ({ active, handleMobileMenu }) => {\n  const { pathname } = useLocation();\n\n  console.log({ active });\n\n  return (\n    <Styles.Header active={active}>\n      <Styles.Container className=\"container\">\n        <Styles.MobileMenuContainer>\n          <X onClick={handleMobileMenu} />\n        </Styles.MobileMenuContainer>\n        <Styles.Wtapper>\n          <AppLogo />\n          <Styles.MenuContainer>\n            <Styles.MenuListWrapper>\n              <Styles.MenuItem active={pathname === \"/\"}>\n                <Link to=\"/\">Home</Link>\n              </Styles.MenuItem>\n              <Styles.MenuItem active={pathname === \"/about\"}>\n                <Link to=\"/about\">About</Link>\n              </Styles.MenuItem>\n              <Styles.MenuItem active={pathname === \"/projects\"}>\n                <Link to=\"/projects\">Projects</Link>\n              </Styles.MenuItem>\n              <Styles.MenuItem active={pathname === \"/clients\"}>\n                <Link to=\"/clients\">Clients</Link>\n              </Styles.MenuItem>\n              <Styles.MenuItem\n                active={pathname === \"/contactus\"}\n                className=\"button\"\n              >\n                <Link to=\"/contactus\">Contact us</Link>\n              </Styles.MenuItem>\n            </Styles.MenuListWrapper>\n          </Styles.MenuContainer>\n        </Styles.Wtapper>\n      </Styles.Container>\n    </Styles.Header>\n  );\n};\n\nexport default MobileMenu;\n"], "mappings": ";;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SAASC,IAAT,EAAeC,WAAf,QAAkC,kBAAlC;AACA,OAAOC,OAAP,MAAoB,YAApB;AAEA,SAASC,CAAT,QAAkB,eAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,UAAU,GAAG,QAAkC;EAAA;;EAAA,IAAjC;IAAEC,MAAF;IAAUC;EAAV,CAAiC;EACnD,MAAM;IAAEC;EAAF,IAAeP,WAAW,EAAhC;EAEAQ,OAAO,CAACC,GAAR,CAAY;IAAEJ;EAAF,CAAZ;EAEA,oBACE,QAAC,MAAD,CAAQ,MAAR;IAAe,MAAM,EAAEA,MAAvB;IAAA,uBACE,QAAC,MAAD,CAAQ,SAAR;MAAkB,SAAS,EAAC,WAA5B;MAAA,wBACE,QAAC,MAAD,CAAQ,mBAAR;QAAA,uBACE,QAAC,CAAD;UAAG,OAAO,EAAEC;QAAZ;UAAA;UAAA;UAAA;QAAA;MADF;QAAA;QAAA;QAAA;MAAA,QADF,eAIE,QAAC,MAAD,CAAQ,OAAR;QAAA,wBACE,QAAC,OAAD;UAAA;UAAA;UAAA;QAAA,QADF,eAEE,QAAC,MAAD,CAAQ,aAAR;UAAA,uBACE,QAAC,MAAD,CAAQ,eAAR;YAAA,wBACE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEC,QAAQ,KAAK,GAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,GAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QADF,eAIE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEA,QAAQ,KAAK,QAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,QAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QAJF,eAOE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEA,QAAQ,KAAK,WAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,WAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QAPF,eAUE,QAAC,MAAD,CAAQ,QAAR;cAAiB,MAAM,EAAEA,QAAQ,KAAK,UAAtC;cAAA,uBACE,QAAC,IAAD;gBAAM,EAAE,EAAC,UAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YADF;cAAA;cAAA;cAAA;YAAA,QAVF,eAaE,QAAC,MAAD,CAAQ,QAAR;cACE,MAAM,EAAEA,QAAQ,KAAK,YADvB;cAEE,SAAS,EAAC,QAFZ;cAAA,uBAIE,QAAC,IAAD;gBAAM,EAAE,EAAC,YAAT;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;YAJF;cAAA;cAAA;cAAA;YAAA,QAbF;UAAA;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAFF;MAAA;QAAA;QAAA;QAAA;MAAA,QAJF;IAAA;MAAA;MAAA;MAAA;IAAA;EADF;IAAA;IAAA;IAAA;EAAA,QADF;AAkCD,CAvCD;;GAAMH,U;UACiBJ,W;;;KADjBI,U;AAyCN,eAAeA,UAAf"}, "metadata": {}, "sourceType": "module"}