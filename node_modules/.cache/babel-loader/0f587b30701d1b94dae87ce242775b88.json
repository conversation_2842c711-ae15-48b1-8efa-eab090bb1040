{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/index.jsx\";\nimport React from \"react\";\nimport { ServicesData } from \"../servicesData\";\nimport ServicesCard from \"../card\";\nimport ServiceItem from \"../../projects/project-details/service-item\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst ServicesList = () => {\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: ServicesData.map((item, key) => {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(ServiceItem, {\n          title: item === null || item === void 0 ? void 0 : item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 13\n        }, this)\n      }, void 0, false);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n\n_c = ServicesList;\nexport default ServicesList;\n\nvar _c;\n\n$RefreshReg$(_c, \"ServicesList\");", "map": {"version": 3, "names": ["React", "ServicesData", "ServicesCard", "ServiceItem", "Styles", "ServicesList", "map", "item", "key", "name"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/index.jsx"], "sourcesContent": ["import React from \"react\";\n\nimport { ServicesData } from \"../servicesData\";\n\nimport ServicesCard from \"../card\";\n\nimport ServiceItem from \"../../projects/project-details/service-item\";\n\nimport * as Styles from \"./styles\";\n\nconst ServicesList = () => {\n  return (\n    <Styles.Container>\n      {ServicesData.map((item, key) => {\n        return (\n          <>\n            <ServiceItem title={item?.name} />\n            {/* <ServicesCard\n                            key={key}\n                            item={item}\n                        /> */}\n          </>\n        );\n      })}\n    </Styles.Container>\n  );\n};\n\nexport default ServicesList;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,YAAT,QAA6B,iBAA7B;AAEA,OAAOC,YAAP,MAAyB,SAAzB;AAEA,OAAOC,WAAP,MAAwB,6CAAxB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;;AAEA,MAAMC,YAAY,GAAG,MAAM;EACzB,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAA,UACGJ,YAAY,CAACK,GAAb,CAAiB,CAACC,IAAD,EAAOC,GAAP,KAAe;MAC/B,oBACE;QAAA,uBACE,QAAC,WAAD;UAAa,KAAK,EAAED,IAAF,aAAEA,IAAF,uBAAEA,IAAI,CAAEE;QAA1B;UAAA;UAAA;UAAA;QAAA;MADF,iBADF;IASD,CAVA;EADH;IAAA;IAAA;IAAA;EAAA,QADF;AAeD,CAhBD;;KAAMJ,Y;AAkBN,eAAeA,YAAf"}, "metadata": {}, "sourceType": "module"}