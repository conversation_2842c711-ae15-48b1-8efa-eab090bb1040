{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.min.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/scheduler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.min.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCC,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,mCAAD,CAAxB;AACD,CAFD,MAEO;EACLF,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,gCAAD,CAAxB;AACD"}, "metadata": {}, "sourceType": "script"}