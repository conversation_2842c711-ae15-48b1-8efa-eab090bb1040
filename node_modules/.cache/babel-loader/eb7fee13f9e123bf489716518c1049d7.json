{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: !0\n});\n\nvar React = require(\"react\"),\n    clones_1 = require(\"./utils/clones\"),\n    dots_1 = require(\"./utils/dots\"),\n    common_1 = require(\"./utils/common\"),\n    Dots = function (_a) {\n  var props = _a.props,\n      state = _a.state,\n      goToSlide = _a.goToSlide,\n      getState = _a.getState,\n      showDots = props.showDots,\n      customDot = props.customDot,\n      dotListClass = props.dotListClass,\n      infinite = props.infinite,\n      children = props.children;\n  if (!showDots || common_1.notEnoughChildren(state)) return null;\n  var numberOfDotsToShow,\n      currentSlide = state.currentSlide,\n      slidesToShow = state.slidesToShow,\n      slidesToSlide = common_1.getSlidesToSlide(state, props),\n      childrenArr = React.Children.toArray(children);\n  numberOfDotsToShow = infinite ? Math.ceil(childrenArr.length / slidesToSlide) : Math.ceil((childrenArr.length - slidesToShow) / slidesToSlide) + 1;\n  var nextSlidesTable = dots_1.getLookupTableForNextSlides(numberOfDotsToShow, state, props, childrenArr),\n      lookupTable = clones_1.getOriginalIndexLookupTableByClones(slidesToShow, childrenArr),\n      currentSlides = lookupTable[currentSlide];\n  return React.createElement(\"ul\", {\n    className: \"react-multi-carousel-dot-list \" + dotListClass\n  }, Array(numberOfDotsToShow).fill(0).map(function (_, index) {\n    var isActive, nextSlide;\n\n    if (infinite) {\n      nextSlide = nextSlidesTable[index];\n      var cloneIndex = lookupTable[nextSlide];\n      isActive = currentSlides === cloneIndex || cloneIndex <= currentSlides && currentSlides < cloneIndex + slidesToSlide;\n    } else {\n      var maximumNextSlide = childrenArr.length - slidesToShow,\n          possibileNextSlides = index * slidesToSlide;\n      isActive = (nextSlide = maximumNextSlide < possibileNextSlides ? maximumNextSlide : possibileNextSlides) === currentSlide || nextSlide < currentSlide && currentSlide < nextSlide + slidesToSlide && currentSlide < childrenArr.length - slidesToShow;\n    }\n\n    return customDot ? React.cloneElement(customDot, {\n      index: index,\n      active: isActive,\n      key: index,\n      onClick: function () {\n        return goToSlide(nextSlide);\n      },\n      carouselState: getState()\n    }) : React.createElement(\"li\", {\n      \"data-index\": index,\n      key: index,\n      className: \"react-multi-carousel-dot \" + (isActive ? \"react-multi-carousel-dot--active\" : \"\")\n    }, React.createElement(\"button\", {\n      \"aria-label\": \"Go to slide \" + (index + 1),\n      onClick: function () {\n        return goToSlide(nextSlide);\n      }\n    }));\n  }));\n};\n\nexports.default = Dots;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "React", "require", "clones_1", "dots_1", "common_1", "Dots", "_a", "props", "state", "goToSlide", "getState", "showDots", "customDot", "dotListClass", "infinite", "children", "notEnoughChildren", "numberOfDotsToShow", "currentSlide", "slidesToShow", "slidesToSlide", "getSlidesToSlide", "childrenArr", "Children", "toArray", "Math", "ceil", "length", "nextSlidesTable", "getLookupTableForNextSlides", "lookupTable", "getOriginalIndexLookupTableByClones", "currentSlides", "createElement", "className", "Array", "fill", "map", "_", "index", "isActive", "nextSlide", "cloneIndex", "maximumNextSlide", "possibileNextSlides", "cloneElement", "active", "key", "onClick", "carouselState", "default"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/react-multi-carousel/lib/Dots.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:!0});var React=require(\"react\"),clones_1=require(\"./utils/clones\"),dots_1=require(\"./utils/dots\"),common_1=require(\"./utils/common\"),Dots=function(_a){var props=_a.props,state=_a.state,goToSlide=_a.goToSlide,getState=_a.getState,showDots=props.showDots,customDot=props.customDot,dotListClass=props.dotListClass,infinite=props.infinite,children=props.children;if(!showDots||common_1.notEnoughChildren(state))return null;var numberOfDotsToShow,currentSlide=state.currentSlide,slidesToShow=state.slidesToShow,slidesToSlide=common_1.getSlidesToSlide(state,props),childrenArr=React.Children.toArray(children);numberOfDotsToShow=infinite?Math.ceil(childrenArr.length/slidesToSlide):Math.ceil((childrenArr.length-slidesToShow)/slidesToSlide)+1;var nextSlidesTable=dots_1.getLookupTableForNextSlides(numberOfDotsToShow,state,props,childrenArr),lookupTable=clones_1.getOriginalIndexLookupTableByClones(slidesToShow,childrenArr),currentSlides=lookupTable[currentSlide];return React.createElement(\"ul\",{className:\"react-multi-carousel-dot-list \"+dotListClass},Array(numberOfDotsToShow).fill(0).map(function(_,index){var isActive,nextSlide;if(infinite){nextSlide=nextSlidesTable[index];var cloneIndex=lookupTable[nextSlide];isActive=currentSlides===cloneIndex||cloneIndex<=currentSlides&&currentSlides<cloneIndex+slidesToSlide}else{var maximumNextSlide=childrenArr.length-slidesToShow,possibileNextSlides=index*slidesToSlide;isActive=(nextSlide=maximumNextSlide<possibileNextSlides?maximumNextSlide:possibileNextSlides)===currentSlide||nextSlide<currentSlide&&currentSlide<nextSlide+slidesToSlide&&currentSlide<childrenArr.length-slidesToShow}return customDot?React.cloneElement(customDot,{index:index,active:isActive,key:index,onClick:function(){return goToSlide(nextSlide)},carouselState:getState()}):React.createElement(\"li\",{\"data-index\":index,key:index,className:\"react-multi-carousel-dot \"+(isActive?\"react-multi-carousel-dot--active\":\"\")},React.createElement(\"button\",{\"aria-label\":\"Go to slide \"+(index+1),onClick:function(){return goToSlide(nextSlide)}}))}))};exports.default=Dots;"], "mappings": "AAAA;;AAAaA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA8B,YAA9B,EAA2C;EAACC,KAAK,EAAC,CAAC;AAAR,CAA3C;;AAAuD,IAAIC,KAAK,GAACC,OAAO,CAAC,OAAD,CAAjB;AAAA,IAA2BC,QAAQ,GAACD,OAAO,CAAC,gBAAD,CAA3C;AAAA,IAA8DE,MAAM,GAACF,OAAO,CAAC,cAAD,CAA5E;AAAA,IAA6FG,QAAQ,GAACH,OAAO,CAAC,gBAAD,CAA7G;AAAA,IAAgII,IAAI,GAAC,UAASC,EAAT,EAAY;EAAC,IAAIC,KAAK,GAACD,EAAE,CAACC,KAAb;EAAA,IAAmBC,KAAK,GAACF,EAAE,CAACE,KAA5B;EAAA,IAAkCC,SAAS,GAACH,EAAE,CAACG,SAA/C;EAAA,IAAyDC,QAAQ,GAACJ,EAAE,CAACI,QAArE;EAAA,IAA8EC,QAAQ,GAACJ,KAAK,CAACI,QAA7F;EAAA,IAAsGC,SAAS,GAACL,KAAK,CAACK,SAAtH;EAAA,IAAgIC,YAAY,GAACN,KAAK,CAACM,YAAnJ;EAAA,IAAgKC,QAAQ,GAACP,KAAK,CAACO,QAA/K;EAAA,IAAwLC,QAAQ,GAACR,KAAK,CAACQ,QAAvM;EAAgN,IAAG,CAACJ,QAAD,IAAWP,QAAQ,CAACY,iBAAT,CAA2BR,KAA3B,CAAd,EAAgD,OAAO,IAAP;EAAY,IAAIS,kBAAJ;EAAA,IAAuBC,YAAY,GAACV,KAAK,CAACU,YAA1C;EAAA,IAAuDC,YAAY,GAACX,KAAK,CAACW,YAA1E;EAAA,IAAuFC,aAAa,GAAChB,QAAQ,CAACiB,gBAAT,CAA0Bb,KAA1B,EAAgCD,KAAhC,CAArG;EAAA,IAA4Ie,WAAW,GAACtB,KAAK,CAACuB,QAAN,CAAeC,OAAf,CAAuBT,QAAvB,CAAxJ;EAAyLE,kBAAkB,GAACH,QAAQ,GAACW,IAAI,CAACC,IAAL,CAAUJ,WAAW,CAACK,MAAZ,GAAmBP,aAA7B,CAAD,GAA6CK,IAAI,CAACC,IAAL,CAAU,CAACJ,WAAW,CAACK,MAAZ,GAAmBR,YAApB,IAAkCC,aAA5C,IAA2D,CAAnI;EAAqI,IAAIQ,eAAe,GAACzB,MAAM,CAAC0B,2BAAP,CAAmCZ,kBAAnC,EAAsDT,KAAtD,EAA4DD,KAA5D,EAAkEe,WAAlE,CAApB;EAAA,IAAmGQ,WAAW,GAAC5B,QAAQ,CAAC6B,mCAAT,CAA6CZ,YAA7C,EAA0DG,WAA1D,CAA/G;EAAA,IAAsLU,aAAa,GAACF,WAAW,CAACZ,YAAD,CAA/M;EAA8N,OAAOlB,KAAK,CAACiC,aAAN,CAAoB,IAApB,EAAyB;IAACC,SAAS,EAAC,mCAAiCrB;EAA5C,CAAzB,EAAmFsB,KAAK,CAAClB,kBAAD,CAAL,CAA0BmB,IAA1B,CAA+B,CAA/B,EAAkCC,GAAlC,CAAsC,UAASC,CAAT,EAAWC,KAAX,EAAiB;IAAC,IAAIC,QAAJ,EAAaC,SAAb;;IAAuB,IAAG3B,QAAH,EAAY;MAAC2B,SAAS,GAACb,eAAe,CAACW,KAAD,CAAzB;MAAiC,IAAIG,UAAU,GAACZ,WAAW,CAACW,SAAD,CAA1B;MAAsCD,QAAQ,GAACR,aAAa,KAAGU,UAAhB,IAA4BA,UAAU,IAAEV,aAAZ,IAA2BA,aAAa,GAACU,UAAU,GAACtB,aAAzF;IAAuG,CAA3L,MAA+L;MAAC,IAAIuB,gBAAgB,GAACrB,WAAW,CAACK,MAAZ,GAAmBR,YAAxC;MAAA,IAAqDyB,mBAAmB,GAACL,KAAK,GAACnB,aAA/E;MAA6FoB,QAAQ,GAAC,CAACC,SAAS,GAACE,gBAAgB,GAACC,mBAAjB,GAAqCD,gBAArC,GAAsDC,mBAAjE,MAAwF1B,YAAxF,IAAsGuB,SAAS,GAACvB,YAAV,IAAwBA,YAAY,GAACuB,SAAS,GAACrB,aAA/C,IAA8DF,YAAY,GAACI,WAAW,CAACK,MAAZ,GAAmBR,YAA7M;IAA0N;;IAAA,OAAOP,SAAS,GAACZ,KAAK,CAAC6C,YAAN,CAAmBjC,SAAnB,EAA6B;MAAC2B,KAAK,EAACA,KAAP;MAAaO,MAAM,EAACN,QAApB;MAA6BO,GAAG,EAACR,KAAjC;MAAuCS,OAAO,EAAC,YAAU;QAAC,OAAOvC,SAAS,CAACgC,SAAD,CAAhB;MAA4B,CAAtF;MAAuFQ,aAAa,EAACvC,QAAQ;IAA7G,CAA7B,CAAD,GAAgJV,KAAK,CAACiC,aAAN,CAAoB,IAApB,EAAyB;MAAC,cAAaM,KAAd;MAAoBQ,GAAG,EAACR,KAAxB;MAA8BL,SAAS,EAAC,+BAA6BM,QAAQ,GAAC,kCAAD,GAAoC,EAAzE;IAAxC,CAAzB,EAA+IxC,KAAK,CAACiC,aAAN,CAAoB,QAApB,EAA6B;MAAC,cAAa,kBAAgBM,KAAK,GAAC,CAAtB,CAAd;MAAuCS,OAAO,EAAC,YAAU;QAAC,OAAOvC,SAAS,CAACgC,SAAD,CAAhB;MAA4B;IAAtF,CAA7B,CAA/I,CAAhK;EAAsa,CAA5+B,CAAnF,CAAP;AAAykC,CAAngE;;AAAogE3C,OAAO,CAACoD,OAAR,GAAgB7C,IAAhB"}, "metadata": {}, "sourceType": "script"}