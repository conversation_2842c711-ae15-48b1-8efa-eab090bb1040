{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/index.jsx\";\nimport React from 'react';\nimport * as Styles from './styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n\nconst SeactionHeading = _ref => {\n  let {\n    title\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    children: title\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n\n_c = SeactionHeading;\nexport default SeactionHeading;\n\nvar _c;\n\n$RefreshReg$(_c, \"SeactionHeading\");", "map": {"version": 3, "names": ["React", "Styles", "SeactionHeading", "title"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/index.jsx"], "sourcesContent": ["import React from 'react'\n\nimport * as Styles from './styles';\n\nconst SeactionHeading = ({ title }) => {\n    return (\n        <Styles.Container>\n            {title}\n        </Styles.Container>\n    )\n}\n\nexport default SeactionHeading"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;AAEA,MAAMC,eAAe,GAAG,QAAe;EAAA,IAAd;IAAEC;EAAF,CAAc;EACnC,oBACI,QAAC,MAAD,CAAQ,SAAR;IAAA,UACKA;EADL;IAAA;IAAA;IAAA;EAAA,QADJ;AAKH,CAND;;KAAMD,e;AAQN,eAAeA,eAAf"}, "metadata": {}, "sourceType": "module"}