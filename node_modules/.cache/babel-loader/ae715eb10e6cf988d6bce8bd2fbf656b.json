{"ast": null, "code": "var parent = require('../es/global-this');\n\nmodule.exports = parent;", "map": {"version": 3, "names": ["parent", "require", "module", "exports"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/stable/global-this.js"], "sourcesContent": ["var parent = require('../es/global-this');\n\nmodule.exports = parent;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,mBAAD,CAApB;;AAEAC,MAAM,CAACC,OAAP,GAAiBH,MAAjB"}, "metadata": {}, "sourceType": "script"}