{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/index.jsx\",\n    _s = $RefreshSig$();\n\nimport React from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport { AssetsList } from \"../../elements/assetsList\";\nimport Button from \"../../elements/button\";\nimport PageDimensions from \"../../../styles/pageDimensions\";\nimport * as Styles from \"./styles\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst BgTitle = _ref => {\n  _s();\n\n  let {\n    routes\n  } = _ref;\n  const {\n    pathname\n  } = useLocation();\n  const {\n    width\n  } = PageDimensions();\n  const isMobile = width < 659;\n  const isHome = pathname === \"/\";\n  const isAbout = pathname === \"/about\";\n  const isProjects = pathname === \"/projects\";\n  const isClients = pathname === \"/clients\";\n  const isContact = pathname === \"/contactus\";\n\n  if (pathname.includes(\"/projects/\") && pathname.length > \"/projects/\".length) {\n    return null;\n  }\n\n  const {\n    name\n  } = routes.find(item => item.path === pathname);\n  const {\n    home,\n    about,\n    projects,\n    clients,\n    contact\n  } = AssetsList.titleBg;\n  const currentBg = isHome ? home : isAbout ? about : isProjects ? projects : isClients ? clients : isContact ? contact : home;\n  return /*#__PURE__*/_jsxDEV(Styles.Container, {\n    img: currentBg,\n    small: true,\n    children: [/*#__PURE__*/_jsxDEV(Styles.Overlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Styles.Wrapper, {\n      className: \"px-sm-5\",\n      children: /*#__PURE__*/_jsxDEV(Styles.Holder, {\n        className: pathname === \"/contactus\" ? `py-sm-1` : `py-sm-5`,\n        children: [/*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(Styles.Title, {\n            className: \"pt-2 pb-2\",\n            children: \"We Provide Best Retrofitting Structural Strengthening Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false), !isContact ? /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/contactus\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"my-3\",\n            children: \"Contact us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n\n_s(BgTitle, \"qVMqkCpYCjknUqSjfMln5RFSkbo=\", false, function () {\n  return [useLocation];\n});\n\n_c = BgTitle;\nexport default BgTitle;\n\nvar _c;\n\n$RefreshReg$(_c, \"BgTitle\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "AssetsList", "<PERSON><PERSON>", "PageDimensions", "Styles", "BgTitle", "routes", "pathname", "width", "isMobile", "isHome", "isAbout", "isProjects", "isClients", "isContact", "includes", "length", "name", "find", "item", "path", "home", "about", "projects", "clients", "contact", "titleBg", "currentBg"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/index.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport { AssetsList } from \"../../elements/assetsList\";\n\nimport Button from \"../../elements/button\";\n\nimport PageDimensions from \"../../../styles/pageDimensions\";\n\nimport * as Styles from \"./styles\";\n\nconst BgTitle = ({ routes }) => {\n  const { pathname } = useLocation();\n\n  const { width } = PageDimensions();\n\n  const isMobile = width < 659;\n\n  const isHome = pathname === \"/\";\n  const isAbout = pathname === \"/about\";\n  const isProjects = pathname === \"/projects\";\n  const isClients = pathname === \"/clients\";\n  const isContact = pathname === \"/contactus\";\n\n  if (\n    pathname.includes(\"/projects/\") &&\n    pathname.length > \"/projects/\".length\n  ) {\n    return null;\n  }\n\n  const { name } = routes.find((item) => item.path === pathname);\n\n  const { home, about, projects, clients, contact } = AssetsList.titleBg;\n\n  const currentBg = isHome\n    ? home\n    : isAbout\n    ? about\n    : isProjects\n    ? projects\n    : isClients\n    ? clients\n    : isContact\n    ? contact\n    : home;\n\n\n  return (\n    <Styles.Container img={currentBg} small={true}>\n      <Styles.Overlay />\n      <Styles.Wrapper className=\"px-sm-5\">\n        <Styles.Holder\n          className={pathname === \"/contactus\" ? `py-sm-1` : `py-sm-5`}\n        >\n          {/* <Styles.Title className=\"pt-4 pb-1\">{name}</Styles.Title> */}\n\n          <>\n            <Styles.Title className=\"pt-2 pb-2\">\n              We Provide Best Retrofitting Structural Strengthening Services\n            </Styles.Title>\n            {/* <Styles.SubTitle>\n                The largest real estate hub with various services\n              </Styles.SubTitle> */}\n          </>\n          {!isContact ? (\n            <Link to=\"/contactus\">\n              <Button className=\"my-3\">Contact us</Button>\n            </Link>\n          ) : null}\n        </Styles.Holder>\n      </Styles.Wrapper>\n    </Styles.Container>\n  );\n};\n\nexport default BgTitle;\n"], "mappings": ";;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SAASC,IAAT,EAAeC,WAAf,QAAkC,kBAAlC;AACA,SAASC,UAAT,QAA2B,2BAA3B;AAEA,OAAOC,MAAP,MAAmB,uBAAnB;AAEA,OAAOC,cAAP,MAA2B,gCAA3B;AAEA,OAAO,KAAKC,MAAZ,MAAwB,UAAxB;;;;AAEA,MAAMC,OAAO,GAAG,QAAgB;EAAA;;EAAA,IAAf;IAAEC;EAAF,CAAe;EAC9B,MAAM;IAAEC;EAAF,IAAeP,WAAW,EAAhC;EAEA,MAAM;IAAEQ;EAAF,IAAYL,cAAc,EAAhC;EAEA,MAAMM,QAAQ,GAAGD,KAAK,GAAG,GAAzB;EAEA,MAAME,MAAM,GAAGH,QAAQ,KAAK,GAA5B;EACA,MAAMI,OAAO,GAAGJ,QAAQ,KAAK,QAA7B;EACA,MAAMK,UAAU,GAAGL,QAAQ,KAAK,WAAhC;EACA,MAAMM,SAAS,GAAGN,QAAQ,KAAK,UAA/B;EACA,MAAMO,SAAS,GAAGP,QAAQ,KAAK,YAA/B;;EAEA,IACEA,QAAQ,CAACQ,QAAT,CAAkB,YAAlB,KACAR,QAAQ,CAACS,MAAT,GAAkB,aAAaA,MAFjC,EAGE;IACA,OAAO,IAAP;EACD;;EAED,MAAM;IAAEC;EAAF,IAAWX,MAAM,CAACY,IAAP,CAAaC,IAAD,IAAUA,IAAI,CAACC,IAAL,KAAcb,QAApC,CAAjB;EAEA,MAAM;IAAEc,IAAF;IAAQC,KAAR;IAAeC,QAAf;IAAyBC,OAAzB;IAAkCC;EAAlC,IAA8CxB,UAAU,CAACyB,OAA/D;EAEA,MAAMC,SAAS,GAAGjB,MAAM,GACpBW,IADoB,GAEpBV,OAAO,GACPW,KADO,GAEPV,UAAU,GACVW,QADU,GAEVV,SAAS,GACTW,OADS,GAETV,SAAS,GACTW,OADS,GAETJ,IAVJ;EAaA,oBACE,QAAC,MAAD,CAAQ,SAAR;IAAkB,GAAG,EAAEM,SAAvB;IAAkC,KAAK,EAAE,IAAzC;IAAA,wBACE,QAAC,MAAD,CAAQ,OAAR;MAAA;MAAA;MAAA;IAAA,QADF,eAEE,QAAC,MAAD,CAAQ,OAAR;MAAgB,SAAS,EAAC,SAA1B;MAAA,uBACE,QAAC,MAAD,CAAQ,MAAR;QACE,SAAS,EAAEpB,QAAQ,KAAK,YAAb,GAA6B,SAA7B,GAAyC,SADtD;QAAA,wBAKE;UAAA,uBACE,QAAC,MAAD,CAAQ,KAAR;YAAc,SAAS,EAAC,WAAxB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA;QADF,iBALF,EAaG,CAACO,SAAD,gBACC,QAAC,IAAD;UAAM,EAAE,EAAC,YAAT;UAAA,uBACE,QAAC,MAAD;YAAQ,SAAS,EAAC,MAAlB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QADD,GAIG,IAjBN;MAAA;QAAA;QAAA;QAAA;MAAA;IADF;MAAA;MAAA;MAAA;IAAA,QAFF;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AA0BD,CA/DD;;GAAMT,O;UACiBL,W;;;KADjBK,O;AAiEN,eAAeA,OAAf"}, "metadata": {}, "sourceType": "module"}