{"ast": null, "code": "/* global __resourceQuery, __webpack_hash__ */\n/// <reference types=\"webpack/module\" />\nimport webpackHotLog from \"webpack/hot/log.js\";\nimport stripAnsi from \"./utils/stripAnsi.js\";\nimport parseURL from \"./utils/parseURL.js\";\nimport socket from \"./socket.js\";\nimport { formatProblem, show, hide } from \"./overlay.js\";\nimport { log, setLogLevel } from \"./utils/log.js\";\nimport sendMessage from \"./utils/sendMessage.js\";\nimport reloadApp from \"./utils/reloadApp.js\";\nimport createSocketURL from \"./utils/createSocketURL.js\";\n/**\n * @typedef {Object} Options\n * @property {boolean} hot\n * @property {boolean} liveReload\n * @property {boolean} progress\n * @property {boolean | { warnings?: boolean, errors?: boolean, trustedTypesPolicyName?: string }} overlay\n * @property {string} [logging]\n * @property {number} [reconnect]\n */\n\n/**\n * @typedef {Object} Status\n * @property {boolean} isUnloading\n * @property {string} currentHash\n * @property {string} [previousHash]\n */\n\n/**\n * @type {Status}\n */\n\nvar status = {\n  isUnloading: false,\n  // TODO Workaround for webpack v4, `__webpack_hash__` is not replaced without HotModuleReplacement\n  // eslint-disable-next-line camelcase\n  currentHash: typeof __webpack_hash__ !== \"undefined\" ? __webpack_hash__ : \"\"\n};\n/** @type {Options} */\n\nvar options = {\n  hot: false,\n  liveReload: false,\n  progress: false,\n  overlay: false\n};\nvar parsedResourceQuery = parseURL(__resourceQuery);\n\nif (parsedResourceQuery.hot === \"true\") {\n  options.hot = true;\n  log.info(\"Hot Module Replacement enabled.\");\n}\n\nif (parsedResourceQuery[\"live-reload\"] === \"true\") {\n  options.liveReload = true;\n  log.info(\"Live Reloading enabled.\");\n}\n\nif (parsedResourceQuery.logging) {\n  options.logging = parsedResourceQuery.logging;\n}\n\nif (typeof parsedResourceQuery.reconnect !== \"undefined\") {\n  options.reconnect = Number(parsedResourceQuery.reconnect);\n}\n/**\n * @param {string} level\n */\n\n\nfunction setAllLogLevel(level) {\n  // This is needed because the HMR logger operate separately from dev server logger\n  webpackHotLog.setLogLevel(level === \"verbose\" || level === \"log\" ? \"info\" : level);\n  setLogLevel(level);\n}\n\nif (options.logging) {\n  setAllLogLevel(options.logging);\n}\n\nself.addEventListener(\"beforeunload\", function () {\n  status.isUnloading = true;\n});\nvar onSocketMessage = {\n  hot: function hot() {\n    if (parsedResourceQuery.hot === \"false\") {\n      return;\n    }\n\n    options.hot = true;\n    log.info(\"Hot Module Replacement enabled.\");\n  },\n  liveReload: function liveReload() {\n    if (parsedResourceQuery[\"live-reload\"] === \"false\") {\n      return;\n    }\n\n    options.liveReload = true;\n    log.info(\"Live Reloading enabled.\");\n  },\n  invalid: function invalid() {\n    log.info(\"App updated. Recompiling...\"); // Fixes #1042. overlay doesn't clear if errors are fixed but warnings remain.\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"Invalid\");\n  },\n\n  /**\n   * @param {string} hash\n   */\n  hash: function hash(_hash) {\n    status.previousHash = status.currentHash;\n    status.currentHash = _hash;\n  },\n  logging: setAllLogLevel,\n\n  /**\n   * @param {boolean} value\n   */\n  overlay: function overlay(value) {\n    if (typeof document === \"undefined\") {\n      return;\n    }\n\n    options.overlay = value;\n  },\n\n  /**\n   * @param {number} value\n   */\n  reconnect: function reconnect(value) {\n    if (parsedResourceQuery.reconnect === \"false\") {\n      return;\n    }\n\n    options.reconnect = value;\n  },\n\n  /**\n   * @param {boolean} value\n   */\n  progress: function progress(value) {\n    options.progress = value;\n  },\n\n  /**\n   * @param {{ pluginName?: string, percent: number, msg: string }} data\n   */\n  \"progress-update\": function progressUpdate(data) {\n    if (options.progress) {\n      log.info(\"\".concat(data.pluginName ? \"[\".concat(data.pluginName, \"] \") : \"\").concat(data.percent, \"% - \").concat(data.msg, \".\"));\n    }\n\n    sendMessage(\"Progress\", data);\n  },\n  \"still-ok\": function stillOk() {\n    log.info(\"Nothing changed.\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"StillOk\");\n  },\n  ok: function ok() {\n    sendMessage(\"Ok\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    reloadApp(options, status);\n  },\n  // TODO: remove in v5 in favor of 'static-changed'\n\n  /**\n   * @param {string} file\n   */\n  \"content-changed\": function contentChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n\n  /**\n   * @param {string} file\n   */\n  \"static-changed\": function staticChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n\n  /**\n   * @param {Error[]} warnings\n   * @param {any} params\n   */\n  warnings: function warnings(_warnings, params) {\n    log.warn(\"Warnings while compiling.\");\n\n    var printableWarnings = _warnings.map(function (error) {\n      var _formatProblem = formatProblem(\"warning\", error),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n\n    sendMessage(\"Warnings\", printableWarnings);\n\n    for (var i = 0; i < printableWarnings.length; i++) {\n      log.warn(printableWarnings[i]);\n    }\n\n    var needShowOverlayForWarnings = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.warnings;\n\n    if (needShowOverlayForWarnings) {\n      var trustedTypesPolicyName = typeof options.overlay === \"object\" && options.overlay.trustedTypesPolicyName;\n      show(\"warning\", _warnings, trustedTypesPolicyName || null);\n    }\n\n    if (params && params.preventReloading) {\n      return;\n    }\n\n    reloadApp(options, status);\n  },\n\n  /**\n   * @param {Error[]} errors\n   */\n  errors: function errors(_errors) {\n    log.error(\"Errors while compiling. Reload prevented.\");\n\n    var printableErrors = _errors.map(function (error) {\n      var _formatProblem2 = formatProblem(\"error\", error),\n          header = _formatProblem2.header,\n          body = _formatProblem2.body;\n\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n\n    sendMessage(\"Errors\", printableErrors);\n\n    for (var i = 0; i < printableErrors.length; i++) {\n      log.error(printableErrors[i]);\n    }\n\n    var needShowOverlayForErrors = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.errors;\n\n    if (needShowOverlayForErrors) {\n      var trustedTypesPolicyName = typeof options.overlay === \"object\" && options.overlay.trustedTypesPolicyName;\n      show(\"error\", _errors, trustedTypesPolicyName || null);\n    }\n  },\n\n  /**\n   * @param {Error} error\n   */\n  error: function error(_error) {\n    log.error(_error);\n  },\n  close: function close() {\n    log.info(\"Disconnected!\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"Close\");\n  }\n};\nvar socketURL = createSocketURL(parsedResourceQuery);\nsocket(socketURL, onSocketMessage, options.reconnect);", "map": {"version": 3, "names": ["webpackHotLog", "stripAnsi", "parseURL", "socket", "formatProblem", "show", "hide", "log", "setLogLevel", "sendMessage", "reloadApp", "createSocketURL", "status", "isUnloading", "currentHash", "__webpack_hash__", "options", "hot", "liveReload", "progress", "overlay", "parsedResourceQuery", "__resourceQuery", "info", "logging", "reconnect", "Number", "setAllLogLevel", "level", "self", "addEventListener", "onSocketMessage", "invalid", "hash", "_hash", "previousHash", "value", "document", "progressUpdate", "data", "concat", "pluginName", "percent", "msg", "stillOk", "ok", "contentChanged", "file", "location", "reload", "staticChanged", "warnings", "_warnings", "params", "warn", "printableWarnings", "map", "error", "_formatProblem", "header", "body", "i", "length", "needShowOverlayForWarnings", "trustedTypesPolicyName", "preventReloading", "errors", "_errors", "printableErrors", "_formatProblem2", "needShowOverlayForErrors", "_error", "close", "socketURL"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/webpack-dev-server/client/index.js"], "sourcesContent": ["/* global __resourceQuery, __webpack_hash__ */\n/// <reference types=\"webpack/module\" />\nimport webpackHotLog from \"webpack/hot/log.js\";\nimport stripAnsi from \"./utils/stripAnsi.js\";\nimport parseURL from \"./utils/parseURL.js\";\nimport socket from \"./socket.js\";\nimport { formatProblem, show, hide } from \"./overlay.js\";\nimport { log, setLogLevel } from \"./utils/log.js\";\nimport sendMessage from \"./utils/sendMessage.js\";\nimport reloadApp from \"./utils/reloadApp.js\";\nimport createSocketURL from \"./utils/createSocketURL.js\";\n/**\n * @typedef {Object} Options\n * @property {boolean} hot\n * @property {boolean} liveReload\n * @property {boolean} progress\n * @property {boolean | { warnings?: boolean, errors?: boolean, trustedTypesPolicyName?: string }} overlay\n * @property {string} [logging]\n * @property {number} [reconnect]\n */\n\n/**\n * @typedef {Object} Status\n * @property {boolean} isUnloading\n * @property {string} currentHash\n * @property {string} [previousHash]\n */\n\n/**\n * @type {Status}\n */\n\nvar status = {\n  isUnloading: false,\n  // TODO Workaround for webpack v4, `__webpack_hash__` is not replaced without HotModuleReplacement\n  // eslint-disable-next-line camelcase\n  currentHash: typeof __webpack_hash__ !== \"undefined\" ? __webpack_hash__ : \"\"\n};\n/** @type {Options} */\n\nvar options = {\n  hot: false,\n  liveReload: false,\n  progress: false,\n  overlay: false\n};\nvar parsedResourceQuery = parseURL(__resourceQuery);\n\nif (parsedResourceQuery.hot === \"true\") {\n  options.hot = true;\n  log.info(\"Hot Module Replacement enabled.\");\n}\n\nif (parsedResourceQuery[\"live-reload\"] === \"true\") {\n  options.liveReload = true;\n  log.info(\"Live Reloading enabled.\");\n}\n\nif (parsedResourceQuery.logging) {\n  options.logging = parsedResourceQuery.logging;\n}\n\nif (typeof parsedResourceQuery.reconnect !== \"undefined\") {\n  options.reconnect = Number(parsedResourceQuery.reconnect);\n}\n/**\n * @param {string} level\n */\n\n\nfunction setAllLogLevel(level) {\n  // This is needed because the HMR logger operate separately from dev server logger\n  webpackHotLog.setLogLevel(level === \"verbose\" || level === \"log\" ? \"info\" : level);\n  setLogLevel(level);\n}\n\nif (options.logging) {\n  setAllLogLevel(options.logging);\n}\n\nself.addEventListener(\"beforeunload\", function () {\n  status.isUnloading = true;\n});\nvar onSocketMessage = {\n  hot: function hot() {\n    if (parsedResourceQuery.hot === \"false\") {\n      return;\n    }\n\n    options.hot = true;\n    log.info(\"Hot Module Replacement enabled.\");\n  },\n  liveReload: function liveReload() {\n    if (parsedResourceQuery[\"live-reload\"] === \"false\") {\n      return;\n    }\n\n    options.liveReload = true;\n    log.info(\"Live Reloading enabled.\");\n  },\n  invalid: function invalid() {\n    log.info(\"App updated. Recompiling...\"); // Fixes #1042. overlay doesn't clear if errors are fixed but warnings remain.\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"Invalid\");\n  },\n\n  /**\n   * @param {string} hash\n   */\n  hash: function hash(_hash) {\n    status.previousHash = status.currentHash;\n    status.currentHash = _hash;\n  },\n  logging: setAllLogLevel,\n\n  /**\n   * @param {boolean} value\n   */\n  overlay: function overlay(value) {\n    if (typeof document === \"undefined\") {\n      return;\n    }\n\n    options.overlay = value;\n  },\n\n  /**\n   * @param {number} value\n   */\n  reconnect: function reconnect(value) {\n    if (parsedResourceQuery.reconnect === \"false\") {\n      return;\n    }\n\n    options.reconnect = value;\n  },\n\n  /**\n   * @param {boolean} value\n   */\n  progress: function progress(value) {\n    options.progress = value;\n  },\n\n  /**\n   * @param {{ pluginName?: string, percent: number, msg: string }} data\n   */\n  \"progress-update\": function progressUpdate(data) {\n    if (options.progress) {\n      log.info(\"\".concat(data.pluginName ? \"[\".concat(data.pluginName, \"] \") : \"\").concat(data.percent, \"% - \").concat(data.msg, \".\"));\n    }\n\n    sendMessage(\"Progress\", data);\n  },\n  \"still-ok\": function stillOk() {\n    log.info(\"Nothing changed.\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"StillOk\");\n  },\n  ok: function ok() {\n    sendMessage(\"Ok\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    reloadApp(options, status);\n  },\n  // TODO: remove in v5 in favor of 'static-changed'\n\n  /**\n   * @param {string} file\n   */\n  \"content-changed\": function contentChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n\n  /**\n   * @param {string} file\n   */\n  \"static-changed\": function staticChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n\n  /**\n   * @param {Error[]} warnings\n   * @param {any} params\n   */\n  warnings: function warnings(_warnings, params) {\n    log.warn(\"Warnings while compiling.\");\n\n    var printableWarnings = _warnings.map(function (error) {\n      var _formatProblem = formatProblem(\"warning\", error),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n\n    sendMessage(\"Warnings\", printableWarnings);\n\n    for (var i = 0; i < printableWarnings.length; i++) {\n      log.warn(printableWarnings[i]);\n    }\n\n    var needShowOverlayForWarnings = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.warnings;\n\n    if (needShowOverlayForWarnings) {\n      var trustedTypesPolicyName = typeof options.overlay === \"object\" && options.overlay.trustedTypesPolicyName;\n      show(\"warning\", _warnings, trustedTypesPolicyName || null);\n    }\n\n    if (params && params.preventReloading) {\n      return;\n    }\n\n    reloadApp(options, status);\n  },\n\n  /**\n   * @param {Error[]} errors\n   */\n  errors: function errors(_errors) {\n    log.error(\"Errors while compiling. Reload prevented.\");\n\n    var printableErrors = _errors.map(function (error) {\n      var _formatProblem2 = formatProblem(\"error\", error),\n          header = _formatProblem2.header,\n          body = _formatProblem2.body;\n\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n\n    sendMessage(\"Errors\", printableErrors);\n\n    for (var i = 0; i < printableErrors.length; i++) {\n      log.error(printableErrors[i]);\n    }\n\n    var needShowOverlayForErrors = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.errors;\n\n    if (needShowOverlayForErrors) {\n      var trustedTypesPolicyName = typeof options.overlay === \"object\" && options.overlay.trustedTypesPolicyName;\n      show(\"error\", _errors, trustedTypesPolicyName || null);\n    }\n  },\n\n  /**\n   * @param {Error} error\n   */\n  error: function error(_error) {\n    log.error(_error);\n  },\n  close: function close() {\n    log.info(\"Disconnected!\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"Close\");\n  }\n};\nvar socketURL = createSocketURL(parsedResourceQuery);\nsocket(socketURL, onSocketMessage, options.reconnect);"], "mappings": "AAAA;AACA;AACA,OAAOA,aAAP,MAA0B,oBAA1B;AACA,OAAOC,SAAP,MAAsB,sBAAtB;AACA,OAAOC,QAAP,MAAqB,qBAArB;AACA,OAAOC,MAAP,MAAmB,aAAnB;AACA,SAASC,aAAT,EAAwBC,IAAxB,EAA8BC,IAA9B,QAA0C,cAA1C;AACA,SAASC,GAAT,EAAcC,WAAd,QAAiC,gBAAjC;AACA,OAAOC,WAAP,MAAwB,wBAAxB;AACA,OAAOC,SAAP,MAAsB,sBAAtB;AACA,OAAOC,eAAP,MAA4B,4BAA5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,IAAIC,MAAM,GAAG;EACXC,WAAW,EAAE,KADF;EAEX;EACA;EACAC,WAAW,EAAE,OAAOC,gBAAP,KAA4B,WAA5B,GAA0CA,gBAA1C,GAA6D;AAJ/D,CAAb;AAMA;;AAEA,IAAIC,OAAO,GAAG;EACZC,GAAG,EAAE,KADO;EAEZC,UAAU,EAAE,KAFA;EAGZC,QAAQ,EAAE,KAHE;EAIZC,OAAO,EAAE;AAJG,CAAd;AAMA,IAAIC,mBAAmB,GAAGnB,QAAQ,CAACoB,eAAD,CAAlC;;AAEA,IAAID,mBAAmB,CAACJ,GAApB,KAA4B,MAAhC,EAAwC;EACtCD,OAAO,CAACC,GAAR,GAAc,IAAd;EACAV,GAAG,CAACgB,IAAJ,CAAS,iCAAT;AACD;;AAED,IAAIF,mBAAmB,CAAC,aAAD,CAAnB,KAAuC,MAA3C,EAAmD;EACjDL,OAAO,CAACE,UAAR,GAAqB,IAArB;EACAX,GAAG,CAACgB,IAAJ,CAAS,yBAAT;AACD;;AAED,IAAIF,mBAAmB,CAACG,OAAxB,EAAiC;EAC/BR,OAAO,CAACQ,OAAR,GAAkBH,mBAAmB,CAACG,OAAtC;AACD;;AAED,IAAI,OAAOH,mBAAmB,CAACI,SAA3B,KAAyC,WAA7C,EAA0D;EACxDT,OAAO,CAACS,SAAR,GAAoBC,MAAM,CAACL,mBAAmB,CAACI,SAArB,CAA1B;AACD;AACD;AACA;AACA;;;AAGA,SAASE,cAAT,CAAwBC,KAAxB,EAA+B;EAC7B;EACA5B,aAAa,CAACQ,WAAd,CAA0BoB,KAAK,KAAK,SAAV,IAAuBA,KAAK,KAAK,KAAjC,GAAyC,MAAzC,GAAkDA,KAA5E;EACApB,WAAW,CAACoB,KAAD,CAAX;AACD;;AAED,IAAIZ,OAAO,CAACQ,OAAZ,EAAqB;EACnBG,cAAc,CAACX,OAAO,CAACQ,OAAT,CAAd;AACD;;AAEDK,IAAI,CAACC,gBAAL,CAAsB,cAAtB,EAAsC,YAAY;EAChDlB,MAAM,CAACC,WAAP,GAAqB,IAArB;AACD,CAFD;AAGA,IAAIkB,eAAe,GAAG;EACpBd,GAAG,EAAE,SAASA,GAAT,GAAe;IAClB,IAAII,mBAAmB,CAACJ,GAApB,KAA4B,OAAhC,EAAyC;MACvC;IACD;;IAEDD,OAAO,CAACC,GAAR,GAAc,IAAd;IACAV,GAAG,CAACgB,IAAJ,CAAS,iCAAT;EACD,CARmB;EASpBL,UAAU,EAAE,SAASA,UAAT,GAAsB;IAChC,IAAIG,mBAAmB,CAAC,aAAD,CAAnB,KAAuC,OAA3C,EAAoD;MAClD;IACD;;IAEDL,OAAO,CAACE,UAAR,GAAqB,IAArB;IACAX,GAAG,CAACgB,IAAJ,CAAS,yBAAT;EACD,CAhBmB;EAiBpBS,OAAO,EAAE,SAASA,OAAT,GAAmB;IAC1BzB,GAAG,CAACgB,IAAJ,CAAS,6BAAT,EAD0B,CACe;;IAEzC,IAAIP,OAAO,CAACI,OAAZ,EAAqB;MACnBd,IAAI;IACL;;IAEDG,WAAW,CAAC,SAAD,CAAX;EACD,CAzBmB;;EA2BpB;AACF;AACA;EACEwB,IAAI,EAAE,SAASA,IAAT,CAAcC,KAAd,EAAqB;IACzBtB,MAAM,CAACuB,YAAP,GAAsBvB,MAAM,CAACE,WAA7B;IACAF,MAAM,CAACE,WAAP,GAAqBoB,KAArB;EACD,CAjCmB;EAkCpBV,OAAO,EAAEG,cAlCW;;EAoCpB;AACF;AACA;EACEP,OAAO,EAAE,SAASA,OAAT,CAAiBgB,KAAjB,EAAwB;IAC/B,IAAI,OAAOC,QAAP,KAAoB,WAAxB,EAAqC;MACnC;IACD;;IAEDrB,OAAO,CAACI,OAAR,GAAkBgB,KAAlB;EACD,CA7CmB;;EA+CpB;AACF;AACA;EACEX,SAAS,EAAE,SAASA,SAAT,CAAmBW,KAAnB,EAA0B;IACnC,IAAIf,mBAAmB,CAACI,SAApB,KAAkC,OAAtC,EAA+C;MAC7C;IACD;;IAEDT,OAAO,CAACS,SAAR,GAAoBW,KAApB;EACD,CAxDmB;;EA0DpB;AACF;AACA;EACEjB,QAAQ,EAAE,SAASA,QAAT,CAAkBiB,KAAlB,EAAyB;IACjCpB,OAAO,CAACG,QAAR,GAAmBiB,KAAnB;EACD,CA/DmB;;EAiEpB;AACF;AACA;EACE,mBAAmB,SAASE,cAAT,CAAwBC,IAAxB,EAA8B;IAC/C,IAAIvB,OAAO,CAACG,QAAZ,EAAsB;MACpBZ,GAAG,CAACgB,IAAJ,CAAS,GAAGiB,MAAH,CAAUD,IAAI,CAACE,UAAL,GAAkB,IAAID,MAAJ,CAAWD,IAAI,CAACE,UAAhB,EAA4B,IAA5B,CAAlB,GAAsD,EAAhE,EAAoED,MAApE,CAA2ED,IAAI,CAACG,OAAhF,EAAyF,MAAzF,EAAiGF,MAAjG,CAAwGD,IAAI,CAACI,GAA7G,EAAkH,GAAlH,CAAT;IACD;;IAEDlC,WAAW,CAAC,UAAD,EAAa8B,IAAb,CAAX;EACD,CA1EmB;EA2EpB,YAAY,SAASK,OAAT,GAAmB;IAC7BrC,GAAG,CAACgB,IAAJ,CAAS,kBAAT;;IAEA,IAAIP,OAAO,CAACI,OAAZ,EAAqB;MACnBd,IAAI;IACL;;IAEDG,WAAW,CAAC,SAAD,CAAX;EACD,CAnFmB;EAoFpBoC,EAAE,EAAE,SAASA,EAAT,GAAc;IAChBpC,WAAW,CAAC,IAAD,CAAX;;IAEA,IAAIO,OAAO,CAACI,OAAZ,EAAqB;MACnBd,IAAI;IACL;;IAEDI,SAAS,CAACM,OAAD,EAAUJ,MAAV,CAAT;EACD,CA5FmB;EA6FpB;;EAEA;AACF;AACA;EACE,mBAAmB,SAASkC,cAAT,CAAwBC,IAAxB,EAA8B;IAC/CxC,GAAG,CAACgB,IAAJ,CAAS,GAAGiB,MAAH,CAAUO,IAAI,GAAG,KAAKP,MAAL,CAAYO,IAAZ,EAAkB,IAAlB,CAAH,GAA6B,SAA3C,EAAsD,kDAAtD,CAAT;IACAlB,IAAI,CAACmB,QAAL,CAAcC,MAAd;EACD,CArGmB;;EAuGpB;AACF;AACA;EACE,kBAAkB,SAASC,aAAT,CAAuBH,IAAvB,EAA6B;IAC7CxC,GAAG,CAACgB,IAAJ,CAAS,GAAGiB,MAAH,CAAUO,IAAI,GAAG,KAAKP,MAAL,CAAYO,IAAZ,EAAkB,IAAlB,CAAH,GAA6B,SAA3C,EAAsD,kDAAtD,CAAT;IACAlB,IAAI,CAACmB,QAAL,CAAcC,MAAd;EACD,CA7GmB;;EA+GpB;AACF;AACA;AACA;EACEE,QAAQ,EAAE,SAASA,QAAT,CAAkBC,SAAlB,EAA6BC,MAA7B,EAAqC;IAC7C9C,GAAG,CAAC+C,IAAJ,CAAS,2BAAT;;IAEA,IAAIC,iBAAiB,GAAGH,SAAS,CAACI,GAAV,CAAc,UAAUC,KAAV,EAAiB;MACrD,IAAIC,cAAc,GAAGtD,aAAa,CAAC,SAAD,EAAYqD,KAAZ,CAAlC;MAAA,IACIE,MAAM,GAAGD,cAAc,CAACC,MAD5B;MAAA,IAEIC,IAAI,GAAGF,cAAc,CAACE,IAF1B;;MAIA,OAAO,GAAGpB,MAAH,CAAUmB,MAAV,EAAkB,IAAlB,EAAwBnB,MAAxB,CAA+BvC,SAAS,CAAC2D,IAAD,CAAxC,CAAP;IACD,CANuB,CAAxB;;IAQAnD,WAAW,CAAC,UAAD,EAAa8C,iBAAb,CAAX;;IAEA,KAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,iBAAiB,CAACO,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;MACjDtD,GAAG,CAAC+C,IAAJ,CAASC,iBAAiB,CAACM,CAAD,CAA1B;IACD;;IAED,IAAIE,0BAA0B,GAAG,OAAO/C,OAAO,CAACI,OAAf,KAA2B,SAA3B,GAAuCJ,OAAO,CAACI,OAA/C,GAAyDJ,OAAO,CAACI,OAAR,IAAmBJ,OAAO,CAACI,OAAR,CAAgB+B,QAA7H;;IAEA,IAAIY,0BAAJ,EAAgC;MAC9B,IAAIC,sBAAsB,GAAG,OAAOhD,OAAO,CAACI,OAAf,KAA2B,QAA3B,IAAuCJ,OAAO,CAACI,OAAR,CAAgB4C,sBAApF;MACA3D,IAAI,CAAC,SAAD,EAAY+C,SAAZ,EAAuBY,sBAAsB,IAAI,IAAjD,CAAJ;IACD;;IAED,IAAIX,MAAM,IAAIA,MAAM,CAACY,gBAArB,EAAuC;MACrC;IACD;;IAEDvD,SAAS,CAACM,OAAD,EAAUJ,MAAV,CAAT;EACD,CAhJmB;;EAkJpB;AACF;AACA;EACEsD,MAAM,EAAE,SAASA,MAAT,CAAgBC,OAAhB,EAAyB;IAC/B5D,GAAG,CAACkD,KAAJ,CAAU,2CAAV;;IAEA,IAAIW,eAAe,GAAGD,OAAO,CAACX,GAAR,CAAY,UAAUC,KAAV,EAAiB;MACjD,IAAIY,eAAe,GAAGjE,aAAa,CAAC,OAAD,EAAUqD,KAAV,CAAnC;MAAA,IACIE,MAAM,GAAGU,eAAe,CAACV,MAD7B;MAAA,IAEIC,IAAI,GAAGS,eAAe,CAACT,IAF3B;;MAIA,OAAO,GAAGpB,MAAH,CAAUmB,MAAV,EAAkB,IAAlB,EAAwBnB,MAAxB,CAA+BvC,SAAS,CAAC2D,IAAD,CAAxC,CAAP;IACD,CANqB,CAAtB;;IAQAnD,WAAW,CAAC,QAAD,EAAW2D,eAAX,CAAX;;IAEA,KAAK,IAAIP,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGO,eAAe,CAACN,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;MAC/CtD,GAAG,CAACkD,KAAJ,CAAUW,eAAe,CAACP,CAAD,CAAzB;IACD;;IAED,IAAIS,wBAAwB,GAAG,OAAOtD,OAAO,CAACI,OAAf,KAA2B,SAA3B,GAAuCJ,OAAO,CAACI,OAA/C,GAAyDJ,OAAO,CAACI,OAAR,IAAmBJ,OAAO,CAACI,OAAR,CAAgB8C,MAA3H;;IAEA,IAAII,wBAAJ,EAA8B;MAC5B,IAAIN,sBAAsB,GAAG,OAAOhD,OAAO,CAACI,OAAf,KAA2B,QAA3B,IAAuCJ,OAAO,CAACI,OAAR,CAAgB4C,sBAApF;MACA3D,IAAI,CAAC,OAAD,EAAU8D,OAAV,EAAmBH,sBAAsB,IAAI,IAA7C,CAAJ;IACD;EACF,CA5KmB;;EA8KpB;AACF;AACA;EACEP,KAAK,EAAE,SAASA,KAAT,CAAec,MAAf,EAAuB;IAC5BhE,GAAG,CAACkD,KAAJ,CAAUc,MAAV;EACD,CAnLmB;EAoLpBC,KAAK,EAAE,SAASA,KAAT,GAAiB;IACtBjE,GAAG,CAACgB,IAAJ,CAAS,eAAT;;IAEA,IAAIP,OAAO,CAACI,OAAZ,EAAqB;MACnBd,IAAI;IACL;;IAEDG,WAAW,CAAC,OAAD,CAAX;EACD;AA5LmB,CAAtB;AA8LA,IAAIgE,SAAS,GAAG9D,eAAe,CAACU,mBAAD,CAA/B;AACAlB,MAAM,CAACsE,SAAD,EAAY1C,eAAZ,EAA6Bf,OAAO,CAACS,SAArC,CAAN"}, "metadata": {}, "sourceType": "module"}