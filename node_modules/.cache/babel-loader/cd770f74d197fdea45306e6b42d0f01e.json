{"ast": null, "code": "import _taggedTemplateLiteral from\"/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6;import styled from\"styled-components\";export const Container=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n\\n  background-color: #fefefe;\\n\\n  border-radius: 7px;\\n\\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\\n\\n  min-height: 200px;\\n  max-height: 420px;\\n\\n  margin: 0 10px;\\n\\n  font-size: \",\";\\n  font-weight: 700;\\n\\n  &:hover {\\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\\n    background-color: \",\";\\n    transition: all 0.3 ease-in-out;\\n\\n\\n    .title {\\n      background-color: \",\";\\n    }\\n  }\\n\\n  cursor: pointer;\\n  transition: all 0.3 ease-in-out;\\n\"])),_ref=>{let{theme:{font}}=_ref;return font.title;},_ref2=>{let{theme:{colors}}=_ref2;return\"\".concat(colors.grey,\"01\");},_ref3=>{let{theme:{colors}}=_ref3;return colors.white;});export const TitleWrapper=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n\\n  font-size: 25px;\\n\\n  padding: 20px 60px;\\n\"])));export const IconHolder=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n\\n  margin-right: 10px;\\n\\n  svg {\\n    width: 30px;\\n    height: 30px;\\n  }\\n\"])));export const TextHolder=styled.span(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\"])));export const ListHolder=styled.ul(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  padding: 20px 60px;\\n  width: 100%;\\n\\n  height: 100%;\\n  overflow-y: auto;\\n\"])));export const ListItme=styled.li(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  padding: 10px;\\n\\n  display: flex;\\n  align-items: center;\\n\\n  svg {\\n    margin-right: 10px;\\n  }\\n\\n  &:hover {\\n    background-color: \",\";\\n    color: #ffffff;\\n  }\\n\"])),_ref4=>{let{theme:{colors}}=_ref4;return colors.main.blue;});", "map": {"version": 3, "names": ["styled", "Container", "div", "_templateObject", "_taggedTemplateLiteral", "_ref", "theme", "font", "title", "_ref2", "colors", "concat", "grey", "_ref3", "white", "TitleWrapper", "_templateObject2", "IconHolder", "_templateObject3", "TextHolder", "span", "_templateObject4", "ListHolder", "ul", "_templateObject5", "ListItme", "li", "_templateObject6", "_ref4", "main", "blue"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/styles.js"], "sourcesContent": ["import styled from \"styled-components\";\n\nexport const Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n\n  background-color: #fefefe;\n\n  border-radius: 7px;\n\n  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.15);\n\n  min-height: 200px;\n  max-height: 420px;\n\n  margin: 0 10px;\n\n  font-size: ${({ theme: { font } }) => font.title};\n  font-weight: 700;\n\n  &:hover {\n    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.15);\n    background-color: ${({ theme: { colors } }) => `${colors.grey}01`};\n    transition: all 0.3 ease-in-out;\n\n\n    .title {\n      background-color: ${({ theme: { colors } }) => colors.white};\n    }\n  }\n\n  cursor: pointer;\n  transition: all 0.3 ease-in-out;\n`;\n\nexport const TitleWrapper = styled.div`\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  font-size: 25px;\n\n  padding: 20px 60px;\n`;\n\nexport const IconHolder = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  margin-right: 10px;\n\n  svg {\n    width: 30px;\n    height: 30px;\n  }\n`;\n\nexport const TextHolder = styled.span``;\n\nexport const ListHolder = styled.ul`\n  padding: 20px 60px;\n  width: 100%;\n\n  height: 100%;\n  overflow-y: auto;\n`;\n\nexport const ListItme = styled.li`\n  padding: 10px;\n\n  display: flex;\n  align-items: center;\n\n  svg {\n    margin-right: 10px;\n  }\n\n  &:hover {\n    background-color: ${({ theme: { colors } }) => colors.main.blue};\n    color: #ffffff;\n  }\n`;\n"], "mappings": "sQAAA,MAAO,CAAAA,MAAM,KAAM,mBAAmB,CAEtC,MAAO,MAAM,CAAAC,SAAS,CAAGD,MAAM,CAACE,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,qiBAgBpBC,IAAA,MAAC,CAAEC,KAAK,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAAF,IAAA,OAAK,CAAAE,IAAI,CAACC,KAAK,GAK1BC,KAAA,MAAC,CAAEH,KAAK,CAAE,CAAEI,MAAO,CAAE,CAAC,CAAAD,KAAA,UAAAE,MAAA,CAAQD,MAAM,CAACE,IAAI,QAAI,CAK3CC,KAAA,MAAC,CAAEP,KAAK,CAAE,CAAEI,MAAO,CAAE,CAAC,CAAAG,KAAA,OAAK,CAAAH,MAAM,CAACI,KAAK,GAMhE,CAED,MAAO,MAAM,CAAAC,YAAY,CAAGf,MAAM,CAACE,GAAG,CAAAc,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,mHAQrC,CAED,MAAO,MAAM,CAAAa,UAAU,CAAGjB,MAAM,CAACE,GAAG,CAAAgB,gBAAA,GAAAA,gBAAA,CAAAd,sBAAA,8JAWnC,CAED,MAAO,MAAM,CAAAe,UAAU,CAAGnB,MAAM,CAACoB,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,QAAE,CAEvC,MAAO,MAAM,CAAAkB,UAAU,CAAGtB,MAAM,CAACuB,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,yFAMlC,CAED,MAAO,MAAM,CAAAqB,QAAQ,CAAGzB,MAAM,CAAC0B,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAvB,sBAAA,sLAWTwB,KAAA,MAAC,CAAEtB,KAAK,CAAE,CAAEI,MAAO,CAAE,CAAC,CAAAkB,KAAA,OAAK,CAAAlB,MAAM,CAACmB,IAAI,CAACC,IAAI,GAGlE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}