{"ast": null, "code": "var global = require('../internals/global');\n\nvar shared = require('../internals/shared');\n\nvar hasOwn = require('../internals/has-own-property');\n\nvar uid = require('../internals/uid');\n\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\n\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar WellKnownSymbolsStore = shared('wks');\nvar Symbol = global.Symbol;\nvar symbolFor = Symbol && Symbol['for'];\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name) || !(NATIVE_SYMBOL || typeof WellKnownSymbolsStore[name] == 'string')) {\n    var description = 'Symbol.' + name;\n\n    if (NATIVE_SYMBOL && hasOwn(Symbol, name)) {\n      WellKnownSymbolsStore[name] = Symbol[name];\n    } else if (USE_SYMBOL_AS_UID && symbolFor) {\n      WellKnownSymbolsStore[name] = symbolFor(description);\n    } else {\n      WellKnownSymbolsStore[name] = createWellKnownSymbol(description);\n    }\n  }\n\n  return WellKnownSymbolsStore[name];\n};", "map": {"version": 3, "names": ["global", "require", "shared", "hasOwn", "uid", "NATIVE_SYMBOL", "USE_SYMBOL_AS_UID", "WellKnownSymbolsStore", "Symbol", "symbolFor", "createWellKnownSymbol", "withoutSetter", "module", "exports", "name", "description"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/core-js-pure/internals/well-known-symbol.js"], "sourcesContent": ["var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar WellKnownSymbolsStore = shared('wks');\nvar Symbol = global.Symbol;\nvar symbolFor = Symbol && Symbol['for'];\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name) || !(NATIVE_SYMBOL || typeof WellKnownSymbolsStore[name] == 'string')) {\n    var description = 'Symbol.' + name;\n    if (NATIVE_SYMBOL && hasOwn(Symbol, name)) {\n      WellKnownSymbolsStore[name] = Symbol[name];\n    } else if (USE_SYMBOL_AS_UID && symbolFor) {\n      WellKnownSymbolsStore[name] = symbolFor(description);\n    } else {\n      WellKnownSymbolsStore[name] = createWellKnownSymbol(description);\n    }\n  } return WellKnownSymbolsStore[name];\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAD,CAApB;;AACA,IAAIC,MAAM,GAAGD,OAAO,CAAC,qBAAD,CAApB;;AACA,IAAIE,MAAM,GAAGF,OAAO,CAAC,+BAAD,CAApB;;AACA,IAAIG,GAAG,GAAGH,OAAO,CAAC,kBAAD,CAAjB;;AACA,IAAII,aAAa,GAAGJ,OAAO,CAAC,4BAAD,CAA3B;;AACA,IAAIK,iBAAiB,GAAGL,OAAO,CAAC,gCAAD,CAA/B;;AAEA,IAAIM,qBAAqB,GAAGL,MAAM,CAAC,KAAD,CAAlC;AACA,IAAIM,MAAM,GAAGR,MAAM,CAACQ,MAApB;AACA,IAAIC,SAAS,GAAGD,MAAM,IAAIA,MAAM,CAAC,KAAD,CAAhC;AACA,IAAIE,qBAAqB,GAAGJ,iBAAiB,GAAGE,MAAH,GAAYA,MAAM,IAAIA,MAAM,CAACG,aAAjB,IAAkCP,GAA3F;;AAEAQ,MAAM,CAACC,OAAP,GAAiB,UAAUC,IAAV,EAAgB;EAC/B,IAAI,CAACX,MAAM,CAACI,qBAAD,EAAwBO,IAAxB,CAAP,IAAwC,EAAET,aAAa,IAAI,OAAOE,qBAAqB,CAACO,IAAD,CAA5B,IAAsC,QAAzD,CAA5C,EAAgH;IAC9G,IAAIC,WAAW,GAAG,YAAYD,IAA9B;;IACA,IAAIT,aAAa,IAAIF,MAAM,CAACK,MAAD,EAASM,IAAT,CAA3B,EAA2C;MACzCP,qBAAqB,CAACO,IAAD,CAArB,GAA8BN,MAAM,CAACM,IAAD,CAApC;IACD,CAFD,MAEO,IAAIR,iBAAiB,IAAIG,SAAzB,EAAoC;MACzCF,qBAAqB,CAACO,IAAD,CAArB,GAA8BL,SAAS,CAACM,WAAD,CAAvC;IACD,CAFM,MAEA;MACLR,qBAAqB,CAACO,IAAD,CAArB,GAA8BJ,qBAAqB,CAACK,WAAD,CAAnD;IACD;EACF;;EAAC,OAAOR,qBAAqB,CAACO,IAAD,CAA5B;AACH,CAXD"}, "metadata": {}, "sourceType": "script"}