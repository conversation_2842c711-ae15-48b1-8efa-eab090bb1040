{"ast": null, "code": "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\nmodule.exports = function (updatedModules, renewedModules) {\n  var unacceptedModules = updatedModules.filter(function (moduleId) {\n    return renewedModules && renewedModules.indexOf(moduleId) < 0;\n  });\n\n  var log = require(\"./log\");\n\n  if (unacceptedModules.length > 0) {\n    log(\"warning\", \"[HMR] The following modules couldn't be hot updated: (They would need a full reload!)\");\n    unacceptedModules.forEach(function (moduleId) {\n      log(\"warning\", \"[HMR]  - \" + moduleId);\n    });\n  }\n\n  if (!renewedModules || renewedModules.length === 0) {\n    log(\"info\", \"[HMR] Nothing hot updated.\");\n  } else {\n    log(\"info\", \"[HMR] Updated modules:\");\n    renewedModules.forEach(function (moduleId) {\n      if (typeof moduleId === \"string\" && moduleId.indexOf(\"!\") !== -1) {\n        var parts = moduleId.split(\"!\");\n        log.groupCollapsed(\"info\", \"[HMR]  - \" + parts.pop());\n        log(\"info\", \"[HMR]  - \" + moduleId);\n        log.groupEnd(\"info\");\n      } else {\n        log(\"info\", \"[HMR]  - \" + moduleId);\n      }\n    });\n    var numberIds = renewedModules.every(function (moduleId) {\n      return typeof moduleId === \"number\";\n    });\n    if (numberIds) log(\"info\", '[HMR] Consider using the optimization.moduleIds: \"named\" for module names.');\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "updatedModules", "renewedModules", "unacceptedModules", "filter", "moduleId", "indexOf", "log", "require", "length", "for<PERSON>ach", "parts", "split", "groupCollapsed", "pop", "groupEnd", "numberIds", "every"], "sources": ["/Users/<USER>/Mac/Projects/Frontend/capastrength/node_modules/webpack/hot/log-apply-result.js"], "sourcesContent": ["/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\nmodule.exports = function (updatedModules, renewedModules) {\n\tvar unacceptedModules = updatedModules.filter(function (moduleId) {\n\t\treturn renewedModules && renewedModules.indexOf(moduleId) < 0;\n\t});\n\tvar log = require(\"./log\");\n\n\tif (unacceptedModules.length > 0) {\n\t\tlog(\n\t\t\t\"warning\",\n\t\t\t\"[HMR] The following modules couldn't be hot updated: (They would need a full reload!)\"\n\t\t);\n\t\tunacceptedModules.forEach(function (moduleId) {\n\t\t\tlog(\"warning\", \"[HMR]  - \" + moduleId);\n\t\t});\n\t}\n\n\tif (!renewedModules || renewedModules.length === 0) {\n\t\tlog(\"info\", \"[HMR] Nothing hot updated.\");\n\t} else {\n\t\tlog(\"info\", \"[HMR] Updated modules:\");\n\t\trenewedModules.forEach(function (moduleId) {\n\t\t\tif (typeof moduleId === \"string\" && moduleId.indexOf(\"!\") !== -1) {\n\t\t\t\tvar parts = moduleId.split(\"!\");\n\t\t\t\tlog.groupCollapsed(\"info\", \"[HMR]  - \" + parts.pop());\n\t\t\t\tlog(\"info\", \"[HMR]  - \" + moduleId);\n\t\t\t\tlog.groupEnd(\"info\");\n\t\t\t} else {\n\t\t\t\tlog(\"info\", \"[HMR]  - \" + moduleId);\n\t\t\t}\n\t\t});\n\t\tvar numberIds = renewedModules.every(function (moduleId) {\n\t\t\treturn typeof moduleId === \"number\";\n\t\t});\n\t\tif (numberIds)\n\t\t\tlog(\n\t\t\t\t\"info\",\n\t\t\t\t'[HMR] Consider using the optimization.moduleIds: \"named\" for module names.'\n\t\t\t);\n\t}\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAP,GAAiB,UAAUC,cAAV,EAA0BC,cAA1B,EAA0C;EAC1D,IAAIC,iBAAiB,GAAGF,cAAc,CAACG,MAAf,CAAsB,UAAUC,QAAV,EAAoB;IACjE,OAAOH,cAAc,IAAIA,cAAc,CAACI,OAAf,CAAuBD,QAAvB,IAAmC,CAA5D;EACA,CAFuB,CAAxB;;EAGA,IAAIE,GAAG,GAAGC,OAAO,CAAC,OAAD,CAAjB;;EAEA,IAAIL,iBAAiB,CAACM,MAAlB,GAA2B,CAA/B,EAAkC;IACjCF,GAAG,CACF,SADE,EAEF,uFAFE,CAAH;IAIAJ,iBAAiB,CAACO,OAAlB,CAA0B,UAAUL,QAAV,EAAoB;MAC7CE,GAAG,CAAC,SAAD,EAAY,cAAcF,QAA1B,CAAH;IACA,CAFD;EAGA;;EAED,IAAI,CAACH,cAAD,IAAmBA,cAAc,CAACO,MAAf,KAA0B,CAAjD,EAAoD;IACnDF,GAAG,CAAC,MAAD,EAAS,4BAAT,CAAH;EACA,CAFD,MAEO;IACNA,GAAG,CAAC,MAAD,EAAS,wBAAT,CAAH;IACAL,cAAc,CAACQ,OAAf,CAAuB,UAAUL,QAAV,EAAoB;MAC1C,IAAI,OAAOA,QAAP,KAAoB,QAApB,IAAgCA,QAAQ,CAACC,OAAT,CAAiB,GAAjB,MAA0B,CAAC,CAA/D,EAAkE;QACjE,IAAIK,KAAK,GAAGN,QAAQ,CAACO,KAAT,CAAe,GAAf,CAAZ;QACAL,GAAG,CAACM,cAAJ,CAAmB,MAAnB,EAA2B,cAAcF,KAAK,CAACG,GAAN,EAAzC;QACAP,GAAG,CAAC,MAAD,EAAS,cAAcF,QAAvB,CAAH;QACAE,GAAG,CAACQ,QAAJ,CAAa,MAAb;MACA,CALD,MAKO;QACNR,GAAG,CAAC,MAAD,EAAS,cAAcF,QAAvB,CAAH;MACA;IACD,CATD;IAUA,IAAIW,SAAS,GAAGd,cAAc,CAACe,KAAf,CAAqB,UAAUZ,QAAV,EAAoB;MACxD,OAAO,OAAOA,QAAP,KAAoB,QAA3B;IACA,CAFe,CAAhB;IAGA,IAAIW,SAAJ,EACCT,GAAG,CACF,MADE,EAEF,4EAFE,CAAH;EAID;AACD,CAvCD"}, "metadata": {}, "sourceType": "script"}