[{"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/index.js": "1", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/App.js": "2", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/global.styles.js": "3", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/variables.js": "4", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/index.js": "5", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/routeData.js": "6", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/index.jsx": "7", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/index.jsx": "8", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/index.jsx": "9", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/index.jsx": "10", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/index.jsx": "11", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/index.jsx": "12", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/index.jsx": "13", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/t&c/index.jsx": "14", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/clients/index.js": "15", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/index.jsx": "16", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/contact-us/index.jsx": "17", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/about/index.jsx": "18", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/home/<USER>": "19", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/index.jsx": "20", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/pageDimensions.js": "21", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/styles.js": "22", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/styles.js": "23", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/styles.js": "24", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/styles.js": "25", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/styles.js": "26", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/styles.js": "27", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/assetsList.js": "28", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/styles.js": "29", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/index.jsx": "30", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/index.jsx": "31", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/index.jsx": "32", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/index.jsx": "33", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/index.jsx": "34", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/styles.js": "35", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/projectData.js": "36", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/styles.js": "37", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/index.jsx": "38", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/index.jsx": "39", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/index.jsx": "40", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/index.jsx": "41", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/index.jsx": "42", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/index.jsx": "43", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/index.jsx": "44", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/index.jsx": "45", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/index.jsx": "46", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/index.jsx": "47", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/index.jsx": "48", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/styles.js": "49", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/styles.js": "50", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/styles.js": "51", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/styles.js": "52", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/data.js": "53", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/data.js": "54", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/styles.js": "55", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/styles.js": "56", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/styles.js": "57", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/styles.js": "58", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/styles.js": "59", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/index.jsx": "60", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/index.jsx": "61", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/index.jsx": "62", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/list.jsx": "63", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/styles.js": "64", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/index.jsx": "65", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/styles.js": "66", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/index.jsx": "67", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/styles.js": "68", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/styles.js": "69", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/styles.js": "70", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/index.jsx": "71", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/index.jsx": "72", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/servicesData.js": "73", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/data.js": "74", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/styles.js": "75", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/styles.js": "76", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/styles.js": "77", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/styles.js": "78", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/styles.js": "79", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/styles.js": "80", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/styles.js": "81", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/card.jsx": "82", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/styles.js": "83", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/index.jsx": "84", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/styles.js": "85"}, {"size": 299, "mtime": 1721488298515, "results": "86", "hashOfConfig": "87"}, {"size": 672, "mtime": 1721488298394, "results": "88", "hashOfConfig": "87"}, {"size": 1202, "mtime": 1721488298516, "results": "89", "hashOfConfig": "87"}, {"size": 298, "mtime": 1721488298516, "results": "90", "hashOfConfig": "87"}, {"size": 1132, "mtime": 1721488298516, "results": "91", "hashOfConfig": "87"}, {"size": 1085, "mtime": 1721488298516, "results": "92", "hashOfConfig": "87"}, {"size": 1968, "mtime": 1721488298511, "results": "93", "hashOfConfig": "87"}, {"size": 421, "mtime": 1721488298512, "results": "94", "hashOfConfig": "87"}, {"size": 2246, "mtime": 1721488298511, "results": "95", "hashOfConfig": "87"}, {"size": 2717, "mtime": 1721488298511, "results": "96", "hashOfConfig": "87"}, {"size": 1028, "mtime": 1721488298512, "results": "97", "hashOfConfig": "87"}, {"size": 629, "mtime": 1721488298512, "results": "98", "hashOfConfig": "87"}, {"size": 169, "mtime": 1721488298513, "results": "99", "hashOfConfig": "87"}, {"size": 323, "mtime": 1721488298516, "results": "100", "hashOfConfig": "87"}, {"size": 214, "mtime": 1721488298515, "results": "101", "hashOfConfig": "87"}, {"size": 1553, "mtime": 1721488298516, "results": "102", "hashOfConfig": "87"}, {"size": 341, "mtime": 1721488298515, "results": "103", "hashOfConfig": "87"}, {"size": 204, "mtime": 1721488298515, "results": "104", "hashOfConfig": "87"}, {"size": 527, "mtime": 1721488298515, "results": "105", "hashOfConfig": "87"}, {"size": 322, "mtime": 1721488298515, "results": "106", "hashOfConfig": "87"}, {"size": 894, "mtime": 1721488298516, "results": "107", "hashOfConfig": "87"}, {"size": 967, "mtime": 1721488298512, "results": "108", "hashOfConfig": "87"}, {"size": 1252, "mtime": 1721488298511, "results": "109", "hashOfConfig": "87"}, {"size": 2632, "mtime": 1721488298511, "results": "110", "hashOfConfig": "87"}, {"size": 505, "mtime": 1721488298512, "results": "111", "hashOfConfig": "87"}, {"size": 1181, "mtime": 1721488298512, "results": "112", "hashOfConfig": "87"}, {"size": 894, "mtime": 1721488298513, "results": "113", "hashOfConfig": "87"}, {"size": 5096, "mtime": 1721488298511, "results": "114", "hashOfConfig": "87"}, {"size": 83, "mtime": 1721488298513, "results": "115", "hashOfConfig": "87"}, {"size": 413, "mtime": 1721488298512, "results": "116", "hashOfConfig": "87"}, {"size": 1619, "mtime": 1721488298511, "results": "117", "hashOfConfig": "87"}, {"size": 210, "mtime": 1721488298511, "results": "118", "hashOfConfig": "87"}, {"size": 662, "mtime": 1721488298513, "results": "119", "hashOfConfig": "87"}, {"size": 486, "mtime": 1721488298513, "results": "120", "hashOfConfig": "87"}, {"size": 368, "mtime": 1721488298516, "results": "121", "hashOfConfig": "87"}, {"size": 8165, "mtime": 1721488298514, "results": "122", "hashOfConfig": "87"}, {"size": 83, "mtime": 1721488298515, "results": "123", "hashOfConfig": "87"}, {"size": 295, "mtime": 1721488298513, "results": "124", "hashOfConfig": "87"}, {"size": 4636, "mtime": 1721488298515, "results": "125", "hashOfConfig": "87"}, {"size": 967, "mtime": 1721488298510, "results": "126", "hashOfConfig": "87"}, {"size": 3906, "mtime": 1721488298510, "results": "127", "hashOfConfig": "87"}, {"size": 232, "mtime": 1721488298512, "results": "128", "hashOfConfig": "87"}, {"size": 242, "mtime": 1721488298512, "results": "129", "hashOfConfig": "87"}, {"size": 1696, "mtime": 1721488298513, "results": "130", "hashOfConfig": "87"}, {"size": 276, "mtime": 1721488298514, "results": "131", "hashOfConfig": "87"}, {"size": 6167, "mtime": 1721488298509, "results": "132", "hashOfConfig": "87"}, {"size": 2301, "mtime": 1721488298514, "results": "133", "hashOfConfig": "87"}, {"size": 1354, "mtime": 1721488298513, "results": "134", "hashOfConfig": "87"}, {"size": 462, "mtime": 1721488298515, "results": "135", "hashOfConfig": "87"}, {"size": 885, "mtime": 1721488298510, "results": "136", "hashOfConfig": "87"}, {"size": 461, "mtime": 1721488298511, "results": "137", "hashOfConfig": "87"}, {"size": 1478, "mtime": 1721488298509, "results": "138", "hashOfConfig": "87"}, {"size": 22995, "mtime": 1721488298510, "results": "139", "hashOfConfig": "87"}, {"size": 1022, "mtime": 1721488298509, "results": "140", "hashOfConfig": "87"}, {"size": 227, "mtime": 1721488298512, "results": "141", "hashOfConfig": "87"}, {"size": 604, "mtime": 1721488298511, "results": "142", "hashOfConfig": "87"}, {"size": 166, "mtime": 1721488298512, "results": "143", "hashOfConfig": "87"}, {"size": 207, "mtime": 1721488298512, "results": "144", "hashOfConfig": "87"}, {"size": 2352, "mtime": 1721488298513, "results": "145", "hashOfConfig": "87"}, {"size": 238, "mtime": 1721488298510, "results": "146", "hashOfConfig": "87"}, {"size": 812, "mtime": 1721488298510, "results": "147", "hashOfConfig": "87"}, {"size": 640, "mtime": 1721488298514, "results": "148", "hashOfConfig": "87"}, {"size": 1189, "mtime": 1721488298509, "results": "149", "hashOfConfig": "87"}, {"size": 671, "mtime": 1721488298513, "results": "150", "hashOfConfig": "87"}, {"size": 613, "mtime": 1721488298514, "results": "151", "hashOfConfig": "87"}, {"size": 1333, "mtime": 1721488298514, "results": "152", "hashOfConfig": "87"}, {"size": 4609, "mtime": 1721488298510, "results": "153", "hashOfConfig": "87"}, {"size": 1449, "mtime": 1721488298511, "results": "154", "hashOfConfig": "87"}, {"size": 581, "mtime": 1721488298513, "results": "155", "hashOfConfig": "87"}, {"size": 460, "mtime": 1721488298513, "results": "156", "hashOfConfig": "87"}, {"size": 388, "mtime": 1721488298509, "results": "157", "hashOfConfig": "87"}, {"size": 385, "mtime": 1721488298514, "results": "158", "hashOfConfig": "87"}, {"size": 600, "mtime": 1721488298514, "results": "159", "hashOfConfig": "87"}, {"size": 1485, "mtime": 1721488298509, "results": "160", "hashOfConfig": "87"}, {"size": 202, "mtime": 1721488298514, "results": "161", "hashOfConfig": "87"}, {"size": 1428, "mtime": 1721488298510, "results": "162", "hashOfConfig": "87"}, {"size": 749, "mtime": 1721488298509, "results": "163", "hashOfConfig": "87"}, {"size": 581, "mtime": 1721488298510, "results": "164", "hashOfConfig": "87"}, {"size": 322, "mtime": 1721488298514, "results": "165", "hashOfConfig": "87"}, {"size": 1352, "mtime": 1721488298510, "results": "166", "hashOfConfig": "87"}, {"size": 237, "mtime": 1721488298509, "results": "167", "hashOfConfig": "87"}, {"size": 364, "mtime": 1721488298509, "results": "168", "hashOfConfig": "87"}, {"size": 758, "mtime": 1721488298514, "results": "169", "hashOfConfig": "87"}, {"size": 491, "mtime": 1721488298514, "results": "170", "hashOfConfig": "87"}, {"size": 885, "mtime": 1721488298514, "results": "171", "hashOfConfig": "87"}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1w8kyv7", {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/index.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/App.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/global.styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/variables.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/index.js", ["427", "428"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/routeData.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/index.jsx", ["429", "430"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/index.jsx", ["431"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/t&c/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/clients/index.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/index.jsx", ["432"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/contact-us/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/about/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/home/<USER>", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/pageDimensions.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/assetsList.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/projectData.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/index.jsx", ["433", "434", "435", "436", "437", "438", "439"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/index.jsx", ["440"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/data.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/data.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/index.jsx", ["441"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/list.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/index.jsx", ["442"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/index.jsx", ["443"], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/servicesData.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/data.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/card.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/styles.js", [], [], {"ruleId": "444", "severity": 1, "message": "445", "line": 10, "column": 8, "nodeType": "446", "messageId": "447", "endLine": 10, "endColumn": 14}, {"ruleId": "444", "severity": 1, "message": "448", "line": 13, "column": 8, "nodeType": "446", "messageId": "447", "endLine": 13, "endColumn": 14}, {"ruleId": "444", "severity": 1, "message": "449", "line": 16, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 16, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "450", "line": 31, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 31, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "451", "line": 1, "column": 38, "nodeType": "446", "messageId": "447", "endLine": 1, "endColumn": 49}, {"ruleId": "444", "severity": 1, "message": "452", "line": 10, "column": 8, "nodeType": "446", "messageId": "447", "endLine": 10, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "453", "line": 15, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 15, "endColumn": 23}, {"ruleId": "444", "severity": 1, "message": "454", "line": 16, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 16, "endColumn": 18}, {"ruleId": "444", "severity": 1, "message": "455", "line": 17, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 17, "endColumn": 14}, {"ruleId": "444", "severity": 1, "message": "456", "line": 18, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 18, "endColumn": 18}, {"ruleId": "444", "severity": 1, "message": "457", "line": 19, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 19, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "458", "line": 20, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 20, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "459", "line": 49, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 49, "endColumn": 22}, {"ruleId": "444", "severity": 1, "message": "460", "line": 5, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 5, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "461", "line": 5, "column": 8, "nodeType": "446", "messageId": "447", "endLine": 5, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "462", "line": 12, "column": 19, "nodeType": "446", "messageId": "447", "endLine": 12, "endColumn": 29}, {"ruleId": "444", "severity": 1, "message": "460", "line": 3, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 3, "endColumn": 20}, "no-unused-vars", "'Signup' is defined but never used.", "Identifier", "unusedVar", "'Slider' is defined but never used.", "'isMobile' is assigned a value but never used.", "'name' is assigned a value but never used.", "'useCallback' is defined but never used.", "'Projects' is defined but never used.", "'industrialCommercial' is defined but never used.", "'residentialData' is defined but never used.", "'collegeData' is defined but never used.", "'publicTrustData' is defined but never used.", "'hotelData' is defined but never used.", "'mallsData' is defined but never used.", "'allResponsive' is assigned a value but never used.", "'AssetsList' is defined but never used.", "'ServicesCard' is defined but never used.", "'setSuccess' is assigned a value but never used."]