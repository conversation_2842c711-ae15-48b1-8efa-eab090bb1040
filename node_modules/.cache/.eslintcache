[{"/Users/<USER>/Mac/Projects/Frontend/capastrength/src/index.js": "1", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/App.js": "2", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/global.styles.js": "3", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/variables.js": "4", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/index.js": "5", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/routeData.js": "6", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/index.jsx": "7", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/index.jsx": "8", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/index.jsx": "9", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/index.jsx": "10", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/index.jsx": "11", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/index.jsx": "12", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/index.jsx": "13", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/t&c/index.jsx": "14", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/clients/index.js": "15", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/index.jsx": "16", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/contact-us/index.jsx": "17", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/about/index.jsx": "18", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/home/<USER>": "19", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/index.jsx": "20", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/pageDimensions.js": "21", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/styles.js": "22", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/styles.js": "23", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/styles.js": "24", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/styles.js": "25", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/styles.js": "26", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/styles.js": "27", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/assetsList.js": "28", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/styles.js": "29", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/index.jsx": "30", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/index.jsx": "31", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/index.jsx": "32", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/index.jsx": "33", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/index.jsx": "34", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/styles.js": "35", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/projectData.js": "36", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/styles.js": "37", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/index.jsx": "38", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/index.jsx": "39", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/index.jsx": "40", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/index.jsx": "41", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/index.jsx": "42", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/index.jsx": "43", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/index.jsx": "44", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/index.jsx": "45", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/index.jsx": "46", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/index.jsx": "47", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/index.jsx": "48", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/styles.js": "49", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/styles.js": "50", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/styles.js": "51", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/styles.js": "52", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/data.js": "53", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/data.js": "54", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/styles.js": "55", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/styles.js": "56", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/styles.js": "57", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/styles.js": "58", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/styles.js": "59", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/index.jsx": "60", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/index.jsx": "61", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/index.jsx": "62", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/list.jsx": "63", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/styles.js": "64", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/index.jsx": "65", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/styles.js": "66", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/index.jsx": "67", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/styles.js": "68", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/styles.js": "69", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/styles.js": "70", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/index.jsx": "71", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/index.jsx": "72", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/servicesData.js": "73", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/data.js": "74", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/styles.js": "75", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/styles.js": "76", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/styles.js": "77", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/styles.js": "78", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/styles.js": "79", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/styles.js": "80", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/styles.js": "81", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/card.jsx": "82", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/styles.js": "83", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/index.jsx": "84", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/styles.js": "85", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/ErrorBoundary.js": "86"}, {"size": 299, "mtime": 1721488298515, "results": "87", "hashOfConfig": "88"}, {"size": 761, "mtime": 1749463501996, "results": "89", "hashOfConfig": "88"}, {"size": 1202, "mtime": 1721488298516, "results": "90", "hashOfConfig": "88"}, {"size": 298, "mtime": 1721488298516, "results": "91", "hashOfConfig": "88"}, {"size": 1132, "mtime": 1721488298516, "results": "92", "hashOfConfig": "88"}, {"size": 1085, "mtime": 1721488298516, "results": "93", "hashOfConfig": "88"}, {"size": 1968, "mtime": 1721488298511, "results": "94", "hashOfConfig": "88"}, {"size": 421, "mtime": 1721488298512, "results": "95", "hashOfConfig": "88"}, {"size": 2246, "mtime": 1721488298511, "results": "96", "hashOfConfig": "88"}, {"size": 2717, "mtime": 1721488298511, "results": "97", "hashOfConfig": "88"}, {"size": 1028, "mtime": 1721488298512, "results": "98", "hashOfConfig": "88"}, {"size": 629, "mtime": 1721488298512, "results": "99", "hashOfConfig": "88"}, {"size": 169, "mtime": 1721488298513, "results": "100", "hashOfConfig": "88"}, {"size": 323, "mtime": 1721488298516, "results": "101", "hashOfConfig": "88"}, {"size": 214, "mtime": 1721488298515, "results": "102", "hashOfConfig": "88"}, {"size": 1553, "mtime": 1721488298516, "results": "103", "hashOfConfig": "88"}, {"size": 341, "mtime": 1721488298515, "results": "104", "hashOfConfig": "88"}, {"size": 204, "mtime": 1721488298515, "results": "105", "hashOfConfig": "88"}, {"size": 527, "mtime": 1721488298515, "results": "106", "hashOfConfig": "88"}, {"size": 322, "mtime": 1721488298515, "results": "107", "hashOfConfig": "88"}, {"size": 894, "mtime": 1721488298516, "results": "108", "hashOfConfig": "88"}, {"size": 967, "mtime": 1721488298512, "results": "109", "hashOfConfig": "88"}, {"size": 1252, "mtime": 1721488298511, "results": "110", "hashOfConfig": "88"}, {"size": 2632, "mtime": 1721488298511, "results": "111", "hashOfConfig": "88"}, {"size": 505, "mtime": 1721488298512, "results": "112", "hashOfConfig": "88"}, {"size": 1181, "mtime": 1721488298512, "results": "113", "hashOfConfig": "88"}, {"size": 894, "mtime": 1721488298513, "results": "114", "hashOfConfig": "88"}, {"size": 5096, "mtime": 1721488298511, "results": "115", "hashOfConfig": "88"}, {"size": 83, "mtime": 1721488298513, "results": "116", "hashOfConfig": "88"}, {"size": 413, "mtime": 1721488298512, "results": "117", "hashOfConfig": "88"}, {"size": 1619, "mtime": 1721488298511, "results": "118", "hashOfConfig": "88"}, {"size": 210, "mtime": 1721488298511, "results": "119", "hashOfConfig": "88"}, {"size": 662, "mtime": 1721488298513, "results": "120", "hashOfConfig": "88"}, {"size": 486, "mtime": 1721488298513, "results": "121", "hashOfConfig": "88"}, {"size": 368, "mtime": 1721488298516, "results": "122", "hashOfConfig": "88"}, {"size": 8165, "mtime": 1721488298514, "results": "123", "hashOfConfig": "88"}, {"size": 83, "mtime": 1721488298515, "results": "124", "hashOfConfig": "88"}, {"size": 295, "mtime": 1721488298513, "results": "125", "hashOfConfig": "88"}, {"size": 4636, "mtime": 1721488298515, "results": "126", "hashOfConfig": "88"}, {"size": 967, "mtime": 1721488298510, "results": "127", "hashOfConfig": "88"}, {"size": 3906, "mtime": 1721488298510, "results": "128", "hashOfConfig": "88"}, {"size": 232, "mtime": 1721488298512, "results": "129", "hashOfConfig": "88"}, {"size": 242, "mtime": 1721488298512, "results": "130", "hashOfConfig": "88"}, {"size": 1696, "mtime": 1721488298513, "results": "131", "hashOfConfig": "88"}, {"size": 276, "mtime": 1721488298514, "results": "132", "hashOfConfig": "88"}, {"size": 6167, "mtime": 1721488298509, "results": "133", "hashOfConfig": "88"}, {"size": 2301, "mtime": 1721488298514, "results": "134", "hashOfConfig": "88"}, {"size": 1354, "mtime": 1721488298513, "results": "135", "hashOfConfig": "88"}, {"size": 462, "mtime": 1721488298515, "results": "136", "hashOfConfig": "88"}, {"size": 885, "mtime": 1721488298510, "results": "137", "hashOfConfig": "88"}, {"size": 461, "mtime": 1721488298511, "results": "138", "hashOfConfig": "88"}, {"size": 1478, "mtime": 1721488298509, "results": "139", "hashOfConfig": "88"}, {"size": 22995, "mtime": 1721488298510, "results": "140", "hashOfConfig": "88"}, {"size": 1022, "mtime": 1721488298509, "results": "141", "hashOfConfig": "88"}, {"size": 227, "mtime": 1721488298512, "results": "142", "hashOfConfig": "88"}, {"size": 604, "mtime": 1721488298511, "results": "143", "hashOfConfig": "88"}, {"size": 166, "mtime": 1721488298512, "results": "144", "hashOfConfig": "88"}, {"size": 207, "mtime": 1721488298512, "results": "145", "hashOfConfig": "88"}, {"size": 2352, "mtime": 1721488298513, "results": "146", "hashOfConfig": "88"}, {"size": 238, "mtime": 1721488298510, "results": "147", "hashOfConfig": "88"}, {"size": 812, "mtime": 1721488298510, "results": "148", "hashOfConfig": "88"}, {"size": 640, "mtime": 1721488298514, "results": "149", "hashOfConfig": "88"}, {"size": 1189, "mtime": 1721488298509, "results": "150", "hashOfConfig": "88"}, {"size": 671, "mtime": 1721488298513, "results": "151", "hashOfConfig": "88"}, {"size": 613, "mtime": 1721488298514, "results": "152", "hashOfConfig": "88"}, {"size": 1333, "mtime": 1721488298514, "results": "153", "hashOfConfig": "88"}, {"size": 4609, "mtime": 1721488298510, "results": "154", "hashOfConfig": "88"}, {"size": 1449, "mtime": 1721488298511, "results": "155", "hashOfConfig": "88"}, {"size": 581, "mtime": 1721488298513, "results": "156", "hashOfConfig": "88"}, {"size": 460, "mtime": 1721488298513, "results": "157", "hashOfConfig": "88"}, {"size": 388, "mtime": 1721488298509, "results": "158", "hashOfConfig": "88"}, {"size": 385, "mtime": 1721488298514, "results": "159", "hashOfConfig": "88"}, {"size": 600, "mtime": 1721488298514, "results": "160", "hashOfConfig": "88"}, {"size": 1485, "mtime": 1721488298509, "results": "161", "hashOfConfig": "88"}, {"size": 202, "mtime": 1721488298514, "results": "162", "hashOfConfig": "88"}, {"size": 1428, "mtime": 1721488298510, "results": "163", "hashOfConfig": "88"}, {"size": 749, "mtime": 1721488298509, "results": "164", "hashOfConfig": "88"}, {"size": 581, "mtime": 1721488298510, "results": "165", "hashOfConfig": "88"}, {"size": 322, "mtime": 1721488298514, "results": "166", "hashOfConfig": "88"}, {"size": 1352, "mtime": 1721488298510, "results": "167", "hashOfConfig": "88"}, {"size": 237, "mtime": 1721488298509, "results": "168", "hashOfConfig": "88"}, {"size": 364, "mtime": 1721488298509, "results": "169", "hashOfConfig": "88"}, {"size": 758, "mtime": 1721488298514, "results": "170", "hashOfConfig": "88"}, {"size": 491, "mtime": 1721488298514, "results": "171", "hashOfConfig": "88"}, {"size": 885, "mtime": 1721488298514, "results": "172", "hashOfConfig": "88"}, {"size": 2725, "mtime": 1749463468123, "results": "173", "hashOfConfig": "88"}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1w8kyv7", {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "189"}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "196"}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "203"}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "225"}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "301"}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "311"}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "366"}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "382"}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "398"}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/index.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/App.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/global.styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/variables.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/index.js", ["441", "442"], [], "import React, { Suspense } from \"react\";\nimport { Route, Routes } from \"react-router-dom\";\n\nimport Loading from \"../components/global/loading\";\n\nimport ScrollToTop from \"../components/global/scroll-to-top\";\n\nimport AppHeader from \"../components/global/header\";\nimport BgTitle from \"../components/global/bg-titile\";\nimport Signup from \"../components/global/signup\";\nimport AppFooter from \"../components/global/footer\";\n\nimport Slider from \"../components/global/slider\";\n\nimport { AllRoutes } from \"./routeData\";\n\nconst AppRoute = () => {\n  return (\n    <>\n      <AppHeader />\n      <BgTitle routes={AllRoutes} />\n      {/* <Slider /> */}\n      <ScrollToTop />\n      <Suspense fallback={<Loading />}>\n        <Routes>\n          {AllRoutes.map((route, index) => {\n            return (\n              <Route\n                key={route.path + index}\n                exact\n                path={route.path}\n                element={<route.component {...route.props} />}\n              />\n            );\n          })}\n        </Routes>\n      </Suspense>\n      {/* <Signup /> */}\n      <AppFooter />\n    </>\n  );\n};\n\nexport default AppRoute;\n", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/route/routeData.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/index.jsx", ["443", "444"], [], "import React from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport { AssetsList } from \"../../elements/assetsList\";\n\nimport Button from \"../../elements/button\";\n\nimport PageDimensions from \"../../../styles/pageDimensions\";\n\nimport * as Styles from \"./styles\";\n\nconst BgTitle = ({ routes }) => {\n  const { pathname } = useLocation();\n\n  const { width } = PageDimensions();\n\n  const isMobile = width < 659;\n\n  const isHome = pathname === \"/\";\n  const isAbout = pathname === \"/about\";\n  const isProjects = pathname === \"/projects\";\n  const isClients = pathname === \"/clients\";\n  const isContact = pathname === \"/contactus\";\n\n  if (\n    pathname.includes(\"/projects/\") &&\n    pathname.length > \"/projects/\".length\n  ) {\n    return null;\n  }\n\n  const { name } = routes.find((item) => item.path === pathname);\n\n  const { home, about, projects, clients, contact } = AssetsList.titleBg;\n\n  const currentBg = isHome\n    ? home\n    : isAbout\n    ? about\n    : isProjects\n    ? projects\n    : isClients\n    ? clients\n    : isContact\n    ? contact\n    : home;\n\n\n  return (\n    <Styles.Container img={currentBg} small={true}>\n      <Styles.Overlay />\n      <Styles.Wrapper className=\"px-sm-5\">\n        <Styles.Holder\n          className={pathname === \"/contactus\" ? `py-sm-1` : `py-sm-5`}\n        >\n          {/* <Styles.Title className=\"pt-4 pb-1\">{name}</Styles.Title> */}\n\n          <>\n            <Styles.Title className=\"pt-2 pb-2\">\n              We Provide Best Retrofitting Structural Strengthening Services\n            </Styles.Title>\n            {/* <Styles.SubTitle>\n                The largest real estate hub with various services\n              </Styles.SubTitle> */}\n          </>\n          {!isContact ? (\n            <Link to=\"/contactus\">\n              <Button className=\"my-3\">Contact us</Button>\n            </Link>\n          ) : null}\n        </Styles.Holder>\n      </Styles.Wrapper>\n    </Styles.Container>\n  );\n};\n\nexport default BgTitle;\n", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/index.jsx", ["445"], [], "import React, { useState, useEffect, useCallback } from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport AppLogo from \"../logo\";\n\nimport Button from \"../../elements/button\";\n\nimport MobileMenu from \"./mobile\";\nimport PageDimensions from \"../../../styles/pageDimensions\";\nimport { Menu } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst AppHeader = () => {\n  const [mobileMenu, setMobileMenu] = useState(false);\n\n  const { pathname } = useLocation();\n\n  const { width } = PageDimensions();\n\n  const isMobile = width < 659;\n\n  const handleMobileMenu = () => {\n    if (isMobile) {\n      setMobileMenu(!mobileMenu);\n    }\n  };\n\n  useEffect(() => {\n    setMobileMenu(false);\n  }, [pathname]);\n\n  return (\n    <Styles.Header>\n      <Styles.Container className=\"container\">\n        <Styles.Wtapper>\n          <AppLogo />\n          {isMobile ? (\n            <Styles.MobileMenuContainer>\n              <Menu onClick={handleMobileMenu} />\n              <MobileMenu\n                handleMobileMenu={handleMobileMenu}\n                active={mobileMenu}\n              />\n            </Styles.MobileMenuContainer>\n          ) : (\n            <Styles.MenuContainer>\n              <Styles.MenuListWrapper>\n                <Styles.MenuItem active={pathname === \"/\"}>\n                  <Link to=\"/\">Home</Link>\n                </Styles.MenuItem>\n                <Styles.MenuItem active={pathname === \"/about\"}>\n                  <Link to=\"/about\">About</Link>\n                </Styles.MenuItem>\n                <Styles.MenuItem active={pathname === \"/projects\"}>\n                  <Link to=\"/projects\">Projects</Link>\n                </Styles.MenuItem>\n                <Styles.MenuItem active={pathname === \"/clients\"}>\n                  <Link to=\"/clients\">Clients</Link>\n                </Styles.MenuItem>\n                <Styles.MenuItem active={pathname === \"/contactus\"}>\n                  <Link to=\"/contactus\">\n                    <Button>Contact us</Button>\n                  </Link>\n                </Styles.MenuItem>\n              </Styles.MenuListWrapper>\n            </Styles.MenuContainer>\n          )}\n        </Styles.Wtapper>\n      </Styles.Container>\n    </Styles.Header>\n  );\n};\n\nexport default AppHeader;\n", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/t&c/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/clients/index.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/index.jsx", ["446"], [], "import React from 'react'\n\nimport Seaction from '../../components/global/seaction'\n\nimport SeactionHeading from '../../components/global/seaction-title'\n\nimport { ProjectData } from '../../components/projects/projectData'\n\nimport ProjectCard from '../../components/projects/card';\nimport Projects from '../../components/projects';\n\nimport * as Styles from './styles'\n\nconst ProjectPage = () => {\n    return (\n        <div className=\"container\">\n            <Seaction>\n                <SeactionHeading title=\"Explore New features\" />\n                <Styles.CardGrid className=\"grid-md-none\">\n                    {\n                        ProjectData.map((item, key) => {\n                            return (\n                                <ProjectCard\n                                    key={key}\n                                    item={item}\n                                />\n                            )\n                        })\n                    }\n                </Styles.CardGrid>\n            </Seaction>\n            {/* <Seaction>\n                <Styles.VideoContainer>\n                    <SeactionHeading title=\"Short Glimpse Of the Working\" />\n                    <iframe width=\"100%\" height=\"615\" src=\"https://www.youtube.com/embed/KbTjl1PNCzg\" title=\"YouTube video player\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>\n                </Styles.VideoContainer>\n            </Seaction> */}\n        </div>\n    )\n}\n\nexport default ProjectPage", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/contact-us/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/about/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/home/<USER>", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/styles/pageDimensions.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/bg-titile/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/footer/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/loading/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/scroll-to-top/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/assetsList.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/slider/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/projectData.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/pages/projects/details-page/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/index.jsx", ["447", "448", "449", "450", "451", "452", "453"], [], "import React from \"react\";\n\nimport Carousel from \"react-multi-carousel\";\nimport \"react-multi-carousel/lib/styles.css\";\n\nimport SeactionHeading from \"../global/seaction-title\";\n\nimport * as Styles from \"./styles\";\n\nimport ClientItem from \"./item\";\nimport NameCard from \"./name-card\";\n\nimport {\n  clientdata,\n  industrialCommercial,\n  residentialData,\n  collegeData,\n  publicTrustData,\n  hotelData,\n  mallsData,\n  allClientsData,\n} from \"./data\";\n\nconst Clients = () => {\n  const responsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: { max: 4000, min: 3000 },\n      items: 4,\n      slidesToSlide: 4,\n    },\n    desktop: {\n      breakpoint: { max: 3000, min: 1024 },\n      items: 4,\n      slidesToSlide: 4,\n    },\n    tablet: {\n      breakpoint: { max: 1024, min: 664 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    mobile: {\n      breakpoint: { max: 664, min: 0 },\n      items: 1,\n      slidesToSlide: 1,\n    },\n  };\n\n  const allResponsive = {\n    superLargeDesktop: {\n      // the naming can be any, depends on you.\n      breakpoint: { max: 4000, min: 3000 },\n      items: 3,\n      slidesToSlide: 3,\n    },\n    desktop: {\n      breakpoint: { max: 3000, min: 1024 },\n      items: 2,\n      slidesToSlide: 2,\n    },\n    tablet: {\n      breakpoint: { max: 1024, min: 664 },\n      items: 2,\n      slidesToSlide: 2,\n    },\n    mobile: {\n      breakpoint: { max: 664, min: 0 },\n      items: 1,\n      slidesToSlide: 1,\n    },\n  };\n\n  // const hey = residentialData.map((item) => item?.split(\"•\"));\n  // const hey2 = hey[0].map((item) => {\n  //   return { title: item.trim() };\n  // });\n\n  // console.log({ hey2 });\n\n  return (\n    <Styles.Container>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Clients\" />\n        <Carousel\n          responsive={responsive}\n          autoPlay\n          infinite\n          autoPlaySpeed={6000}\n          slidesToSlide={1}\n          transitionDuration={30000}\n        >\n          {clientdata.map((item, index) => {\n            return <ClientItem key={index} data={item} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.AllListHolder>\n        {allClientsData.map((item, index) => {\n          return <NameCard key={index} client={item} />;\n        })}\n      </Styles.AllListHolder>\n      {/* <Styles.ListHolder>\n        <SeactionHeading title=\"Residential\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {residentialData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"School / College\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {collegeData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Public Trust\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {publicTrustData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Hotels\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {hotelData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder>\n      <Styles.ListHolder>\n        <SeactionHeading title=\"Malls\" />\n        <Carousel responsive={responsive} autoPlay infinite>\n          {mallsData.map((item, index) => {\n            return <NameCard key={index} title={item?.title} />;\n          })}\n        </Carousel>\n      </Styles.ListHolder> */}\n    </Styles.Container>\n  );\n};\n\nexport default Clients;\n", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/index.jsx", ["454"], [], "import React from \"react\";\n\nimport { Link } from \"react-router-dom\";\n\nimport { AssetsList } from \"../../elements/assetsList\";\nimport { ArrowRight, MapPin } from \"react-feather\";\n\nimport * as Styles from \"./styles\";\n\nconst ProjectCard = ({ item }) => {\n  console.log({ item: item.banner[0] });\n  return (\n    <Styles.Container>\n      <Link to={`/projects/${item.id}`}>\n        <>\n          <Styles.ImgContainer img={item?.thumbnail} className=\"image\">\n            <Styles.TagWrapper>\n              <Styles.TextHolder sub bold upper>\n                {item.tag}\n              </Styles.TextHolder>\n            </Styles.TagWrapper>\n          </Styles.ImgContainer>\n          <Styles.TextWrapper>\n            {/* <Styles.SaveWrapper>\n              <Styles.IconHolder src={AssetsList.save} />\n            </Styles.SaveWrapper> */}\n            <Styles.LocationWrapper>\n              <Styles.IconHolder>\n                <MapPin />\n              </Styles.IconHolder>\n              <Styles.TextHolder sub>{item.location}</Styles.TextHolder>\n            </Styles.LocationWrapper>\n            <Styles.Padding>\n              <Styles.TextHolder className=\"title\" heading>\n                {item.title}\n              </Styles.TextHolder>\n            </Styles.Padding>\n            <Styles.TextHolder sub>{item.info}</Styles.TextHolder>\n          </Styles.TextWrapper>\n          <Styles.LinkWrapper className=\"viewlink\">\n            <Styles.TextHolder bold>View</Styles.TextHolder>\n            <Styles.ArrowHolder className=\"arrow\">\n              <ArrowRight />\n            </Styles.ArrowHolder>\n          </Styles.LinkWrapper>\n        </>\n      </Link>\n    </Styles.Container>\n  );\n};\n\nexport default ProjectCard;\n", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/t&c/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/data.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/data.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/logo/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/elements/button/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/seaction-title/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/card/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/index.jsx", ["455"], [], "import React from \"react\";\n\nimport { ServicesData } from \"../servicesData\";\n\nimport ServicesCard from \"../card\";\n\nimport ServiceItem from \"../../projects/project-details/service-item\";\n\nimport * as Styles from \"./styles\";\n\nconst ServicesList = () => {\n  return (\n    <Styles.Container>\n      {ServicesData.map((item, key) => {\n        return (\n          <>\n            <ServiceItem title={item?.name} />\n            {/* <ServicesCard\n                            key={key}\n                            item={item}\n                        /> */}\n          </>\n        );\n      })}\n    </Styles.Container>\n  );\n};\n\nexport default ServicesList;\n", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/list.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/home/<USER>/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/index.jsx", ["456"], [], "import { useState, useEffect, useRef } from \"react\";\n\n// import { sendMail } from \"../../../api\";\nimport emailjs from \"@emailjs/browser\";\n\nimport * as Styles from \"./styles\";\n\nconst ContactForm = () => {\n  let errors = useRef([]);\n  const form = useRef();\n\n  const [success, setSuccess] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [inputValue, setInputValue] = useState({\n\n    name: \"\",\n    company: \"\",\n    email: \"\",\n    mobile: \"\",\n  });\n\n  const { name, company, email, mobile } = inputValue;\n\n  const [inputErrors, setInputErrors] = useState(null);\n\n  const errChange = errors.current;\n\n  useEffect(() => {\n    setInputErrors(errors.current);\n  }, [errChange]);\n\n  const handleChange = (e) => {\n    const value = e.target.value;\n    const name = e.target.name;\n\n    setInputValue({ ...inputValue, [name]: value });\n    setInputErrors({ ...inputErrors, [name]: \"\" });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    console.log({ inputValue });\n    console.log({ form: form.current });\n    emailjs\n      .sendForm(\n        \"service_ifbucgn\",\n        \"template_gryehv4\",\n        form.current,\n        \"ye7UvAtvh7-vkICwy\"\n      )\n      .then(\n        (result) => {\n          console.log(result);\n          setLoading(false);\n        },\n        (error) => {\n          console.log(error.text);\n          setLoading(false);\n        }\n      );\n    // const res = await sendMail(inputValue);\n    // if (res.success) {\n    //   setSuccess(res.data);\n    //   setInputValue({ name: \"\", company: \"\", email: \"\", mobile: \"\" });\n    //   setLoading(false);\n\n    //   setTimeout(() => {\n    //     setSuccess(null);\n    //   }, 6000);\n    // } else if (!res.success) {\n    //   let i = 0;\n    //   while (i < res.data.length) {\n    //     errors.current[res.data[i].param] = res.data[i].msg;\n    //     setInputErrors({\n    //       ...inputErrors,\n    //       [res.data[i].param]: res.data[i].msg,\n    //     });\n    //     i++;\n    //   }\n    //   setLoading(false);\n    // }\n  };\n\n  return (\n    <Styles.Contaier>\n\n<h2 style={{ color: \"Black\",font: \"-moz-initial\", textAlign: \"center\" }}>Who are already onboard</h2>\n      <Styles.FormHolder ref={form} onSubmit={(e) => handleSubmit(e)}>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Name</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"name\"\n            type=\"text\"\n            value={name}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.name}\n            required\n          />\n          {inputErrors?.name ? (\n            <Styles.ShowError>{inputErrors?.name}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Company</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"company\"\n            type=\"text\"\n            value={company}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.company}\n            required\n          />\n          {inputErrors?.company ? (\n            <Styles.ShowError>{inputErrors?.company}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Email</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"email\"\n            type=\"email\"\n            value={email}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.email}\n            required\n          />\n          {inputErrors?.email ? (\n            <Styles.ShowError>{inputErrors?.email}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.RowHolder>\n          <Styles.LabelHolder>Contact Details</Styles.LabelHolder>\n          <Styles.InputHolder\n            name=\"mobile\"\n            type=\"tel\"\n            value={mobile}\n            onChange={(e) => handleChange(e)}\n            error={inputErrors?.mobile}\n            required\n          />\n          {inputErrors?.mobile ? (\n            <Styles.ShowError>{inputErrors?.mobile}</Styles.ShowError>\n          ) : null}\n        </Styles.RowHolder>\n        <Styles.Button\n          success={success?.delivered}\n          type=\"submit\"\n          disabled={success?.delivered}\n        >\n          {loading && !success?.delivered\n            ? \"Sending....\"\n            : !loading && success?.delivered\n            ? \"Sent Successfully\"\n            : \"Send Message\"}\n        </Styles.Button>\n      </Styles.FormHolder>\n      <Styles.ShowSuccess>{success?.msg}</Styles.ShowSuccess>\n    </Styles.Contaier>\n  );\n};\n\nexport default ContactForm;\n", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/header/mobile/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/input-field/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/signup/info/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/index.jsx", ["457"], [], "import React from \"react\";\n\nimport { AssetsList } from \"../../../elements/assetsList\";\n\nimport * as Styles from \"./styles\";\n\nconst ServiceItem = ({ title }) => {\n  return (\n    <Styles.Container>\n      {/* <Styles.ImageHolder src={AssetsList.logo} /> */}\n      <Styles.TextWrapper title={title}>{title}</Styles.TextWrapper>\n    </Styles.Container>\n  );\n};\n\nexport default ServiceItem;\n", "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/servicesData.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/data.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/list/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/name-card/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/clients/item/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/list/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/contact-us/form/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/paragraph/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/about/card/card.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/projects/project-details/service-item/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/index.jsx", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/services/card/styles.js", [], [], "/Users/<USER>/Mac/Projects/Frontend/capastrength/src/components/global/ErrorBoundary.js", [], [], {"ruleId": "458", "severity": 1, "message": "459", "line": 10, "column": 8, "nodeType": "460", "messageId": "461", "endLine": 10, "endColumn": 14}, {"ruleId": "458", "severity": 1, "message": "462", "line": 13, "column": 8, "nodeType": "460", "messageId": "461", "endLine": 13, "endColumn": 14}, {"ruleId": "458", "severity": 1, "message": "463", "line": 16, "column": 9, "nodeType": "460", "messageId": "461", "endLine": 16, "endColumn": 17}, {"ruleId": "458", "severity": 1, "message": "464", "line": 31, "column": 11, "nodeType": "460", "messageId": "461", "endLine": 31, "endColumn": 15}, {"ruleId": "458", "severity": 1, "message": "465", "line": 1, "column": 38, "nodeType": "460", "messageId": "461", "endLine": 1, "endColumn": 49}, {"ruleId": "458", "severity": 1, "message": "466", "line": 10, "column": 8, "nodeType": "460", "messageId": "461", "endLine": 10, "endColumn": 16}, {"ruleId": "458", "severity": 1, "message": "467", "line": 15, "column": 3, "nodeType": "460", "messageId": "461", "endLine": 15, "endColumn": 23}, {"ruleId": "458", "severity": 1, "message": "468", "line": 16, "column": 3, "nodeType": "460", "messageId": "461", "endLine": 16, "endColumn": 18}, {"ruleId": "458", "severity": 1, "message": "469", "line": 17, "column": 3, "nodeType": "460", "messageId": "461", "endLine": 17, "endColumn": 14}, {"ruleId": "458", "severity": 1, "message": "470", "line": 18, "column": 3, "nodeType": "460", "messageId": "461", "endLine": 18, "endColumn": 18}, {"ruleId": "458", "severity": 1, "message": "471", "line": 19, "column": 3, "nodeType": "460", "messageId": "461", "endLine": 19, "endColumn": 12}, {"ruleId": "458", "severity": 1, "message": "472", "line": 20, "column": 3, "nodeType": "460", "messageId": "461", "endLine": 20, "endColumn": 12}, {"ruleId": "458", "severity": 1, "message": "473", "line": 49, "column": 9, "nodeType": "460", "messageId": "461", "endLine": 49, "endColumn": 22}, {"ruleId": "458", "severity": 1, "message": "474", "line": 5, "column": 10, "nodeType": "460", "messageId": "461", "endLine": 5, "endColumn": 20}, {"ruleId": "458", "severity": 1, "message": "475", "line": 5, "column": 8, "nodeType": "460", "messageId": "461", "endLine": 5, "endColumn": 20}, {"ruleId": "458", "severity": 1, "message": "476", "line": 12, "column": 19, "nodeType": "460", "messageId": "461", "endLine": 12, "endColumn": 29}, {"ruleId": "458", "severity": 1, "message": "474", "line": 3, "column": 10, "nodeType": "460", "messageId": "461", "endLine": 3, "endColumn": 20}, "no-unused-vars", "'Signup' is defined but never used.", "Identifier", "unusedVar", "'Slider' is defined but never used.", "'isMobile' is assigned a value but never used.", "'name' is assigned a value but never used.", "'useCallback' is defined but never used.", "'Projects' is defined but never used.", "'industrialCommercial' is defined but never used.", "'residentialData' is defined but never used.", "'collegeData' is defined but never used.", "'publicTrustData' is defined but never used.", "'hotelData' is defined but never used.", "'mallsData' is defined but never used.", "'allResponsive' is assigned a value but never used.", "'AssetsList' is defined but never used.", "'ServicesCard' is defined but never used.", "'setSuccess' is assigned a value but never used."]