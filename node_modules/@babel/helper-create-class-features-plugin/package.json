{"name": "@babel/helper-create-class-features-plugin", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "Compile class public and private fields, private methods and decorators to ES6", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-class-features-plugin"}, "main": "./lib/index.js", "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.18.9", "@babel/helper-member-expression-to-functions": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-replace-supers": "^7.18.9", "@babel/helper-split-export-declaration": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/preset-env": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}