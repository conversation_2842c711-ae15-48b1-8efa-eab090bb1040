{"version": 3, "file": "gen-mapping.umd.js", "sources": ["../src/sourcemap-segment.ts", "../src/gen-mapping.ts"], "sourcesContent": ["type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n", "import { SetArray, put } from '@jridgewell/set-array';\nimport { encode } from '@jridgewell/sourcemap-codec';\nimport { TraceMap, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\n\nimport type { SourceMapInput } from '@jridgewell/trace-mapping';\nimport type { SourceMapSegment } from './sourcemap-segment';\nimport type { DecodedSourceMap, EncodedSourceMap, Pos, Mapping } from './types';\n\nexport type { DecodedSourceMap, EncodedSourceMap, Mapping };\n\nexport type Options = {\n  file?: string | null;\n  sourceRoot?: string | null;\n};\n\nconst NO_NAME = -1;\n\n/**\n * A low-level API to associate a generated position with an original source position. Line and\n * column here are 0-based, unlike `addMapping`.\n */\nexport let addSegment: {\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source?: null,\n    sourceLine?: null,\n    sourceColumn?: null,\n    name?: null,\n    content?: null,\n  ): void;\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source: string,\n    sourceLine: number,\n    sourceColumn: number,\n    name?: null,\n    content?: string | null,\n  ): void;\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source: string,\n    sourceLine: number,\n    sourceColumn: number,\n    name: string,\n    content?: string | null,\n  ): void;\n};\n\n/**\n * A high-level API to associate a generated position with an original source position. Line is\n * 1-based, but column is 0-based, due to legacy behavior in `source-map` library.\n */\nexport let addMapping: {\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source?: null;\n      original?: null;\n      name?: null;\n      content?: null;\n    },\n  ): void;\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source: string;\n      original: Pos;\n      name?: null;\n      content?: string | null;\n    },\n  ): void;\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source: string;\n      original: Pos;\n      name: string;\n      content?: string | null;\n    },\n  ): void;\n};\n\n/**\n * Same as `addSegment`, but will only add the segment if it generates useful information in the\n * resulting map. This only works correctly if segments are added **in order**, meaning you should\n * not add a segment with a lower generated line/column than one that came before.\n */\nexport let maybeAddSegment: typeof addSegment;\n\n/**\n * Same as `addMapping`, but will only add the mapping if it generates useful information in the\n * resulting map. This only works correctly if mappings are added **in order**, meaning you should\n * not add a mapping with a lower generated line/column than one that came before.\n */\nexport let maybeAddMapping: typeof addMapping;\n\n/**\n * Adds/removes the content of the source file to the source map.\n */\nexport let setSourceContent: (map: GenMapping, source: string, content: string | null) => void;\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport let toDecodedMap: (map: GenMapping) => DecodedSourceMap;\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport let toEncodedMap: (map: GenMapping) => EncodedSourceMap;\n\n/**\n * Constructs a new GenMapping, using the already present mappings of the input.\n */\nexport let fromMap: (input: SourceMapInput) => GenMapping;\n\n/**\n * Returns an array of high-level mapping objects for every recorded segment, which could then be\n * passed to the `source-map` library.\n */\nexport let allMappings: (map: GenMapping) => Mapping[];\n\n// This split declaration is only so that terser can elminiate the static initialization block.\nlet addSegmentInternal: <S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: S,\n  sourceLine: S extends string ? number : null | undefined,\n  sourceColumn: S extends string ? number : null | undefined,\n  name: S extends string ? string | null | undefined : null | undefined,\n  content: S extends string ? string | null | undefined : null | undefined,\n) => void;\n\n/**\n * Provides the state to generate a sourcemap.\n */\nexport class GenMapping {\n  private _names = new SetArray();\n  private _sources = new SetArray();\n  private _sourcesContent: (string | null)[] = [];\n  private _mappings: SourceMapSegment[][] = [];\n  declare file: string | null | undefined;\n  declare sourceRoot: string | null | undefined;\n\n  constructor({ file, sourceRoot }: Options = {}) {\n    this.file = file;\n    this.sourceRoot = sourceRoot;\n  }\n\n  static {\n    addSegment = (map, genLine, genColumn, source, sourceLine, sourceColumn, name, content) => {\n      return addSegmentInternal(\n        false,\n        map,\n        genLine,\n        genColumn,\n        source,\n        sourceLine,\n        sourceColumn,\n        name,\n        content,\n      );\n    };\n\n    maybeAddSegment = (\n      map,\n      genLine,\n      genColumn,\n      source,\n      sourceLine,\n      sourceColumn,\n      name,\n      content,\n    ) => {\n      return addSegmentInternal(\n        true,\n        map,\n        genLine,\n        genColumn,\n        source,\n        sourceLine,\n        sourceColumn,\n        name,\n        content,\n      );\n    };\n\n    addMapping = (map, mapping) => {\n      return addMappingInternal(false, map, mapping as Parameters<typeof addMappingInternal>[2]);\n    };\n\n    maybeAddMapping = (map, mapping) => {\n      return addMappingInternal(true, map, mapping as Parameters<typeof addMappingInternal>[2]);\n    };\n\n    setSourceContent = (map, source, content) => {\n      const { _sources: sources, _sourcesContent: sourcesContent } = map;\n      sourcesContent[put(sources, source)] = content;\n    };\n\n    toDecodedMap = (map) => {\n      const {\n        file,\n        sourceRoot,\n        _mappings: mappings,\n        _sources: sources,\n        _sourcesContent: sourcesContent,\n        _names: names,\n      } = map;\n      removeEmptyFinalLines(mappings);\n\n      return {\n        version: 3,\n        file: file || undefined,\n        names: names.array,\n        sourceRoot: sourceRoot || undefined,\n        sources: sources.array,\n        sourcesContent,\n        mappings,\n      };\n    };\n\n    toEncodedMap = (map) => {\n      const decoded = toDecodedMap(map);\n      return {\n        ...decoded,\n        mappings: encode(decoded.mappings as SourceMapSegment[][]),\n      };\n    };\n\n    allMappings = (map) => {\n      const out: Mapping[] = [];\n      const { _mappings: mappings, _sources: sources, _names: names } = map;\n\n      for (let i = 0; i < mappings.length; i++) {\n        const line = mappings[i];\n        for (let j = 0; j < line.length; j++) {\n          const seg = line[j];\n\n          const generated = { line: i + 1, column: seg[COLUMN] };\n          let source: string | undefined = undefined;\n          let original: Pos | undefined = undefined;\n          let name: string | undefined = undefined;\n\n          if (seg.length !== 1) {\n            source = sources.array[seg[SOURCES_INDEX]];\n            original = { line: seg[SOURCE_LINE] + 1, column: seg[SOURCE_COLUMN] };\n\n            if (seg.length === 5) name = names.array[seg[NAMES_INDEX]];\n          }\n\n          out.push({ generated, source, original, name } as Mapping);\n        }\n      }\n\n      return out;\n    };\n\n    fromMap = (input) => {\n      const map = new TraceMap(input);\n      const gen = new GenMapping({ file: map.file, sourceRoot: map.sourceRoot });\n\n      putAll(gen._names, map.names);\n      putAll(gen._sources, map.sources as string[]);\n      gen._sourcesContent = map.sourcesContent || map.sources.map(() => null);\n      gen._mappings = decodedMappings(map) as GenMapping['_mappings'];\n\n      return gen;\n    };\n\n    // Internal helpers\n    addSegmentInternal = (\n      skipable,\n      map,\n      genLine,\n      genColumn,\n      source,\n      sourceLine,\n      sourceColumn,\n      name,\n      content,\n    ) => {\n      const {\n        _mappings: mappings,\n        _sources: sources,\n        _sourcesContent: sourcesContent,\n        _names: names,\n      } = map;\n      const line = getLine(mappings, genLine);\n      const index = getColumnIndex(line, genColumn);\n\n      if (!source) {\n        if (skipable && skipSourceless(line, index)) return;\n        return insert(line, index, [genColumn]);\n      }\n\n      // Sigh, TypeScript can't figure out sourceLine and sourceColumn aren't nullish if source\n      // isn't nullish.\n      assert<number>(sourceLine);\n      assert<number>(sourceColumn);\n\n      const sourcesIndex = put(sources, source);\n      const namesIndex = name ? put(names, name) : NO_NAME;\n      if (sourcesIndex === sourcesContent.length) sourcesContent[sourcesIndex] = content ?? null;\n\n      if (skipable && skipSource(line, index, sourcesIndex, sourceLine, sourceColumn, namesIndex)) {\n        return;\n      }\n\n      return insert(\n        line,\n        index,\n        name\n          ? [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex]\n          : [genColumn, sourcesIndex, sourceLine, sourceColumn],\n      );\n    };\n  }\n}\n\nfunction assert<T>(_val: unknown): asserts _val is T {\n  // noop.\n}\n\nfunction getLine(mappings: SourceMapSegment[][], index: number): SourceMapSegment[] {\n  for (let i = mappings.length; i <= index; i++) {\n    mappings[i] = [];\n  }\n  return mappings[index];\n}\n\nfunction getColumnIndex(line: SourceMapSegment[], genColumn: number): number {\n  let index = line.length;\n  for (let i = index - 1; i >= 0; index = i--) {\n    const current = line[i];\n    if (genColumn >= current[COLUMN]) break;\n  }\n  return index;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\nfunction removeEmptyFinalLines(mappings: SourceMapSegment[][]) {\n  const { length } = mappings;\n  let len = length;\n  for (let i = len - 1; i >= 0; len = i, i--) {\n    if (mappings[i].length > 0) break;\n  }\n  if (len < length) mappings.length = len;\n}\n\nfunction putAll(strarr: SetArray, array: string[]) {\n  for (let i = 0; i < array.length; i++) put(strarr, array[i]);\n}\n\nfunction skipSourceless(line: SourceMapSegment[], index: number): boolean {\n  // The start of a line is already sourceless, so adding a sourceless segment to the beginning\n  // doesn't generate any useful information.\n  if (index === 0) return true;\n\n  const prev = line[index - 1];\n  // If the previous segment is also sourceless, then adding another sourceless segment doesn't\n  // genrate any new information. Else, this segment will end the source/named segment and point to\n  // a sourceless position, which is useful.\n  return prev.length === 1;\n}\n\nfunction skipSource(\n  line: SourceMapSegment[],\n  index: number,\n  sourcesIndex: number,\n  sourceLine: number,\n  sourceColumn: number,\n  namesIndex: number,\n): boolean {\n  // A source/named segment at the start of a line gives position at that genColumn\n  if (index === 0) return false;\n\n  const prev = line[index - 1];\n\n  // If the previous segment is sourceless, then we're transitioning to a source.\n  if (prev.length === 1) return false;\n\n  // If the previous segment maps to the exact same source position, then this segment doesn't\n  // provide any new position information.\n  return (\n    sourcesIndex === prev[SOURCES_INDEX] &&\n    sourceLine === prev[SOURCE_LINE] &&\n    sourceColumn === prev[SOURCE_COLUMN] &&\n    namesIndex === (prev.length === 5 ? prev[NAMES_INDEX] : NO_NAME)\n  );\n}\n\nfunction addMappingInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: S;\n    original: S extends string ? Pos : null | undefined;\n    name: S extends string ? string | null | undefined : null | undefined;\n    content: S extends string ? string | null | undefined : null | undefined;\n  },\n) {\n  const { generated, source, original, name, content } = mapping;\n  if (!source) {\n    return addSegmentInternal(\n      skipable,\n      map,\n      generated.line - 1,\n      generated.column,\n      null,\n      null,\n      null,\n      null,\n      null,\n    );\n  }\n  const s: string = source;\n  assert<Pos>(original);\n  return addSegmentInternal(\n    skipable,\n    map,\n    generated.line - 1,\n    generated.column,\n    s,\n    original.line - 1,\n    original.column,\n    name,\n    content,\n  );\n}\n"], "names": ["addSegment", "addMapping", "maybeAddSegment", "maybeAddMapping", "setSourceContent", "toDecodedMap", "toEncodedMap", "fromMap", "allMappings", "SetArray", "put", "encode", "TraceMap", "decodedMappings"], "mappings": ";;;;;;IAWO,MAAM,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM,aAAa,GAAG,CAAC,CAAC;IACxB,MAAM,WAAW,GAAG,CAAC,CAAC;IACtB,MAAM,aAAa,GAAG,CAAC,CAAC;IACxB,MAAM,WAAW,GAAG,CAAC;;ICQ5B,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;IAEnB;;;IAGG;AACQA,gCA+BT;IAEF;;;IAGG;AACQC,gCA+BT;IAEF;;;;IAIG;AACQC,qCAAmC;IAE9C;;;;IAIG;AACQC,qCAAmC;IAE9C;;IAEG;AACQC,sCAAoF;IAE/F;;;IAGG;AACQC,kCAAoD;IAE/D;;;IAGG;AACQC,kCAAoD;IAE/D;;IAEG;AACQC,6BAA+C;IAE1D;;;IAGG;AACQC,iCAA4C;IAEvD;IACA,IAAI,kBAUK,CAAC;IAEV;;IAEG;UACU,UAAU,CAAA;IAQrB,IAAA,WAAA,CAAY,EAAE,IAAI,EAAE,UAAU,KAAc,EAAE,EAAA;IAPtC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAIC,iBAAQ,EAAE,CAAC;IACxB,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAIA,iBAAQ,EAAE,CAAC;YAC1B,IAAe,CAAA,eAAA,GAAsB,EAAE,CAAC;YACxC,IAAS,CAAA,SAAA,GAAyB,EAAE,CAAC;IAK3C,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;SAC9B;IA2KF,CAAA;IAzKC,CAAA,MAAA;IACE,IAAAT,kBAAU,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,KAAI;YACxF,OAAO,kBAAkB,CACvB,KAAK,EACL,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,CACR,CAAC;IACJ,KAAC,CAAC;IAEF,IAAAE,uBAAe,GAAG,CAChB,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,KACL;YACF,OAAO,kBAAkB,CACvB,IAAI,EACJ,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,CACR,CAAC;IACJ,KAAC,CAAC;IAEF,IAAAD,kBAAU,GAAG,CAAC,GAAG,EAAE,OAAO,KAAI;YAC5B,OAAO,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAmD,CAAC,CAAC;IAC7F,KAAC,CAAC;IAEF,IAAAE,uBAAe,GAAG,CAAC,GAAG,EAAE,OAAO,KAAI;YACjC,OAAO,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAmD,CAAC,CAAC;IAC5F,KAAC,CAAC;QAEFC,wBAAgB,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,KAAI;YAC1C,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC;YACnE,cAAc,CAACM,YAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;IACjD,KAAC,CAAC;IAEF,IAAAL,oBAAY,GAAG,CAAC,GAAG,KAAI;YACrB,MAAM,EACJ,IAAI,EACJ,UAAU,EACV,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,GAAG,CAAC;YACR,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAEhC,OAAO;IACL,YAAA,OAAO,EAAE,CAAC;gBACV,IAAI,EAAE,IAAI,IAAI,SAAS;gBACvB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,UAAU,EAAE,UAAU,IAAI,SAAS;gBACnC,OAAO,EAAE,OAAO,CAAC,KAAK;gBACtB,cAAc;gBACd,QAAQ;aACT,CAAC;IACJ,KAAC,CAAC;IAEF,IAAAC,oBAAY,GAAG,CAAC,GAAG,KAAI;IACrB,QAAA,MAAM,OAAO,GAAGD,oBAAY,CAAC,GAAG,CAAC,CAAC;YAClC,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,QAAQ,EAAEM,qBAAM,CAAC,OAAO,CAAC,QAAgC,CAAC,EAC1D,CAAA,CAAA;IACJ,KAAC,CAAC;IAEF,IAAAH,mBAAW,GAAG,CAAC,GAAG,KAAI;YACpB,MAAM,GAAG,GAAc,EAAE,CAAC;IAC1B,QAAA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC;IAEtE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACxC,YAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAEpB,gBAAA,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBACvD,IAAI,MAAM,GAAuB,SAAS,CAAC;oBAC3C,IAAI,QAAQ,GAAoB,SAAS,CAAC;oBAC1C,IAAI,IAAI,GAAuB,SAAS,CAAC;IAEzC,gBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;wBACpB,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;IAC3C,oBAAA,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;IAEtE,oBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;4BAAE,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;IAC5D,iBAAA;IAED,gBAAA,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAa,CAAC,CAAC;IAC5D,aAAA;IACF,SAAA;IAED,QAAA,OAAO,GAAG,CAAC;IACb,KAAC,CAAC;IAEF,IAAAD,eAAO,GAAG,CAAC,KAAK,KAAI;IAClB,QAAA,MAAM,GAAG,GAAG,IAAIK,qBAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,QAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;YAE3E,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAmB,CAAC,CAAC;IAC9C,QAAA,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,cAAc,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;IACxE,QAAA,GAAG,CAAC,SAAS,GAAGC,4BAAe,CAAC,GAAG,CAA4B,CAAC;IAEhE,QAAA,OAAO,GAAG,CAAC;IACb,KAAC,CAAC;;QAGF,kBAAkB,GAAG,CACnB,QAAQ,EACR,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,KACL;IACF,QAAA,MAAM,EACJ,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,GAAG,CAAC;YACR,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACxC,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAE9C,IAAI,CAAC,MAAM,EAAE;IACX,YAAA,IAAI,QAAQ,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;oBAAE,OAAO;gBACpD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IACzC,SAAA;YAOD,MAAM,YAAY,GAAGH,YAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,GAAGA,YAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC;IACrD,QAAA,IAAI,YAAY,KAAK,cAAc,CAAC,MAAM;gBAAE,cAAc,CAAC,YAAY,CAAC,GAAG,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,OAAO,GAAI,IAAI,CAAC;IAE3F,QAAA,IAAI,QAAQ,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE;gBAC3F,OAAO;IACR,SAAA;IAED,QAAA,OAAO,MAAM,CACX,IAAI,EACJ,KAAK,EACL,IAAI;kBACA,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC;kBAC/D,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CACxD,CAAC;IACJ,KAAC,CAAC;IACJ,CAAC,GAAA,CAAA;IAOH,SAAS,OAAO,CAAC,QAA8B,EAAE,KAAa,EAAA;IAC5D,IAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;IAC7C,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IAClB,KAAA;IACD,IAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,SAAS,cAAc,CAAC,IAAwB,EAAE,SAAiB,EAAA;IACjE,IAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IACxB,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE;IAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,QAAA,IAAI,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;gBAAE,MAAM;IACzC,KAAA;IACD,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,MAAM,CAAI,KAAU,EAAE,KAAa,EAAE,KAAQ,EAAA;IACpD,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YACzC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzB,KAAA;IACD,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,SAAS,qBAAqB,CAAC,QAA8B,EAAA;IAC3D,IAAA,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;QAC5B,IAAI,GAAG,GAAG,MAAM,CAAC;IACjB,IAAA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1C,QAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;gBAAE,MAAM;IACnC,KAAA;QACD,IAAI,GAAG,GAAG,MAAM;IAAE,QAAA,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;IAC1C,CAAC;IAED,SAAS,MAAM,CAAC,MAAgB,EAAE,KAAe,EAAA;IAC/C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;YAAEA,YAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,SAAS,cAAc,CAAC,IAAwB,EAAE,KAAa,EAAA;;;QAG7D,IAAI,KAAK,KAAK,CAAC;IAAE,QAAA,OAAO,IAAI,CAAC;QAE7B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;;;IAI7B,IAAA,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,SAAS,UAAU,CACjB,IAAwB,EACxB,KAAa,EACb,YAAoB,EACpB,UAAkB,EAClB,YAAoB,EACpB,UAAkB,EAAA;;QAGlB,IAAI,KAAK,KAAK,CAAC;IAAE,QAAA,OAAO,KAAK,CAAC;QAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;IAG7B,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;IAAE,QAAA,OAAO,KAAK,CAAC;;;IAIpC,IAAA,QACE,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC;IACpC,QAAA,UAAU,KAAK,IAAI,CAAC,WAAW,CAAC;IAChC,QAAA,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC;YACpC,UAAU,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,EAChE;IACJ,CAAC;IAED,SAAS,kBAAkB,CACzB,QAAiB,EACjB,GAAe,EACf,OAMC,EAAA;IAED,IAAA,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAC/D,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,kBAAkB,CACvB,QAAQ,EACR,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CAAC;IACH,KAAA;QACD,MAAM,CAAC,GAAW,MAAM,CAAC;IAEzB,IAAA,OAAO,kBAAkB,CACvB,QAAQ,EACR,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,CAAC,EACD,QAAQ,CAAC,IAAI,GAAG,CAAC,EACjB,QAAQ,CAAC,MAAM,EACf,IAAI,EACJ,OAAO,CACR,CAAC;IACJ;;;;;;;;;;"}