{"name": "@babel/helper-builder-binary-assignment-operator-visitor", "version": "7.18.9", "description": "Helper function to build binary assignment operator visitors", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-builder-binary-assignment-operator-visitor"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-builder-binary-assignment-operator-visitor", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-explode-assignable-expression": "^7.18.6", "@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}