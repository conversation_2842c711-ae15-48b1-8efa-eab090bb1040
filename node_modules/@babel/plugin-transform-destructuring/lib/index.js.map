{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { Scope, NodePath } from \"@babel/traverse\";\nimport type { TraversalAncestors } from \"@babel/types\";\n\nexport function unshiftForXStatementBody(\n  statementPath: NodePath<t.ForXStatement>,\n  newStatements: t.Statement[],\n) {\n  statementPath.ensureBlock();\n  const { scope, node } = statementPath;\n  const bodyScopeBindings = statementPath.get(\"body\").scope.bindings;\n  const hasShadowedBlockScopedBindings = Object.keys(bodyScopeBindings).some(\n    name => scope.hasBinding(name),\n  );\n\n  if (hasShadowedBlockScopedBindings) {\n    // handle shadowed variables referenced in computed keys:\n    // var a = 0;for (const { #x: x, [a++]: y } of z) { const a = 1; }\n    node.body = t.blockStatement([...newStatements, node.body]);\n  } else {\n    node.body.body.unshift(...newStatements);\n  }\n}\n\n/**\n * Test if an ArrayPattern's elements contain any RestElements.\n */\n\nfunction hasArrayRest(pattern: t.ArrayPattern) {\n  return pattern.elements.some(elem => t.isRestElement(elem));\n}\n\n/**\n * Test if an ObjectPattern's properties contain any RestElements.\n */\n\nfunction hasObjectRest(pattern: t.ObjectPattern) {\n  return pattern.properties.some(prop => t.isRestElement(prop));\n}\n\ninterface UnpackableArrayExpression extends t.ArrayExpression {\n  elements: (null | t.Expression)[];\n}\n\nconst STOP_TRAVERSAL = {};\n\ninterface ArrayUnpackVisitorState {\n  deopt: boolean;\n  bindings: Record<string, t.Identifier>;\n}\n\n// NOTE: This visitor is meant to be used via t.traverse\nconst arrayUnpackVisitor = (\n  node: t.Node,\n  ancestors: TraversalAncestors,\n  state: ArrayUnpackVisitorState,\n) => {\n  if (!ancestors.length) {\n    // Top-level node: this is the array literal.\n    return;\n  }\n\n  if (\n    t.isIdentifier(node) &&\n    t.isReferenced(node, ancestors[ancestors.length - 1].node) &&\n    state.bindings[node.name]\n  ) {\n    state.deopt = true;\n    throw STOP_TRAVERSAL;\n  }\n};\n\nexport type DestructuringTransformerNode =\n  | t.VariableDeclaration\n  | t.ExpressionStatement\n  | t.ReturnStatement;\n\ninterface DestructuringTransformerOption {\n  blockHoist?: number;\n  operator?: t.AssignmentExpression[\"operator\"];\n  nodes?: DestructuringTransformerNode[];\n  kind?: t.VariableDeclaration[\"kind\"];\n  scope: Scope;\n  arrayLikeIsIterable: boolean;\n  iterableIsArray: boolean;\n  objectRestNoSymbols: boolean;\n  useBuiltIns: boolean;\n  addHelper: File[\"addHelper\"];\n}\nexport class DestructuringTransformer {\n  private blockHoist: number;\n  private operator: t.AssignmentExpression[\"operator\"];\n  arrayRefSet: Set<string>;\n  private nodes: DestructuringTransformerNode[];\n  private scope: Scope;\n  private kind: t.VariableDeclaration[\"kind\"];\n  private iterableIsArray: boolean;\n  private arrayLikeIsIterable: boolean;\n  private objectRestNoSymbols: boolean;\n  private useBuiltIns: boolean;\n  private addHelper: File[\"addHelper\"];\n  constructor(opts: DestructuringTransformerOption) {\n    this.blockHoist = opts.blockHoist;\n    this.operator = opts.operator;\n    this.arrayRefSet = new Set();\n    this.nodes = opts.nodes || [];\n    this.scope = opts.scope;\n    this.kind = opts.kind;\n    this.iterableIsArray = opts.iterableIsArray;\n    this.arrayLikeIsIterable = opts.arrayLikeIsIterable;\n    this.objectRestNoSymbols = opts.objectRestNoSymbols;\n    this.useBuiltIns = opts.useBuiltIns;\n    this.addHelper = opts.addHelper;\n  }\n\n  getExtendsHelper() {\n    return this.useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : this.addHelper(\"extends\");\n  }\n\n  buildVariableAssignment(\n    id: t.AssignmentExpression[\"left\"],\n    init: t.Expression,\n  ) {\n    let op = this.operator;\n    if (t.isMemberExpression(id)) op = \"=\";\n\n    let node: t.ExpressionStatement | t.VariableDeclaration;\n\n    if (op) {\n      node = t.expressionStatement(\n        t.assignmentExpression(\n          op,\n          id,\n          t.cloneNode(init) || this.scope.buildUndefinedNode(),\n        ),\n      );\n    } else {\n      let nodeInit: t.Expression;\n\n      if (this.kind === \"const\" && init === null) {\n        nodeInit = this.scope.buildUndefinedNode();\n      } else {\n        nodeInit = t.cloneNode(init);\n      }\n\n      node = t.variableDeclaration(this.kind, [\n        t.variableDeclarator(id, nodeInit),\n      ]);\n    }\n\n    //@ts-expect-error(todo): document block hoist property\n    node._blockHoist = this.blockHoist;\n\n    return node;\n  }\n\n  buildVariableDeclaration(id: t.Identifier, init: t.Expression) {\n    const declar = t.variableDeclaration(\"var\", [\n      t.variableDeclarator(t.cloneNode(id), t.cloneNode(init)),\n    ]);\n    // @ts-expect-error todo(flow->ts): avoid mutations\n    declar._blockHoist = this.blockHoist;\n    return declar;\n  }\n\n  push(id: t.LVal, _init: t.Expression | null) {\n    const init = t.cloneNode(_init);\n    if (t.isObjectPattern(id)) {\n      this.pushObjectPattern(id, init);\n    } else if (t.isArrayPattern(id)) {\n      this.pushArrayPattern(id, init);\n    } else if (t.isAssignmentPattern(id)) {\n      this.pushAssignmentPattern(id, init);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(id, init));\n    }\n  }\n\n  toArray(node: t.Expression, count?: boolean | number) {\n    if (\n      this.iterableIsArray ||\n      (t.isIdentifier(node) && this.arrayRefSet.has(node.name))\n    ) {\n      return node;\n    } else {\n      return this.scope.toArray(node, count, this.arrayLikeIsIterable);\n    }\n  }\n\n  pushAssignmentPattern(\n    { left, right }: t.AssignmentPattern,\n    valueRef: t.Expression | null,\n  ) {\n    // handle array init hole\n    // const [x = 42] = [,];\n    // -> const x = 42;\n    if (valueRef === null) {\n      this.push(left, right);\n      return;\n    }\n\n    // we need to assign the current value of the assignment to avoid evaluating\n    // it more than once\n    const tempId = this.scope.generateUidIdentifierBasedOnNode(valueRef);\n\n    this.nodes.push(this.buildVariableDeclaration(tempId, valueRef));\n\n    const tempConditional = t.conditionalExpression(\n      t.binaryExpression(\n        \"===\",\n        t.cloneNode(tempId),\n        this.scope.buildUndefinedNode(),\n      ),\n      right,\n      t.cloneNode(tempId),\n    );\n\n    if (t.isPattern(left)) {\n      let patternId;\n      let node;\n\n      if (this.kind === \"const\" || this.kind === \"let\") {\n        patternId = this.scope.generateUidIdentifier(tempId.name);\n        node = this.buildVariableDeclaration(patternId, tempConditional);\n      } else {\n        patternId = tempId;\n\n        node = t.expressionStatement(\n          t.assignmentExpression(\"=\", t.cloneNode(tempId), tempConditional),\n        );\n      }\n\n      this.nodes.push(node);\n      this.push(left, patternId);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(left, tempConditional));\n    }\n  }\n\n  pushObjectRest(\n    pattern: t.ObjectPattern,\n    objRef: t.Expression,\n    spreadProp: t.RestElement,\n    spreadPropIndex: number,\n  ) {\n    const value = buildObjectExcludingKeys(\n      pattern.properties.slice(0, spreadPropIndex) as t.ObjectProperty[],\n      objRef,\n      this.scope,\n      name => this.addHelper(name),\n      this.objectRestNoSymbols,\n      this.useBuiltIns,\n    );\n    this.nodes.push(this.buildVariableAssignment(spreadProp.argument, value));\n  }\n\n  pushObjectProperty(prop: t.ObjectProperty, propRef: t.Expression) {\n    if (t.isLiteral(prop.key)) prop.computed = true;\n\n    const pattern = prop.value as t.LVal;\n    const objRef = t.memberExpression(\n      t.cloneNode(propRef),\n      prop.key,\n      prop.computed,\n    );\n\n    if (t.isPattern(pattern)) {\n      this.push(pattern, objRef);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(pattern, objRef));\n    }\n  }\n\n  pushObjectPattern(pattern: t.ObjectPattern, objRef: t.Expression | null) {\n    // https://github.com/babel/babel/issues/681\n\n    if (!pattern.properties.length || objRef === null) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(\n            this.addHelper(\"objectDestructuringEmpty\"),\n            objRef !== null ? [objRef] : [],\n          ),\n        ),\n      );\n      return;\n    }\n\n    // if we have more than one properties in this pattern and the objectRef is a\n    // member expression then we need to assign it to a temporary variable so it's\n    // only evaluated once\n\n    if (pattern.properties.length > 1 && !this.scope.isStatic(objRef)) {\n      const temp = this.scope.generateUidIdentifierBasedOnNode(objRef);\n      this.nodes.push(this.buildVariableDeclaration(temp, objRef));\n      objRef = temp;\n    }\n\n    // Replace impure computed key expressions if we have a rest parameter\n    if (hasObjectRest(pattern)) {\n      let copiedPattern: t.ObjectPattern;\n      for (let i = 0; i < pattern.properties.length; i++) {\n        const prop = pattern.properties[i];\n        if (t.isRestElement(prop)) {\n          break;\n        }\n        const key = prop.key;\n        if (prop.computed && !this.scope.isPure(key)) {\n          const name = this.scope.generateUidIdentifierBasedOnNode(key);\n          this.nodes.push(\n            //@ts-expect-error PrivateName has been handled by destructuring-private\n            this.buildVariableDeclaration(name, key),\n          );\n          if (!copiedPattern) {\n            copiedPattern = pattern = {\n              ...pattern,\n              properties: pattern.properties.slice(),\n            };\n          }\n          copiedPattern.properties[i] = {\n            ...prop,\n            key: name,\n          };\n        }\n      }\n    }\n    //\n\n    for (let i = 0; i < pattern.properties.length; i++) {\n      const prop = pattern.properties[i];\n      if (t.isRestElement(prop)) {\n        this.pushObjectRest(pattern, objRef, prop, i);\n      } else {\n        this.pushObjectProperty(prop, objRef);\n      }\n    }\n  }\n\n  canUnpackArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: t.Expression,\n  ): arr is UnpackableArrayExpression {\n    // not an array so there's no way we can deal with this\n    if (!t.isArrayExpression(arr)) return false;\n\n    // pattern has less elements than the array and doesn't have a rest so some\n    // elements wont be evaluated\n    if (pattern.elements.length > arr.elements.length) return;\n    if (\n      pattern.elements.length < arr.elements.length &&\n      !hasArrayRest(pattern)\n    ) {\n      return false;\n    }\n\n    for (const elem of pattern.elements) {\n      // deopt on holes\n      if (!elem) return false;\n\n      // deopt on member expressions as they may be included in the RHS\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    for (const elem of arr.elements) {\n      // deopt on spread elements\n      if (t.isSpreadElement(elem)) return false;\n\n      // deopt call expressions as they might change values of LHS variables\n      if (t.isCallExpression(elem)) return false;\n\n      // deopt on member expressions as they may be getter/setters and have side-effects\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    // deopt on reference to left side identifiers\n    const bindings = t.getBindingIdentifiers(pattern);\n    const state: ArrayUnpackVisitorState = { deopt: false, bindings };\n\n    try {\n      t.traverse(arr, arrayUnpackVisitor, state);\n    } catch (e) {\n      if (e !== STOP_TRAVERSAL) throw e;\n    }\n\n    return !state.deopt;\n  }\n\n  pushUnpackedArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: UnpackableArrayExpression,\n  ) {\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n      if (t.isRestElement(elem)) {\n        this.push(elem.argument, t.arrayExpression(arr.elements.slice(i)));\n      } else {\n        this.push(elem, arr.elements[i]);\n      }\n    }\n  }\n\n  pushArrayPattern(pattern: t.ArrayPattern, arrayRef: t.Expression | null) {\n    if (arrayRef === null) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(this.addHelper(\"objectDestructuringEmpty\"), []),\n        ),\n      );\n      return;\n    }\n    if (!pattern.elements) return;\n\n    // optimise basic array destructuring of an array expression\n    //\n    // we can't do this to a pattern of unequal size to it's right hand\n    // array expression as then there will be values that wont be evaluated\n    //\n    // eg: let [a, b] = [1, 2];\n\n    if (this.canUnpackArrayPattern(pattern, arrayRef)) {\n      return this.pushUnpackedArrayPattern(pattern, arrayRef);\n    }\n\n    // if we have a rest then we need all the elements so don't tell\n    // `scope.toArray` to only get a certain amount\n\n    const count = !hasArrayRest(pattern) && pattern.elements.length;\n\n    // so we need to ensure that the `arrayRef` is an array, `scope.toArray` will\n    // return a locally bound identifier if it's been inferred to be an array,\n    // otherwise it'll be a call to a helper that will ensure it's one\n\n    const toArray = this.toArray(arrayRef, count);\n\n    if (t.isIdentifier(toArray)) {\n      // we've been given an identifier so it must have been inferred to be an\n      // array\n      arrayRef = toArray;\n    } else {\n      arrayRef = this.scope.generateUidIdentifierBasedOnNode(arrayRef);\n      this.arrayRefSet.add(arrayRef.name);\n      this.nodes.push(this.buildVariableDeclaration(arrayRef, toArray));\n    }\n\n    //\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n\n      // hole\n      if (!elem) continue;\n\n      let elemRef;\n\n      if (t.isRestElement(elem)) {\n        elemRef = this.toArray(arrayRef);\n        elemRef = t.callExpression(\n          t.memberExpression(elemRef, t.identifier(\"slice\")),\n          [t.numericLiteral(i)],\n        );\n\n        // set the element to the rest element argument since we've dealt with it\n        // being a rest already\n        this.push(elem.argument, elemRef);\n      } else {\n        elemRef = t.memberExpression(arrayRef, t.numericLiteral(i), true);\n        this.push(elem, elemRef);\n      }\n    }\n  }\n\n  init(pattern: t.LVal, ref: t.Expression) {\n    // trying to destructure a value that we can't evaluate more than once so we\n    // need to save it to a variable\n\n    if (!t.isArrayExpression(ref) && !t.isMemberExpression(ref)) {\n      const memo = this.scope.maybeGenerateMemoised(ref, true);\n      if (memo) {\n        this.nodes.push(this.buildVariableDeclaration(memo, t.cloneNode(ref)));\n        ref = memo;\n      }\n    }\n\n    //\n\n    this.push(pattern, ref);\n\n    return this.nodes;\n  }\n}\n\ninterface ExcludingKey {\n  key: t.Expression | t.PrivateName;\n  computed: boolean;\n}\n\nexport function buildObjectExcludingKeys<T extends ExcludingKey>(\n  excludedKeys: T[],\n  objRef: t.Expression,\n  scope: Scope,\n  addHelper: File[\"addHelper\"],\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n): t.CallExpression {\n  // get all the keys that appear in this object before the current spread\n\n  const keys = [];\n  let allLiteral = true;\n  let hasTemplateLiteral = false;\n  for (let i = 0; i < excludedKeys.length; i++) {\n    const prop = excludedKeys[i];\n    const key = prop.key;\n    if (t.isIdentifier(key) && !prop.computed) {\n      keys.push(t.stringLiteral(key.name));\n    } else if (t.isTemplateLiteral(key)) {\n      keys.push(t.cloneNode(key));\n      hasTemplateLiteral = true;\n    } else if (t.isLiteral(key)) {\n      // @ts-expect-error todo(flow->ts) NullLiteral\n      keys.push(t.stringLiteral(String(key.value)));\n    } else if (t.isPrivateName(key)) {\n      // private key is not enumerable\n    } else {\n      keys.push(t.cloneNode(key));\n      allLiteral = false;\n    }\n  }\n\n  let value;\n  if (keys.length === 0) {\n    const extendsHelper = useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : addHelper(\"extends\");\n    value = t.callExpression(extendsHelper, [\n      t.objectExpression([]),\n      t.cloneNode(objRef),\n    ]);\n  } else {\n    let keyExpression: t.Expression = t.arrayExpression(keys);\n\n    if (!allLiteral) {\n      keyExpression = t.callExpression(\n        t.memberExpression(keyExpression, t.identifier(\"map\")),\n        [addHelper(\"toPropertyKey\")],\n      );\n    } else if (!hasTemplateLiteral && !t.isProgram(scope.block)) {\n      // Hoist definition of excluded keys, so that it's not created each time.\n      const programScope = scope.getProgramParent();\n      const id = programScope.generateUidIdentifier(\"excluded\");\n\n      programScope.push({\n        id,\n        init: keyExpression,\n        kind: \"const\",\n      });\n\n      keyExpression = t.cloneNode(id);\n    }\n\n    value = t.callExpression(\n      addHelper(`objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`),\n      [t.cloneNode(objRef), keyExpression],\n    );\n  }\n  return value;\n}\n\nexport function convertVariableDeclaration(\n  path: NodePath<t.VariableDeclaration>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope } = path;\n\n  const nodeKind = node.kind;\n  const nodeLoc = node.loc;\n  const nodes = [];\n\n  for (let i = 0; i < node.declarations.length; i++) {\n    const declar = node.declarations[i];\n\n    const patternId = declar.init;\n    const pattern = declar.id;\n\n    const destructuring: DestructuringTransformer =\n      new DestructuringTransformer({\n        // @ts-expect-error(todo): avoid internal properties access\n        blockHoist: node._blockHoist,\n        nodes: nodes,\n        scope: scope,\n        kind: node.kind,\n        iterableIsArray,\n        arrayLikeIsIterable,\n        useBuiltIns,\n        objectRestNoSymbols,\n        addHelper,\n      });\n\n    if (t.isPattern(pattern)) {\n      destructuring.init(pattern, patternId);\n\n      if (+i !== node.declarations.length - 1) {\n        // we aren't the last declarator so let's just make the\n        // last transformed node inherit from us\n        t.inherits(nodes[nodes.length - 1], declar);\n      }\n    } else {\n      nodes.push(\n        t.inherits(\n          destructuring.buildVariableAssignment(pattern, patternId),\n          declar,\n        ),\n      );\n    }\n  }\n\n  let tail: t.VariableDeclaration | null = null;\n  const nodesOut = [];\n  for (const node of nodes) {\n    if (t.isVariableDeclaration(node)) {\n      if (tail !== null) {\n        // Create a single compound declarations\n        tail.declarations.push(...node.declarations);\n        continue;\n      } else {\n        // Make sure the original node kind is used for each compound declaration\n        node.kind = nodeKind;\n        tail = node;\n      }\n    } else {\n      tail = null;\n    }\n    // Propagate the original declaration node's location\n    if (!node.loc) {\n      node.loc = nodeLoc;\n    }\n    nodesOut.push(node);\n  }\n\n  if (nodesOut.length === 1) {\n    path.replaceWith(nodesOut[0]);\n  } else {\n    path.replaceWithMultiple(nodesOut);\n  }\n  scope.crawl();\n}\n\nexport function convertAssignmentExpression(\n  path: NodePath<t.AssignmentExpression>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope, parentPath } = path;\n\n  const nodes: DestructuringTransformerNode[] = [];\n\n  const destructuring = new DestructuringTransformer({\n    operator: node.operator,\n    scope: scope,\n    nodes: nodes,\n    arrayLikeIsIterable,\n    iterableIsArray,\n    objectRestNoSymbols,\n    useBuiltIns,\n    addHelper,\n  });\n\n  let ref: t.Identifier | void;\n  if (\n    (!parentPath.isExpressionStatement() &&\n      !parentPath.isSequenceExpression()) ||\n    path.isCompletionRecord()\n  ) {\n    ref = scope.generateUidIdentifierBasedOnNode(node.right, \"ref\");\n\n    nodes.push(\n      t.variableDeclaration(\"var\", [t.variableDeclarator(ref, node.right)]),\n    );\n\n    if (t.isArrayExpression(node.right)) {\n      destructuring.arrayRefSet.add(ref.name);\n    }\n  }\n\n  destructuring.init(node.left, ref || node.right);\n\n  if (ref) {\n    if (parentPath.isArrowFunctionExpression()) {\n      path.replaceWith(t.blockStatement([]));\n      nodes.push(t.returnStatement(t.cloneNode(ref)));\n    } else {\n      nodes.push(t.expressionStatement(t.cloneNode(ref)));\n    }\n  }\n\n  path.replaceWithMultiple(nodes);\n  scope.crawl();\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\nimport {\n  DestructuringTransformer,\n  convertVariableDeclaration,\n  convertAssignmentExpression,\n  unshiftForXStatementBody,\n  type DestructuringTransformerNode,\n} from \"./util\";\nexport { buildObjectExcludingKeys, unshiftForXStatementBody } from \"./util\";\nimport type { NodePath } from \"@babel/traverse\";\n\n/**\n * Test if a VariableDeclaration's declarations contains any Patterns.\n */\n\nfunction variableDeclarationHasPattern(node: t.VariableDeclaration) {\n  for (const declar of node.declarations) {\n    if (t.isPattern(declar.id)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport interface Options {\n  allowArrayLike?: boolean;\n  loose?: boolean;\n  useBuiltIns?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(7);\n\n  const { useBuiltIns = false } = options;\n\n  const iterableIsArray = (api.assumption(\"iterableIsArray\") ??\n    options.loose ??\n    false) as boolean;\n  const arrayLikeIsIterable = (options.allowArrayLike ??\n    api.assumption(\"arrayLikeIsIterable\") ??\n    false) as boolean;\n  const objectRestNoSymbols = (api.assumption(\"objectRestNoSymbols\") ??\n    options.loose ??\n    false) as boolean;\n\n  return {\n    name: \"transform-destructuring\",\n\n    visitor: {\n      ExportNamedDeclaration(path) {\n        const declaration = path.get(\"declaration\");\n        if (!declaration.isVariableDeclaration()) return;\n        if (!variableDeclarationHasPattern(declaration.node)) return;\n\n        const specifiers = [];\n\n        for (const name of Object.keys(path.getOuterBindingIdentifiers())) {\n          specifiers.push(\n            t.exportSpecifier(t.identifier(name), t.identifier(name)),\n          );\n        }\n\n        // Split the declaration and export list into two declarations so that the variable\n        // declaration can be split up later without needing to worry about not being a\n        // top-level statement.\n        path.replaceWith(declaration.node);\n        path.insertAfter(t.exportNamedDeclaration(null, specifiers));\n        path.scope.crawl();\n      },\n\n      ForXStatement(path: NodePath<t.ForXStatement>) {\n        const { node, scope } = path;\n        const left = node.left;\n\n        if (t.isPattern(left)) {\n          // for ({ length: k } in { abc: 3 });\n\n          const temp = scope.generateUidIdentifier(\"ref\");\n\n          node.left = t.variableDeclaration(\"var\", [\n            t.variableDeclarator(temp),\n          ]);\n\n          path.ensureBlock();\n          const statementBody = path.node.body.body;\n          const nodes = [];\n          // todo: the completion of a for statement can only be observed from\n          // a do block (or eval that we don't support),\n          // but the new do-expression proposal plans to ban iteration ends in the\n          // do block, maybe we can get rid of this\n          if (statementBody.length === 0 && path.isCompletionRecord()) {\n            nodes.unshift(t.expressionStatement(scope.buildUndefinedNode()));\n          }\n\n          nodes.unshift(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", left, t.cloneNode(temp)),\n            ),\n          );\n\n          unshiftForXStatementBody(path, nodes);\n          scope.crawl();\n          return;\n        }\n\n        if (!t.isVariableDeclaration(left)) return;\n\n        const pattern = left.declarations[0].id;\n        if (!t.isPattern(pattern)) return;\n\n        const key = scope.generateUidIdentifier(\"ref\");\n        node.left = t.variableDeclaration(left.kind, [\n          t.variableDeclarator(key, null),\n        ]);\n\n        const nodes: DestructuringTransformerNode[] = [];\n\n        const destructuring = new DestructuringTransformer({\n          kind: left.kind,\n          scope: scope,\n          nodes: nodes,\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n          addHelper: name => this.addHelper(name),\n        });\n\n        destructuring.init(pattern, key);\n\n        unshiftForXStatementBody(path, nodes);\n        scope.crawl();\n      },\n\n      CatchClause({ node, scope }) {\n        const pattern = node.param;\n        if (!t.isPattern(pattern)) return;\n\n        const ref = scope.generateUidIdentifier(\"ref\");\n        node.param = ref;\n\n        const nodes: DestructuringTransformerNode[] = [];\n\n        const destructuring = new DestructuringTransformer({\n          kind: \"let\",\n          scope: scope,\n          nodes: nodes,\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n          addHelper: name => this.addHelper(name),\n        });\n        destructuring.init(pattern, ref);\n\n        node.body.body = [...nodes, ...node.body.body];\n        scope.crawl();\n      },\n\n      AssignmentExpression(path, state) {\n        if (!t.isPattern(path.node.left)) return;\n        convertAssignmentExpression(\n          path,\n          name => state.addHelper(name),\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n        );\n      },\n\n      VariableDeclaration(path, state) {\n        const { node, parent } = path;\n        if (t.isForXStatement(parent)) return;\n        if (!parent || !path.container) return; // i don't know why this is necessary - TODO\n        if (!variableDeclarationHasPattern(node)) return;\n        convertVariableDeclaration(\n          path,\n          name => state.addHelper(name),\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n        );\n      },\n    },\n  };\n});\n"], "names": ["unshiftForXStatementBody", "statementPath", "newStatements", "ensureBlock", "scope", "node", "bodyScopeBindings", "get", "bindings", "hasShadowedBlockScopedBindings", "Object", "keys", "some", "name", "hasBinding", "body", "t", "blockStatement", "unshift", "hasArrayRest", "pattern", "elements", "elem", "isRestElement", "hasObjectRest", "properties", "prop", "STOP_TRAVERSAL", "arrayUnpackVisitor", "ancestors", "state", "length", "isIdentifier", "isReferenced", "de<PERSON>t", "DestructuringTransformer", "constructor", "opts", "blockHoist", "operator", "arrayRefSet", "nodes", "kind", "iterableIsArray", "arrayLikeIsIterable", "objectRestNoSymbols", "useBuiltIns", "addHelper", "Set", "getExtendsHelper", "memberExpression", "identifier", "buildVariableAssignment", "id", "init", "op", "isMemberExpression", "expressionStatement", "assignmentExpression", "cloneNode", "buildUndefinedNode", "nodeInit", "variableDeclaration", "variableDeclarator", "_blockHoist", "buildVariableDeclaration", "declar", "push", "_init", "isObjectPattern", "pushObjectPattern", "isArrayPattern", "pushArrayPattern", "isAssignmentPattern", "pushAssignmentPattern", "toArray", "count", "has", "left", "right", "valueRef", "tempId", "generateUidIdentifierBasedOnNode", "tempConditional", "conditionalExpression", "binaryExpression", "isPattern", "patternId", "generateUidIdentifier", "pushObjectRest", "objRef", "spreadProp", "spreadPropIndex", "value", "buildObjectExcludingKeys", "slice", "argument", "pushObjectProperty", "propRef", "isLiteral", "key", "computed", "callExpression", "isStatic", "temp", "copiedPattern", "i", "isPure", "canUnpackArrayPattern", "arr", "isArrayExpression", "isSpreadElement", "isCallExpression", "getBindingIdentifiers", "traverse", "e", "pushUnpackedArrayPattern", "arrayExpression", "arrayRef", "add", "elemRef", "numericLiteral", "ref", "memo", "maybeGenerateMemoised", "<PERSON><PERSON><PERSON><PERSON>", "allLiteral", "hasTemplateLiteral", "stringLiteral", "isTemplateLiteral", "String", "isPrivateName", "extendsHelper", "objectExpression", "keyExpression", "isProgram", "block", "programScope", "getProgramParent", "convertVariableDeclaration", "path", "nodeKind", "nodeLoc", "loc", "declarations", "destructuring", "inherits", "tail", "nodesOut", "isVariableDeclaration", "replaceWith", "replaceWithMultiple", "crawl", "convertAssignmentExpression", "parentPath", "isExpressionStatement", "isSequenceExpression", "isCompletionRecord", "isArrowFunctionExpression", "returnStatement", "variableDeclarationHasPattern", "declare", "api", "options", "assertVersion", "assumption", "loose", "allowArrayLike", "visitor", "ExportNamedDeclaration", "declaration", "specifiers", "getOuterBindingIdentifiers", "exportSpecifier", "insertAfter", "exportNamedDeclaration", "ForXStatement", "statementBody", "CatchClause", "param", "AssignmentExpression", "VariableDeclaration", "parent", "isForXStatement", "container"], "mappings": ";;;;;;;AAKO,SAASA,wBAAT,CACLC,aADK,EAELC,aAFK,EAGL;AACAD,EAAAA,aAAa,CAACE,WAAd,EAAA,CAAA;EACA,MAAM;IAAEC,KAAF;AAASC,IAAAA,IAAAA;AAAT,GAAA,GAAkBJ,aAAxB,CAAA;EACA,MAAMK,iBAAiB,GAAGL,aAAa,CAACM,GAAd,CAAkB,MAAlB,CAAA,CAA0BH,KAA1B,CAAgCI,QAA1D,CAAA;AACA,EAAA,MAAMC,8BAA8B,GAAGC,MAAM,CAACC,IAAP,CAAYL,iBAAZ,CAA+BM,CAAAA,IAA/B,CACrCC,IAAI,IAAIT,KAAK,CAACU,UAAN,CAAiBD,IAAjB,CAD6B,CAAvC,CAAA;;AAIA,EAAA,IAAIJ,8BAAJ,EAAoC;AAGlCJ,IAAAA,IAAI,CAACU,IAAL,GAAYC,UAAC,CAACC,cAAF,CAAiB,CAAC,GAAGf,aAAJ,EAAmBG,IAAI,CAACU,IAAxB,CAAjB,CAAZ,CAAA;AACD,GAJD,MAIO;IACLV,IAAI,CAACU,IAAL,CAAUA,IAAV,CAAeG,OAAf,CAAuB,GAAGhB,aAA1B,CAAA,CAAA;AACD,GAAA;AACF,CAAA;;AAMD,SAASiB,YAAT,CAAsBC,OAAtB,EAA+C;AAC7C,EAAA,OAAOA,OAAO,CAACC,QAAR,CAAiBT,IAAjB,CAAsBU,IAAI,IAAIN,UAAC,CAACO,aAAF,CAAgBD,IAAhB,CAA9B,CAAP,CAAA;AACD,CAAA;;AAMD,SAASE,aAAT,CAAuBJ,OAAvB,EAAiD;AAC/C,EAAA,OAAOA,OAAO,CAACK,UAAR,CAAmBb,IAAnB,CAAwBc,IAAI,IAAIV,UAAC,CAACO,aAAF,CAAgBG,IAAhB,CAAhC,CAAP,CAAA;AACD,CAAA;;AAMD,MAAMC,cAAc,GAAG,EAAvB,CAAA;;AAQA,MAAMC,kBAAkB,GAAG,CACzBvB,IADyB,EAEzBwB,SAFyB,EAGzBC,KAHyB,KAItB;AACH,EAAA,IAAI,CAACD,SAAS,CAACE,MAAf,EAAuB;AAErB,IAAA,OAAA;AACD,GAAA;;AAED,EAAA,IACEf,UAAC,CAACgB,YAAF,CAAe3B,IAAf,CACAW,IAAAA,UAAC,CAACiB,YAAF,CAAe5B,IAAf,EAAqBwB,SAAS,CAACA,SAAS,CAACE,MAAV,GAAmB,CAApB,CAAT,CAAgC1B,IAArD,CADA,IAEAyB,KAAK,CAACtB,QAAN,CAAeH,IAAI,CAACQ,IAApB,CAHF,EAIE;IACAiB,KAAK,CAACI,KAAN,GAAc,IAAd,CAAA;AACA,IAAA,MAAMP,cAAN,CAAA;AACD,GAAA;AACF,CAlBD,CAAA;;AAqCO,MAAMQ,wBAAN,CAA+B;EAYpCC,WAAW,CAACC,IAAD,EAAuC;AAAA,IAAA,IAAA,CAX1CC,UAW0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAV1CC,QAU0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CATlDC,WASkD,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAR1CC,KAQ0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAP1CrC,KAO0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAN1CsC,IAM0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAL1CC,eAK0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAJ1CC,mBAI0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAH1CC,mBAG0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAF1CC,WAE0C,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAD1CC,SAC0C,GAAA,KAAA,CAAA,CAAA;AAChD,IAAA,IAAA,CAAKT,UAAL,GAAkBD,IAAI,CAACC,UAAvB,CAAA;AACA,IAAA,IAAA,CAAKC,QAAL,GAAgBF,IAAI,CAACE,QAArB,CAAA;AACA,IAAA,IAAA,CAAKC,WAAL,GAAmB,IAAIQ,GAAJ,EAAnB,CAAA;AACA,IAAA,IAAA,CAAKP,KAAL,GAAaJ,IAAI,CAACI,KAAL,IAAc,EAA3B,CAAA;AACA,IAAA,IAAA,CAAKrC,KAAL,GAAaiC,IAAI,CAACjC,KAAlB,CAAA;AACA,IAAA,IAAA,CAAKsC,IAAL,GAAYL,IAAI,CAACK,IAAjB,CAAA;AACA,IAAA,IAAA,CAAKC,eAAL,GAAuBN,IAAI,CAACM,eAA5B,CAAA;AACA,IAAA,IAAA,CAAKC,mBAAL,GAA2BP,IAAI,CAACO,mBAAhC,CAAA;AACA,IAAA,IAAA,CAAKC,mBAAL,GAA2BR,IAAI,CAACQ,mBAAhC,CAAA;AACA,IAAA,IAAA,CAAKC,WAAL,GAAmBT,IAAI,CAACS,WAAxB,CAAA;AACA,IAAA,IAAA,CAAKC,SAAL,GAAiBV,IAAI,CAACU,SAAtB,CAAA;AACD,GAAA;;AAEDE,EAAAA,gBAAgB,GAAG;IACjB,OAAO,IAAA,CAAKH,WAAL,GACH9B,UAAC,CAACkC,gBAAF,CAAmBlC,UAAC,CAACmC,UAAF,CAAa,QAAb,CAAnB,EAA2CnC,UAAC,CAACmC,UAAF,CAAa,QAAb,CAA3C,CADG,GAEH,IAAKJ,CAAAA,SAAL,CAAe,SAAf,CAFJ,CAAA;AAGD,GAAA;;AAEDK,EAAAA,uBAAuB,CACrBC,EADqB,EAErBC,IAFqB,EAGrB;IACA,IAAIC,EAAE,GAAG,IAAA,CAAKhB,QAAd,CAAA;IACA,IAAIvB,UAAC,CAACwC,kBAAF,CAAqBH,EAArB,CAAJ,EAA8BE,EAAE,GAAG,GAAL,CAAA;AAE9B,IAAA,IAAIlD,IAAJ,CAAA;;AAEA,IAAA,IAAIkD,EAAJ,EAAQ;MACNlD,IAAI,GAAGW,UAAC,CAACyC,mBAAF,CACLzC,UAAC,CAAC0C,oBAAF,CACEH,EADF,EAEEF,EAFF,EAGErC,UAAC,CAAC2C,SAAF,CAAYL,IAAZ,CAAA,IAAqB,IAAKlD,CAAAA,KAAL,CAAWwD,kBAAX,EAHvB,CADK,CAAP,CAAA;AAOD,KARD,MAQO;AACL,MAAA,IAAIC,QAAJ,CAAA;;MAEA,IAAI,IAAA,CAAKnB,IAAL,KAAc,OAAd,IAAyBY,IAAI,KAAK,IAAtC,EAA4C;AAC1CO,QAAAA,QAAQ,GAAG,IAAA,CAAKzD,KAAL,CAAWwD,kBAAX,EAAX,CAAA;AACD,OAFD,MAEO;AACLC,QAAAA,QAAQ,GAAG7C,UAAC,CAAC2C,SAAF,CAAYL,IAAZ,CAAX,CAAA;AACD,OAAA;;AAEDjD,MAAAA,IAAI,GAAGW,UAAC,CAAC8C,mBAAF,CAAsB,IAAA,CAAKpB,IAA3B,EAAiC,CACtC1B,UAAC,CAAC+C,kBAAF,CAAqBV,EAArB,EAAyBQ,QAAzB,CADsC,CAAjC,CAAP,CAAA;AAGD,KAAA;;AAGDxD,IAAAA,IAAI,CAAC2D,WAAL,GAAmB,IAAA,CAAK1B,UAAxB,CAAA;AAEA,IAAA,OAAOjC,IAAP,CAAA;AACD,GAAA;;AAED4D,EAAAA,wBAAwB,CAACZ,EAAD,EAAmBC,IAAnB,EAAuC;AAC7D,IAAA,MAAMY,MAAM,GAAGlD,UAAC,CAAC8C,mBAAF,CAAsB,KAAtB,EAA6B,CAC1C9C,UAAC,CAAC+C,kBAAF,CAAqB/C,UAAC,CAAC2C,SAAF,CAAYN,EAAZ,CAArB,EAAsCrC,UAAC,CAAC2C,SAAF,CAAYL,IAAZ,CAAtC,CAD0C,CAA7B,CAAf,CAAA;AAIAY,IAAAA,MAAM,CAACF,WAAP,GAAqB,IAAA,CAAK1B,UAA1B,CAAA;AACA,IAAA,OAAO4B,MAAP,CAAA;AACD,GAAA;;AAEDC,EAAAA,IAAI,CAACd,EAAD,EAAae,KAAb,EAAyC;AAC3C,IAAA,MAAMd,IAAI,GAAGtC,UAAC,CAAC2C,SAAF,CAAYS,KAAZ,CAAb,CAAA;;AACA,IAAA,IAAIpD,UAAC,CAACqD,eAAF,CAAkBhB,EAAlB,CAAJ,EAA2B;AACzB,MAAA,IAAA,CAAKiB,iBAAL,CAAuBjB,EAAvB,EAA2BC,IAA3B,CAAA,CAAA;KADF,MAEO,IAAItC,UAAC,CAACuD,cAAF,CAAiBlB,EAAjB,CAAJ,EAA0B;AAC/B,MAAA,IAAA,CAAKmB,gBAAL,CAAsBnB,EAAtB,EAA0BC,IAA1B,CAAA,CAAA;KADK,MAEA,IAAItC,UAAC,CAACyD,mBAAF,CAAsBpB,EAAtB,CAAJ,EAA+B;AACpC,MAAA,IAAA,CAAKqB,qBAAL,CAA2BrB,EAA3B,EAA+BC,IAA/B,CAAA,CAAA;AACD,KAFM,MAEA;MACL,IAAKb,CAAAA,KAAL,CAAW0B,IAAX,CAAgB,IAAA,CAAKf,uBAAL,CAA6BC,EAA7B,EAAiCC,IAAjC,CAAhB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDqB,EAAAA,OAAO,CAACtE,IAAD,EAAqBuE,KAArB,EAA+C;AACpD,IAAA,IACE,KAAKjC,eAAL,IACC3B,UAAC,CAACgB,YAAF,CAAe3B,IAAf,CAAA,IAAwB,IAAKmC,CAAAA,WAAL,CAAiBqC,GAAjB,CAAqBxE,IAAI,CAACQ,IAA1B,CAF3B,EAGE;AACA,MAAA,OAAOR,IAAP,CAAA;AACD,KALD,MAKO;MACL,OAAO,IAAA,CAAKD,KAAL,CAAWuE,OAAX,CAAmBtE,IAAnB,EAAyBuE,KAAzB,EAAgC,IAAKhC,CAAAA,mBAArC,CAAP,CAAA;AACD,KAAA;AACF,GAAA;;AAED8B,EAAAA,qBAAqB,CACnB;IAAEI,IAAF;AAAQC,IAAAA,KAAAA;GADW,EAEnBC,QAFmB,EAGnB;IAIA,IAAIA,QAAQ,KAAK,IAAjB,EAAuB;AACrB,MAAA,IAAA,CAAKb,IAAL,CAAUW,IAAV,EAAgBC,KAAhB,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IAID,MAAME,MAAM,GAAG,IAAK7E,CAAAA,KAAL,CAAW8E,gCAAX,CAA4CF,QAA5C,CAAf,CAAA;IAEA,IAAKvC,CAAAA,KAAL,CAAW0B,IAAX,CAAgB,IAAA,CAAKF,wBAAL,CAA8BgB,MAA9B,EAAsCD,QAAtC,CAAhB,CAAA,CAAA;AAEA,IAAA,MAAMG,eAAe,GAAGnE,UAAC,CAACoE,qBAAF,CACtBpE,UAAC,CAACqE,gBAAF,CACE,KADF,EAEErE,UAAC,CAAC2C,SAAF,CAAYsB,MAAZ,CAFF,EAGE,IAAK7E,CAAAA,KAAL,CAAWwD,kBAAX,EAHF,CADsB,EAMtBmB,KANsB,EAOtB/D,UAAC,CAAC2C,SAAF,CAAYsB,MAAZ,CAPsB,CAAxB,CAAA;;AAUA,IAAA,IAAIjE,UAAC,CAACsE,SAAF,CAAYR,IAAZ,CAAJ,EAAuB;AACrB,MAAA,IAAIS,SAAJ,CAAA;AACA,MAAA,IAAIlF,IAAJ,CAAA;;MAEA,IAAI,IAAA,CAAKqC,IAAL,KAAc,OAAd,IAAyB,IAAKA,CAAAA,IAAL,KAAc,KAA3C,EAAkD;QAChD6C,SAAS,GAAG,KAAKnF,KAAL,CAAWoF,qBAAX,CAAiCP,MAAM,CAACpE,IAAxC,CAAZ,CAAA;AACAR,QAAAA,IAAI,GAAG,IAAK4D,CAAAA,wBAAL,CAA8BsB,SAA9B,EAAyCJ,eAAzC,CAAP,CAAA;AACD,OAHD,MAGO;AACLI,QAAAA,SAAS,GAAGN,MAAZ,CAAA;QAEA5E,IAAI,GAAGW,UAAC,CAACyC,mBAAF,CACLzC,UAAC,CAAC0C,oBAAF,CAAuB,GAAvB,EAA4B1C,UAAC,CAAC2C,SAAF,CAAYsB,MAAZ,CAA5B,EAAiDE,eAAjD,CADK,CAAP,CAAA;AAGD,OAAA;;AAED,MAAA,IAAA,CAAK1C,KAAL,CAAW0B,IAAX,CAAgB9D,IAAhB,CAAA,CAAA;AACA,MAAA,IAAA,CAAK8D,IAAL,CAAUW,IAAV,EAAgBS,SAAhB,CAAA,CAAA;AACD,KAjBD,MAiBO;MACL,IAAK9C,CAAAA,KAAL,CAAW0B,IAAX,CAAgB,IAAA,CAAKf,uBAAL,CAA6B0B,IAA7B,EAAmCK,eAAnC,CAAhB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDM,cAAc,CACZrE,OADY,EAEZsE,MAFY,EAGZC,UAHY,EAIZC,eAJY,EAKZ;AACA,IAAA,MAAMC,KAAK,GAAGC,wBAAwB,CACpC1E,OAAO,CAACK,UAAR,CAAmBsE,KAAnB,CAAyB,CAAzB,EAA4BH,eAA5B,CADoC,EAEpCF,MAFoC,EAGpC,IAAA,CAAKtF,KAH+B,EAIpCS,IAAI,IAAI,KAAKkC,SAAL,CAAelC,IAAf,CAJ4B,EAKpC,IAAKgC,CAAAA,mBAL+B,EAMpC,IAAA,CAAKC,WAN+B,CAAtC,CAAA;AAQA,IAAA,IAAA,CAAKL,KAAL,CAAW0B,IAAX,CAAgB,IAAKf,CAAAA,uBAAL,CAA6BuC,UAAU,CAACK,QAAxC,EAAkDH,KAAlD,CAAhB,CAAA,CAAA;AACD,GAAA;;AAEDI,EAAAA,kBAAkB,CAACvE,IAAD,EAAyBwE,OAAzB,EAAgD;AAChE,IAAA,IAAIlF,UAAC,CAACmF,SAAF,CAAYzE,IAAI,CAAC0E,GAAjB,CAAJ,EAA2B1E,IAAI,CAAC2E,QAAL,GAAgB,IAAhB,CAAA;AAE3B,IAAA,MAAMjF,OAAO,GAAGM,IAAI,CAACmE,KAArB,CAAA;IACA,MAAMH,MAAM,GAAG1E,UAAC,CAACkC,gBAAF,CACblC,UAAC,CAAC2C,SAAF,CAAYuC,OAAZ,CADa,EAEbxE,IAAI,CAAC0E,GAFQ,EAGb1E,IAAI,CAAC2E,QAHQ,CAAf,CAAA;;AAMA,IAAA,IAAIrF,UAAC,CAACsE,SAAF,CAAYlE,OAAZ,CAAJ,EAA0B;AACxB,MAAA,IAAA,CAAK+C,IAAL,CAAU/C,OAAV,EAAmBsE,MAAnB,CAAA,CAAA;AACD,KAFD,MAEO;MACL,IAAKjD,CAAAA,KAAL,CAAW0B,IAAX,CAAgB,IAAA,CAAKf,uBAAL,CAA6BhC,OAA7B,EAAsCsE,MAAtC,CAAhB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDpB,EAAAA,iBAAiB,CAAClD,OAAD,EAA2BsE,MAA3B,EAAwD;IAGvE,IAAI,CAACtE,OAAO,CAACK,UAAR,CAAmBM,MAApB,IAA8B2D,MAAM,KAAK,IAA7C,EAAmD;AACjD,MAAA,IAAA,CAAKjD,KAAL,CAAW0B,IAAX,CACEnD,UAAC,CAACyC,mBAAF,CACEzC,UAAC,CAACsF,cAAF,CACE,IAAKvD,CAAAA,SAAL,CAAe,0BAAf,CADF,EAEE2C,MAAM,KAAK,IAAX,GAAkB,CAACA,MAAD,CAAlB,GAA6B,EAF/B,CADF,CADF,CAAA,CAAA;AAQA,MAAA,OAAA;AACD,KAAA;;AAMD,IAAA,IAAItE,OAAO,CAACK,UAAR,CAAmBM,MAAnB,GAA4B,CAA5B,IAAiC,CAAC,IAAA,CAAK3B,KAAL,CAAWmG,QAAX,CAAoBb,MAApB,CAAtC,EAAmE;MACjE,MAAMc,IAAI,GAAG,IAAKpG,CAAAA,KAAL,CAAW8E,gCAAX,CAA4CQ,MAA5C,CAAb,CAAA;MACA,IAAKjD,CAAAA,KAAL,CAAW0B,IAAX,CAAgB,IAAA,CAAKF,wBAAL,CAA8BuC,IAA9B,EAAoCd,MAApC,CAAhB,CAAA,CAAA;AACAA,MAAAA,MAAM,GAAGc,IAAT,CAAA;AACD,KAAA;;AAGD,IAAA,IAAIhF,aAAa,CAACJ,OAAD,CAAjB,EAA4B;AAC1B,MAAA,IAAIqF,aAAJ,CAAA;;AACA,MAAA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtF,OAAO,CAACK,UAAR,CAAmBM,MAAvC,EAA+C2E,CAAC,EAAhD,EAAoD;AAClD,QAAA,MAAMhF,IAAI,GAAGN,OAAO,CAACK,UAAR,CAAmBiF,CAAnB,CAAb,CAAA;;AACA,QAAA,IAAI1F,UAAC,CAACO,aAAF,CAAgBG,IAAhB,CAAJ,EAA2B;AACzB,UAAA,MAAA;AACD,SAAA;;AACD,QAAA,MAAM0E,GAAG,GAAG1E,IAAI,CAAC0E,GAAjB,CAAA;;AACA,QAAA,IAAI1E,IAAI,CAAC2E,QAAL,IAAiB,CAAC,IAAA,CAAKjG,KAAL,CAAWuG,MAAX,CAAkBP,GAAlB,CAAtB,EAA8C;UAC5C,MAAMvF,IAAI,GAAG,IAAKT,CAAAA,KAAL,CAAW8E,gCAAX,CAA4CkB,GAA5C,CAAb,CAAA;UACA,IAAK3D,CAAAA,KAAL,CAAW0B,IAAX,CAEE,IAAA,CAAKF,wBAAL,CAA8BpD,IAA9B,EAAoCuF,GAApC,CAFF,CAAA,CAAA;;UAIA,IAAI,CAACK,aAAL,EAAoB;YAClBA,aAAa,GAAGrF,OAAO,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAClBA,OADkB,EAAA;AAErBK,cAAAA,UAAU,EAAEL,OAAO,CAACK,UAAR,CAAmBsE,KAAnB,EAAA;aAFd,CAAA,CAAA;AAID,WAAA;;AACDU,UAAAA,aAAa,CAAChF,UAAd,CAAyBiF,CAAzB,sBACKhF,IADL,EAAA;AAEE0E,YAAAA,GAAG,EAAEvF,IAAAA;AAFP,WAAA,CAAA,CAAA;AAID,SAAA;AACF,OAAA;AACF,KAAA;;AAGD,IAAA,KAAK,IAAI6F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtF,OAAO,CAACK,UAAR,CAAmBM,MAAvC,EAA+C2E,CAAC,EAAhD,EAAoD;AAClD,MAAA,MAAMhF,IAAI,GAAGN,OAAO,CAACK,UAAR,CAAmBiF,CAAnB,CAAb,CAAA;;AACA,MAAA,IAAI1F,UAAC,CAACO,aAAF,CAAgBG,IAAhB,CAAJ,EAA2B;QACzB,IAAK+D,CAAAA,cAAL,CAAoBrE,OAApB,EAA6BsE,MAA7B,EAAqChE,IAArC,EAA2CgF,CAA3C,CAAA,CAAA;AACD,OAFD,MAEO;AACL,QAAA,IAAA,CAAKT,kBAAL,CAAwBvE,IAAxB,EAA8BgE,MAA9B,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;AAEDkB,EAAAA,qBAAqB,CACnBxF,OADmB,EAEnByF,GAFmB,EAGe;IAElC,IAAI,CAAC7F,UAAC,CAAC8F,iBAAF,CAAoBD,GAApB,CAAL,EAA+B,OAAO,KAAP,CAAA;IAI/B,IAAIzF,OAAO,CAACC,QAAR,CAAiBU,MAAjB,GAA0B8E,GAAG,CAACxF,QAAJ,CAAaU,MAA3C,EAAmD,OAAA;;AACnD,IAAA,IACEX,OAAO,CAACC,QAAR,CAAiBU,MAAjB,GAA0B8E,GAAG,CAACxF,QAAJ,CAAaU,MAAvC,IACA,CAACZ,YAAY,CAACC,OAAD,CAFf,EAGE;AACA,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;;AAED,IAAA,KAAK,MAAME,IAAX,IAAmBF,OAAO,CAACC,QAA3B,EAAqC;AAEnC,MAAA,IAAI,CAACC,IAAL,EAAW,OAAO,KAAP,CAAA;MAGX,IAAIN,UAAC,CAACwC,kBAAF,CAAqBlC,IAArB,CAAJ,EAAgC,OAAO,KAAP,CAAA;AACjC,KAAA;;AAED,IAAA,KAAK,MAAMA,IAAX,IAAmBuF,GAAG,CAACxF,QAAvB,EAAiC;MAE/B,IAAIL,UAAC,CAAC+F,eAAF,CAAkBzF,IAAlB,CAAJ,EAA6B,OAAO,KAAP,CAAA;MAG7B,IAAIN,UAAC,CAACgG,gBAAF,CAAmB1F,IAAnB,CAAJ,EAA8B,OAAO,KAAP,CAAA;MAG9B,IAAIN,UAAC,CAACwC,kBAAF,CAAqBlC,IAArB,CAAJ,EAAgC,OAAO,KAAP,CAAA;AACjC,KAAA;;AAGD,IAAA,MAAMd,QAAQ,GAAGQ,UAAC,CAACiG,qBAAF,CAAwB7F,OAAxB,CAAjB,CAAA;AACA,IAAA,MAAMU,KAA8B,GAAG;AAAEI,MAAAA,KAAK,EAAE,KAAT;AAAgB1B,MAAAA,QAAAA;KAAvD,CAAA;;IAEA,IAAI;AACFQ,MAAAA,UAAC,CAACkG,QAAF,CAAWL,GAAX,EAAgBjF,kBAAhB,EAAoCE,KAApC,CAAA,CAAA;KADF,CAEE,OAAOqF,CAAP,EAAU;AACV,MAAA,IAAIA,CAAC,KAAKxF,cAAV,EAA0B,MAAMwF,CAAN,CAAA;AAC3B,KAAA;;IAED,OAAO,CAACrF,KAAK,CAACI,KAAd,CAAA;AACD,GAAA;;AAEDkF,EAAAA,wBAAwB,CACtBhG,OADsB,EAEtByF,GAFsB,EAGtB;AACA,IAAA,KAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtF,OAAO,CAACC,QAAR,CAAiBU,MAArC,EAA6C2E,CAAC,EAA9C,EAAkD;AAChD,MAAA,MAAMpF,IAAI,GAAGF,OAAO,CAACC,QAAR,CAAiBqF,CAAjB,CAAb,CAAA;;AACA,MAAA,IAAI1F,UAAC,CAACO,aAAF,CAAgBD,IAAhB,CAAJ,EAA2B;AACzB,QAAA,IAAA,CAAK6C,IAAL,CAAU7C,IAAI,CAAC0E,QAAf,EAAyBhF,UAAC,CAACqG,eAAF,CAAkBR,GAAG,CAACxF,QAAJ,CAAa0E,KAAb,CAAmBW,CAAnB,CAAlB,CAAzB,CAAA,CAAA;AACD,OAFD,MAEO;QACL,IAAKvC,CAAAA,IAAL,CAAU7C,IAAV,EAAgBuF,GAAG,CAACxF,QAAJ,CAAaqF,CAAb,CAAhB,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;AAEDlC,EAAAA,gBAAgB,CAACpD,OAAD,EAA0BkG,QAA1B,EAAyD;IACvE,IAAIA,QAAQ,KAAK,IAAjB,EAAuB;MACrB,IAAK7E,CAAAA,KAAL,CAAW0B,IAAX,CACEnD,UAAC,CAACyC,mBAAF,CACEzC,UAAC,CAACsF,cAAF,CAAiB,IAAA,CAAKvD,SAAL,CAAe,0BAAf,CAAjB,EAA6D,EAA7D,CADF,CADF,CAAA,CAAA;AAKA,MAAA,OAAA;AACD,KAAA;;AACD,IAAA,IAAI,CAAC3B,OAAO,CAACC,QAAb,EAAuB,OAAA;;AASvB,IAAA,IAAI,KAAKuF,qBAAL,CAA2BxF,OAA3B,EAAoCkG,QAApC,CAAJ,EAAmD;AACjD,MAAA,OAAO,KAAKF,wBAAL,CAA8BhG,OAA9B,EAAuCkG,QAAvC,CAAP,CAAA;AACD,KAAA;;AAKD,IAAA,MAAM1C,KAAK,GAAG,CAACzD,YAAY,CAACC,OAAD,CAAb,IAA0BA,OAAO,CAACC,QAAR,CAAiBU,MAAzD,CAAA;IAMA,MAAM4C,OAAO,GAAG,IAAKA,CAAAA,OAAL,CAAa2C,QAAb,EAAuB1C,KAAvB,CAAhB,CAAA;;AAEA,IAAA,IAAI5D,UAAC,CAACgB,YAAF,CAAe2C,OAAf,CAAJ,EAA6B;AAG3B2C,MAAAA,QAAQ,GAAG3C,OAAX,CAAA;AACD,KAJD,MAIO;AACL2C,MAAAA,QAAQ,GAAG,IAAKlH,CAAAA,KAAL,CAAW8E,gCAAX,CAA4CoC,QAA5C,CAAX,CAAA;AACA,MAAA,IAAA,CAAK9E,WAAL,CAAiB+E,GAAjB,CAAqBD,QAAQ,CAACzG,IAA9B,CAAA,CAAA;MACA,IAAK4B,CAAAA,KAAL,CAAW0B,IAAX,CAAgB,IAAA,CAAKF,wBAAL,CAA8BqD,QAA9B,EAAwC3C,OAAxC,CAAhB,CAAA,CAAA;AACD,KAAA;;AAID,IAAA,KAAK,IAAI+B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtF,OAAO,CAACC,QAAR,CAAiBU,MAArC,EAA6C2E,CAAC,EAA9C,EAAkD;AAChD,MAAA,MAAMpF,IAAI,GAAGF,OAAO,CAACC,QAAR,CAAiBqF,CAAjB,CAAb,CAAA;MAGA,IAAI,CAACpF,IAAL,EAAW,SAAA;AAEX,MAAA,IAAIkG,OAAJ,CAAA;;AAEA,MAAA,IAAIxG,UAAC,CAACO,aAAF,CAAgBD,IAAhB,CAAJ,EAA2B;AACzBkG,QAAAA,OAAO,GAAG,IAAA,CAAK7C,OAAL,CAAa2C,QAAb,CAAV,CAAA;QACAE,OAAO,GAAGxG,UAAC,CAACsF,cAAF,CACRtF,UAAC,CAACkC,gBAAF,CAAmBsE,OAAnB,EAA4BxG,UAAC,CAACmC,UAAF,CAAa,OAAb,CAA5B,CADQ,EAER,CAACnC,UAAC,CAACyG,cAAF,CAAiBf,CAAjB,CAAD,CAFQ,CAAV,CAAA;AAOA,QAAA,IAAA,CAAKvC,IAAL,CAAU7C,IAAI,CAAC0E,QAAf,EAAyBwB,OAAzB,CAAA,CAAA;AACD,OAVD,MAUO;AACLA,QAAAA,OAAO,GAAGxG,UAAC,CAACkC,gBAAF,CAAmBoE,QAAnB,EAA6BtG,UAAC,CAACyG,cAAF,CAAiBf,CAAjB,CAA7B,EAAkD,IAAlD,CAAV,CAAA;AACA,QAAA,IAAA,CAAKvC,IAAL,CAAU7C,IAAV,EAAgBkG,OAAhB,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;AAEDlE,EAAAA,IAAI,CAAClC,OAAD,EAAkBsG,GAAlB,EAAqC;AAIvC,IAAA,IAAI,CAAC1G,UAAC,CAAC8F,iBAAF,CAAoBY,GAApB,CAAD,IAA6B,CAAC1G,UAAC,CAACwC,kBAAF,CAAqBkE,GAArB,CAAlC,EAA6D;MAC3D,MAAMC,IAAI,GAAG,IAAA,CAAKvH,KAAL,CAAWwH,qBAAX,CAAiCF,GAAjC,EAAsC,IAAtC,CAAb,CAAA;;AACA,MAAA,IAAIC,IAAJ,EAAU;AACR,QAAA,IAAA,CAAKlF,KAAL,CAAW0B,IAAX,CAAgB,KAAKF,wBAAL,CAA8B0D,IAA9B,EAAoC3G,UAAC,CAAC2C,SAAF,CAAY+D,GAAZ,CAApC,CAAhB,CAAA,CAAA;AACAA,QAAAA,GAAG,GAAGC,IAAN,CAAA;AACD,OAAA;AACF,KAAA;;AAID,IAAA,IAAA,CAAKxD,IAAL,CAAU/C,OAAV,EAAmBsG,GAAnB,CAAA,CAAA;AAEA,IAAA,OAAO,KAAKjF,KAAZ,CAAA;AACD,GAAA;;AAjZmC,CAAA;AAyZ/B,SAASqD,wBAAT,CACL+B,YADK,EAELnC,MAFK,EAGLtF,KAHK,EAIL2C,SAJK,EAKLF,mBALK,EAMLC,WANK,EAOa;EAGlB,MAAMnC,IAAI,GAAG,EAAb,CAAA;EACA,IAAImH,UAAU,GAAG,IAAjB,CAAA;EACA,IAAIC,kBAAkB,GAAG,KAAzB,CAAA;;AACA,EAAA,KAAK,IAAIrB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmB,YAAY,CAAC9F,MAAjC,EAAyC2E,CAAC,EAA1C,EAA8C;AAC5C,IAAA,MAAMhF,IAAI,GAAGmG,YAAY,CAACnB,CAAD,CAAzB,CAAA;AACA,IAAA,MAAMN,GAAG,GAAG1E,IAAI,CAAC0E,GAAjB,CAAA;;IACA,IAAIpF,UAAC,CAACgB,YAAF,CAAeoE,GAAf,KAAuB,CAAC1E,IAAI,CAAC2E,QAAjC,EAA2C;MACzC1F,IAAI,CAACwD,IAAL,CAAUnD,UAAC,CAACgH,aAAF,CAAgB5B,GAAG,CAACvF,IAApB,CAAV,CAAA,CAAA;KADF,MAEO,IAAIG,UAAC,CAACiH,iBAAF,CAAoB7B,GAApB,CAAJ,EAA8B;MACnCzF,IAAI,CAACwD,IAAL,CAAUnD,UAAC,CAAC2C,SAAF,CAAYyC,GAAZ,CAAV,CAAA,CAAA;AACA2B,MAAAA,kBAAkB,GAAG,IAArB,CAAA;KAFK,MAGA,IAAI/G,UAAC,CAACmF,SAAF,CAAYC,GAAZ,CAAJ,EAAsB;AAE3BzF,MAAAA,IAAI,CAACwD,IAAL,CAAUnD,UAAC,CAACgH,aAAF,CAAgBE,MAAM,CAAC9B,GAAG,CAACP,KAAL,CAAtB,CAAV,CAAA,CAAA;KAFK,MAGA,IAAI7E,UAAC,CAACmH,aAAF,CAAgB/B,GAAhB,CAAJ,EAA0B,CAA1B,MAEA;MACLzF,IAAI,CAACwD,IAAL,CAAUnD,UAAC,CAAC2C,SAAF,CAAYyC,GAAZ,CAAV,CAAA,CAAA;AACA0B,MAAAA,UAAU,GAAG,KAAb,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,IAAIjC,KAAJ,CAAA;;AACA,EAAA,IAAIlF,IAAI,CAACoB,MAAL,KAAgB,CAApB,EAAuB;IACrB,MAAMqG,aAAa,GAAGtF,WAAW,GAC7B9B,UAAC,CAACkC,gBAAF,CAAmBlC,UAAC,CAACmC,UAAF,CAAa,QAAb,CAAnB,EAA2CnC,UAAC,CAACmC,UAAF,CAAa,QAAb,CAA3C,CAD6B,GAE7BJ,SAAS,CAAC,SAAD,CAFb,CAAA;IAGA8C,KAAK,GAAG7E,UAAC,CAACsF,cAAF,CAAiB8B,aAAjB,EAAgC,CACtCpH,UAAC,CAACqH,gBAAF,CAAmB,EAAnB,CADsC,EAEtCrH,UAAC,CAAC2C,SAAF,CAAY+B,MAAZ,CAFsC,CAAhC,CAAR,CAAA;AAID,GARD,MAQO;AACL,IAAA,IAAI4C,aAA2B,GAAGtH,UAAC,CAACqG,eAAF,CAAkB1G,IAAlB,CAAlC,CAAA;;IAEA,IAAI,CAACmH,UAAL,EAAiB;MACfQ,aAAa,GAAGtH,UAAC,CAACsF,cAAF,CACdtF,UAAC,CAACkC,gBAAF,CAAmBoF,aAAnB,EAAkCtH,UAAC,CAACmC,UAAF,CAAa,KAAb,CAAlC,CADc,EAEd,CAACJ,SAAS,CAAC,eAAD,CAAV,CAFc,CAAhB,CAAA;AAID,KALD,MAKO,IAAI,CAACgF,kBAAD,IAAuB,CAAC/G,UAAC,CAACuH,SAAF,CAAYnI,KAAK,CAACoI,KAAlB,CAA5B,EAAsD;AAE3D,MAAA,MAAMC,YAAY,GAAGrI,KAAK,CAACsI,gBAAN,EAArB,CAAA;AACA,MAAA,MAAMrF,EAAE,GAAGoF,YAAY,CAACjD,qBAAb,CAAmC,UAAnC,CAAX,CAAA;MAEAiD,YAAY,CAACtE,IAAb,CAAkB;QAChBd,EADgB;AAEhBC,QAAAA,IAAI,EAAEgF,aAFU;AAGhB5F,QAAAA,IAAI,EAAE,OAAA;OAHR,CAAA,CAAA;AAMA4F,MAAAA,aAAa,GAAGtH,UAAC,CAAC2C,SAAF,CAAYN,EAAZ,CAAhB,CAAA;AACD,KAAA;;IAEDwC,KAAK,GAAG7E,UAAC,CAACsF,cAAF,CACNvD,SAAS,CAAE,CAAyBF,uBAAAA,EAAAA,mBAAmB,GAAG,OAAH,GAAa,EAAG,EAA9D,CADH,EAEN,CAAC7B,UAAC,CAAC2C,SAAF,CAAY+B,MAAZ,CAAD,EAAsB4C,aAAtB,CAFM,CAAR,CAAA;AAID,GAAA;;AACD,EAAA,OAAOzC,KAAP,CAAA;AACD,CAAA;AAEM,SAAS8C,0BAAT,CACLC,IADK,EAEL7F,SAFK,EAGLH,mBAHK,EAILD,eAJK,EAKLE,mBALK,EAMLC,WANK,EAOL;EACA,MAAM;IAAEzC,IAAF;AAAQD,IAAAA,KAAAA;AAAR,GAAA,GAAkBwI,IAAxB,CAAA;AAEA,EAAA,MAAMC,QAAQ,GAAGxI,IAAI,CAACqC,IAAtB,CAAA;AACA,EAAA,MAAMoG,OAAO,GAAGzI,IAAI,CAAC0I,GAArB,CAAA;EACA,MAAMtG,KAAK,GAAG,EAAd,CAAA;;AAEA,EAAA,KAAK,IAAIiE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrG,IAAI,CAAC2I,YAAL,CAAkBjH,MAAtC,EAA8C2E,CAAC,EAA/C,EAAmD;AACjD,IAAA,MAAMxC,MAAM,GAAG7D,IAAI,CAAC2I,YAAL,CAAkBtC,CAAlB,CAAf,CAAA;AAEA,IAAA,MAAMnB,SAAS,GAAGrB,MAAM,CAACZ,IAAzB,CAAA;AACA,IAAA,MAAMlC,OAAO,GAAG8C,MAAM,CAACb,EAAvB,CAAA;AAEA,IAAA,MAAM4F,aAAuC,GAC3C,IAAI9G,wBAAJ,CAA6B;MAE3BG,UAAU,EAAEjC,IAAI,CAAC2D,WAFU;AAG3BvB,MAAAA,KAAK,EAAEA,KAHoB;AAI3BrC,MAAAA,KAAK,EAAEA,KAJoB;MAK3BsC,IAAI,EAAErC,IAAI,CAACqC,IALgB;MAM3BC,eAN2B;MAO3BC,mBAP2B;MAQ3BE,WAR2B;MAS3BD,mBAT2B;AAU3BE,MAAAA,SAAAA;AAV2B,KAA7B,CADF,CAAA;;AAcA,IAAA,IAAI/B,UAAC,CAACsE,SAAF,CAAYlE,OAAZ,CAAJ,EAA0B;AACxB6H,MAAAA,aAAa,CAAC3F,IAAd,CAAmBlC,OAAnB,EAA4BmE,SAA5B,CAAA,CAAA;;MAEA,IAAI,CAACmB,CAAD,KAAOrG,IAAI,CAAC2I,YAAL,CAAkBjH,MAAlB,GAA2B,CAAtC,EAAyC;AAGvCf,QAAAA,UAAC,CAACkI,QAAF,CAAWzG,KAAK,CAACA,KAAK,CAACV,MAAN,GAAe,CAAhB,CAAhB,EAAoCmC,MAApC,CAAA,CAAA;AACD,OAAA;AACF,KARD,MAQO;AACLzB,MAAAA,KAAK,CAAC0B,IAAN,CACEnD,UAAC,CAACkI,QAAF,CACED,aAAa,CAAC7F,uBAAd,CAAsChC,OAAtC,EAA+CmE,SAA/C,CADF,EAEErB,MAFF,CADF,CAAA,CAAA;AAMD,KAAA;AACF,GAAA;;EAED,IAAIiF,IAAkC,GAAG,IAAzC,CAAA;EACA,MAAMC,QAAQ,GAAG,EAAjB,CAAA;;AACA,EAAA,KAAK,MAAM/I,IAAX,IAAmBoC,KAAnB,EAA0B;AACxB,IAAA,IAAIzB,UAAC,CAACqI,qBAAF,CAAwBhJ,IAAxB,CAAJ,EAAmC;MACjC,IAAI8I,IAAI,KAAK,IAAb,EAAmB;QAEjBA,IAAI,CAACH,YAAL,CAAkB7E,IAAlB,CAAuB,GAAG9D,IAAI,CAAC2I,YAA/B,CAAA,CAAA;AACA,QAAA,SAAA;AACD,OAJD,MAIO;QAEL3I,IAAI,CAACqC,IAAL,GAAYmG,QAAZ,CAAA;AACAM,QAAAA,IAAI,GAAG9I,IAAP,CAAA;AACD,OAAA;AACF,KAVD,MAUO;AACL8I,MAAAA,IAAI,GAAG,IAAP,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,CAAC9I,IAAI,CAAC0I,GAAV,EAAe;MACb1I,IAAI,CAAC0I,GAAL,GAAWD,OAAX,CAAA;AACD,KAAA;;IACDM,QAAQ,CAACjF,IAAT,CAAc9D,IAAd,CAAA,CAAA;AACD,GAAA;;AAED,EAAA,IAAI+I,QAAQ,CAACrH,MAAT,KAAoB,CAAxB,EAA2B;AACzB6G,IAAAA,IAAI,CAACU,WAAL,CAAiBF,QAAQ,CAAC,CAAD,CAAzB,CAAA,CAAA;AACD,GAFD,MAEO;IACLR,IAAI,CAACW,mBAAL,CAAyBH,QAAzB,CAAA,CAAA;AACD,GAAA;;AACDhJ,EAAAA,KAAK,CAACoJ,KAAN,EAAA,CAAA;AACD,CAAA;AAEM,SAASC,2BAAT,CACLb,IADK,EAEL7F,SAFK,EAGLH,mBAHK,EAILD,eAJK,EAKLE,mBALK,EAMLC,WANK,EAOL;EACA,MAAM;IAAEzC,IAAF;IAAQD,KAAR;AAAesJ,IAAAA,UAAAA;AAAf,GAAA,GAA8Bd,IAApC,CAAA;EAEA,MAAMnG,KAAqC,GAAG,EAA9C,CAAA;AAEA,EAAA,MAAMwG,aAAa,GAAG,IAAI9G,wBAAJ,CAA6B;IACjDI,QAAQ,EAAElC,IAAI,CAACkC,QADkC;AAEjDnC,IAAAA,KAAK,EAAEA,KAF0C;AAGjDqC,IAAAA,KAAK,EAAEA,KAH0C;IAIjDG,mBAJiD;IAKjDD,eALiD;IAMjDE,mBANiD;IAOjDC,WAPiD;AAQjDC,IAAAA,SAAAA;AARiD,GAA7B,CAAtB,CAAA;AAWA,EAAA,IAAI2E,GAAJ,CAAA;;AACA,EAAA,IACG,CAACgC,UAAU,CAACC,qBAAX,EAAD,IACC,CAACD,UAAU,CAACE,oBAAX,EADH,IAEAhB,IAAI,CAACiB,kBAAL,EAHF,EAIE;IACAnC,GAAG,GAAGtH,KAAK,CAAC8E,gCAAN,CAAuC7E,IAAI,CAAC0E,KAA5C,EAAmD,KAAnD,CAAN,CAAA;IAEAtC,KAAK,CAAC0B,IAAN,CACEnD,UAAC,CAAC8C,mBAAF,CAAsB,KAAtB,EAA6B,CAAC9C,UAAC,CAAC+C,kBAAF,CAAqB2D,GAArB,EAA0BrH,IAAI,CAAC0E,KAA/B,CAAD,CAA7B,CADF,CAAA,CAAA;;IAIA,IAAI/D,UAAC,CAAC8F,iBAAF,CAAoBzG,IAAI,CAAC0E,KAAzB,CAAJ,EAAqC;AACnCkE,MAAAA,aAAa,CAACzG,WAAd,CAA0B+E,GAA1B,CAA8BG,GAAG,CAAC7G,IAAlC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDoI,aAAa,CAAC3F,IAAd,CAAmBjD,IAAI,CAACyE,IAAxB,EAA8B4C,GAAG,IAAIrH,IAAI,CAAC0E,KAA1C,CAAA,CAAA;;AAEA,EAAA,IAAI2C,GAAJ,EAAS;AACP,IAAA,IAAIgC,UAAU,CAACI,yBAAX,EAAJ,EAA4C;MAC1ClB,IAAI,CAACU,WAAL,CAAiBtI,UAAC,CAACC,cAAF,CAAiB,EAAjB,CAAjB,CAAA,CAAA;AACAwB,MAAAA,KAAK,CAAC0B,IAAN,CAAWnD,UAAC,CAAC+I,eAAF,CAAkB/I,UAAC,CAAC2C,SAAF,CAAY+D,GAAZ,CAAlB,CAAX,CAAA,CAAA;AACD,KAHD,MAGO;AACLjF,MAAAA,KAAK,CAAC0B,IAAN,CAAWnD,UAAC,CAACyC,mBAAF,CAAsBzC,UAAC,CAAC2C,SAAF,CAAY+D,GAAZ,CAAtB,CAAX,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDkB,IAAI,CAACW,mBAAL,CAAyB9G,KAAzB,CAAA,CAAA;AACArC,EAAAA,KAAK,CAACoJ,KAAN,EAAA,CAAA;AACD;;AClrBD,SAASQ,6BAAT,CAAuC3J,IAAvC,EAAoE;AAClE,EAAA,KAAK,MAAM6D,MAAX,IAAqB7D,IAAI,CAAC2I,YAA1B,EAAwC;IACtC,IAAIhI,UAAC,CAACsE,SAAF,CAAYpB,MAAM,CAACb,EAAnB,CAAJ,EAA4B;AAC1B,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;AACF,GAAA;;AACD,EAAA,OAAO,KAAP,CAAA;AACD,CAAA;;AAQD,YAAe4G,yBAAO,CAAC,CAACC,GAAD,EAAMC,OAAN,KAA2B;AAAA,EAAA,IAAA,IAAA,EAAA,eAAA,EAAA,KAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,gBAAA,CAAA;;EAChDD,GAAG,CAACE,aAAJ,CAAkB,CAAlB,CAAA,CAAA;EAEA,MAAM;AAAEtH,IAAAA,WAAW,GAAG,KAAA;AAAhB,GAAA,GAA0BqH,OAAhC,CAAA;AAEA,EAAA,MAAMxH,eAAe,GAAA,CAAA,IAAA,GAAA,CAAA,eAAA,GAAIuH,GAAG,CAACG,UAAJ,CAAe,iBAAf,CAAJ,KACnBF,IAAAA,GAAAA,eAAAA,GAAAA,OAAO,CAACG,KADW,mBAEnB,KAFF,CAAA;AAGA,EAAA,MAAM1H,mBAAmB,GAAA,CAAA,KAAA,GAAA,CAAA,qBAAA,GAAIuH,OAAO,CAACI,cAAZ,KAAA,IAAA,GAAA,qBAAA,GACvBL,GAAG,CAACG,UAAJ,CAAe,qBAAf,CADuB,oBAEvB,KAFF,CAAA;AAGA,EAAA,MAAMxH,mBAAmB,GAAA,CAAA,KAAA,GAAA,CAAA,gBAAA,GAAIqH,GAAG,CAACG,UAAJ,CAAe,qBAAf,CAAJ,KACvBF,IAAAA,GAAAA,gBAAAA,GAAAA,OAAO,CAACG,KADe,oBAEvB,KAFF,CAAA;EAIA,OAAO;AACLzJ,IAAAA,IAAI,EAAE,yBADD;AAGL2J,IAAAA,OAAO,EAAE;MACPC,sBAAsB,CAAC7B,IAAD,EAAO;AAC3B,QAAA,MAAM8B,WAAW,GAAG9B,IAAI,CAACrI,GAAL,CAAS,aAAT,CAApB,CAAA;AACA,QAAA,IAAI,CAACmK,WAAW,CAACrB,qBAAZ,EAAL,EAA0C,OAAA;AAC1C,QAAA,IAAI,CAACW,6BAA6B,CAACU,WAAW,CAACrK,IAAb,CAAlC,EAAsD,OAAA;QAEtD,MAAMsK,UAAU,GAAG,EAAnB,CAAA;;AAEA,QAAA,KAAK,MAAM9J,IAAX,IAAmBH,MAAM,CAACC,IAAP,CAAYiI,IAAI,CAACgC,0BAAL,EAAZ,CAAnB,EAAmE;UACjED,UAAU,CAACxG,IAAX,CACEnD,UAAC,CAAC6J,eAAF,CAAkB7J,UAAC,CAACmC,UAAF,CAAatC,IAAb,CAAlB,EAAsCG,UAAC,CAACmC,UAAF,CAAatC,IAAb,CAAtC,CADF,CAAA,CAAA;AAGD,SAAA;;AAKD+H,QAAAA,IAAI,CAACU,WAAL,CAAiBoB,WAAW,CAACrK,IAA7B,CAAA,CAAA;QACAuI,IAAI,CAACkC,WAAL,CAAiB9J,UAAC,CAAC+J,sBAAF,CAAyB,IAAzB,EAA+BJ,UAA/B,CAAjB,CAAA,CAAA;QACA/B,IAAI,CAACxI,KAAL,CAAWoJ,KAAX,EAAA,CAAA;OAnBK;;MAsBPwB,aAAa,CAACpC,IAAD,EAAkC;QAC7C,MAAM;UAAEvI,IAAF;AAAQD,UAAAA,KAAAA;AAAR,SAAA,GAAkBwI,IAAxB,CAAA;AACA,QAAA,MAAM9D,IAAI,GAAGzE,IAAI,CAACyE,IAAlB,CAAA;;AAEA,QAAA,IAAI9D,UAAC,CAACsE,SAAF,CAAYR,IAAZ,CAAJ,EAAuB;AAGrB,UAAA,MAAM0B,IAAI,GAAGpG,KAAK,CAACoF,qBAAN,CAA4B,KAA5B,CAAb,CAAA;AAEAnF,UAAAA,IAAI,CAACyE,IAAL,GAAY9D,UAAC,CAAC8C,mBAAF,CAAsB,KAAtB,EAA6B,CACvC9C,UAAC,CAAC+C,kBAAF,CAAqByC,IAArB,CADuC,CAA7B,CAAZ,CAAA;AAIAoC,UAAAA,IAAI,CAACzI,WAAL,EAAA,CAAA;UACA,MAAM8K,aAAa,GAAGrC,IAAI,CAACvI,IAAL,CAAUU,IAAV,CAAeA,IAArC,CAAA;UACA,MAAM0B,KAAK,GAAG,EAAd,CAAA;;UAKA,IAAIwI,aAAa,CAAClJ,MAAd,KAAyB,CAAzB,IAA8B6G,IAAI,CAACiB,kBAAL,EAAlC,EAA6D;YAC3DpH,KAAK,CAACvB,OAAN,CAAcF,UAAC,CAACyC,mBAAF,CAAsBrD,KAAK,CAACwD,kBAAN,EAAtB,CAAd,CAAA,CAAA;AACD,WAAA;;UAEDnB,KAAK,CAACvB,OAAN,CACEF,UAAC,CAACyC,mBAAF,CACEzC,UAAC,CAAC0C,oBAAF,CAAuB,GAAvB,EAA4BoB,IAA5B,EAAkC9D,UAAC,CAAC2C,SAAF,CAAY6C,IAAZ,CAAlC,CADF,CADF,CAAA,CAAA;AAMAxG,UAAAA,wBAAwB,CAAC4I,IAAD,EAAOnG,KAAP,CAAxB,CAAA;AACArC,UAAAA,KAAK,CAACoJ,KAAN,EAAA,CAAA;AACA,UAAA,OAAA;AACD,SAAA;;AAED,QAAA,IAAI,CAACxI,UAAC,CAACqI,qBAAF,CAAwBvE,IAAxB,CAAL,EAAoC,OAAA;QAEpC,MAAM1D,OAAO,GAAG0D,IAAI,CAACkE,YAAL,CAAkB,CAAlB,EAAqB3F,EAArC,CAAA;AACA,QAAA,IAAI,CAACrC,UAAC,CAACsE,SAAF,CAAYlE,OAAZ,CAAL,EAA2B,OAAA;AAE3B,QAAA,MAAMgF,GAAG,GAAGhG,KAAK,CAACoF,qBAAN,CAA4B,KAA5B,CAAZ,CAAA;QACAnF,IAAI,CAACyE,IAAL,GAAY9D,UAAC,CAAC8C,mBAAF,CAAsBgB,IAAI,CAACpC,IAA3B,EAAiC,CAC3C1B,UAAC,CAAC+C,kBAAF,CAAqBqC,GAArB,EAA0B,IAA1B,CAD2C,CAAjC,CAAZ,CAAA;QAIA,MAAM3D,KAAqC,GAAG,EAA9C,CAAA;AAEA,QAAA,MAAMwG,aAAa,GAAG,IAAI9G,wBAAJ,CAA6B;UACjDO,IAAI,EAAEoC,IAAI,CAACpC,IADsC;AAEjDtC,UAAAA,KAAK,EAAEA,KAF0C;AAGjDqC,UAAAA,KAAK,EAAEA,KAH0C;UAIjDG,mBAJiD;UAKjDD,eALiD;UAMjDE,mBANiD;UAOjDC,WAPiD;AAQjDC,UAAAA,SAAS,EAAElC,IAAI,IAAI,IAAKkC,CAAAA,SAAL,CAAelC,IAAf,CAAA;AAR8B,SAA7B,CAAtB,CAAA;AAWAoI,QAAAA,aAAa,CAAC3F,IAAd,CAAmBlC,OAAnB,EAA4BgF,GAA5B,CAAA,CAAA;AAEApG,QAAAA,wBAAwB,CAAC4I,IAAD,EAAOnG,KAAP,CAAxB,CAAA;AACArC,QAAAA,KAAK,CAACoJ,KAAN,EAAA,CAAA;OAnFK;;AAsFP0B,MAAAA,WAAW,CAAC;QAAE7K,IAAF;AAAQD,QAAAA,KAAAA;AAAR,OAAD,EAAkB;AAC3B,QAAA,MAAMgB,OAAO,GAAGf,IAAI,CAAC8K,KAArB,CAAA;AACA,QAAA,IAAI,CAACnK,UAAC,CAACsE,SAAF,CAAYlE,OAAZ,CAAL,EAA2B,OAAA;AAE3B,QAAA,MAAMsG,GAAG,GAAGtH,KAAK,CAACoF,qBAAN,CAA4B,KAA5B,CAAZ,CAAA;QACAnF,IAAI,CAAC8K,KAAL,GAAazD,GAAb,CAAA;QAEA,MAAMjF,KAAqC,GAAG,EAA9C,CAAA;AAEA,QAAA,MAAMwG,aAAa,GAAG,IAAI9G,wBAAJ,CAA6B;AACjDO,UAAAA,IAAI,EAAE,KAD2C;AAEjDtC,UAAAA,KAAK,EAAEA,KAF0C;AAGjDqC,UAAAA,KAAK,EAAEA,KAH0C;UAIjDG,mBAJiD;UAKjDD,eALiD;UAMjDE,mBANiD;UAOjDC,WAPiD;AAQjDC,UAAAA,SAAS,EAAElC,IAAI,IAAI,IAAKkC,CAAAA,SAAL,CAAelC,IAAf,CAAA;AAR8B,SAA7B,CAAtB,CAAA;AAUAoI,QAAAA,aAAa,CAAC3F,IAAd,CAAmBlC,OAAnB,EAA4BsG,GAA5B,CAAA,CAAA;AAEArH,QAAAA,IAAI,CAACU,IAAL,CAAUA,IAAV,GAAiB,CAAC,GAAG0B,KAAJ,EAAW,GAAGpC,IAAI,CAACU,IAAL,CAAUA,IAAxB,CAAjB,CAAA;AACAX,QAAAA,KAAK,CAACoJ,KAAN,EAAA,CAAA;OA5GK;;AA+GP4B,MAAAA,oBAAoB,CAACxC,IAAD,EAAO9G,KAAP,EAAc;QAChC,IAAI,CAACd,UAAC,CAACsE,SAAF,CAAYsD,IAAI,CAACvI,IAAL,CAAUyE,IAAtB,CAAL,EAAkC,OAAA;AAClC2E,QAAAA,2BAA2B,CACzBb,IADyB,EAEzB/H,IAAI,IAAIiB,KAAK,CAACiB,SAAN,CAAgBlC,IAAhB,CAFiB,EAGzB+B,mBAHyB,EAIzBD,eAJyB,EAKzBE,mBALyB,EAMzBC,WANyB,CAA3B,CAAA;OAjHK;;AA2HPuI,MAAAA,mBAAmB,CAACzC,IAAD,EAAO9G,KAAP,EAAc;QAC/B,MAAM;UAAEzB,IAAF;AAAQiL,UAAAA,MAAAA;AAAR,SAAA,GAAmB1C,IAAzB,CAAA;AACA,QAAA,IAAI5H,UAAC,CAACuK,eAAF,CAAkBD,MAAlB,CAAJ,EAA+B,OAAA;AAC/B,QAAA,IAAI,CAACA,MAAD,IAAW,CAAC1C,IAAI,CAAC4C,SAArB,EAAgC,OAAA;AAChC,QAAA,IAAI,CAACxB,6BAA6B,CAAC3J,IAAD,CAAlC,EAA0C,OAAA;AAC1CsI,QAAAA,0BAA0B,CACxBC,IADwB,EAExB/H,IAAI,IAAIiB,KAAK,CAACiB,SAAN,CAAgBlC,IAAhB,CAFgB,EAGxB+B,mBAHwB,EAIxBD,eAJwB,EAKxBE,mBALwB,EAMxBC,WANwB,CAA1B,CAAA;AAQD,OAAA;;AAxIM,KAAA;GAHX,CAAA;AA8ID,CA7JqB,CAAtB;;;;;;"}