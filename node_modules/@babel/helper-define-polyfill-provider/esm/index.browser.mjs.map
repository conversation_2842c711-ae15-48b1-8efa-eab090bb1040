{"version": 3, "file": "index.browser.mjs", "sources": ["../src/utils.ts", "../src/imports-cache.ts", "../src/debug-utils.ts", "../src/normalize-options.ts", "../src/visitors/usage.ts", "../src/visitors/entry.ts", "../src/browser/dependencies.ts", "../src/meta-resolver.ts", "../src/index.ts"], "sourcesContent": ["import { types as t, template } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type { Utils } from \"./types\";\nimport type ImportsCache from \"./imports-cache\";\n\nexport function intersection<T>(a: Set<T>, b: Set<T>): Set<T> {\n  const result = new Set<T>();\n  a.forEach(v => b.has(v) && result.add(v));\n  return result;\n}\n\nexport function has(object: any, key: string) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nfunction getType(target: any): string {\n  return Object.prototype.toString.call(target).slice(8, -1);\n}\n\nfunction resolveId(path): string {\n  if (\n    path.isIdentifier() &&\n    !path.scope.hasBinding(path.node.name, /* noGlobals */ true)\n  ) {\n    return path.node.name;\n  }\n\n  const { deopt } = path.evaluate();\n  if (deopt && deopt.isIdentifier()) {\n    return deopt.node.name;\n  }\n}\n\nexport function resolveKey(\n  path: NodePath<t.Expression | t.PrivateName>,\n  computed: boolean = false,\n) {\n  const { scope } = path;\n  if (path.isStringLiteral()) return path.node.value;\n  const isIdentifier = path.isIdentifier();\n  if (\n    isIdentifier &&\n    !(computed || (path.parent as t.MemberExpression).computed)\n  ) {\n    return path.node.name;\n  }\n\n  if (\n    computed &&\n    path.isMemberExpression() &&\n    path.get(\"object\").isIdentifier({ name: \"Symbol\" }) &&\n    !scope.hasBinding(\"Symbol\", /* noGlobals */ true)\n  ) {\n    const sym = resolveKey(path.get(\"property\"), path.node.computed);\n    if (sym) return \"Symbol.\" + sym;\n  }\n\n  if (!isIdentifier || scope.hasBinding(path.node.name, /* noGlobals */ true)) {\n    const { value } = path.evaluate();\n    if (typeof value === \"string\") return value;\n  }\n}\n\nexport function resolveSource(obj: NodePath): {\n  id: string | null;\n  placement: \"prototype\" | \"static\" | null;\n} {\n  if (\n    obj.isMemberExpression() &&\n    obj.get(\"property\").isIdentifier({ name: \"prototype\" })\n  ) {\n    const id = resolveId(obj.get(\"object\"));\n\n    if (id) {\n      return { id, placement: \"prototype\" };\n    }\n    return { id: null, placement: null };\n  }\n\n  const id = resolveId(obj);\n  if (id) {\n    return { id, placement: \"static\" };\n  }\n\n  const { value } = obj.evaluate();\n  if (value !== undefined) {\n    return { id: getType(value), placement: \"prototype\" };\n  } else if (obj.isRegExpLiteral()) {\n    return { id: \"RegExp\", placement: \"prototype\" };\n  } else if (obj.isFunction()) {\n    return { id: \"Function\", placement: \"prototype\" };\n  }\n\n  return { id: null, placement: null };\n}\n\nexport function getImportSource({ node }: NodePath<t.ImportDeclaration>) {\n  if (node.specifiers.length === 0) return node.source.value;\n}\n\nexport function getRequireSource({ node }: NodePath<t.Statement>) {\n  if (!t.isExpressionStatement(node)) return;\n  const { expression } = node;\n  if (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee) &&\n    expression.callee.name === \"require\" &&\n    expression.arguments.length === 1 &&\n    t.isStringLiteral(expression.arguments[0])\n  ) {\n    return expression.arguments[0].value;\n  }\n}\n\nfunction hoist(node: t.Node) {\n  // @ts-expect-error\n  node._blockHoist = 3;\n  return node;\n}\n\nexport function createUtilsGetter(cache: ImportsCache) {\n  return (path: NodePath): Utils => {\n    const prog = path.findParent(p => p.isProgram()) as NodePath<t.Program>;\n\n    return {\n      injectGlobalImport(url) {\n        cache.storeAnonymous(prog, url, (isScript, source) => {\n          return isScript\n            ? template.statement.ast`require(${source})`\n            : t.importDeclaration([], source);\n        });\n      },\n      injectNamedImport(url, name, hint = name) {\n        return cache.storeNamed(prog, url, name, (isScript, source, name) => {\n          const id = prog.scope.generateUidIdentifier(hint);\n          return {\n            node: isScript\n              ? hoist(template.statement.ast`\n                  var ${id} = require(${source}).${name}\n                `)\n              : t.importDeclaration([t.importSpecifier(id, name)], source),\n            name: id.name,\n          };\n        });\n      },\n      injectDefaultImport(url, hint = url) {\n        return cache.storeNamed(prog, url, \"default\", (isScript, source) => {\n          const id = prog.scope.generateUidIdentifier(hint);\n          return {\n            node: isScript\n              ? hoist(template.statement.ast`var ${id} = require(${source})`)\n              : t.importDeclaration([t.importDefaultSpecifier(id)], source),\n            name: id.name,\n          };\n        });\n      },\n    };\n  };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n\ntype StrMap<K> = Map<string, K>;\n\nexport default class ImportsCache {\n  _imports: WeakMap<NodePath<t.Program>, StrMap<string>>;\n  _anonymousImports: WeakMap<NodePath<t.Program>, Set<string>>;\n  _lastImports: WeakMap<NodePath<t.Program>, NodePath<t.Node>>;\n  _resolver: (url: string) => string;\n\n  constructor(resolver: (url: string) => string) {\n    this._imports = new WeakMap();\n    this._anonymousImports = new WeakMap();\n    this._lastImports = new WeakMap();\n    this._resolver = resolver;\n  }\n\n  storeAnonymous(\n    programPath: NodePath<t.Program>,\n    url: string,\n    // eslint-disable-next-line no-undef\n    getVal: (isScript: boolean, source: t.StringLiteral) => t.Node,\n  ) {\n    const key = this._normalizeKey(programPath, url);\n    const imports = this._ensure<Set<string>>(\n      this._anonymousImports,\n      programPath,\n      Set,\n    );\n\n    if (imports.has(key)) return;\n\n    const node = getVal(\n      programPath.node.sourceType === \"script\",\n      t.stringLiteral(this._resolver(url)),\n    );\n    imports.add(key);\n    this._injectImport(programPath, node);\n  }\n\n  storeNamed(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string,\n    getVal: (\n      isScript: boolean,\n      // eslint-disable-next-line no-undef\n      source: t.StringLiteral,\n      // eslint-disable-next-line no-undef\n      name: t.Identifier,\n    ) => { node: t.Node; name: string },\n  ) {\n    const key = this._normalizeKey(programPath, url, name);\n    const imports = this._ensure<Map<string, any>>(\n      this._imports,\n      programPath,\n      Map,\n    );\n\n    if (!imports.has(key)) {\n      const { node, name: id } = getVal(\n        programPath.node.sourceType === \"script\",\n        t.stringLiteral(this._resolver(url)),\n        t.identifier(name),\n      );\n      imports.set(key, id);\n      this._injectImport(programPath, node);\n    }\n\n    return t.identifier(imports.get(key));\n  }\n\n  _injectImport(programPath: NodePath<t.Program>, node: t.Node) {\n    const lastImport = this._lastImports.get(programPath);\n    let newNodes: [NodePath];\n    if (\n      lastImport &&\n      lastImport.node &&\n      // Sometimes the AST is modified and the \"last import\"\n      // we have has been replaced\n      lastImport.parent === programPath.node &&\n      lastImport.container === programPath.node.body\n    ) {\n      newNodes = lastImport.insertAfter(node);\n    } else {\n      newNodes = programPath.unshiftContainer(\"body\", node);\n    }\n    const newNode = newNodes[newNodes.length - 1];\n    this._lastImports.set(programPath, newNode);\n\n    /*\n    let lastImport;\n\n    programPath.get(\"body\").forEach(path => {\n      if (path.isImportDeclaration()) lastImport = path;\n      if (\n        path.isExpressionStatement() &&\n        isRequireCall(path.get(\"expression\"))\n      ) {\n        lastImport = path;\n      }\n      if (\n        path.isVariableDeclaration() &&\n        path.get(\"declarations\").length === 1 &&\n        (isRequireCall(path.get(\"declarations.0.init\")) ||\n          (path.get(\"declarations.0.init\").isMemberExpression() &&\n            isRequireCall(path.get(\"declarations.0.init.object\"))))\n      ) {\n        lastImport = path;\n      }\n    });*/\n  }\n\n  _ensure<C extends Map<string, any> | Set<string>>(\n    map: WeakMap<NodePath<t.Program>, C>,\n    programPath: NodePath<t.Program>,\n    Collection: { new (...args: any): C },\n  ): C {\n    let collection = map.get(programPath);\n    if (!collection) {\n      collection = new Collection();\n      map.set(programPath, collection);\n    }\n    return collection;\n  }\n\n  _normalizeKey(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string = \"\",\n  ): string {\n    const { sourceType } = programPath.node;\n\n    // If we rely on the imported binding (the \"name\" parameter), we also need to cache\n    // based on the sourceType. This is because the module transforms change the names\n    // of the import variables.\n    return `${name && sourceType}::${url}::${name}`;\n  }\n}\n", "import { prettifyTargets } from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"./types\";\n\nexport const presetEnvSilentDebugHeader =\n  \"#__secret_key__@babel/preset-env__don't_log_debug_header_and_resolved_targets\";\n\nexport function stringifyTargetsMultiline(targets: Targets): string {\n  return JSON.stringify(prettifyTargets(targets), null, 2);\n}\n\nexport function stringifyTargets(targets: Targets): string {\n  return JSON.stringify(targets)\n    .replace(/,/g, \", \")\n    .replace(/^\\{\"/, '{ \"')\n    .replace(/\"\\}$/, '\" }');\n}\n", "import { intersection } from \"./utils\";\nimport type {\n  Pattern,\n  PluginOptions,\n  MissingDependenciesOption,\n} from \"./types\";\n\nfunction patternToRegExp(pattern: Pattern): RegExp | null {\n  if (pattern instanceof RegExp) return pattern;\n\n  try {\n    return new RegExp(`^${pattern}$`);\n  } catch {\n    return null;\n  }\n}\n\nfunction buildUnusedError(label, unused) {\n  if (!unused.length) return \"\";\n  return (\n    `  - The following \"${label}\" patterns didn't match any polyfill:\\n` +\n    unused.map(original => `    ${String(original)}\\n`).join(\"\")\n  );\n}\n\nfunction buldDuplicatesError(duplicates) {\n  if (!duplicates.size) return \"\";\n  return (\n    `  - The following polyfills were matched both by \"include\" and \"exclude\" patterns:\\n` +\n    Array.from(duplicates, name => `    ${name}\\n`).join(\"\")\n  );\n}\n\nexport function validateIncludeExclude(\n  provider: string,\n  polyfills: Set<string>,\n  includePatterns: Pattern[],\n  excludePatterns: Pattern[],\n) {\n  let current;\n  const filter = pattern => {\n    const regexp = patternToRegExp(pattern);\n    if (!regexp) return false;\n\n    let matched = false;\n    for (const polyfill of polyfills) {\n      if (regexp.test(polyfill)) {\n        matched = true;\n        current.add(polyfill);\n      }\n    }\n    return !matched;\n  };\n\n  // prettier-ignore\n  const include = current = new Set<string> ();\n  const unusedInclude = Array.from(includePatterns).filter(filter);\n\n  // prettier-ignore\n  const exclude = current = new Set<string> ();\n  const unusedExclude = Array.from(excludePatterns).filter(filter);\n\n  const duplicates = intersection(include, exclude);\n\n  if (\n    duplicates.size > 0 ||\n    unusedInclude.length > 0 ||\n    unusedExclude.length > 0\n  ) {\n    throw new Error(\n      `Error while validating the \"${provider}\" provider options:\\n` +\n        buildUnusedError(\"include\", unusedInclude) +\n        buildUnusedError(\"exclude\", unusedExclude) +\n        buldDuplicatesError(duplicates),\n    );\n  }\n\n  return { include, exclude };\n}\n\nexport function applyMissingDependenciesDefaults(\n  options: PluginOptions,\n  babelApi: any,\n): MissingDependenciesOption {\n  const { missingDependencies = {} } = options;\n  if (missingDependencies === false) return false;\n\n  const caller = babelApi.caller(caller => caller?.name);\n\n  const {\n    log = \"deferred\",\n    inject = caller === \"rollup-plugin-babel\" ? \"throw\" : \"import\",\n    all = false,\n  } = missingDependencies;\n\n  return { log, inject, all };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { MetaDescriptor } from \"../types\";\n\nimport { resolveKey, resolveSource } from \"../utils\";\n\nexport default (\n  callProvider: (payload: MetaDescriptor, path: NodePath) => void,\n) => {\n  function property(object, key, placement, path) {\n    return callProvider({ kind: \"property\", object, key, placement }, path);\n  }\n\n  return {\n    // Symbol(), new Promise\n    ReferencedIdentifier(path: NodePath<t.Identifier>) {\n      const {\n        node: { name },\n        scope,\n      } = path;\n      if (scope.getBindingIdentifier(name)) return;\n\n      callProvider({ kind: \"global\", name }, path);\n    },\n\n    MemberExpression(path: NodePath<t.MemberExpression>) {\n      const key = resolveKey(path.get(\"property\"), path.node.computed);\n      if (!key || key === \"prototype\") return;\n\n      const object = path.get(\"object\");\n      if (object.isIdentifier()) {\n        const binding = object.scope.getBinding(object.node.name);\n        if (binding && binding.path.isImportNamespaceSpecifier()) return;\n      }\n\n      const source = resolveSource(object);\n      return property(source.id, key, source.placement, path);\n    },\n\n    ObjectPattern(path: NodePath<t.ObjectPattern>) {\n      const { parentPath, parent } = path;\n      let obj;\n\n      // const { keys, values } = Object\n      if (parentPath.isVariableDeclarator()) {\n        obj = parentPath.get(\"init\");\n        // ({ keys, values } = Object)\n      } else if (parentPath.isAssignmentExpression()) {\n        obj = parentPath.get(\"right\");\n        // !function ({ keys, values }) {...} (Object)\n        // resolution does not work after properties transform :-(\n      } else if (parentPath.isFunction()) {\n        const grand = parentPath.parentPath;\n        if (grand.isCallExpression() || grand.isNewExpression()) {\n          if (grand.node.callee === parent) {\n            obj = grand.get(\"arguments\")[path.key];\n          }\n        }\n      }\n\n      let id = null;\n      let placement = null;\n      if (obj) ({ id, placement } = resolveSource(obj));\n\n      for (const prop of path.get(\"properties\")) {\n        if (prop.isObjectProperty()) {\n          const key = resolveKey(prop.get(\"key\"));\n          if (key) property(id, key, placement, prop);\n        }\n      }\n    },\n\n    BinaryExpression(path: NodePath<t.BinaryExpression>) {\n      if (path.node.operator !== \"in\") return;\n\n      const source = resolveSource(path.get(\"right\"));\n      const key = resolveKey(path.get(\"left\"), true);\n\n      if (!key) return;\n\n      callProvider(\n        {\n          kind: \"in\",\n          object: source.id,\n          key,\n          placement: source.placement,\n        },\n        path,\n      );\n    },\n  };\n};\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { MetaDescriptor } from \"../types\";\n\nimport { getImportSource, getRequireSource } from \"../utils\";\n\nexport default (\n  callProvider: (payload: MetaDescriptor, path: NodePath) => void,\n) => ({\n  ImportDeclaration(path: NodePath<t.ImportDeclaration>) {\n    const source = getImportSource(path);\n    if (!source) return;\n    callProvider({ kind: \"import\", source }, path);\n  },\n  Program(path: NodePath<t.Program>) {\n    path.get(\"body\").forEach(bodyPath => {\n      const source = getRequireSource(bodyPath);\n      if (!source) return;\n      callProvider({ kind: \"import\", source }, bodyPath);\n    });\n  },\n});\n", "export function resolve(\n  dirname: string,\n  moduleName: string,\n  absoluteImports: boolean | string,\n): string {\n  if (absoluteImports === false) return moduleName;\n\n  throw new Error(\n    `\"absoluteImports\" is not supported in bundles prepared for the browser.`,\n  );\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function has(basedir: string, name: string) {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function logMissing(missingDeps: Set<string>) {}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function laterLogMissing(missingDeps: Set<string>) {}\n", "import type {\n  MetaDescriptor,\n  ResolverPolyfills,\n  ResolvedPolyfill,\n} from \"./types\";\n\nimport { has } from \"./utils\";\n\ntype ResolverFn<T> = (meta: MetaDescriptor) => void | ResolvedPolyfill<T>;\n\nconst PossibleGlobalObjects = new Set<string>([\n  \"global\",\n  \"globalThis\",\n  \"self\",\n  \"window\",\n]);\n\nexport default function createMetaResolver<T>(\n  polyfills: ResolverPolyfills<T>,\n): ResolverFn<T> {\n  const { static: staticP, instance: instanceP, global: globalP } = polyfills;\n\n  return meta => {\n    if (meta.kind === \"global\" && globalP && has(globalP, meta.name)) {\n      return { kind: \"global\", desc: globalP[meta.name], name: meta.name };\n    }\n\n    if (meta.kind === \"property\" || meta.kind === \"in\") {\n      const { placement, object, key } = meta;\n\n      if (object && placement === \"static\") {\n        if (globalP && PossibleGlobalObjects.has(object) && has(globalP, key)) {\n          return { kind: \"global\", desc: globalP[key], name: key };\n        }\n\n        if (staticP && has(staticP, object) && has(staticP[object], key)) {\n          return {\n            kind: \"static\",\n            desc: staticP[object][key],\n            name: `${object}$${key}`,\n          };\n        }\n      }\n\n      if (instanceP && has(instanceP, key)) {\n        return { kind: \"instance\", desc: instanceP[key], name: `${key}` };\n      }\n    }\n  };\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport type { NodePath } from \"@babel/traverse\";\n\nimport _getTargets, {\n  isRequired,\n  getInclusionReasons,\n} from \"@babel/helper-compilation-targets\";\nconst getTargets = _getTargets.default || _getTargets;\n\nimport { createUtilsGetter } from \"./utils\";\nimport ImportsCache from \"./imports-cache\";\nimport {\n  stringifyTargetsMultiline,\n  presetEnvSilentDebugHeader,\n} from \"./debug-utils\";\nimport {\n  validateIncludeExclude,\n  applyMissingDependenciesDefaults,\n} from \"./normalize-options\";\n\nimport type {\n  ProviderApi,\n  MethodString,\n  Targets,\n  MetaDescriptor,\n  PolyfillProvider,\n  PluginOptions,\n  ProviderOptions,\n} from \"./types\";\n\nimport * as v from \"./visitors\";\nimport * as deps from \"./node/dependencies\";\n\nimport createMetaResolver from \"./meta-resolver\";\n\nexport type { PolyfillProvider, MetaDescriptor, Utils, Targets } from \"./types\";\n\nfunction resolveOptions<Options>(\n  options: PluginOptions,\n  babelApi,\n): {\n  method: MethodString;\n  methodName: \"usageGlobal\" | \"entryGlobal\" | \"usagePure\";\n  targets: Targets;\n  debug: boolean | typeof presetEnvSilentDebugHeader;\n  shouldInjectPolyfill:\n    | ((name: string, shouldInject: boolean) => boolean)\n    | undefined;\n  providerOptions: ProviderOptions<Options>;\n  absoluteImports: string | boolean;\n} {\n  const {\n    method,\n    targets: targetsOption,\n    ignoreBrowserslistConfig,\n    configPath,\n    debug,\n    shouldInjectPolyfill,\n    absoluteImports,\n    ...providerOptions\n  } = options;\n\n  if (isEmpty(options)) {\n    throw new Error(\n      `\\\nThis plugin requires options, for example:\n    {\n      \"plugins\": [\n        [\"<plugin name>\", { method: \"usage-pure\" }]\n      ]\n    }\n\nSee more options at https://github.com/babel/babel-polyfills/blob/main/docs/usage.md`,\n    );\n  }\n\n  let methodName;\n  if (method === \"usage-global\") methodName = \"usageGlobal\";\n  else if (method === \"entry-global\") methodName = \"entryGlobal\";\n  else if (method === \"usage-pure\") methodName = \"usagePure\";\n  else if (typeof method !== \"string\") {\n    throw new Error(\".method must be a string\");\n  } else {\n    throw new Error(\n      `.method must be one of \"entry-global\", \"usage-global\"` +\n        ` or \"usage-pure\" (received ${JSON.stringify(method)})`,\n    );\n  }\n\n  if (typeof shouldInjectPolyfill === \"function\") {\n    if (options.include || options.exclude) {\n      throw new Error(\n        `.include and .exclude are not supported when using the` +\n          ` .shouldInjectPolyfill function.`,\n      );\n    }\n  } else if (shouldInjectPolyfill != null) {\n    throw new Error(\n      `.shouldInjectPolyfill must be a function, or undefined` +\n        ` (received ${JSON.stringify(shouldInjectPolyfill)})`,\n    );\n  }\n\n  if (\n    absoluteImports != null &&\n    typeof absoluteImports !== \"boolean\" &&\n    typeof absoluteImports !== \"string\"\n  ) {\n    throw new Error(\n      `.absoluteImports must be a boolean, a string, or undefined` +\n        ` (received ${JSON.stringify(absoluteImports)})`,\n    );\n  }\n\n  let targets;\n\n  if (\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    targetsOption ||\n    configPath ||\n    ignoreBrowserslistConfig\n  ) {\n    const targetsObj =\n      typeof targetsOption === \"string\" || Array.isArray(targetsOption)\n        ? { browsers: targetsOption }\n        : targetsOption;\n\n    targets = getTargets(targetsObj, {\n      ignoreBrowserslistConfig,\n      configPath,\n    });\n  } else {\n    targets = babelApi.targets();\n  }\n\n  return {\n    method,\n    methodName,\n    targets,\n    absoluteImports: absoluteImports ?? false,\n    shouldInjectPolyfill,\n    debug: !!debug,\n    providerOptions: providerOptions as any as ProviderOptions<Options>,\n  };\n}\n\nfunction instantiateProvider<Options>(\n  factory: PolyfillProvider<Options>,\n  options: PluginOptions,\n  missingDependencies,\n  dirname,\n  debugLog,\n  babelApi,\n) {\n  const {\n    method,\n    methodName,\n    targets,\n    debug,\n    shouldInjectPolyfill,\n    providerOptions,\n    absoluteImports,\n  } = resolveOptions<Options>(options, babelApi);\n\n  const getUtils = createUtilsGetter(\n    new ImportsCache(moduleName =>\n      deps.resolve(dirname, moduleName, absoluteImports),\n    ),\n  );\n\n  // eslint-disable-next-line prefer-const\n  let include, exclude;\n  let polyfillsSupport;\n  let polyfillsNames;\n  let filterPolyfills;\n\n  const depsCache = new Map();\n\n  const api: ProviderApi = {\n    babel: babelApi,\n    getUtils,\n    method: options.method,\n    targets,\n    createMetaResolver,\n    shouldInjectPolyfill(name) {\n      if (polyfillsNames === undefined) {\n        throw new Error(\n          `Internal error in the ${factory.name} provider: ` +\n            `shouldInjectPolyfill() can't be called during initialization.`,\n        );\n      }\n      if (!polyfillsNames.has(name)) {\n        console.warn(\n          `Internal error in the ${provider.name} provider: ` +\n            `unknown polyfill \"${name}\".`,\n        );\n      }\n\n      if (filterPolyfills && !filterPolyfills(name)) return false;\n\n      let shouldInject = isRequired(name, targets, {\n        compatData: polyfillsSupport,\n        includes: include,\n        excludes: exclude,\n      });\n\n      if (shouldInjectPolyfill) {\n        shouldInject = shouldInjectPolyfill(name, shouldInject);\n        if (typeof shouldInject !== \"boolean\") {\n          throw new Error(`.shouldInjectPolyfill must return a boolean.`);\n        }\n      }\n\n      return shouldInject;\n    },\n    debug(name) {\n      debugLog().found = true;\n\n      if (!debug || !name) return;\n\n      if (debugLog().polyfills.has(provider.name)) return;\n      debugLog().polyfills.set(\n        name,\n        polyfillsSupport && name && polyfillsSupport[name],\n      );\n    },\n    assertDependency(name, version = \"*\") {\n      if (missingDependencies === false) return;\n      if (absoluteImports) {\n        // If absoluteImports is not false, we will try resolving\n        // the dependency and throw if it's not possible. We can\n        // skip the check here.\n        return;\n      }\n\n      const dep = version === \"*\" ? name : `${name}@^${version}`;\n\n      const found = missingDependencies.all\n        ? false\n        : mapGetOr(depsCache, `${name} :: ${dirname}`, () =>\n            deps.has(dirname, name),\n          );\n\n      if (!found) {\n        debugLog().missingDeps.add(dep);\n      }\n    },\n  };\n\n  const provider = factory(api, providerOptions, dirname);\n\n  if (typeof provider[methodName] !== \"function\") {\n    throw new Error(\n      `The \"${provider.name || factory.name}\" provider doesn't ` +\n        `support the \"${method}\" polyfilling method.`,\n    );\n  }\n\n  if (Array.isArray(provider.polyfills)) {\n    polyfillsNames = new Set(provider.polyfills);\n    filterPolyfills = provider.filterPolyfills;\n  } else if (provider.polyfills) {\n    polyfillsNames = new Set(Object.keys(provider.polyfills));\n    polyfillsSupport = provider.polyfills;\n    filterPolyfills = provider.filterPolyfills;\n  } else {\n    polyfillsNames = new Set();\n  }\n\n  ({ include, exclude } = validateIncludeExclude(\n    provider.name || factory.name,\n    polyfillsNames,\n    providerOptions.include || [],\n    providerOptions.exclude || [],\n  ));\n\n  return {\n    debug,\n    method,\n    targets,\n    provider,\n    callProvider(payload: MetaDescriptor, path: NodePath) {\n      const utils = getUtils(path);\n      provider[methodName](payload, utils, path);\n    },\n  };\n}\n\nexport default function definePolyfillProvider<Options>(\n  factory: PolyfillProvider<Options>,\n) {\n  return declare((babelApi, options: PluginOptions, dirname: string) => {\n    babelApi.assertVersion(7);\n    const { traverse } = babelApi;\n\n    let debugLog;\n\n    const missingDependencies = applyMissingDependenciesDefaults(\n      options,\n      babelApi,\n    );\n\n    const { debug, method, targets, provider, callProvider } =\n      instantiateProvider<Options>(\n        factory,\n        options,\n        missingDependencies,\n        dirname,\n        () => debugLog,\n        babelApi,\n      );\n\n    const createVisitor = method === \"entry-global\" ? v.entry : v.usage;\n\n    const visitor = provider.visitor\n      ? traverse.visitors.merge([createVisitor(callProvider), provider.visitor])\n      : createVisitor(callProvider);\n\n    if (debug && debug !== presetEnvSilentDebugHeader) {\n      console.log(`${provider.name}: \\`DEBUG\\` option`);\n      console.log(`\\nUsing targets: ${stringifyTargetsMultiline(targets)}`);\n      console.log(`\\nUsing polyfills with \\`${method}\\` method:`);\n    }\n\n    return {\n      name: \"inject-polyfills\",\n      visitor,\n\n      pre() {\n        debugLog = {\n          polyfills: new Map(),\n          found: false,\n          providers: new Set(),\n          missingDeps: new Set(),\n        };\n\n        provider.pre?.apply(this, arguments);\n      },\n      post() {\n        provider.post?.apply(this, arguments);\n\n        if (missingDependencies !== false) {\n          if (missingDependencies.log === \"per-file\") {\n            deps.logMissing(debugLog.missingDeps);\n          } else {\n            deps.laterLogMissing(debugLog.missingDeps);\n          }\n        }\n\n        if (!debug) return;\n\n        if (this.filename) console.log(`\\n[${this.filename}]`);\n\n        if (debugLog.polyfills.size === 0) {\n          console.log(\n            method === \"entry-global\"\n              ? debugLog.found\n                ? `Based on your targets, the ${provider.name} polyfill did not add any polyfill.`\n                : `The entry point for the ${provider.name} polyfill has not been found.`\n              : `Based on your code and targets, the ${provider.name} polyfill did not add any polyfill.`,\n          );\n\n          return;\n        }\n\n        if (method === \"entry-global\") {\n          console.log(\n            `The ${provider.name} polyfill entry has been replaced with ` +\n              `the following polyfills:`,\n          );\n        } else {\n          console.log(\n            `The ${provider.name} polyfill added the following polyfills:`,\n          );\n        }\n\n        for (const [name, support] of debugLog.polyfills) {\n          if (support) {\n            const filteredTargets = getInclusionReasons(name, targets, support);\n\n            const formattedTargets = JSON.stringify(filteredTargets)\n              .replace(/,/g, \", \")\n              .replace(/^\\{\"/, '{ \"')\n              .replace(/\"\\}$/, '\" }');\n\n            console.log(`  ${name} ${formattedTargets}`);\n          } else {\n            console.log(`  ${name}`);\n          }\n        }\n      },\n    };\n  });\n}\n\nfunction mapGetOr(map, key, getDefault) {\n  let val = map.get(key);\n  if (val === undefined) {\n    val = getDefault();\n    map.set(key, val);\n  }\n  return val;\n}\n\nfunction isEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\n"], "names": ["types", "t", "template", "intersection", "a", "b", "result", "Set", "for<PERSON>ach", "v", "has", "add", "object", "key", "Object", "prototype", "hasOwnProperty", "call", "getType", "target", "toString", "slice", "resolveId", "path", "isIdentifier", "scope", "hasBinding", "node", "name", "de<PERSON>t", "evaluate", "<PERSON><PERSON><PERSON>", "computed", "isStringLiteral", "value", "parent", "isMemberExpression", "get", "sym", "resolveSource", "obj", "id", "placement", "undefined", "isRegExpLiteral", "isFunction", "getImportSource", "specifiers", "length", "source", "getRequireSource", "isExpressionStatement", "expression", "isCallExpression", "callee", "arguments", "hoist", "_blockHoist", "createUtilsGetter", "cache", "prog", "findParent", "p", "isProgram", "injectGlobalImport", "url", "storeAnonymous", "isScript", "statement", "ast", "importDeclaration", "injectNamedImport", "hint", "storeNamed", "generateUidIdentifier", "importSpecifier", "injectDefaultImport", "importDefaultSpecifier", "ImportsCache", "constructor", "resolver", "_imports", "WeakMap", "_anonymousImports", "_lastImports", "_resolver", "programPath", "getVal", "_normalizeKey", "imports", "_ensure", "sourceType", "stringLiteral", "_injectImport", "Map", "identifier", "set", "lastImport", "newNodes", "container", "body", "insertAfter", "unshiftContainer", "newNode", "map", "Collection", "collection", "presetEnvSilentDebugHeader", "stringifyTargetsMultiline", "targets", "JSON", "stringify", "prettifyTargets", "patternToRegExp", "pattern", "RegExp", "buildUnusedError", "label", "unused", "original", "String", "join", "buldDuplicatesError", "duplicates", "size", "Array", "from", "validateIncludeExclude", "provider", "polyfills", "includePatterns", "excludePatterns", "current", "filter", "regexp", "matched", "polyfill", "test", "include", "unusedInclude", "exclude", "unusedExclude", "Error", "applyMissingDependenciesDefaults", "options", "babelApi", "missingDependencies", "caller", "log", "inject", "all", "callProvider", "property", "kind", "ReferencedIdentifier", "getBindingIdentifier", "MemberExpression", "binding", "getBinding", "isImportNamespaceSpecifier", "ObjectPattern", "parentPath", "isVariableDeclarator", "isAssignmentExpression", "grand", "isNewExpression", "prop", "isObjectProperty", "BinaryExpression", "operator", "ImportDeclaration", "Program", "bodyPath", "resolve", "dirname", "moduleName", "absoluteImports", "basedir", "logMissing", "missingDeps", "laterLog<PERSON><PERSON>ing", "PossibleGlobalObjects", "createMetaResolver", "static", "staticP", "instance", "instanceP", "global", "globalP", "meta", "desc", "getTargets", "_getTargets", "default", "resolveOptions", "method", "targetsOption", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "debug", "shouldInjectPolyfill", "providerOptions", "isEmpty", "methodName", "targetsObj", "isArray", "browsers", "instantiateProvider", "factory", "debugLog", "getUtils", "deps", "polyfillsSupport", "polyfillsNames", "filterPolyfills", "deps<PERSON>ache", "api", "babel", "console", "warn", "shouldInject", "isRequired", "compatData", "includes", "excludes", "found", "assertDependency", "version", "dep", "mapGetOr", "keys", "payload", "utils", "definePolyfillProvider", "declare", "assertVersion", "traverse", "createVisitor", "visitor", "visitors", "merge", "pre", "providers", "apply", "post", "filename", "support", "filteredTargets", "getInclusionReasons", "formattedTargets", "replace", "getDefault", "val"], "mappings": ";;;;;AAASA,EAAAA,OAASC;AAAGC,EAAAA,UAAAA;;AAKd,SAASC,YAAT,CAAyBC,CAAzB,EAAoCC,CAApC,EAAuD;AAC5D,QAAMC,MAAM,GAAG,IAAIC,GAAJ,EAAf;AACAH,EAAAA,CAAC,CAACI,OAAF,CAAUC,CAAC,IAAIJ,CAAC,CAACK,GAAF,CAAMD,CAAN,KAAYH,MAAM,CAACK,GAAP,CAAWF,CAAX,CAA3B;AACA,SAAOH,MAAP;AACD;AAEM,SAASI,KAAT,CAAaE,MAAb,EAA0BC,GAA1B,EAAuC;AAC5C,SAAOC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCL,MAArC,EAA6CC,GAA7C,CAAP;AACD;;AAED,SAASK,OAAT,CAAiBC,MAAjB,EAAsC;AACpC,SAAOL,MAAM,CAACC,SAAP,CAAiBK,QAAjB,CAA0BH,IAA1B,CAA+BE,MAA/B,EAAuCE,KAAvC,CAA6C,CAA7C,EAAgD,CAAC,CAAjD,CAAP;AACD;;AAED,SAASC,SAAT,CAAmBC,IAAnB,EAAiC;AAC/B,MACEA,IAAI,CAACC,YAAL,MACA,CAACD,IAAI,CAACE,KAAL,CAAWC,UAAX,CAAsBH,IAAI,CAACI,IAAL,CAAUC,IAAhC;AAAsC;AAAgB,MAAtD,CAFH,EAGE;AACA,WAAOL,IAAI,CAACI,IAAL,CAAUC,IAAjB;AACD;;AAED,QAAM;AAAEC,IAAAA;AAAF,MAAYN,IAAI,CAACO,QAAL,EAAlB;;AACA,MAAID,KAAK,IAAIA,KAAK,CAACL,YAAN,EAAb,EAAmC;AACjC,WAAOK,KAAK,CAACF,IAAN,CAAWC,IAAlB;AACD;AACF;;AAEM,SAASG,UAAT,CACLR,IADK,EAELS,QAAiB,GAAG,KAFf,EAGL;AACA,QAAM;AAAEP,IAAAA;AAAF,MAAYF,IAAlB;AACA,MAAIA,IAAI,CAACU,eAAL,EAAJ,EAA4B,OAAOV,IAAI,CAACI,IAAL,CAAUO,KAAjB;AAC5B,QAAMV,YAAY,GAAGD,IAAI,CAACC,YAAL,EAArB;;AACA,MACEA,YAAY,IACZ,EAAEQ,QAAQ,IAAKT,IAAI,CAACY,MAAN,CAAoCH,QAAlD,CAFF,EAGE;AACA,WAAOT,IAAI,CAACI,IAAL,CAAUC,IAAjB;AACD;;AAED,MACEI,QAAQ,IACRT,IAAI,CAACa,kBAAL,EADA,IAEAb,IAAI,CAACc,GAAL,CAAS,QAAT,EAAmBb,YAAnB,CAAgC;AAAEI,IAAAA,IAAI,EAAE;AAAR,GAAhC,CAFA,IAGA,CAACH,KAAK,CAACC,UAAN,CAAiB,QAAjB;AAA2B;AAAgB,MAA3C,CAJH,EAKE;AACA,UAAMY,GAAG,GAAGP,UAAU,CAACR,IAAI,CAACc,GAAL,CAAS,UAAT,CAAD,EAAuBd,IAAI,CAACI,IAAL,CAAUK,QAAjC,CAAtB;AACA,QAAIM,GAAJ,EAAS,OAAO,YAAYA,GAAnB;AACV;;AAED,MAAI,CAACd,YAAD,IAAiBC,KAAK,CAACC,UAAN,CAAiBH,IAAI,CAACI,IAAL,CAAUC,IAA3B;AAAiC;AAAgB,MAAjD,CAArB,EAA6E;AAC3E,UAAM;AAAEM,MAAAA;AAAF,QAAYX,IAAI,CAACO,QAAL,EAAlB;AACA,QAAI,OAAOI,KAAP,KAAiB,QAArB,EAA+B,OAAOA,KAAP;AAChC;AACF;AAEM,SAASK,aAAT,CAAuBC,GAAvB,EAGL;AACA,MACEA,GAAG,CAACJ,kBAAJ,MACAI,GAAG,CAACH,GAAJ,CAAQ,UAAR,EAAoBb,YAApB,CAAiC;AAAEI,IAAAA,IAAI,EAAE;AAAR,GAAjC,CAFF,EAGE;AACA,UAAMa,EAAE,GAAGnB,SAAS,CAACkB,GAAG,CAACH,GAAJ,CAAQ,QAAR,CAAD,CAApB;;AAEA,QAAII,EAAJ,EAAQ;AACN,aAAO;AAAEA,QAAAA,EAAF;AAAMC,QAAAA,SAAS,EAAE;AAAjB,OAAP;AACD;;AACD,WAAO;AAAED,MAAAA,EAAE,EAAE,IAAN;AAAYC,MAAAA,SAAS,EAAE;AAAvB,KAAP;AACD;;AAED,QAAMD,EAAE,GAAGnB,SAAS,CAACkB,GAAD,CAApB;;AACA,MAAIC,EAAJ,EAAQ;AACN,WAAO;AAAEA,MAAAA,EAAF;AAAMC,MAAAA,SAAS,EAAE;AAAjB,KAAP;AACD;;AAED,QAAM;AAAER,IAAAA;AAAF,MAAYM,GAAG,CAACV,QAAJ,EAAlB;;AACA,MAAII,KAAK,KAAKS,SAAd,EAAyB;AACvB,WAAO;AAAEF,MAAAA,EAAE,EAAEvB,OAAO,CAACgB,KAAD,CAAb;AAAsBQ,MAAAA,SAAS,EAAE;AAAjC,KAAP;AACD,GAFD,MAEO,IAAIF,GAAG,CAACI,eAAJ,EAAJ,EAA2B;AAChC,WAAO;AAAEH,MAAAA,EAAE,EAAE,QAAN;AAAgBC,MAAAA,SAAS,EAAE;AAA3B,KAAP;AACD,GAFM,MAEA,IAAIF,GAAG,CAACK,UAAJ,EAAJ,EAAsB;AAC3B,WAAO;AAAEJ,MAAAA,EAAE,EAAE,UAAN;AAAkBC,MAAAA,SAAS,EAAE;AAA7B,KAAP;AACD;;AAED,SAAO;AAAED,IAAAA,EAAE,EAAE,IAAN;AAAYC,IAAAA,SAAS,EAAE;AAAvB,GAAP;AACD;AAEM,SAASI,eAAT,CAAyB;AAAEnB,EAAAA;AAAF,CAAzB,EAAkE;AACvE,MAAIA,IAAI,CAACoB,UAAL,CAAgBC,MAAhB,KAA2B,CAA/B,EAAkC,OAAOrB,IAAI,CAACsB,MAAL,CAAYf,KAAnB;AACnC;AAEM,SAASgB,gBAAT,CAA0B;AAAEvB,EAAAA;AAAF,CAA1B,EAA2D;AAChE,MAAI,CAAC1B,GAAC,CAACkD,qBAAF,CAAwBxB,IAAxB,CAAL,EAAoC;AACpC,QAAM;AAAEyB,IAAAA;AAAF,MAAiBzB,IAAvB;;AACA,MACE1B,GAAC,CAACoD,gBAAF,CAAmBD,UAAnB,KACAnD,GAAC,CAACuB,YAAF,CAAe4B,UAAU,CAACE,MAA1B,CADA,IAEAF,UAAU,CAACE,MAAX,CAAkB1B,IAAlB,KAA2B,SAF3B,IAGAwB,UAAU,CAACG,SAAX,CAAqBP,MAArB,KAAgC,CAHhC,IAIA/C,GAAC,CAACgC,eAAF,CAAkBmB,UAAU,CAACG,SAAX,CAAqB,CAArB,CAAlB,CALF,EAME;AACA,WAAOH,UAAU,CAACG,SAAX,CAAqB,CAArB,EAAwBrB,KAA/B;AACD;AACF;;AAED,SAASsB,KAAT,CAAe7B,IAAf,EAA6B;AAC3B;AACAA,EAAAA,IAAI,CAAC8B,WAAL,GAAmB,CAAnB;AACA,SAAO9B,IAAP;AACD;;AAEM,SAAS+B,iBAAT,CAA2BC,KAA3B,EAAgD;AACrD,SAAQpC,IAAD,IAA2B;AAChC,UAAMqC,IAAI,GAAGrC,IAAI,CAACsC,UAAL,CAAgBC,CAAC,IAAIA,CAAC,CAACC,SAAF,EAArB,CAAb;AAEA,WAAO;AACLC,MAAAA,kBAAkB,CAACC,GAAD,EAAM;AACtBN,QAAAA,KAAK,CAACO,cAAN,CAAqBN,IAArB,EAA2BK,GAA3B,EAAgC,CAACE,QAAD,EAAWlB,MAAX,KAAsB;AACpD,iBAAOkB,QAAQ,GACXjE,QAAQ,CAACkE,SAAT,CAAmBC,GAAI,WAAUpB,MAAO,GAD7B,GAEXhD,GAAC,CAACqE,iBAAF,CAAoB,EAApB,EAAwBrB,MAAxB,CAFJ;AAGD,SAJD;AAKD,OAPI;;AAQLsB,MAAAA,iBAAiB,CAACN,GAAD,EAAMrC,IAAN,EAAY4C,IAAI,GAAG5C,IAAnB,EAAyB;AACxC,eAAO+B,KAAK,CAACc,UAAN,CAAiBb,IAAjB,EAAuBK,GAAvB,EAA4BrC,IAA5B,EAAkC,CAACuC,QAAD,EAAWlB,MAAX,EAAmBrB,IAAnB,KAA4B;AACnE,gBAAMa,EAAE,GAAGmB,IAAI,CAACnC,KAAL,CAAWiD,qBAAX,CAAiCF,IAAjC,CAAX;AACA,iBAAO;AACL7C,YAAAA,IAAI,EAAEwC,QAAQ,GACVX,KAAK,CAACtD,QAAQ,CAACkE,SAAT,CAAmBC,GAAI;AAC7C,wBAAwB5B,EAAG,cAAaQ,MAAO,KAAIrB,IAAK;AACxD,iBAFqB,CADK,GAIV3B,GAAC,CAACqE,iBAAF,CAAoB,CAACrE,GAAC,CAAC0E,eAAF,CAAkBlC,EAAlB,EAAsBb,IAAtB,CAAD,CAApB,EAAmDqB,MAAnD,CALC;AAMLrB,YAAAA,IAAI,EAAEa,EAAE,CAACb;AANJ,WAAP;AAQD,SAVM,CAAP;AAWD,OApBI;;AAqBLgD,MAAAA,mBAAmB,CAACX,GAAD,EAAMO,IAAI,GAAGP,GAAb,EAAkB;AACnC,eAAON,KAAK,CAACc,UAAN,CAAiBb,IAAjB,EAAuBK,GAAvB,EAA4B,SAA5B,EAAuC,CAACE,QAAD,EAAWlB,MAAX,KAAsB;AAClE,gBAAMR,EAAE,GAAGmB,IAAI,CAACnC,KAAL,CAAWiD,qBAAX,CAAiCF,IAAjC,CAAX;AACA,iBAAO;AACL7C,YAAAA,IAAI,EAAEwC,QAAQ,GACVX,KAAK,CAACtD,QAAQ,CAACkE,SAAT,CAAmBC,GAAI,OAAM5B,EAAG,cAAaQ,MAAO,GAArD,CADK,GAEVhD,GAAC,CAACqE,iBAAF,CAAoB,CAACrE,GAAC,CAAC4E,sBAAF,CAAyBpC,EAAzB,CAAD,CAApB,EAAoDQ,MAApD,CAHC;AAILrB,YAAAA,IAAI,EAAEa,EAAE,CAACb;AAJJ,WAAP;AAMD,SARM,CAAP;AASD;;AA/BI,KAAP;AAiCD,GApCD;AAqCD;;;AC7JQ5B,EAAAA,OAASC;;AAIH,MAAM6E,YAAN,CAAmB;AAMhCC,EAAAA,WAAW,CAACC,QAAD,EAAoC;AAC7C,SAAKC,QAAL,GAAgB,IAAIC,OAAJ,EAAhB;AACA,SAAKC,iBAAL,GAAyB,IAAID,OAAJ,EAAzB;AACA,SAAKE,YAAL,GAAoB,IAAIF,OAAJ,EAApB;AACA,SAAKG,SAAL,GAAiBL,QAAjB;AACD;;AAEDd,EAAAA,cAAc,CACZoB,WADY,EAEZrB,GAFY;AAIZsB,EAAAA,MAJY,EAKZ;AACA,UAAM1E,GAAG,GAAG,KAAK2E,aAAL,CAAmBF,WAAnB,EAAgCrB,GAAhC,CAAZ;;AACA,UAAMwB,OAAO,GAAG,KAAKC,OAAL,CACd,KAAKP,iBADS,EAEdG,WAFc,EAGd/E,GAHc,CAAhB;;AAMA,QAAIkF,OAAO,CAAC/E,GAAR,CAAYG,GAAZ,CAAJ,EAAsB;AAEtB,UAAMc,IAAI,GAAG4D,MAAM,CACjBD,WAAW,CAAC3D,IAAZ,CAAiBgE,UAAjB,KAAgC,QADf,EAEjB1F,CAAC,CAAC2F,aAAF,CAAgB,KAAKP,SAAL,CAAepB,GAAf,CAAhB,CAFiB,CAAnB;AAIAwB,IAAAA,OAAO,CAAC9E,GAAR,CAAYE,GAAZ;;AACA,SAAKgF,aAAL,CAAmBP,WAAnB,EAAgC3D,IAAhC;AACD;;AAED8C,EAAAA,UAAU,CACRa,WADQ,EAERrB,GAFQ,EAGRrC,IAHQ,EAIR2D,MAJQ,EAWR;AACA,UAAM1E,GAAG,GAAG,KAAK2E,aAAL,CAAmBF,WAAnB,EAAgCrB,GAAhC,EAAqCrC,IAArC,CAAZ;;AACA,UAAM6D,OAAO,GAAG,KAAKC,OAAL,CACd,KAAKT,QADS,EAEdK,WAFc,EAGdQ,GAHc,CAAhB;;AAMA,QAAI,CAACL,OAAO,CAAC/E,GAAR,CAAYG,GAAZ,CAAL,EAAuB;AACrB,YAAM;AAAEc,QAAAA,IAAF;AAAQC,QAAAA,IAAI,EAAEa;AAAd,UAAqB8C,MAAM,CAC/BD,WAAW,CAAC3D,IAAZ,CAAiBgE,UAAjB,KAAgC,QADD,EAE/B1F,CAAC,CAAC2F,aAAF,CAAgB,KAAKP,SAAL,CAAepB,GAAf,CAAhB,CAF+B,EAG/BhE,CAAC,CAAC8F,UAAF,CAAanE,IAAb,CAH+B,CAAjC;AAKA6D,MAAAA,OAAO,CAACO,GAAR,CAAYnF,GAAZ,EAAiB4B,EAAjB;;AACA,WAAKoD,aAAL,CAAmBP,WAAnB,EAAgC3D,IAAhC;AACD;;AAED,WAAO1B,CAAC,CAAC8F,UAAF,CAAaN,OAAO,CAACpD,GAAR,CAAYxB,GAAZ,CAAb,CAAP;AACD;;AAEDgF,EAAAA,aAAa,CAACP,WAAD,EAAmC3D,IAAnC,EAAiD;AAC5D,UAAMsE,UAAU,GAAG,KAAKb,YAAL,CAAkB/C,GAAlB,CAAsBiD,WAAtB,CAAnB;;AACA,QAAIY,QAAJ;;AACA,QACED,UAAU,IACVA,UAAU,CAACtE,IADX;AAGA;AACAsE,IAAAA,UAAU,CAAC9D,MAAX,KAAsBmD,WAAW,CAAC3D,IAJlC,IAKAsE,UAAU,CAACE,SAAX,KAAyBb,WAAW,CAAC3D,IAAZ,CAAiByE,IAN5C,EAOE;AACAF,MAAAA,QAAQ,GAAGD,UAAU,CAACI,WAAX,CAAuB1E,IAAvB,CAAX;AACD,KATD,MASO;AACLuE,MAAAA,QAAQ,GAAGZ,WAAW,CAACgB,gBAAZ,CAA6B,MAA7B,EAAqC3E,IAArC,CAAX;AACD;;AACD,UAAM4E,OAAO,GAAGL,QAAQ,CAACA,QAAQ,CAAClD,MAAT,GAAkB,CAAnB,CAAxB;;AACA,SAAKoC,YAAL,CAAkBY,GAAlB,CAAsBV,WAAtB,EAAmCiB,OAAnC;AAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEG;;AAEDb,EAAAA,OAAO,CACLc,GADK,EAELlB,WAFK,EAGLmB,UAHK,EAIF;AACH,QAAIC,UAAU,GAAGF,GAAG,CAACnE,GAAJ,CAAQiD,WAAR,CAAjB;;AACA,QAAI,CAACoB,UAAL,EAAiB;AACfA,MAAAA,UAAU,GAAG,IAAID,UAAJ,EAAb;AACAD,MAAAA,GAAG,CAACR,GAAJ,CAAQV,WAAR,EAAqBoB,UAArB;AACD;;AACD,WAAOA,UAAP;AACD;;AAEDlB,EAAAA,aAAa,CACXF,WADW,EAEXrB,GAFW,EAGXrC,IAAY,GAAG,EAHJ,EAIH;AACR,UAAM;AAAE+D,MAAAA;AAAF,QAAiBL,WAAW,CAAC3D,IAAnC,CADQ;AAIR;AACA;;AACA,WAAQ,GAAEC,IAAI,IAAI+D,UAAW,KAAI1B,GAAI,KAAIrC,IAAK,EAA9C;AACD;;AArI+B;;ACD3B,MAAM+E,0BAA0B,GACrC,+EADK;AAGA,SAASC,yBAAT,CAAmCC,OAAnC,EAA6D;AAClE,SAAOC,IAAI,CAACC,SAAL,CAAeC,eAAe,CAACH,OAAD,CAA9B,EAAyC,IAAzC,EAA+C,CAA/C,CAAP;AACD;;ACFD,SAASI,eAAT,CAAyBC,OAAzB,EAA0D;AACxD,MAAIA,OAAO,YAAYC,MAAvB,EAA+B,OAAOD,OAAP;;AAE/B,MAAI;AACF,WAAO,IAAIC,MAAJ,CAAY,IAAGD,OAAQ,GAAvB,CAAP;AACD,GAFD,CAEE,MAAM;AACN,WAAO,IAAP;AACD;AACF;;AAED,SAASE,gBAAT,CAA0BC,KAA1B,EAAiCC,MAAjC,EAAyC;AACvC,MAAI,CAACA,MAAM,CAACtE,MAAZ,EAAoB,OAAO,EAAP;AACpB,SACG,sBAAqBqE,KAAM,yCAA5B,GACAC,MAAM,CAACd,GAAP,CAAWe,QAAQ,IAAK,OAAMC,MAAM,CAACD,QAAD,CAAW,IAA/C,EAAoDE,IAApD,CAAyD,EAAzD,CAFF;AAID;;AAED,SAASC,mBAAT,CAA6BC,UAA7B,EAAyC;AACvC,MAAI,CAACA,UAAU,CAACC,IAAhB,EAAsB,OAAO,EAAP;AACtB,SACG,sFAAD,GACAC,KAAK,CAACC,IAAN,CAAWH,UAAX,EAAuB/F,IAAI,IAAK,OAAMA,IAAK,IAA3C,EAAgD6F,IAAhD,CAAqD,EAArD,CAFF;AAID;;AAEM,SAASM,sBAAT,CACLC,QADK,EAELC,SAFK,EAGLC,eAHK,EAILC,eAJK,EAKL;AACA,MAAIC,OAAJ;;AACA,QAAMC,MAAM,GAAGnB,OAAO,IAAI;AACxB,UAAMoB,MAAM,GAAGrB,eAAe,CAACC,OAAD,CAA9B;AACA,QAAI,CAACoB,MAAL,EAAa,OAAO,KAAP;AAEb,QAAIC,OAAO,GAAG,KAAd;;AACA,SAAK,MAAMC,QAAX,IAAuBP,SAAvB,EAAkC;AAChC,UAAIK,MAAM,CAACG,IAAP,CAAYD,QAAZ,CAAJ,EAA2B;AACzBD,QAAAA,OAAO,GAAG,IAAV;AACAH,QAAAA,OAAO,CAACzH,GAAR,CAAY6H,QAAZ;AACD;AACF;;AACD,WAAO,CAACD,OAAR;AACD,GAZD,CAFA;;;AAiBA,QAAMG,OAAO,GAAGN,OAAO,GAAG,IAAI7H,GAAJ,EAA1B;AACA,QAAMoI,aAAa,GAAGd,KAAK,CAACC,IAAN,CAAWI,eAAX,EAA4BG,MAA5B,CAAmCA,MAAnC,CAAtB,CAlBA;;AAqBA,QAAMO,OAAO,GAAGR,OAAO,GAAG,IAAI7H,GAAJ,EAA1B;AACA,QAAMsI,aAAa,GAAGhB,KAAK,CAACC,IAAN,CAAWK,eAAX,EAA4BE,MAA5B,CAAmCA,MAAnC,CAAtB;AAEA,QAAMV,UAAU,GAAGxH,YAAY,CAACuI,OAAD,EAAUE,OAAV,CAA/B;;AAEA,MACEjB,UAAU,CAACC,IAAX,GAAkB,CAAlB,IACAe,aAAa,CAAC3F,MAAd,GAAuB,CADvB,IAEA6F,aAAa,CAAC7F,MAAd,GAAuB,CAHzB,EAIE;AACA,UAAM,IAAI8F,KAAJ,CACH,+BAA8Bd,QAAS,uBAAxC,GACEZ,gBAAgB,CAAC,SAAD,EAAYuB,aAAZ,CADlB,GAEEvB,gBAAgB,CAAC,SAAD,EAAYyB,aAAZ,CAFlB,GAGEnB,mBAAmB,CAACC,UAAD,CAJjB,CAAN;AAMD;;AAED,SAAO;AAAEe,IAAAA,OAAF;AAAWE,IAAAA;AAAX,GAAP;AACD;AAEM,SAASG,gCAAT,CACLC,OADK,EAELC,QAFK,EAGsB;AAC3B,QAAM;AAAEC,IAAAA,mBAAmB,GAAG;AAAxB,MAA+BF,OAArC;AACA,MAAIE,mBAAmB,KAAK,KAA5B,EAAmC,OAAO,KAAP;AAEnC,QAAMC,MAAM,GAAGF,QAAQ,CAACE,MAAT,CAAgBA,MAAM,IAAIA,MAAJ,oBAAIA,MAAM,CAAEvH,IAAlC,CAAf;AAEA,QAAM;AACJwH,IAAAA,GAAG,GAAG,UADF;AAEJC,IAAAA,MAAM,GAAGF,MAAM,KAAK,qBAAX,GAAmC,OAAnC,GAA6C,QAFlD;AAGJG,IAAAA,GAAG,GAAG;AAHF,MAIFJ,mBAJJ;AAMA,SAAO;AAAEE,IAAAA,GAAF;AAAOC,IAAAA,MAAP;AAAeC,IAAAA;AAAf,GAAP;AACD;;AC1FD,aACEC,YADa,IAEV;AACH,WAASC,QAAT,CAAkB5I,MAAlB,EAA0BC,GAA1B,EAA+B6B,SAA/B,EAA0CnB,IAA1C,EAAgD;AAC9C,WAAOgI,YAAY,CAAC;AAAEE,MAAAA,IAAI,EAAE,UAAR;AAAoB7I,MAAAA,MAApB;AAA4BC,MAAAA,GAA5B;AAAiC6B,MAAAA;AAAjC,KAAD,EAA+CnB,IAA/C,CAAnB;AACD;;AAED,SAAO;AACL;AACAmI,IAAAA,oBAAoB,CAACnI,IAAD,EAA+B;AACjD,YAAM;AACJI,QAAAA,IAAI,EAAE;AAAEC,UAAAA;AAAF,SADF;AAEJH,QAAAA;AAFI,UAGFF,IAHJ;AAIA,UAAIE,KAAK,CAACkI,oBAAN,CAA2B/H,IAA3B,CAAJ,EAAsC;AAEtC2H,MAAAA,YAAY,CAAC;AAAEE,QAAAA,IAAI,EAAE,QAAR;AAAkB7H,QAAAA;AAAlB,OAAD,EAA2BL,IAA3B,CAAZ;AACD,KAVI;;AAYLqI,IAAAA,gBAAgB,CAACrI,IAAD,EAAqC;AACnD,YAAMV,GAAG,GAAGkB,UAAU,CAACR,IAAI,CAACc,GAAL,CAAS,UAAT,CAAD,EAAuBd,IAAI,CAACI,IAAL,CAAUK,QAAjC,CAAtB;AACA,UAAI,CAACnB,GAAD,IAAQA,GAAG,KAAK,WAApB,EAAiC;AAEjC,YAAMD,MAAM,GAAGW,IAAI,CAACc,GAAL,CAAS,QAAT,CAAf;;AACA,UAAIzB,MAAM,CAACY,YAAP,EAAJ,EAA2B;AACzB,cAAMqI,OAAO,GAAGjJ,MAAM,CAACa,KAAP,CAAaqI,UAAb,CAAwBlJ,MAAM,CAACe,IAAP,CAAYC,IAApC,CAAhB;AACA,YAAIiI,OAAO,IAAIA,OAAO,CAACtI,IAAR,CAAawI,0BAAb,EAAf,EAA0D;AAC3D;;AAED,YAAM9G,MAAM,GAAGV,aAAa,CAAC3B,MAAD,CAA5B;AACA,aAAO4I,QAAQ,CAACvG,MAAM,CAACR,EAAR,EAAY5B,GAAZ,EAAiBoC,MAAM,CAACP,SAAxB,EAAmCnB,IAAnC,CAAf;AACD,KAxBI;;AA0BLyI,IAAAA,aAAa,CAACzI,IAAD,EAAkC;AAC7C,YAAM;AAAE0I,QAAAA,UAAF;AAAc9H,QAAAA;AAAd,UAAyBZ,IAA/B;AACA,UAAIiB,GAAJ,CAF6C;;AAK7C,UAAIyH,UAAU,CAACC,oBAAX,EAAJ,EAAuC;AACrC1H,QAAAA,GAAG,GAAGyH,UAAU,CAAC5H,GAAX,CAAe,MAAf,CAAN,CADqC;AAGtC,OAHD,MAGO,IAAI4H,UAAU,CAACE,sBAAX,EAAJ,EAAyC;AAC9C3H,QAAAA,GAAG,GAAGyH,UAAU,CAAC5H,GAAX,CAAe,OAAf,CAAN,CAD8C;AAG9C;AACD,OAJM,MAIA,IAAI4H,UAAU,CAACpH,UAAX,EAAJ,EAA6B;AAClC,cAAMuH,KAAK,GAAGH,UAAU,CAACA,UAAzB;;AACA,YAAIG,KAAK,CAAC/G,gBAAN,MAA4B+G,KAAK,CAACC,eAAN,EAAhC,EAAyD;AACvD,cAAID,KAAK,CAACzI,IAAN,CAAW2B,MAAX,KAAsBnB,MAA1B,EAAkC;AAChCK,YAAAA,GAAG,GAAG4H,KAAK,CAAC/H,GAAN,CAAU,WAAV,EAAuBd,IAAI,CAACV,GAA5B,CAAN;AACD;AACF;AACF;;AAED,UAAI4B,EAAE,GAAG,IAAT;AACA,UAAIC,SAAS,GAAG,IAAhB;AACA,UAAIF,GAAJ,EAAS,CAAC;AAAEC,QAAAA,EAAF;AAAMC,QAAAA;AAAN,UAAoBH,aAAa,CAACC,GAAD,CAAlC;;AAET,WAAK,MAAM8H,IAAX,IAAmB/I,IAAI,CAACc,GAAL,CAAS,YAAT,CAAnB,EAA2C;AACzC,YAAIiI,IAAI,CAACC,gBAAL,EAAJ,EAA6B;AAC3B,gBAAM1J,GAAG,GAAGkB,UAAU,CAACuI,IAAI,CAACjI,GAAL,CAAS,KAAT,CAAD,CAAtB;AACA,cAAIxB,GAAJ,EAAS2I,QAAQ,CAAC/G,EAAD,EAAK5B,GAAL,EAAU6B,SAAV,EAAqB4H,IAArB,CAAR;AACV;AACF;AACF,KAzDI;;AA2DLE,IAAAA,gBAAgB,CAACjJ,IAAD,EAAqC;AACnD,UAAIA,IAAI,CAACI,IAAL,CAAU8I,QAAV,KAAuB,IAA3B,EAAiC;AAEjC,YAAMxH,MAAM,GAAGV,aAAa,CAAChB,IAAI,CAACc,GAAL,CAAS,OAAT,CAAD,CAA5B;AACA,YAAMxB,GAAG,GAAGkB,UAAU,CAACR,IAAI,CAACc,GAAL,CAAS,MAAT,CAAD,EAAmB,IAAnB,CAAtB;AAEA,UAAI,CAACxB,GAAL,EAAU;AAEV0I,MAAAA,YAAY,CACV;AACEE,QAAAA,IAAI,EAAE,IADR;AAEE7I,QAAAA,MAAM,EAAEqC,MAAM,CAACR,EAFjB;AAGE5B,QAAAA,GAHF;AAIE6B,QAAAA,SAAS,EAAEO,MAAM,CAACP;AAJpB,OADU,EAOVnB,IAPU,CAAZ;AASD;;AA5EI,GAAP;AA8ED,CArFD;;ACAA,aACEgI,YADa,KAET;AACJmB,EAAAA,iBAAiB,CAACnJ,IAAD,EAAsC;AACrD,UAAM0B,MAAM,GAAGH,eAAe,CAACvB,IAAD,CAA9B;AACA,QAAI,CAAC0B,MAAL,EAAa;AACbsG,IAAAA,YAAY,CAAC;AAAEE,MAAAA,IAAI,EAAE,QAAR;AAAkBxG,MAAAA;AAAlB,KAAD,EAA6B1B,IAA7B,CAAZ;AACD,GALG;;AAMJoJ,EAAAA,OAAO,CAACpJ,IAAD,EAA4B;AACjCA,IAAAA,IAAI,CAACc,GAAL,CAAS,MAAT,EAAiB7B,OAAjB,CAAyBoK,QAAQ,IAAI;AACnC,YAAM3H,MAAM,GAAGC,gBAAgB,CAAC0H,QAAD,CAA/B;AACA,UAAI,CAAC3H,MAAL,EAAa;AACbsG,MAAAA,YAAY,CAAC;AAAEE,QAAAA,IAAI,EAAE,QAAR;AAAkBxG,QAAAA;AAAlB,OAAD,EAA6B2H,QAA7B,CAAZ;AACD,KAJD;AAKD;;AAZG,CAFS,CAAf;;ACNO,SAASC,OAAT,CACLC,OADK,EAELC,UAFK,EAGLC,eAHK,EAIG;AACR,MAAIA,eAAe,KAAK,KAAxB,EAA+B,OAAOD,UAAP;AAE/B,QAAM,IAAIjC,KAAJ,CACH,yEADG,CAAN;AAGD;;AAGM,SAASpI,GAAT,CAAauK,OAAb,EAA8BrJ,IAA9B,EAA4C;AACjD,SAAO,IAAP;AACD;;AAGM,SAASsJ,UAAT,CAAoBC,WAApB,EAA8C;;AAG9C,SAASC,eAAT,CAAyBD,WAAzB,EAAmD;;ACX1D,MAAME,qBAAqB,GAAG,IAAI9K,GAAJ,CAAgB,CAC5C,QAD4C,EAE5C,YAF4C,EAG5C,MAH4C,EAI5C,QAJ4C,CAAhB,CAA9B;AAOe,SAAS+K,kBAAT,CACbrD,SADa,EAEE;AACf,QAAM;AAAEsD,IAAAA,MAAM,EAAEC,OAAV;AAAmBC,IAAAA,QAAQ,EAAEC,SAA7B;AAAwCC,IAAAA,MAAM,EAAEC;AAAhD,MAA4D3D,SAAlE;AAEA,SAAO4D,IAAI,IAAI;AACb,QAAIA,IAAI,CAACpC,IAAL,KAAc,QAAd,IAA0BmC,OAA1B,IAAqClL,KAAG,CAACkL,OAAD,EAAUC,IAAI,CAACjK,IAAf,CAA5C,EAAkE;AAChE,aAAO;AAAE6H,QAAAA,IAAI,EAAE,QAAR;AAAkBqC,QAAAA,IAAI,EAAEF,OAAO,CAACC,IAAI,CAACjK,IAAN,CAA/B;AAA4CA,QAAAA,IAAI,EAAEiK,IAAI,CAACjK;AAAvD,OAAP;AACD;;AAED,QAAIiK,IAAI,CAACpC,IAAL,KAAc,UAAd,IAA4BoC,IAAI,CAACpC,IAAL,KAAc,IAA9C,EAAoD;AAClD,YAAM;AAAE/G,QAAAA,SAAF;AAAa9B,QAAAA,MAAb;AAAqBC,QAAAA;AAArB,UAA6BgL,IAAnC;;AAEA,UAAIjL,MAAM,IAAI8B,SAAS,KAAK,QAA5B,EAAsC;AACpC,YAAIkJ,OAAO,IAAIP,qBAAqB,CAAC3K,GAAtB,CAA0BE,MAA1B,CAAX,IAAgDF,KAAG,CAACkL,OAAD,EAAU/K,GAAV,CAAvD,EAAuE;AACrE,iBAAO;AAAE4I,YAAAA,IAAI,EAAE,QAAR;AAAkBqC,YAAAA,IAAI,EAAEF,OAAO,CAAC/K,GAAD,CAA/B;AAAsCe,YAAAA,IAAI,EAAEf;AAA5C,WAAP;AACD;;AAED,YAAI2K,OAAO,IAAI9K,KAAG,CAAC8K,OAAD,EAAU5K,MAAV,CAAd,IAAmCF,KAAG,CAAC8K,OAAO,CAAC5K,MAAD,CAAR,EAAkBC,GAAlB,CAA1C,EAAkE;AAChE,iBAAO;AACL4I,YAAAA,IAAI,EAAE,QADD;AAELqC,YAAAA,IAAI,EAAEN,OAAO,CAAC5K,MAAD,CAAP,CAAgBC,GAAhB,CAFD;AAGLe,YAAAA,IAAI,EAAG,GAAEhB,MAAO,IAAGC,GAAI;AAHlB,WAAP;AAKD;AACF;;AAED,UAAI6K,SAAS,IAAIhL,KAAG,CAACgL,SAAD,EAAY7K,GAAZ,CAApB,EAAsC;AACpC,eAAO;AAAE4I,UAAAA,IAAI,EAAE,UAAR;AAAoBqC,UAAAA,IAAI,EAAEJ,SAAS,CAAC7K,GAAD,CAAnC;AAA0Ce,UAAAA,IAAI,EAAG,GAAEf,GAAI;AAAvD,SAAP;AACD;AACF;AACF,GA1BD;AA2BD;;AC1CD,MAAMkL,UAAU,GAAGC,WAAW,CAACC,OAAZ,IAAuBD,WAA1C;;AA8BA,SAASE,cAAT,CACElD,OADF,EAEEC,QAFF,EAaE;AACA,QAAM;AACJkD,IAAAA,MADI;AAEJtF,IAAAA,OAAO,EAAEuF,aAFL;AAGJC,IAAAA,wBAHI;AAIJC,IAAAA,UAJI;AAKJC,IAAAA,KALI;AAMJC,IAAAA,oBANI;AAOJxB,IAAAA,eAPI;AAQJ,OAAGyB;AARC,MASFzD,OATJ;;AAWA,MAAI0D,OAAO,CAAC1D,OAAD,CAAX,EAAsB;AACpB,UAAM,IAAIF,KAAJ,CACH;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFATU,CAAN;AAWD;;AAED,MAAI6D,UAAJ;AACA,MAAIR,MAAM,KAAK,cAAf,EAA+BQ,UAAU,GAAG,aAAb,CAA/B,KACK,IAAIR,MAAM,KAAK,cAAf,EAA+BQ,UAAU,GAAG,aAAb,CAA/B,KACA,IAAIR,MAAM,KAAK,YAAf,EAA6BQ,UAAU,GAAG,WAAb,CAA7B,KACA,IAAI,OAAOR,MAAP,KAAkB,QAAtB,EAAgC;AACnC,UAAM,IAAIrD,KAAJ,CAAU,0BAAV,CAAN;AACD,GAFI,MAEE;AACL,UAAM,IAAIA,KAAJ,CACH,uDAAD,GACG,8BAA6BhC,IAAI,CAACC,SAAL,CAAeoF,MAAf,CAAuB,GAFnD,CAAN;AAID;;AAED,MAAI,OAAOK,oBAAP,KAAgC,UAApC,EAAgD;AAC9C,QAAIxD,OAAO,CAACN,OAAR,IAAmBM,OAAO,CAACJ,OAA/B,EAAwC;AACtC,YAAM,IAAIE,KAAJ,CACH,wDAAD,GACG,kCAFC,CAAN;AAID;AACF,GAPD,MAOO,IAAI0D,oBAAoB,IAAI,IAA5B,EAAkC;AACvC,UAAM,IAAI1D,KAAJ,CACH,wDAAD,GACG,cAAahC,IAAI,CAACC,SAAL,CAAeyF,oBAAf,CAAqC,GAFjD,CAAN;AAID;;AAED,MACExB,eAAe,IAAI,IAAnB,IACA,OAAOA,eAAP,KAA2B,SAD3B,IAEA,OAAOA,eAAP,KAA2B,QAH7B,EAIE;AACA,UAAM,IAAIlC,KAAJ,CACH,4DAAD,GACG,cAAahC,IAAI,CAACC,SAAL,CAAeiE,eAAf,CAAgC,GAF5C,CAAN;AAID;;AAED,MAAInE,OAAJ;;AAEA;AAEE;AACAuF,EAAAA,aAAa,IACbE,UADA,IAEAD,wBALF,EAME;AACA,UAAMO,UAAU,GACd,OAAOR,aAAP,KAAyB,QAAzB,IAAqCvE,KAAK,CAACgF,OAAN,CAAcT,aAAd,CAArC,GACI;AAAEU,MAAAA,QAAQ,EAAEV;AAAZ,KADJ,GAEIA,aAHN;AAKAvF,IAAAA,OAAO,GAAGkF,UAAU,CAACa,UAAD,EAAa;AAC/BP,MAAAA,wBAD+B;AAE/BC,MAAAA;AAF+B,KAAb,CAApB;AAID,GAhBD,MAgBO;AACLzF,IAAAA,OAAO,GAAGoC,QAAQ,CAACpC,OAAT,EAAV;AACD;;AAED,SAAO;AACLsF,IAAAA,MADK;AAELQ,IAAAA,UAFK;AAGL9F,IAAAA,OAHK;AAILmE,IAAAA,eAAe,EAAEA,eAAF,WAAEA,eAAF,GAAqB,KAJ/B;AAKLwB,IAAAA,oBALK;AAMLD,IAAAA,KAAK,EAAE,CAAC,CAACA,KANJ;AAOLE,IAAAA,eAAe,EAAEA;AAPZ,GAAP;AASD;;AAED,SAASM,mBAAT,CACEC,OADF,EAEEhE,OAFF,EAGEE,mBAHF,EAIE4B,OAJF,EAKEmC,QALF,EAMEhE,QANF,EAOE;AACA,QAAM;AACJkD,IAAAA,MADI;AAEJQ,IAAAA,UAFI;AAGJ9F,IAAAA,OAHI;AAIJ0F,IAAAA,KAJI;AAKJC,IAAAA,oBALI;AAMJC,IAAAA,eANI;AAOJzB,IAAAA;AAPI,MAQFkB,cAAc,CAAUlD,OAAV,EAAmBC,QAAnB,CARlB;AAUA,QAAMiE,QAAQ,GAAGxJ,iBAAiB,CAChC,IAAIoB,YAAJ,CAAiBiG,UAAU,IACzBoC,OAAA,CAAarC,OAAb,EAAsBC,UAAtB,EAAkCC,eAAlC,CADF,CADgC,CAAlC,CAXA;;AAkBA,MAAItC,OAAJ,EAAaE,OAAb;AACA,MAAIwE,gBAAJ;AACA,MAAIC,cAAJ;AACA,MAAIC,eAAJ;AAEA,QAAMC,SAAS,GAAG,IAAIzH,GAAJ,EAAlB;AAEA,QAAM0H,GAAgB,GAAG;AACvBC,IAAAA,KAAK,EAAExE,QADgB;AAEvBiE,IAAAA,QAFuB;AAGvBf,IAAAA,MAAM,EAAEnD,OAAO,CAACmD,MAHO;AAIvBtF,IAAAA,OAJuB;AAKvByE,IAAAA,kBALuB;;AAMvBkB,IAAAA,oBAAoB,CAAC5K,IAAD,EAAO;AACzB,UAAIyL,cAAc,KAAK1K,SAAvB,EAAkC;AAChC,cAAM,IAAImG,KAAJ,CACH,yBAAwBkE,OAAO,CAACpL,IAAK,aAAtC,GACG,+DAFC,CAAN;AAID;;AACD,UAAI,CAACyL,cAAc,CAAC3M,GAAf,CAAmBkB,IAAnB,CAAL,EAA+B;AAC7B8L,QAAAA,OAAO,CAACC,IAAR,CACG,yBAAwB3F,QAAQ,CAACpG,IAAK,aAAvC,GACG,qBAAoBA,IAAK,IAF9B;AAID;;AAED,UAAI0L,eAAe,IAAI,CAACA,eAAe,CAAC1L,IAAD,CAAvC,EAA+C,OAAO,KAAP;AAE/C,UAAIgM,YAAY,GAAGC,UAAU,CAACjM,IAAD,EAAOiF,OAAP,EAAgB;AAC3CiH,QAAAA,UAAU,EAAEV,gBAD+B;AAE3CW,QAAAA,QAAQ,EAAErF,OAFiC;AAG3CsF,QAAAA,QAAQ,EAAEpF;AAHiC,OAAhB,CAA7B;;AAMA,UAAI4D,oBAAJ,EAA0B;AACxBoB,QAAAA,YAAY,GAAGpB,oBAAoB,CAAC5K,IAAD,EAAOgM,YAAP,CAAnC;;AACA,YAAI,OAAOA,YAAP,KAAwB,SAA5B,EAAuC;AACrC,gBAAM,IAAI9E,KAAJ,CAAW,8CAAX,CAAN;AACD;AACF;;AAED,aAAO8E,YAAP;AACD,KApCsB;;AAqCvBrB,IAAAA,KAAK,CAAC3K,IAAD,EAAO;AACVqL,MAAAA,QAAQ,GAAGgB,KAAX,GAAmB,IAAnB;AAEA,UAAI,CAAC1B,KAAD,IAAU,CAAC3K,IAAf,EAAqB;AAErB,UAAIqL,QAAQ,GAAGhF,SAAX,CAAqBvH,GAArB,CAAyBsH,QAAQ,CAACpG,IAAlC,CAAJ,EAA6C;AAC7CqL,MAAAA,QAAQ,GAAGhF,SAAX,CAAqBjC,GAArB,CACEpE,IADF,EAEEwL,gBAAgB,IAAIxL,IAApB,IAA4BwL,gBAAgB,CAACxL,IAAD,CAF9C;AAID,KA/CsB;;AAgDvBsM,IAAAA,gBAAgB,CAACtM,IAAD,EAAOuM,OAAO,GAAG,GAAjB,EAAsB;AACpC,UAAIjF,mBAAmB,KAAK,KAA5B,EAAmC;;AACnC,UAAI8B,eAAJ,EAAqB;AACnB;AACA;AACA;AACA;AACD;;AAED,YAAMoD,GAAG,GAAGD,OAAO,KAAK,GAAZ,GAAkBvM,IAAlB,GAA0B,GAAEA,IAAK,KAAIuM,OAAQ,EAAzD;AAEA,YAAMF,KAAK,GAAG/E,mBAAmB,CAACI,GAApB,GACV,KADU,GAEV+E,QAAQ,CAACd,SAAD,EAAa,GAAE3L,IAAK,OAAMkJ,OAAQ,EAAlC,EAAqC,MAC3CqC,GAAA,CAAA,CADM,CAFZ;;AAMA,UAAI,CAACc,KAAL,EAAY;AACVhB,QAAAA,QAAQ,GAAG9B,WAAX,CAAuBxK,GAAvB,CAA2ByN,GAA3B;AACD;AACF;;AApEsB,GAAzB;AAuEA,QAAMpG,QAAQ,GAAGgF,OAAO,CAACQ,GAAD,EAAMf,eAAN,EAAuB3B,OAAvB,CAAxB;;AAEA,MAAI,OAAO9C,QAAQ,CAAC2E,UAAD,CAAf,KAAgC,UAApC,EAAgD;AAC9C,UAAM,IAAI7D,KAAJ,CACH,QAAOd,QAAQ,CAACpG,IAAT,IAAiBoL,OAAO,CAACpL,IAAK,qBAAtC,GACG,gBAAeuK,MAAO,uBAFrB,CAAN;AAID;;AAED,MAAItE,KAAK,CAACgF,OAAN,CAAc7E,QAAQ,CAACC,SAAvB,CAAJ,EAAuC;AACrCoF,IAAAA,cAAc,GAAG,IAAI9M,GAAJ,CAAQyH,QAAQ,CAACC,SAAjB,CAAjB;AACAqF,IAAAA,eAAe,GAAGtF,QAAQ,CAACsF,eAA3B;AACD,GAHD,MAGO,IAAItF,QAAQ,CAACC,SAAb,EAAwB;AAC7BoF,IAAAA,cAAc,GAAG,IAAI9M,GAAJ,CAAQO,MAAM,CAACwN,IAAP,CAAYtG,QAAQ,CAACC,SAArB,CAAR,CAAjB;AACAmF,IAAAA,gBAAgB,GAAGpF,QAAQ,CAACC,SAA5B;AACAqF,IAAAA,eAAe,GAAGtF,QAAQ,CAACsF,eAA3B;AACD,GAJM,MAIA;AACLD,IAAAA,cAAc,GAAG,IAAI9M,GAAJ,EAAjB;AACD;;AAED,GAAC;AAAEmI,IAAAA,OAAF;AAAWE,IAAAA;AAAX,MAAuBb,sBAAsB,CAC5CC,QAAQ,CAACpG,IAAT,IAAiBoL,OAAO,CAACpL,IADmB,EAE5CyL,cAF4C,EAG5CZ,eAAe,CAAC/D,OAAhB,IAA2B,EAHiB,EAI5C+D,eAAe,CAAC7D,OAAhB,IAA2B,EAJiB,CAA9C;AAOA,SAAO;AACL2D,IAAAA,KADK;AAELJ,IAAAA,MAFK;AAGLtF,IAAAA,OAHK;AAILmB,IAAAA,QAJK;;AAKLuB,IAAAA,YAAY,CAACgF,OAAD,EAA0BhN,IAA1B,EAA0C;AACpD,YAAMiN,KAAK,GAAGtB,QAAQ,CAAC3L,IAAD,CAAtB;AACAyG,MAAAA,QAAQ,CAAC2E,UAAD,CAAR,CAAqB4B,OAArB,EAA8BC,KAA9B,EAAqCjN,IAArC;AACD;;AARI,GAAP;AAUD;;AAEc,SAASkN,sBAAT,CACbzB,OADa,EAEb;AACA,SAAO0B,OAAO,CAAC,CAACzF,QAAD,EAAWD,OAAX,EAAmC8B,OAAnC,KAAuD;AACpE7B,IAAAA,QAAQ,CAAC0F,aAAT,CAAuB,CAAvB;AACA,UAAM;AAAEC,MAAAA;AAAF,QAAe3F,QAArB;AAEA,QAAIgE,QAAJ;AAEA,UAAM/D,mBAAmB,GAAGH,gCAAgC,CAC1DC,OAD0D,EAE1DC,QAF0D,CAA5D;AAKA,UAAM;AAAEsD,MAAAA,KAAF;AAASJ,MAAAA,MAAT;AAAiBtF,MAAAA,OAAjB;AAA0BmB,MAAAA,QAA1B;AAAoCuB,MAAAA;AAApC,QACJwD,mBAAmB,CACjBC,OADiB,EAEjBhE,OAFiB,EAGjBE,mBAHiB,EAIjB4B,OAJiB,EAKjB,MAAMmC,QALW,EAMjBhE,QANiB,CADrB;AAUA,UAAM4F,aAAa,GAAG1C,MAAM,KAAK,cAAX,GAA4B1L,KAA5B,GAAsCA,KAA5D;AAEA,UAAMqO,OAAO,GAAG9G,QAAQ,CAAC8G,OAAT,GACZF,QAAQ,CAACG,QAAT,CAAkBC,KAAlB,CAAwB,CAACH,aAAa,CAACtF,YAAD,CAAd,EAA8BvB,QAAQ,CAAC8G,OAAvC,CAAxB,CADY,GAEZD,aAAa,CAACtF,YAAD,CAFjB;;AAIA,QAAIgD,KAAK,IAAIA,KAAK,KAAK5F,0BAAvB,EAAmD;AACjD+G,MAAAA,OAAO,CAACtE,GAAR,CAAa,GAAEpB,QAAQ,CAACpG,IAAK,oBAA7B;AACA8L,MAAAA,OAAO,CAACtE,GAAR,CAAa,oBAAmBxC,yBAAyB,CAACC,OAAD,CAAU,EAAnE;AACA6G,MAAAA,OAAO,CAACtE,GAAR,CAAa,4BAA2B+C,MAAO,YAA/C;AACD;;AAED,WAAO;AACLvK,MAAAA,IAAI,EAAE,kBADD;AAELkN,MAAAA,OAFK;;AAILG,MAAAA,GAAG,GAAG;AAAA;;AACJhC,QAAAA,QAAQ,GAAG;AACThF,UAAAA,SAAS,EAAE,IAAInC,GAAJ,EADF;AAETmI,UAAAA,KAAK,EAAE,KAFE;AAGTiB,UAAAA,SAAS,EAAE,IAAI3O,GAAJ,EAHF;AAIT4K,UAAAA,WAAW,EAAE,IAAI5K,GAAJ;AAJJ,SAAX;AAOA,yBAAAyH,QAAQ,CAACiH,GAAT,mCAAcE,KAAd,CAAoB,IAApB,EAA0B5L,SAA1B;AACD,OAbI;;AAcL6L,MAAAA,IAAI,GAAG;AAAA;;AACL,0BAAApH,QAAQ,CAACoH,IAAT,oCAAeD,KAAf,CAAqB,IAArB,EAA2B5L,SAA3B;;AAEA,YAAI2F,mBAAmB,KAAK,KAA5B,EAAmC;AACjC,cAAIA,mBAAmB,CAACE,GAApB,KAA4B,UAAhC,EAA4C;AAC1C+D,YAAAA,UAAA,CAAgBF,QAAQ,CAAC9B,WAAzB;AACD,WAFD,MAEO;AACLgC,YAAAA,eAAA,CAAqBF,QAAQ,CAAC9B,WAA9B;AACD;AACF;;AAED,YAAI,CAACoB,KAAL,EAAY;AAEZ,YAAI,KAAK8C,QAAT,EAAmB3B,OAAO,CAACtE,GAAR,CAAa,MAAK,KAAKiG,QAAS,GAAhC;;AAEnB,YAAIpC,QAAQ,CAAChF,SAAT,CAAmBL,IAAnB,KAA4B,CAAhC,EAAmC;AACjC8F,UAAAA,OAAO,CAACtE,GAAR,CACE+C,MAAM,KAAK,cAAX,GACIc,QAAQ,CAACgB,KAAT,GACG,8BAA6BjG,QAAQ,CAACpG,IAAK,qCAD9C,GAEG,2BAA0BoG,QAAQ,CAACpG,IAAK,+BAH/C,GAIK,uCAAsCoG,QAAQ,CAACpG,IAAK,qCAL3D;AAQA;AACD;;AAED,YAAIuK,MAAM,KAAK,cAAf,EAA+B;AAC7BuB,UAAAA,OAAO,CAACtE,GAAR,CACG,OAAMpB,QAAQ,CAACpG,IAAK,yCAArB,GACG,0BAFL;AAID,SALD,MAKO;AACL8L,UAAAA,OAAO,CAACtE,GAAR,CACG,OAAMpB,QAAQ,CAACpG,IAAK,0CADvB;AAGD;;AAED,aAAK,MAAM,CAACA,IAAD,EAAO0N,OAAP,CAAX,IAA8BrC,QAAQ,CAAChF,SAAvC,EAAkD;AAChD,cAAIqH,OAAJ,EAAa;AACX,kBAAMC,eAAe,GAAGC,mBAAmB,CAAC5N,IAAD,EAAOiF,OAAP,EAAgByI,OAAhB,CAA3C;AAEA,kBAAMG,gBAAgB,GAAG3I,IAAI,CAACC,SAAL,CAAewI,eAAf,EACtBG,OADsB,CACd,IADc,EACR,IADQ,EAEtBA,OAFsB,CAEd,MAFc,EAEN,KAFM,EAGtBA,OAHsB,CAGd,MAHc,EAGN,KAHM,CAAzB;AAKAhC,YAAAA,OAAO,CAACtE,GAAR,CAAa,KAAIxH,IAAK,IAAG6N,gBAAiB,EAA1C;AACD,WATD,MASO;AACL/B,YAAAA,OAAO,CAACtE,GAAR,CAAa,KAAIxH,IAAK,EAAtB;AACD;AACF;AACF;;AAlEI,KAAP;AAoED,GArGa,CAAd;AAsGD;;AAED,SAASyM,QAAT,CAAkB7H,GAAlB,EAAuB3F,GAAvB,EAA4B8O,UAA5B,EAAwC;AACtC,MAAIC,GAAG,GAAGpJ,GAAG,CAACnE,GAAJ,CAAQxB,GAAR,CAAV;;AACA,MAAI+O,GAAG,KAAKjN,SAAZ,EAAuB;AACrBiN,IAAAA,GAAG,GAAGD,UAAU,EAAhB;AACAnJ,IAAAA,GAAG,CAACR,GAAJ,CAAQnF,GAAR,EAAa+O,GAAb;AACD;;AACD,SAAOA,GAAP;AACD;;AAED,SAASlD,OAAT,CAAiBlK,GAAjB,EAAsB;AACpB,SAAO1B,MAAM,CAACwN,IAAP,CAAY9L,GAAZ,EAAiBQ,MAAjB,KAA4B,CAAnC;AACD;;;;"}